# إصلاح أخطاء SessionHelper في صفحة الملف الشخصي للطبيب

## المشكلة الجديدة
```
الرسالة: Call to undefined method SessionHelper::hasFlashMessage()
الملف: C:\xampp\htdocs\HealthKey\views\doctor\profile.php
السطر: 30
```

## السبب
استخدام دوال غير موجودة في `SessionHelper`:
- `hasFlashMessage()` - غير موجودة
- `getFlashMessage()` - غير موجودة
- `hasValidationError()` - غير موجودة

## الحل

### 1. تصحيح استخدام رسائل Flash
**قبل الإصلاح:**
```php
<?php if (SessionHelper::hasFlashMessage()): ?>
    <?php $flashMessage = SessionHelper::getFlashMessage(); ?>
    <div class="alert alert-<?= $flashMessage['type'] ?>">
        <?= $flashMessage['message'] ?>
    </div>
<?php endif; ?>
```

**بعد الإصلاح:**
```php
<?php if (SessionHelper::hasFlash()): ?>
    <?php 
    $flashMessages = SessionHelper::getAllFlash();
    foreach ($flashMessages as $key => $flashMessage): 
    ?>
    <div class="alert alert-<?= $flashMessage['type'] ?>">
        <?= $flashMessage['message'] ?>
    </div>
    <?php endforeach; ?>
<?php endif; ?>
```

### 2. تصحيح استخدام أخطاء التحقق
**قبل الإصلاح:**
```php
<?php if (SessionHelper::hasValidationError('field_name')): ?>
    <div class="text-danger"><?= SessionHelper::getValidationError('field_name') ?></div>
<?php endif; ?>
```

**بعد الإصلاح:**
```php
<?php if (SessionHelper::getValidationError('field_name')): ?>
    <div class="text-danger"><?= SessionHelper::getValidationError('field_name') ?></div>
<?php endif; ?>
```

## الدوال الصحيحة في SessionHelper

### رسائل Flash:
- `SessionHelper::hasFlash()` - التحقق من وجود رسائل flash
- `SessionHelper::getAllFlash()` - الحصول على جميع رسائل flash
- `SessionHelper::getFlash($key)` - الحصول على رسالة flash محددة
- `SessionHelper::setFlash($key, $message, $type)` - تعيين رسالة flash

### أخطاء التحقق:
- `SessionHelper::getValidationError($field)` - الحصول على خطأ تحقق لحقل محدد
- `SessionHelper::getValidationErrors()` - الحصول على جميع أخطاء التحقق
- `SessionHelper::setValidationErrors($errors)` - تعيين أخطاء التحقق
- `SessionHelper::hasValidationErrors()` - التحقق من وجود أخطاء تحقق

## الملفات المحدثة

### 1. `views/doctor/profile.php`
- تصحيح استخدام `hasFlashMessage()` إلى `hasFlash()`
- تصحيح استخدام `getFlashMessage()` إلى `getAllFlash()`
- تصحيح استخدام `hasValidationError()` إلى `getValidationError()`
- إضافة حلقة foreach لعرض جميع رسائل Flash

### 2. `test_doctor_profile_fixed.php`
- إنشاء ملف اختبار جديد لاختبار الدوال الصحيحة
- اختبار `hasFlash()`, `getAllFlash()`, `getValidationError()`
- اختبار إضافي لوظائف الجلسة

## كيفية الاختبار

### 1. اختبار الصفحة:
```
http://localhost/HealthKey/doctor/profile
```

### 2. اختبار SessionHelper:
```
http://localhost/HealthKey/test_doctor_profile_fixed.php
```

### 3. اختبار رسائل Flash:
```php
SessionHelper::setFlash('success', 'تم الحفظ بنجاح', 'success');
SessionHelper::setFlash('error', 'حدث خطأ', 'danger');
```

### 4. اختبار أخطاء التحقق:
```php
SessionHelper::setValidationErrors([
    'email' => 'البريد الإلكتروني غير صحيح',
    'phone' => 'رقم الهاتف مطلوب'
]);
```

## النتيجة المتوقعة

- ✅ فتح صفحة الملف الشخصي بدون أخطاء
- ✅ عرض رسائل Flash بشكل صحيح
- ✅ عرض أخطاء التحقق بشكل صحيح
- ✅ عمل جميع النماذج بشكل صحيح

## الملفات المضافة:
1. `test_doctor_profile_fixed.php` - ملف اختبار محدث

## الملفات المعدلة:
1. `views/doctor/profile.php` - تصحيح استخدام SessionHelper

## ملاحظات مهمة

### 1. استخدام رسائل Flash:
```php
// تعيين رسالة
SessionHelper::setFlash('success', 'تم الحفظ بنجاح', 'success');

// عرض الرسائل
<?php if (SessionHelper::hasFlash()): ?>
    <?php foreach (SessionHelper::getAllFlash() as $flash): ?>
        <div class="alert alert-<?= $flash['type'] ?>">
            <?= $flash['message'] ?>
        </div>
    <?php endforeach; ?>
<?php endif; ?>
```

### 2. استخدام أخطاء التحقق:
```php
// تعيين أخطاء
SessionHelper::setValidationErrors($errors);

// عرض الأخطاء
<?php if ($error = SessionHelper::getValidationError('field_name')): ?>
    <div class="text-danger"><?= $error ?></div>
<?php endif; ?>
```

### 3. دوال مفيدة أخرى:
- `SessionHelper::isLoggedIn()` - التحقق من تسجيل الدخول
- `SessionHelper::getUserId()` - معرف المستخدم
- `SessionHelper::getUserType()` - نوع المستخدم
- `SessionHelper::getUserName()` - اسم المستخدم 