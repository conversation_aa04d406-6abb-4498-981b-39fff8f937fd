# 📋 صفحة سجل النشاطات - HealthKey

## 🎯 نظرة عامة

صفحة سجل النشاطات هي أداة متقدمة لمراقبة وتتبع جميع الأنشطة التي تحدث في النظام. تتيح للمديرين عرض وتحليل سلوك المستخدمين والعمليات التي تتم في النظام.

## 🚀 المميزات

### ✅ المميزات الأساسية
- **عرض النشاطات**: عرض جميع النشاطات مع التفاصيل الكاملة
- **فلاتر متقدمة**: تصفية حسب النوع، المستخدم، التاريخ، مستوى الأهمية
- **بحث ذكي**: البحث في النصوص والوصف
- **ترقيم الصفحات**: عرض النشاطات بصفحات مع إمكانية تحديد عدد العناصر
- **إحصائيات سريعة**: عرض إحصائيات فورية عن النشاطات

### ✅ المميزات المتقدمة
- **تفاصيل النشاط**: عرض تفاصيل كاملة لكل نشاط
- **حذف النشاطات**: حذف نشاط واحد أو مجموعة نشاطات
- **مسح السجل**: مسح جميع النشاطات دفعة واحدة
- **تصدير البيانات**: تصدير النشاطات بصيغة CSV
- **تصنيف الأهمية**: تصنيف النشاطات حسب مستوى الأهمية

## 📁 الملفات المطلوبة

### 1. ملفات الواجهة الأمامية
```
views/admin/activity_log.php          # صفحة سجل النشاطات الرئيسية
```

### 2. ملفات الخلفية
```
app/controllers/AdminController.php    # متحكم المدير (يحتوي على طرق سجل النشاطات)
app/core/App.php                      # إعداد المسارات
```

### 3. ملفات قاعدة البيانات
```
database/activity_log_table.sql       # هيكل جدول النشاطات
create_activity_log_table.php         # سكريبت إنشاء الجدول
```

### 4. ملفات الاختبار
```
test_activity_log.php                 # اختبار شامل للصفحة
```

## 🗄️ هيكل قاعدة البيانات

### جدول `activity_log`

| العمود | النوع | الوصف |
|--------|-------|--------|
| `id` | INT | المعرف الفريد (Primary Key) |
| `user_id` | INT | معرف المستخدم (Foreign Key) |
| `activity_type` | VARCHAR(50) | نوع النشاط (login, create, update, delete, etc.) |
| `description` | TEXT | وصف النشاط |
| `ip_address` | VARCHAR(45) | عنوان IP للمستخدم |
| `user_agent` | TEXT | معلومات المتصفح |
| `severity` | ENUM | مستوى الأهمية (low, medium, high, critical) |
| `details` | JSON | تفاصيل إضافية للنشاط |
| `created_at` | TIMESTAMP | تاريخ ووقت النشاط |

### أنواع النشاطات المدعومة
- `login` - تسجيل الدخول
- `logout` - تسجيل الخروج
- `create` - إنشاء
- `update` - تحديث
- `delete` - حذف
- `view` - عرض
- `export` - تصدير
- `import` - استيراد
- `error` - خطأ
- `backup` - نسخة احتياطية
- `settings` - إعدادات
- `notification` - إشعارات
- `analytics` - تحليلات
- `message` - رسائل

## 🔧 الإعداد والتثبيت

### الخطوة 1: إنشاء جدول قاعدة البيانات
```bash
php create_activity_log_table.php
```

### الخطوة 2: التحقق من الإعداد
```bash
php test_activity_log.php
```

### الخطوة 3: الوصول للصفحة
```
http://localhost/HealthKey/admin/activity-log
```

## 🎨 واجهة المستخدم

### 📊 الإحصائيات السريعة
- **إجمالي النشاطات**: عدد جميع النشاطات في النظام
- **نشاطات اليوم**: عدد النشاطات التي حدثت اليوم
- **المستخدمين النشطين**: عدد المستخدمين النشطين في آخر 7 أيام
- **الأخطاء**: عدد النشاطات الحرجة

### 🔍 فلاتر البحث
- **نوع النشاط**: تصفية حسب نوع النشاط
- **نوع المستخدم**: تصفية حسب نوع المستخدم (مدير، طبيب، مريض، صيدلي)
- **التاريخ**: تحديد فترة زمنية
- **البحث**: البحث في النصوص
- **مستوى الأهمية**: تصفية حسب الأهمية

### 📋 جدول النشاطات
- **التاريخ والوقت**: تاريخ ووقت النشاط مع الوقت المنقضي
- **المستخدم**: اسم المستخدم ونوعه
- **نوع النشاط**: نوع النشاط مع لون مميز
- **الوصف**: وصف مختصر للنشاط
- **IP العنوان**: عنوان IP للمستخدم
- **مستوى الأهمية**: مستوى الأهمية مع لون مميز
- **الإجراءات**: عرض التفاصيل أو الحذف

## 🔧 الطرق البرمجية

### الطرق العامة في `AdminController`

#### `activityLog()`
```php
public function activityLog()
```
- **الوصف**: الطريقة الرئيسية لعرض صفحة سجل النشاطات
- **الاستخدام**: `GET /admin/activity-log`

#### `handleAjaxActivityLogRequest()`
```php
private function handleAjaxActivityLogRequest()
```
- **الوصف**: معالجة طلبات AJAX لسجل النشاطات
- **الإجراءات المدعومة**: list, details, delete, clear, export

### الطرق الخاصة

#### `getActivityLogList()`
```php
private function getActivityLogList()
```
- **الوصف**: الحصول على قائمة النشاطات مع الفلاتر
- **المعاملات**: JSON object مع الفلاتر
- **الإرجاع**: JSON response مع النشاطات والإحصائيات

#### `getActivityLogDetails()`
```php
private function getActivityLogDetails()
```
- **الوصف**: الحصول على تفاصيل نشاط محدد
- **المعاملات**: activity ID
- **الإرجاع**: JSON response مع تفاصيل النشاط

#### `deleteActivityLog()`
```php
private function deleteActivityLog()
```
- **الوصف**: حذف نشاط أو مجموعة نشاطات
- **المعاملات**: array of activity IDs
- **الإرجاع**: JSON response مع عدد النشاطات المحذوفة

#### `clearActivityLog()`
```php
private function clearActivityLog()
```
- **الوصف**: مسح جميع النشاطات
- **الإرجاع**: JSON response مع رسالة نجاح

#### `exportActivityLog()`
```php
private function exportActivityLog()
```
- **الوصف**: تصدير النشاطات بصيغة CSV
- **المعاملات**: filters from GET parameters
- **الإرجاع**: CSV file download

## 📊 إحصائيات الأداء

### الفهارس المضافة
- `idx_user_id` - فهرس على معرف المستخدم
- `idx_activity_type` - فهرس على نوع النشاط
- `idx_created_at` - فهرس على تاريخ الإنشاء
- `idx_severity` - فهرس على مستوى الأهمية
- `idx_user_activity` - فهرس مركب على المستخدم والنشاط
- `idx_date_severity` - فهرس مركب على التاريخ والأهمية
- `idx_type_date` - فهرس مركب على النوع والتاريخ

### تحسينات الأداء
- **ترقيم الصفحات**: عرض 25 نشاط في الصفحة الواحدة
- **فلاتر ذكية**: استخدام فهارس للبحث السريع
- **تخزين مؤقت**: تخزين الإحصائيات مؤقتاً
- **استعلامات محسنة**: استخدام JOIN بدلاً من استعلامات متعددة

## 🔒 الأمان

### التحقق من الصلاحيات
- التحقق من تسجيل الدخول
- التحقق من نوع المستخدم (admin فقط)
- التحقق من صحة البيانات المدخلة

### حماية البيانات
- تنظيف المدخلات
- استخدام Prepared Statements
- التحقق من صحة المعرفات

## 🐛 استكشاف الأخطاء

### الأخطاء الشائعة

#### 1. جدول `activity_log` غير موجود
```bash
# الحل: تشغيل سكريبت إنشاء الجدول
php create_activity_log_table.php
```

#### 2. خطأ في الاتصال بقاعدة البيانات
```bash
# التحقق من إعدادات config.php
# التأكد من صحة بيانات الاتصال
```

#### 3. خطأ في عرض النشاطات
```bash
# التحقق من وجود البيانات في الجدول
# تشغيل test_activity_log.php للتحقق
```

### رسائل الخطأ
- `خطأ في تحميل النشاطات` - مشكلة في قاعدة البيانات
- `النشاط غير موجود` - معرف النشاط غير صحيح
- `لم يتم تحديد نشاطات للحذف` - لم يتم تحديد نشاطات للحذف

## 📈 التطوير المستقبلي

### المميزات المخطط لها
- **تنبيهات فورية**: إشعارات فورية للنشاطات المهمة
- **تحليلات متقدمة**: رسوم بيانية وتحليلات إحصائية
- **تصدير متعدد**: دعم صيغ تصدير إضافية (PDF, Excel)
- **فلاتر متقدمة**: فلاتر إضافية حسب الوقت والموقع
- **أرشفة تلقائية**: أرشفة النشاطات القديمة تلقائياً

### التحسينات المخطط لها
- **تخزين مؤقت**: تحسين الأداء باستخدام التخزين المؤقت
- **بحث متقدم**: بحث نصي متقدم مع دعم التعبيرات العادية
- **تصدير مجدول**: تصدير تلقائي للتقارير
- **إشعارات ذكية**: إشعارات ذكية للنشاطات المهمة

## 📞 الدعم

### للمطورين
- مراجعة `test_activity_log.php` للتحقق من الإعداد
- مراجعة `ACTIVITY_LOG_README.md` للتوثيق الكامل
- مراجعة `database/activity_log_table.sql` لهيكل قاعدة البيانات

### للمستخدمين
- الوصول للصفحة عبر: `/admin/activity-log`
- استخدام الفلاتر للبحث السريع
- تصدير البيانات للتحليل الخارجي

---

**HealthKey - نظام إدارة الرعاية الصحية**  
*صفحة سجل النشاطات - الإصدار 1.0* 