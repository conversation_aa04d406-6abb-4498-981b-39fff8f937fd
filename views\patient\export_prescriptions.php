<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الوصفات الطبية - <?= htmlspecialchars($patient['name']) ?></title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fff;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .clinic-name {
            font-size: 28px;
            font-weight: 700;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .report-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .report-subtitle {
            font-size: 16px;
            color: #666;
        }
        
        .patient-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin-bottom: 30px;
        }
        
        .patient-info h3 {
            font-size: 18px;
            font-weight: 600;
            color: #007bff;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #555;
        }
        
        .info-value {
            color: #333;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background-color: #fff;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        
        .stat-active .stat-number {
            color: #28a745;
        }
        
        .stat-dispensed .stat-number {
            color: #17a2b8;
        }
        
        .stat-expired .stat-number {
            color: #dc3545;
        }
        
        .stat-cancelled .stat-number {
            color: #6c757d;
        }
        
        .prescriptions-section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #007bff;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .prescription-card {
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .prescription-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .prescription-id {
            font-size: 18px;
            font-weight: 600;
            color: #007bff;
        }
        
        .prescription-date {
            color: #666;
            font-size: 14px;
        }
        
        .prescription-status {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-dispensed {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .status-expired {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status-cancelled {
            background-color: #e2e3e5;
            color: #383d41;
        }
        
        .prescription-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
        }
        
        .detail-label {
            font-weight: 600;
            color: #555;
        }
        
        .detail-value {
            color: #333;
        }
        
        .medications-list {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        
        .medications-title {
            font-weight: 600;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .medication-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        .medication-item:last-child {
            border-bottom: none;
        }
        
        .medication-name {
            font-weight: 600;
            color: #333;
        }
        
        .medication-details {
            color: #666;
            font-size: 14px;
        }
        
        .diagnosis-section {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .diagnosis-title {
            font-weight: 600;
            color: #0c5460;
            margin-bottom: 10px;
        }
        
        .diagnosis-content {
            color: #0c5460;
        }
        
        .notes-section {
            background-color: #e2e3e5;
            border: 1px solid #d6d8db;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .notes-title {
            font-weight: 600;
            color: #383d41;
            margin-bottom: 10px;
        }
        
        .notes-content {
            color: #383d41;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        @media print {
            body {
                padding: 0;
            }
            
            .header {
                border-bottom: 3px solid #007bff;
            }
            
            .patient-info {
                background-color: #f8f9fa !important;
                border: 1px solid #dee2e6 !important;
            }
            
            .prescription-card {
                border: 1px solid #dee2e6 !important;
                box-shadow: none !important;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="clinic-name">HealthKey</div>
        <div class="report-title">تقرير الوصفات الطبية</div>
        <div class="report-subtitle">نظام إدارة الرعاية الصحية</div>
    </div>
    
    <!-- Patient Information -->
    <div class="patient-info">
        <h3>معلومات المريض</h3>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">الاسم:</span>
                <span class="info-value"><?= htmlspecialchars($patient['name']) ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">رقم الهوية:</span>
                <span class="info-value"><?= htmlspecialchars($patient['id_number'] ?? 'غير محدد') ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">تاريخ الميلاد:</span>
                <span class="info-value"><?= $patient['birth_date'] ? date('Y-m-d', strtotime($patient['birth_date'])) : 'غير محدد' ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">رقم الهاتف:</span>
                <span class="info-value"><?= htmlspecialchars($patient['phone'] ?? 'غير محدد') ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">البريد الإلكتروني:</span>
                <span class="info-value"><?= htmlspecialchars($patient['email'] ?? 'غير محدد') ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">تاريخ التقرير:</span>
                <span class="info-value"><?= date('Y-m-d H:i') ?></span>
            </div>
        </div>
    </div>
    
    <!-- Summary Statistics -->
    <div class="summary-stats">
        <div class="stat-card stat-active">
            <div class="stat-number"><?= count(array_filter($prescriptions, fn($p) => $p['status'] === 'active')) ?></div>
            <div class="stat-label">الوصفات النشطة</div>
        </div>
        <div class="stat-card stat-dispensed">
            <div class="stat-number"><?= count(array_filter($prescriptions, fn($p) => $p['status'] === 'dispensed')) ?></div>
            <div class="stat-label">الوصفات المكتملة</div>
        </div>
        <div class="stat-card stat-expired">
            <div class="stat-number"><?= count(array_filter($prescriptions, fn($p) => $p['status'] === 'expired')) ?></div>
            <div class="stat-label">الوصفات المنتهية</div>
        </div>
        <div class="stat-card stat-cancelled">
            <div class="stat-number"><?= count(array_filter($prescriptions, fn($p) => $p['status'] === 'cancelled')) ?></div>
            <div class="stat-label">الوصفات الملغية</div>
        </div>
    </div>
    
    <!-- Prescriptions Section -->
    <div class="prescriptions-section">
        <div class="section-title">الوصفات الطبية</div>
        
        <?php if (empty($prescriptions)): ?>
            <div style="text-align: center; padding: 40px; color: #666;">
                <p>لا توجد وصفات طبية لهذا المريض</p>
            </div>
        <?php else: ?>
            <?php foreach ($prescriptions as $index => $prescription): ?>
                <div class="prescription-card">
                    <div class="prescription-header">
                        <div>
                            <div class="prescription-id">الوصفة رقم #<?= $prescription['id'] ?></div>
                            <div class="prescription-date">تاريخ الوصفة: <?= $prescription['issue_date'] ? date('Y-m-d', strtotime($prescription['issue_date'])) : 'غير محدد' ?></div>
                        </div>
                        <div class="prescription-status status-<?= $prescription['status'] ?>">
                            <?= Prescription::getStatusLabel($prescription['status']) ?>
                        </div>
                    </div>
                    
                    <div class="prescription-details">
                        <div class="detail-item">
                            <span class="detail-label">الطبيب:</span>
                            <span class="detail-value"><?= htmlspecialchars($prescription['doctor_name'] ?? 'غير محدد') ?></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">تاريخ الانتهاء:</span>
                            <span class="detail-value">
                                <?php if ($prescription['expiry_date']): ?>
                                    <?= date('Y-m-d', strtotime($prescription['expiry_date'])) ?>
                                <?php else: ?>
                                    غير محدد
                                <?php endif; ?>
                            </span>
                        </div>
                    </div>
                    
                    <?php if (!empty($prescription['diagnosis'])): ?>
                        <div class="diagnosis-section">
                            <div class="diagnosis-title">التشخيص:</div>
                            <div class="diagnosis-content">
                                <?= nl2br(htmlspecialchars($prescription['diagnosis'])) ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($prescription['medications'])): ?>
                        <div class="medications-list">
                            <div class="medications-title">الأدوية الموصوفة:</div>
                            <?php foreach ($prescription['medications'] as $medication): ?>
                                <div class="medication-item">
                                    <div class="medication-name"><?= htmlspecialchars($medication['medication_name']) ?></div>
                                    <div class="medication-details">
                                        <?= htmlspecialchars($medication['quantity']) ?> <?= htmlspecialchars($medication['unit']) ?> - 
                                        <?= htmlspecialchars($medication['dosage']) ?> <?= htmlspecialchars($medication['dosage_unit'] ?? '') ?> - 
                                        <?= htmlspecialchars($medication['duration']) ?> <?= htmlspecialchars($medication['duration_unit'] ?? '') ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($prescription['notes'])): ?>
                        <div class="notes-section">
                            <div class="notes-title">ملاحظات:</div>
                            <div class="notes-content">
                                <?= nl2br(htmlspecialchars($prescription['notes'])) ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                
                <?php if (($index + 1) % 3 === 0 && $index + 1 < count($prescriptions)): ?>
                    <div class="page-break"></div>
                <?php endif; ?>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <!-- Footer -->
    <div class="footer">
        <p>تم إنشاء هذا التقرير في: <?= date('Y-m-d H:i') ?></p>
        <p>HealthKey - نظام إدارة الرعاية الصحية</p>
        <p>إجمالي الوصفات: <?= count($prescriptions) ?></p>
    </div>
</body>
</html> 