<!-- CSS Links -->
<link href="<?= App::url('public/css/style.css') ?>?v=<?= time() ?>" rel="stylesheet">
<link href="<?= App::url('public/css/admin.css') ?>?v=<?= time() ?>" rel="stylesheet">

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="fw-bold text-primary mb-2">
            <i class="bi bi-chat-dots-fill me-2"></i>
            إدارة الرسائل
        </h1>
        <p class="text-muted mb-0">إرسال رسائل للمستخدمين وإدارة الرسائل النظام</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-success" onclick="openNewMessageModal()">
            <i class="bi bi-plus-circle me-2"></i>
            رسالة جديدة
        </button>
        <button class="btn btn-outline-primary" onclick="refreshMessages()">
            <i class="bi bi-arrow-clockwise"></i>
            تحديث
        </button>
        <a href="<?= App::url('admin/dashboard') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-speedometer2"></i>
            لوحة التحكم
        </a>
    </div>
</div>

<!-- Flash Messages -->
<?php if (isset($_SESSION['flash_message'])): ?>
    <div class="alert alert-<?= $_SESSION['flash_type'] ?? 'info' ?> alert-dismissible fade show" role="alert">
        <i class="bi bi-<?= $_SESSION['flash_type'] === 'success' ? 'check-circle' : ($_SESSION['flash_type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?> me-2"></i>
        <?= $_SESSION['flash_message'] ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['flash_message'], $_SESSION['flash_type']); ?>
<?php endif; ?>

<div class="row">
    <!-- Message Statistics -->
    <div class="col-12 mb-4">
        <div class="row">
            <div class="col-md-3 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">إجمالي الرسائل</h6>
                                <h3 class="mb-0"><?= $totalMessages ?? 0 ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-chat-dots fs-1 opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">الرسائل المرسلة</h6>
                                <h3 class="mb-0"><?= $sentMessages ?? 0 ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-check-circle fs-1 opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">في الانتظار</h6>
                                <h3 class="mb-0"><?= $pendingMessages ?? 0 ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-clock fs-1 opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title">مقروءة</h6>
                                <h3 class="mb-0"><?= $readMessages ?? 0 ?></h3>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-eye fs-1 opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages List -->
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-white">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>
                            قائمة الرسائل
                        </h5>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex gap-2 justify-content-md-end">
                            <select class="form-select form-select-sm" style="width: auto;" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="sent">مرسلة</option>
                                <option value="pending">في الانتظار</option>
                                <option value="read">مقروءة</option>
                                <option value="failed">فشلت</option>
                            </select>
                            <select class="form-select form-select-sm" style="width: auto;" id="typeFilter">
                                <option value="">جميع الأنواع</option>
                                <option value="system">نظام</option>
                                <option value="notification">إشعار</option>
                                <option value="reminder">تذكير</option>
                                <option value="alert">تنبيه</option>
                            </select>
                            <input type="text" class="form-control form-control-sm" placeholder="بحث..." id="searchInput" style="width: 200px;">
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="messagesTable">
                        <thead class="table-light">
                            <tr>
                                <th>
                                    <input type="checkbox" class="form-check-input" id="selectAll">
                                </th>
                                <th>المرسل</th>
                                <th>المستقبل</th>
                                <th>الموضوع</th>
                                <th>النوع</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="messagesTableBody">
                            <!-- سيتم تحميل البيانات عبر JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer bg-white">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <small class="text-muted">
                            عرض <span id="showingStart">1</span> إلى <span id="showingEnd">10</span> من <span id="totalRecords">0</span> رسالة
                        </small>
                    </div>
                    <div class="col-md-6">
                        <nav aria-label="صفحات الرسائل" class="d-flex justify-content-md-end">
                            <ul class="pagination pagination-sm mb-0" id="pagination">
                                <!-- سيتم إنشاء الصفحات عبر JavaScript -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- New Message Modal -->
<div class="modal fade" id="newMessageModal" tabindex="-1" aria-labelledby="newMessageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="newMessageModalLabel">
                    <i class="bi bi-plus-circle me-2"></i>
                    رسالة جديدة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="newMessageForm" method="POST" action="<?= App::url('admin/messages/send') ?>">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="messageType" class="form-label">
                                <i class="bi bi-tag me-1"></i>
                                نوع الرسالة
                                <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="messageType" name="message_type" required>
                                <option value="">اختر نوع الرسالة</option>
                                <option value="system">رسالة نظام</option>
                                <option value="notification">إشعار</option>
                                <option value="reminder">تذكير</option>
                                <option value="alert">تنبيه</option>
                                <option value="announcement">إعلان</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="priority" class="form-label">
                                <i class="bi bi-flag me-1"></i>
                                الأولوية
                            </label>
                            <select class="form-select" id="priority" name="priority">
                                <option value="low">منخفضة</option>
                                <option value="normal" selected>عادية</option>
                                <option value="high">عالية</option>
                                <option value="urgent">عاجلة</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="recipientType" class="form-label">
                                <i class="bi bi-people me-1"></i>
                                نوع المستقبلين
                                <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="recipientType" name="recipient_type" required>
                                <option value="">اختر نوع المستقبلين</option>
                                <option value="all">جميع المستخدمين</option>
                                <option value="patients">المرضى فقط</option>
                                <option value="doctors">الأطباء فقط</option>
                                <option value="pharmacists">الصيادلة فقط</option>
                                <option value="admins">المدراء فقط</option>
                                <option value="specific">مستخدمين محددين</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="sendMethod" class="form-label">
                                <i class="bi bi-send me-1"></i>
                                طريقة الإرسال
                                <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="sendMethod" name="send_method" required>
                                <option value="in_app">داخل التطبيق</option>
                                <option value="email">بريد إلكتروني</option>
                                <option value="sms">رسالة نصية</option>
                                <option value="all">جميع الطرق</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3" id="specificUsersSection" style="display: none;">
                        <label for="specificUsers" class="form-label">
                            <i class="bi bi-person-check me-1"></i>
                            المستخدمين المحددين
                        </label>
                        <select class="form-select" id="specificUsers" name="specific_users[]" multiple>
                            <!-- سيتم تحميل المستخدمين عبر JavaScript -->
                        </select>
                        <div class="form-text">اضغط Ctrl + Click لاختيار عدة مستخدمين</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="subject" class="form-label">
                            <i class="bi bi-type me-1"></i>
                            موضوع الرسالة
                            <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="subject" name="subject" 
                               placeholder="أدخل موضوع الرسالة" required maxlength="200">
                        <div class="form-text">حد أقصى 200 حرف</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="messageContent" class="form-label">
                            <i class="bi bi-chat-text me-1"></i>
                            محتوى الرسالة
                            <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control" id="messageContent" name="message_content" rows="6" 
                                  placeholder="أدخل محتوى الرسالة" required maxlength="1000"></textarea>
                        <div class="form-text">
                            <span id="charCount">0</span> / 1000 حرف
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="scheduledTime" class="form-label">
                            <i class="bi bi-calendar-event me-1"></i>
                            جدولة الإرسال (اختياري)
                        </label>
                        <input type="datetime-local" class="form-control" id="scheduledTime" name="scheduled_time">
                        <div class="form-text">اتركه فارغاً للإرسال الفوري</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-2"></i>
                        إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-send me-2"></i>
                        إرسال الرسالة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Message Details Modal -->
<div class="modal fade" id="messageDetailsModal" tabindex="-1" aria-labelledby="messageDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="messageDetailsModalLabel">
                    <i class="bi bi-chat-dots me-2"></i>
                    تفاصيل الرسالة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="messageDetailsContent">
                <!-- سيتم تحميل تفاصيل الرسالة عبر JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-2"></i>
                    إغلاق
                </button>
                <button type="button" class="btn btn-danger" id="deleteMessageBtn" style="display: none;">
                    <i class="bi bi-trash me-2"></i>
                    حذف الرسالة
                </button>
                <button type="button" class="btn btn-warning" id="resendMessageBtn" style="display: none;">
                    <i class="bi bi-arrow-repeat me-2"></i>
                    إعادة إرسال
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Actions Modal -->
<div class="modal fade" id="bulkActionsModal" tabindex="-1" aria-labelledby="bulkActionsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="bulkActionsModalLabel">
                    <i class="bi bi-gear me-2"></i>
                    إجراءات جماعية
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>تم تحديد <span id="selectedCount">0</span> رسالة</p>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-danger" onclick="bulkDelete()">
                        <i class="bi bi-trash me-2"></i>
                        حذف المحدد
                    </button>
                    <button type="button" class="btn btn-warning" onclick="bulkResend()">
                        <i class="bi bi-arrow-repeat me-2"></i>
                        إعادة إرسال
                    </button>
                    <button type="button" class="btn btn-info" onclick="bulkMarkAsRead()">
                        <i class="bi bi-eye me-2"></i>
                        تحديد كمقروءة
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
            </div>
        </div>
    </div>
</div>

<script>
// متغيرات عامة
let currentPage = 1;
let totalPages = 1;
let selectedMessages = [];

// تحميل الرسائل عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadMessages();
    setupEventListeners();
});

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // البحث
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function() {
            currentPage = 1;
            loadMessages();
        }, 500));
    }
    
    // الفلاتر
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            currentPage = 1;
            loadMessages();
        });
    }
    
    const typeFilter = document.getElementById('typeFilter');
    if (typeFilter) {
        typeFilter.addEventListener('change', function() {
            currentPage = 1;
            loadMessages();
        });
    }
    
    // تحديد الكل
    const selectAll = document.getElementById('selectAll');
    if (selectAll) {
        selectAll.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('#messagesTableBody input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
                if (this.checked) {
                    selectedMessages.push(checkbox.value);
                } else {
                    selectedMessages = [];
                }
            });
            updateBulkActions();
        });
    }
    
    // عداد الأحرف
    const messageContent = document.getElementById('messageContent');
    if (messageContent) {
        messageContent.addEventListener('input', function() {
            const charCount = document.getElementById('charCount');
            if (charCount) {
                charCount.textContent = this.value.length;
            }
        });
    }
    
    // نوع المستقبلين
    const recipientType = document.getElementById('recipientType');
    if (recipientType) {
        recipientType.addEventListener('change', function() {
            const specificSection = document.getElementById('specificUsersSection');
            if (specificSection) {
                if (this.value === 'specific') {
                    specificSection.style.display = 'block';
                    loadUsers();
                } else {
                    specificSection.style.display = 'none';
                }
            }
        });
    }
}

// تحميل الرسائل
function loadMessages() {
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const typeFilter = document.getElementById('typeFilter');
    
    const search = searchInput ? searchInput.value : '';
    const status = statusFilter ? statusFilter.value : '';
    const type = typeFilter ? typeFilter.value : '';
    
    // عرض رسالة تحميل
    const tbody = document.getElementById('messagesTableBody');
    if (tbody) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2 text-muted">جاري تحميل الرسائل...</p>
                </td>
            </tr>
        `;
    }
    
    fetch(`<?= App::url('admin/messages') ?>?page=${currentPage}&search=${search}&status=${status}&type=${type}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Received data:', data); // للتشخيص
        if (data && data.success) {
            renderMessages(data.messages || []);
            renderPagination(data.pagination || {});
            updateStats(data.stats || {});
        } else {
            console.error('Server error:', data);
            showAlert(data?.message || 'خطأ في تحميل الرسائل', 'error');
            renderMessages([]);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال بالخادم', 'error');
        renderMessages([]);
    });
}

// عرض الرسائل
function renderMessages(messages) {
    const tbody = document.getElementById('messagesTableBody');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    if (!messages || messages.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-4">
                    <i class="bi bi-inbox fs-1 text-muted"></i>
                    <p class="mt-2 text-muted">لا توجد رسائل</p>
                </td>
            </tr>
        `;
        return;
    }
    
    messages.forEach(message => {
        if (!message) return;
        
        // التأكد من وجود جميع الحقول المطلوبة
        const safeMessage = {
            id: message.id || 0,
            sender_name: message.sender_name || 'غير محدد',
            sender_type: message.sender_type || 'غير محدد',
            recipient_name: message.recipient_name || 'غير محدد',
            recipient_type: message.recipient_type || 'غير محدد',
            subject: message.subject || 'بدون موضوع',
            content: message.content || '',
            type: message.type || 'notification',
            status: message.status || 'pending',
            created_at: message.created_at || new Date().toISOString()
        };
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <input type="checkbox" class="form-check-input message-checkbox" 
                       value="${safeMessage.id}" onchange="toggleMessageSelection(this)">
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-2">
                        <i class="bi bi-person text-primary"></i>
                    </div>
                    <div>
                        <div class="fw-bold">${safeMessage.sender_name}</div>
                        <small class="text-muted">${safeMessage.sender_type}</small>
                    </div>
                </div>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="avatar-sm bg-success bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-2">
                        <i class="bi bi-person text-success"></i>
                    </div>
                    <div>
                        <div class="fw-bold">${safeMessage.recipient_name}</div>
                        <small class="text-muted">${safeMessage.recipient_type}</small>
                    </div>
                </div>
            </td>
            <td>
                <div class="fw-bold">${safeMessage.subject}</div>
                <small class="text-muted">${safeMessage.content.substring(0, 50)}${safeMessage.content.length > 50 ? '...' : ''}</small>
            </td>
            <td>
                <span class="badge bg-${getTypeColor(safeMessage.type)}">${getTypeText(safeMessage.type)}</span>
            </td>
            <td>
                <span class="badge bg-${getStatusColor(safeMessage.status)}">${getStatusText(safeMessage.status)}</span>
            </td>
            <td>
                <div>${formatDate(safeMessage.created_at)}</div>
                <small class="text-muted">${formatTime(safeMessage.created_at)}</small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary" onclick="viewMessage(${safeMessage.id})">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="resendMessage(${safeMessage.id})">
                        <i class="bi bi-arrow-repeat"></i>
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="deleteMessage(${safeMessage.id})">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// عرض الصفحات
function renderPagination(pagination) {
    const paginationElement = document.getElementById('pagination');
    if (!paginationElement) return;
    
    paginationElement.innerHTML = '';
    
    if (!pagination || pagination.totalPages <= 1) return;
    
    // زر السابق
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${pagination.currentPage <= 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `
        <a class="page-link" href="#" onclick="changePage(${pagination.currentPage - 1})">
            <i class="bi bi-chevron-right"></i>
        </a>
    `;
    paginationElement.appendChild(prevLi);
    
    // أرقام الصفحات
    for (let i = pagination.startPage || 1; i <= (pagination.endPage || pagination.totalPages); i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === pagination.currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        paginationElement.appendChild(li);
    }
    
    // زر التالي
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${pagination.currentPage >= pagination.totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `
        <a class="page-link" href="#" onclick="changePage(${pagination.currentPage + 1})">
            <i class="bi bi-chevron-left"></i>
        </a>
    `;
    paginationElement.appendChild(nextLi);
}

// تغيير الصفحة
function changePage(page) {
    if (page < 1 || page > totalPages) return;
    currentPage = page;
    loadMessages();
}

// تحديث الإحصائيات
function updateStats(stats) {
    const totalElement = document.getElementById('totalMessages');
    const sentElement = document.getElementById('sentMessages');
    const pendingElement = document.getElementById('pendingMessages');
    const readElement = document.getElementById('readMessages');
    
    if (totalElement) totalElement.textContent = stats.total || 0;
    if (sentElement) sentElement.textContent = stats.sent || 0;
    if (pendingElement) pendingElement.textContent = stats.pending || 0;
    if (readElement) readElement.textContent = stats.read || 0;
}

// فتح modal الرسالة الجديدة
function openNewMessageModal() {
    const form = document.getElementById('newMessageForm');
    const charCount = document.getElementById('charCount');
    const specificSection = document.getElementById('specificUsersSection');
    const modal = document.getElementById('newMessageModal');
    
    if (form) form.reset();
    if (charCount) charCount.textContent = '0';
    if (specificSection) specificSection.style.display = 'none';
    if (modal) new bootstrap.Modal(modal).show();
}

// تحميل المستخدمين
function loadUsers() {
    fetch('<?= App::url('admin/messages/users') ?>', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const select = document.getElementById('specificUsers');
            if (select) {
                select.innerHTML = '';
                data.users.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user.id;
                    option.textContent = `${user.first_name} ${user.last_name} (${user.email})`;
                    select.appendChild(option);
                });
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
    });
}

// إرسال رسالة جديدة
document.getElementById('newMessageForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('<?= App::url('admin/messages/send') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم إرسال الرسالة بنجاح', 'success');
            bootstrap.Modal.getInstance(document.getElementById('newMessageModal')).hide();
            loadMessages();
        } else {
            showAlert(data.message || 'خطأ في إرسال الرسالة', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال', 'error');
    });
});

// عرض تفاصيل الرسالة
function viewMessage(messageId) {
    if (!messageId) {
        showAlert('معرف الرسالة غير صحيح', 'error');
        return;
    }
    
    fetch(`<?= App::url('admin/messages') ?>?action=view&id=${messageId}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Message details:', data); // للتشخيص
        
        if (data && data.success && data.message) {
            const message = data.message;
            
            // التأكد من وجود جميع الحقول المطلوبة
            const safeMessage = {
                id: message.id || 0,
                sender_name: message.sender_name || 'غير محدد',
                sender_type: message.sender_type || 'غير محدد',
                recipient_name: message.recipient_name || 'غير محدد',
                recipient_type: message.recipient_type || 'غير محدد',
                subject: message.subject || 'بدون موضوع',
                content: message.content || 'لا يوجد محتوى',
                type: message.type || 'notification',
                status: message.status || 'pending',
                created_at: message.created_at || new Date().toISOString()
            };
            
            const contentElement = document.getElementById('messageDetailsContent');
            if (contentElement) {
                contentElement.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>المرسل:</h6>
                            <p>${safeMessage.sender_name} (${safeMessage.sender_type})</p>
                        </div>
                        <div class="col-md-6">
                            <h6>المستقبل:</h6>
                            <p>${safeMessage.recipient_name} (${safeMessage.recipient_type})</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>النوع:</h6>
                            <p><span class="badge bg-${getTypeColor(safeMessage.type)}">${getTypeText(safeMessage.type)}</span></p>
                        </div>
                        <div class="col-md-6">
                            <h6>الحالة:</h6>
                            <p><span class="badge bg-${getStatusColor(safeMessage.status)}">${getStatusText(safeMessage.status)}</span></p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <h6>الموضوع:</h6>
                            <p>${safeMessage.subject}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>التاريخ:</h6>
                            <p>${formatDate(safeMessage.created_at)} ${formatTime(safeMessage.created_at)}</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <h6>المحتوى:</h6>
                            <div class="border rounded p-3 bg-light">
                                ${safeMessage.content}
                            </div>
                        </div>
                    </div>
                `;
            }
            
            // إظهار/إخفاء أزرار الإجراءات
            const deleteBtn = document.getElementById('deleteMessageBtn');
            const resendBtn = document.getElementById('resendMessageBtn');
            
            if (deleteBtn) {
                deleteBtn.style.display = 'block';
                deleteBtn.onclick = () => deleteMessage(safeMessage.id);
            }
            
            if (resendBtn) {
                resendBtn.style.display = safeMessage.status === 'failed' ? 'block' : 'none';
                resendBtn.onclick = () => resendMessage(safeMessage.id);
            }
            
            const modal = document.getElementById('messageDetailsModal');
            if (modal) {
                new bootstrap.Modal(modal).show();
            }
        } else {
            console.error('Server error:', data);
            showAlert(data?.message || 'خطأ في تحميل تفاصيل الرسالة', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال بالخادم', 'error');
    });
}

// حذف رسالة
function deleteMessage(messageId) {
    if (!confirm('هل أنت متأكد من حذف هذه الرسالة؟')) return;
    
    fetch(`<?= App::url('admin/messages/delete') ?>/${messageId}`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم حذف الرسالة بنجاح', 'success');
            loadMessages();
            bootstrap.Modal.getInstance(document.getElementById('messageDetailsModal')).hide();
        } else {
            showAlert(data.message || 'خطأ في حذف الرسالة', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال', 'error');
    });
}

// إعادة إرسال رسالة
function resendMessage(messageId) {
    if (!confirm('هل تريد إعادة إرسال هذه الرسالة؟')) return;
    
    fetch(`<?= App::url('admin/messages/resend') ?>/${messageId}`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم إعادة إرسال الرسالة بنجاح', 'success');
            loadMessages();
        } else {
            showAlert(data.message || 'خطأ في إعادة إرسال الرسالة', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال', 'error');
    });
}

// تحديد/إلغاء تحديد رسالة
function toggleMessageSelection(checkbox) {
    if (checkbox.checked) {
        selectedMessages.push(checkbox.value);
    } else {
        selectedMessages = selectedMessages.filter(id => id !== checkbox.value);
    }
    updateBulkActions();
}

// تحديث أزرار الإجراءات الجماعية
function updateBulkActions() {
    const selectedCount = document.getElementById('selectedCount');
    const bulkActionsModal = document.getElementById('bulkActionsModal');
    
    if (selectedMessages.length > 0) {
        if (selectedCount) selectedCount.textContent = selectedMessages.length;
        if (bulkActionsModal) new bootstrap.Modal(bulkActionsModal).show();
    }
}

// حذف جماعي
function bulkDelete() {
    if (!confirm(`هل أنت متأكد من حذف ${selectedMessages.length} رسالة؟`)) return;
    
    fetch('<?= App::url('admin/messages/bulk-delete') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ message_ids: selectedMessages })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(`تم حذف ${data.deleted_count} رسالة بنجاح`, 'success');
            selectedMessages = [];
            loadMessages();
            bootstrap.Modal.getInstance(document.getElementById('bulkActionsModal')).hide();
        } else {
            showAlert(data.message || 'خطأ في حذف الرسائل', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال', 'error');
    });
}

// إعادة إرسال جماعي
function bulkResend() {
    if (!confirm(`هل تريد إعادة إرسال ${selectedMessages.length} رسالة؟`)) return;
    
    fetch('<?= App::url('admin/messages/bulk-resend') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ message_ids: selectedMessages })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(`تم إعادة إرسال ${data.resend_count} رسالة بنجاح`, 'success');
            selectedMessages = [];
            loadMessages();
            bootstrap.Modal.getInstance(document.getElementById('bulkActionsModal')).hide();
        } else {
            showAlert(data.message || 'خطأ في إعادة إرسال الرسائل', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال', 'error');
    });
}

// تحديد كمقروءة جماعياً
function bulkMarkAsRead() {
    fetch('<?= App::url('admin/messages/bulk-mark-read') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ message_ids: selectedMessages })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(`تم تحديد ${data.marked_count} رسالة كمقروءة`, 'success');
            selectedMessages = [];
            loadMessages();
            bootstrap.Modal.getInstance(document.getElementById('bulkActionsModal')).hide();
        } else {
            showAlert(data.message || 'خطأ في تحديد الرسائل', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال', 'error');
    });
}

// دوال مساعدة
function getTypeColor(type) {
    const colors = {
        'system': 'primary',
        'notification': 'info',
        'reminder': 'warning',
        'alert': 'danger',
        'announcement': 'success'
    };
    return colors[type] || 'secondary';
}

function getTypeText(type) {
    const texts = {
        'system': 'نظام',
        'notification': 'إشعار',
        'reminder': 'تذكير',
        'alert': 'تنبيه',
        'announcement': 'إعلان'
    };
    return texts[type] || type;
}

function getStatusColor(status) {
    const colors = {
        'sent': 'success',
        'pending': 'warning',
        'read': 'info',
        'failed': 'danger'
    };
    return colors[status] || 'secondary';
}

function getStatusText(status) {
    const texts = {
        'sent': 'مرسلة',
        'pending': 'في الانتظار',
        'read': 'مقروءة',
        'failed': 'فشلت'
    };
    return texts[status] || status;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function formatTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="bi bi-${type === 'success' ? 'check-circle' : (type === 'error' ? 'exclamation-triangle' : 'info-circle')} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
        
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

function refreshMessages() {
    loadMessages();
}

// إضافة زر الإجراءات الجماعية للصفحة
document.addEventListener('DOMContentLoaded', function() {
    const headerActions = document.querySelector('.d-flex.gap-2');
    if (headerActions) {
        const bulkBtn = document.createElement('button');
        bulkBtn.className = 'btn btn-warning';
        bulkBtn.id = 'bulkActionsBtn';
        bulkBtn.innerHTML = '<i class="bi bi-gear me-2"></i>إجراءات جماعية';
        bulkBtn.onclick = updateBulkActions;
        headerActions.appendChild(bulkBtn);
    }
});
</script> 