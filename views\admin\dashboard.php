<!-- CSS Links -->
<link href="<?= App::url('public/css/style.css') ?>?v=<?= time() ?>" rel="stylesheet">
<link href="<?= App::url('public/css/admin.css') ?>?v=<?= time() ?>" rel="stylesheet">

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="fw-bold text-primary mb-2">
            <i class="bi bi-speedometer2 me-2"></i>
            لوحة تحكم المدير
        </h1>
        <p class="text-muted mb-0">نظرة شاملة على النظام والإحصائيات الحية</p>
        <small class="text-muted">آخر تحديث: <?= date('Y-m-d H:i') ?></small>
    </div>
    <div class="d-flex gap-2">
        <?php if (isset($unreadNotifications) && $unreadNotifications > 0): ?>
            <a href="<?= App::url('admin/notifications') ?>" class="btn btn-outline-primary position-relative">
                <i class="bi bi-bell-fill"></i>
                الإشعارات
                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                    <?= $unreadNotifications ?>
                </span>
            </a>
        <?php endif; ?>
        <button class="btn btn-outline-success" onclick="refreshDashboard()">
            <i class="bi bi-arrow-clockwise"></i>
            تحديث
        </button>
        <a href="<?= App::url('admin/backup') ?>" class="btn btn-outline-warning">
            <i class="bi bi-shield-check-fill"></i>
            النسخ الاحتياطي
        </a>
    </div>
</div>

<!-- System Health Alert -->
<?php if (isset($systemHealth) && $systemHealth['database']['status'] !== 'healthy'): ?>
<div class="alert alert-danger alert-dismissible fade show" role="alert">
    <i class="bi bi-exclamation-triangle-fill me-2"></i>
    <strong>تحذير!</strong> هناك مشكلة في قاعدة البيانات. يرجى التحقق من الإعدادات.
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="stats-number"><?= $stats['users']['total'] ?? 0 ?></div>
                        <div class="stats-label">إجمالي المستخدمين</div>
                        <small class="opacity-75 d-flex align-items-center mt-2">
                            <i class="bi bi-arrow-up me-1"></i>
                            +<?= $stats['users']['new_this_month'] ?? 0 ?> هذا الشهر
                        </small>
                    </div>
                    <div class="stats-icon">
                        <i class="bi bi-people-fill"></i>
                    </div>
                </div>
                <div class="progress mt-3" style="height: 4px;">
                    <div class="progress-bar bg-white" style="width: 75%"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card bg-gradient-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="stats-number"><?= $stats['appointments']['total'] ?? 0 ?></div>
                        <div class="stats-label">إجمالي المواعيد</div>
                        <small class="opacity-75 d-flex align-items-center mt-2">
                            <i class="bi bi-calendar-today me-1"></i>
                            <?= $stats['appointments']['today'] ?? 0 ?> اليوم
                        </small>
                    </div>
                    <div class="stats-icon">
                        <i class="bi bi-calendar-check-fill"></i>
                    </div>
                </div>
                <div class="progress mt-3" style="height: 4px;">
                    <div class="progress-bar bg-white" style="width: 60%"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card bg-gradient-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="stats-number"><?= $stats['prescriptions']['total'] ?? 0 ?></div>
                        <div class="stats-label">الوصفات الطبية</div>
                        <small class="opacity-75 d-flex align-items-center mt-2">
                            <i class="bi bi-check-circle me-1"></i>
                            <?= $stats['prescriptions']['active'] ?? 0 ?> نشطة
                        </small>
                    </div>
                    <div class="stats-icon">
                        <i class="bi bi-file-medical-fill"></i>
                    </div>
                </div>
                <div class="progress mt-3" style="height: 4px;">
                    <div class="progress-bar bg-white" style="width: 85%"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card bg-gradient-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <div class="stats-number"><?= $stats['medical_records']['total'] ?? 0 ?></div>
                        <div class="stats-label">السجلات الطبية</div>
                        <small class="opacity-75 d-flex align-items-center mt-2">
                            <i class="bi bi-graph-up me-1"></i>
                            <?= $stats['medical_records']['this_week'] ?? 0 ?> هذا الأسبوع
                        </small>
                    </div>
                    <div class="stats-icon">
                        <i class="bi bi-clipboard2-pulse-fill"></i>
                    </div>
                </div>
                <div class="progress mt-3" style="height: 4px;">
                    <div class="progress-bar bg-white" style="width: 45%"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Type Distribution -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-pie-chart me-2"></i>
                    توزيع المستخدمين
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <canvas id="userDistributionChart" width="400" height="200"></canvas>
                    </div>
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <h4 class="text-primary"><?= $stats['users']['patients'] ?? 0 ?></h4>
                                    <p class="mb-0">المرضى</p>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <h4 class="text-success"><?= $stats['users']['doctors'] ?? 0 ?></h4>
                                    <p class="mb-0">الأطباء</p>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <h4 class="text-info"><?= $stats['users']['pharmacists'] ?? 0 ?></h4>
                                    <p class="mb-0">الصيادلة</p>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="text-center">
                                    <h4 class="text-warning"><?= $stats['users']['admins'] ?? 0 ?></h4>
                                    <p class="mb-0">المديرين</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- System Health -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-activity me-2"></i>
                    حالة النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span>قاعدة البيانات</span>
                        <span class="badge bg-<?= isset($systemHealth) && $systemHealth['database']['status'] === 'healthy' ? 'success' : 'danger' ?>">
                            <?= isset($systemHealth) && $systemHealth['database']['status'] === 'healthy' ? 'سليمة' : 'خطأ' ?>
                        </span>
                    </div>
                    <small class="text-muted">وقت الاستجابة: <?= isset($systemHealth) ? $systemHealth['database']['response_time'] : 'غير متاح' ?></small>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span>التخزين</span>
                        <span class="badge bg-success">سليم</span>
                    </div>
                    <small class="text-muted">مساحة متاحة: <?= isset($systemHealth) ? $systemHealth['storage']['free_space'] : 'غير متاح' ?></small>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span>الذاكرة</span>
                        <span class="badge bg-info">عادي</span>
                    </div>
                    <small class="text-muted">الاستخدام: <?= isset($systemHealth) ? $systemHealth['memory']['usage'] : 'غير متاح' ?></small>
                </div>
                
                <div>
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span>وقت التشغيل</span>
                        <span class="badge bg-primary">نشط</span>
                    </div>
                    <small class="text-muted"><?= isset($systemHealth) ? $systemHealth['uptime'] : 'غير متاح' ?></small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Users -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-person-plus me-2"></i>
                    المستخدمين الجدد
                </h5>
                <a href="<?= App::url('admin/users') ?>" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($recentUsers)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الاسم</th>
                                    <th>النوع</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach (array_slice($recentUsers, 0, 5) as $user): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-light rounded-circle me-2 d-flex align-items-center justify-content-center">
                                                    <i class="bi bi-person text-muted"></i>
                                                </div>
                                                <div>
                                                    <strong><?= User::getFullName($user) ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?= htmlspecialchars($user['email']) ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= User::getTypeColor($user['user_type']) ?>">
                                                <?= User::getTypeLabel($user['user_type']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <small><?= DateHelper::formatArabic($user['created_at'], 'short') ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $user['is_active'] ? 'success' : 'secondary' ?>">
                                                <?= $user['is_active'] ? 'نشط' : 'غير نشط' ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-person-x display-4 text-muted mb-3"></i>
                        <p class="text-muted">لا توجد مستخدمين جدد</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Recent Appointments -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-calendar-event me-2"></i>
                    المواعيد الحديثة
                </h5>
                <a href="<?= App::url('admin/appointments') ?>" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($recentAppointments)): ?>
                    <?php foreach (array_slice($recentAppointments, 0, 5) as $appointment): ?>
                        <div class="d-flex align-items-center mb-3 p-2 bg-light rounded">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    <?= htmlspecialchars($appointment['patient_name'] ?? 'غير محدد') ?>
                                    <i class="bi bi-arrow-left-right mx-2 text-muted"></i>
                                    د. <?= htmlspecialchars($appointment['doctor_name'] ?? 'غير محدد') ?>
                                </h6>
                                <p class="mb-0 text-muted small">
                                    <i class="bi bi-calendar me-1"></i>
                                    <?= DateHelper::formatArabic($appointment['appointment_date'] ?? '') ?>
                                    <i class="bi bi-clock me-1 ms-2"></i>
                                    <?= DateHelper::formatTime($appointment['appointment_time'] ?? '') ?>
                                </p>
                            </div>
                            <div>
                                <span class="badge bg-<?= Appointment::getStatusColor($appointment['status'] ?? 'scheduled') ?>">
                                    <?= Appointment::getStatusLabel($appointment['status'] ?? 'scheduled') ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-calendar-x display-4 text-muted mb-3"></i>
                        <p class="text-muted">لا توجد مواعيد حديثة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="<?= App::url('admin/add-user') ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-person-plus display-6 mb-2"></i>
                            <span class="small">إضافة مستخدم</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="<?= App::url('admin/users') ?>" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-people display-6 mb-2"></i>
                            <span class="small">إدارة المستخدمين</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="<?= App::url('admin/appointments') ?>" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-calendar-check display-6 mb-2"></i>
                            <span class="small">إدارة المواعيد</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="<?= App::url('admin/reports') ?>" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-graph-up display-6 mb-2"></i>
                            <span class="small">التقارير</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="<?= App::url('admin/notifications') ?>" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-bell display-6 mb-2"></i>
                            <span class="small">الإشعارات</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-3">
                        <a href="<?= App::url('admin/settings') ?>" class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-gear display-6 mb-2"></i>
                            <span class="small">الإعدادات</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// User Distribution Chart
const ctx = document.getElementById('userDistributionChart').getContext('2d');
const userDistributionChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['المرضى', 'الأطباء', 'الصيادلة', 'المديرين'],
        datasets: [{
            data: [
                <?= $stats['users']['patients'] ?? 0 ?>,
                <?= $stats['users']['doctors'] ?? 0 ?>,
                <?= $stats['users']['pharmacists'] ?? 0 ?>,
                <?= $stats['users']['admins'] ?? 0 ?>
            ],
            backgroundColor: [
                '#0d6efd',
                '#198754',
                '#0dcaf0',
                '#ffc107'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 20,
                    usePointStyle: true
                }
            }
        }
    }
});

// تحديث الإحصائيات كل 30 ثانية
setInterval(function() {
    fetch('<?= App::url('admin/api/stats') ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث الأرقام في البطاقات
                document.querySelectorAll('.stats-number').forEach((element, index) => {
                    const newValue = Object.values(data.stats)[index]?.total || 0;
                    if (element.textContent != newValue) {
                        element.style.transform = 'scale(1.1)';
                        element.textContent = newValue;
                        setTimeout(() => {
                            element.style.transform = 'scale(1)';
                        }, 200);
                    }
                });
            }
        })
        .catch(error => console.log('خطأ في تحديث الإحصائيات:', error));
}, 30000);

// تأثير تحديث لوحة التحكم
function refreshDashboard() {
    const refreshBtn = document.querySelector('button[onclick="refreshDashboard()"]');
    const icon = refreshBtn.querySelector('i');

    icon.style.animation = 'spin 1s linear infinite';
    refreshBtn.disabled = true;

    setTimeout(() => {
        location.reload();
    }, 1000);
}

// تأثير دوران للأيقونة
const style = document.createElement('style');
style.textContent = `
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script> 