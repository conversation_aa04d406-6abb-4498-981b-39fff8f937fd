<?php
require_once 'config.php';

try {
    $db = Database::getInstance();
    
    echo "<h1>فحص بيانات المخزون</h1>";
    
    // فحص عدد العناصر
    $count = $db->selectOne("SELECT COUNT(*) as count FROM inventory");
    echo "<p>عدد العناصر في المخزون: " . ($count['count'] ?? 0) . "</p>";
    
    // فحص هيكل الجدول
    $columns = $db->select("SHOW COLUMNS FROM inventory");
    echo "<h3>أعمدة الجدول:</h3>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>" . $column['Field'] . " - " . $column['Type'] . "</li>";
    }
    echo "</ul>";
    
    // فحص البيانات الموجودة
    $items = $db->select("SELECT * FROM inventory LIMIT 5");
    echo "<h3>أول 5 عناصر:</h3>";
    if (!empty($items)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>الاسم</th><th>الكمية</th><th>السعر</th><th>تاريخ الانتهاء</th></tr>";
        foreach ($items as $item) {
            echo "<tr>";
            echo "<td>" . $item['id'] . "</td>";
            echo "<td>" . htmlspecialchars($item['name']) . "</td>";
            echo "<td>" . $item['quantity'] . "</td>";
            echo "<td>" . $item['unit_price'] . "</td>";
            echo "<td>" . $item['expiry_date'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد بيانات في الجدول</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}
?> 