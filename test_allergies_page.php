<?php
/**
 * اختبار صفحة الحساسيات للمريض
 * Test Patient Allergies Page
 */

// تضمين ملف التكوين
require_once 'config.php';

// بدء الجلسة
SessionHelper::start();

// محاكاة تسجيل دخول المريض (استبدل بمعرف المريض الفعلي)
$testPatientId = 1; // استبدل بمعرف المريض الفعلي في قاعدة البيانات

// إنشاء بيانات المريض التجريبية
$testPatient = [
    'id' => $testPatientId,
    'first_name' => 'أحمد',
    'last_name' => 'محمد',
    'email' => '<EMAIL>',
    'user_type' => 'patient',
    'is_active' => 1
];

// تسجيل المريض في الجلسة
SessionHelper::set('user_id', $testPatientId);
SessionHelper::set('user_type', 'patient');
SessionHelper::set('current_user', $testPatient);

echo "<h2>اختبار صفحة الحساسيات</h2>";
echo "<p>تم تسجيل دخول المريض: " . $testPatient['first_name'] . " " . $testPatient['last_name'] . "</p>";

// اختبار الوصول للصفحة
try {
    // إنشاء متحكم المريض
    $patientController = new PatientController();
    
    // إنشاء نموذج السجلات الطبية
    $medicalRecordModel = new MedicalRecord();
    
    // الحصول على الحساسيات
    $allergies = $medicalRecordModel->getAllergies($testPatientId);
    
    echo "<h3>اختبار دالة allergies()</h3>";
    echo "<p>عدد الحساسيات: " . count($allergies) . "</p>";
    
    if (!empty($allergies)) {
        echo "<h4>تفاصيل الحساسيات:</h4>";
        foreach ($allergies as $index => $allergy) {
            echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>";
            echo "<h5>الحساسية رقم " . ($index + 1) . "</h5>";
            echo "<p><strong>المادة المسببة:</strong> " . htmlspecialchars($allergy['allergen']) . "</p>";
            echo "<p><strong>رد الفعل:</strong> " . (htmlspecialchars($allergy['reaction']) ?: 'غير محدد') . "</p>";
            echo "<p><strong>الشدة:</strong> " . MedicalRecord::getAllergySeverityLabel($allergy['severity']) . "</p>";
            echo "<p><strong>التاريخ:</strong> " . $allergy['created_at'] . "</p>";
            if (!empty($allergy['notes'])) {
                echo "<p><strong>ملاحظات:</strong> " . htmlspecialchars($allergy['notes']) . "</p>";
            }
            echo "</div>";
        }
    } else {
        echo "<p style='color: orange;'>لا توجد حساسيات مسجلة لهذا المريض.</p>";
    }
    
    // اختبار إضافة حساسية جديدة
    echo "<h3>اختبار إضافة حساسية جديدة</h3>";
    $testAllergyData = [
        'patient_id' => $testPatientId,
        'allergen' => 'البنسلين',
        'reaction' => 'طفح جلدي وحكة',
        'severity' => 'moderate',
        'notes' => 'تظهر الأعراض خلال ساعة من التعرض'
    ];
    
    $allergyId = $medicalRecordModel->addAllergy(
        $testAllergyData['patient_id'],
        $testAllergyData['allergen'],
        $testAllergyData['reaction'],
        $testAllergyData['severity'],
        $testAllergyData['notes']
    );
    
    if ($allergyId) {
        echo "<p style='color: green;'>✓ تم إضافة الحساسية التجريبية بنجاح (ID: $allergyId)</p>";
        
        // اختبار حذف الحساسية
        $deleteSuccess = $medicalRecordModel->deleteAllergy($allergyId);
        if ($deleteSuccess) {
            echo "<p style='color: green;'>✓ تم حذف الحساسية التجريبية بنجاح</p>";
        } else {
            echo "<p style='color: red;'>✗ فشل في حذف الحساسية التجريبية</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ فشل في إضافة الحساسية التجريبية</p>";
    }
    
    echo "<h3>اختبار الرابط</h3>";
    echo "<p>رابط صفحة الحساسيات: <a href='" . App::url('patient/allergies') . "' target='_blank'>فتح صفحة الحساسيات</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في اختبار الصفحة: " . $e->getMessage() . "</p>";
    echo "<p>تفاصيل الخطأ: " . $e->getTraceAsString() . "</p>";
}

echo "<hr>";
echo "<h3>معلومات إضافية</h3>";
echo "<p>معرف المريض: $testPatientId</p>";
echo "<p>نوع المستخدم: " . SessionHelper::get('user_type') . "</p>";
echo "<p>حالة تسجيل الدخول: " . (SessionHelper::isLoggedIn() ? 'مسجل دخول' : 'غير مسجل دخول') . "</p>";

// إضافة زر للعودة
echo "<p><a href='index.php' class='btn btn-primary'>العودة للصفحة الرئيسية</a></p>";
?> 