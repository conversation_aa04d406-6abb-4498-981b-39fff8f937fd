<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-clipboard2-pulse me-2"></i>
            السجلات الطبية
        </h1>
        <p class="text-muted">إدارة وعرض جميع سجلاتك الطبية</p>
    </div>
    <div>
        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
            <i class="bi bi-printer me-2"></i>
            طباعة
        </button>
        <button type="button" class="btn btn-outline-success" onclick="exportMedicalRecords()">
            <i class="bi bi-download me-2"></i>
            تصدير
        </button>
        <a href="<?= App::url('patient/dashboard') ?>" class="btn btn-outline-primary ms-2">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $summary['total_visits'] ?? 0 ?></h4>
                        <p class="mb-0">إجمالي الزيارات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-check display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count($medicalRecords) ?></h4>
                        <p class="mb-0">السجلات الطبية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-file-earmark-text display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count($allergies) ?></h4>
                        <p class="mb-0">الحساسيات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-exclamation-triangle display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count($labTests) ?></h4>
                        <p class="mb-0">فحوصات المختبر</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-droplet display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <label class="form-label">نوع السجل:</label>
                <select class="form-select" id="recordTypeFilter">
                    <option value="">جميع السجلات</option>
                    <option value="medical">السجلات الطبية</option>
                    <option value="allergy">الحساسيات</option>
                    <option value="lab">فحوصات المختبر</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">التاريخ:</label>
                <input type="date" class="form-control" id="dateFilter">
            </div>
            <div class="col-md-4">
                <label class="form-label">البحث:</label>
                <input type="text" class="form-control" id="searchFilter" placeholder="ابحث في السجلات...">
            </div>
        </div>
    </div>
</div>

<!-- Medical Records Section -->
<div class="row">
    <div class="col-lg-8">
        <!-- Medical Records -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-file-earmark-text me-2"></i>
                    السجلات الطبية
                </h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="sortRecords('date')">
                        <i class="bi bi-calendar me-1"></i>
                        التاريخ
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="sortRecords('doctor')">
                        <i class="bi bi-person-badge me-1"></i>
                        الطبيب
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="sortRecords('type')">
                        <i class="bi bi-tag me-1"></i>
                        النوع
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (empty($medicalRecords)): ?>
                    <!-- Empty State -->
                    <div class="text-center py-5">
                        <i class="bi bi-file-earmark-text display-1 text-muted"></i>
                        <h4 class="mt-3 text-muted">لا توجد سجلات طبية</h4>
                        <p class="text-muted">لم يتم إنشاء أي سجلات طبية بعد</p>
                    </div>
                <?php else: ?>
                    <!-- Medical Records List -->
                    <div class="list-group list-group-flush" id="medicalRecordsList">
                        <?php foreach ($medicalRecords as $record): ?>
                            <div class="list-group-item record-item" 
                                 data-type="medical" 
                                 data-date="<?= $record['visit_date'] ?>" 
                                 data-doctor="<?= htmlspecialchars($record['doctor_name']) ?>" 
                                 data-visit-type="<?= $record['visit_type'] ?>">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <h6 class="mb-0">
                                                <i class="bi bi-calendar-check text-primary me-2"></i>
                                                زيارة بتاريخ: <?= date('Y-m-d', strtotime($record['visit_date'])) ?>
                                            </h6>
                                            <div>
                                                <span class="badge bg-<?= MedicalRecord::getVisitTypeColor($record['visit_type']) ?>">
                                                    <?= MedicalRecord::getVisitTypeLabel($record['visit_type']) ?>
                                                </span>
                                            </div>
                                        </div>
                                        
                                        <div class="row mb-2">
                                            <div class="col-md-6">
                                                <small class="text-muted">
                                                    <i class="bi bi-person-badge me-1"></i>
                                                    الطبيب: <?= htmlspecialchars($record['doctor_name']) ?>
                                                </small>
                                            </div>
                                            <div class="col-md-6">
                                                <small class="text-muted">
                                                    <i class="bi bi-clock me-1"></i>
                                                    نوع الزيارة: <?= MedicalRecord::getVisitTypeLabel($record['visit_type']) ?>
                                                </small>
                                            </div>
                                        </div>
                                        
                                        <?php if (!empty($record['chief_complaint'])): ?>
                                            <div class="mb-2">
                                                <strong>الشكوى الرئيسية:</strong>
                                                <p class="mb-1 text-muted"><?= htmlspecialchars(substr($record['chief_complaint'], 0, 100)) ?><?= strlen($record['chief_complaint']) > 100 ? '...' : '' ?></p>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($record['diagnosis'])): ?>
                                            <div class="mb-2">
                                                <strong>التشخيص:</strong>
                                                <p class="mb-1 text-muted"><?= htmlspecialchars(substr($record['diagnosis'], 0, 100)) ?><?= strlen($record['diagnosis']) > 100 ? '...' : '' ?></p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="ms-3">
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-info" 
                                                onclick="viewMedicalRecord(<?= $record['id'] ?>)" 
                                                title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-secondary" 
                                                onclick="printRecord(<?= $record['id'] ?>)" 
                                                title="طباعة">
                                            <i class="bi bi-printer"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Allergies -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    الحساسيات
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($allergies)): ?>
                    <p class="text-muted text-center mb-0">لا توجد حساسيات مسجلة</p>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($allergies as $allergy): ?>
                            <div class="list-group-item px-0 record-item" data-type="allergy">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?= htmlspecialchars($allergy['allergen']) ?></h6>
                                        <p class="mb-1 text-muted"><?= htmlspecialchars($allergy['reaction']) ?></p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="badge bg-<?= MedicalRecord::getAllergySeverityColor($allergy['severity']) ?>">
                                                <?= MedicalRecord::getAllergySeverityLabel($allergy['severity']) ?>
                                            </span>
                                            <small class="text-muted">
                                                <?= date('Y-m-d', strtotime($allergy['created_at'])) ?>
                                            </small>
                                        </div>
                                        <?php if (!empty($allergy['notes'])): ?>
                                            <small class="text-muted d-block mt-1">
                                                <?= htmlspecialchars($allergy['notes']) ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Lab Tests -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-droplet me-2"></i>
                    فحوصات المختبر
                </h6>
            </div>
            <div class="card-body">
                <?php if (empty($labTests)): ?>
                    <p class="text-muted text-center mb-0">لا توجد فحوصات مختبر</p>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($labTests as $test): ?>
                            <div class="list-group-item px-0 record-item" data-type="lab">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1"><?= htmlspecialchars($test['test_name']) ?></h6>
                                        <p class="mb-1 text-muted"><?= htmlspecialchars($test['test_description']) ?></p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="badge bg-<?= MedicalRecord::getLabTestStatusColor($test['status']) ?>">
                                                <?= MedicalRecord::getLabTestStatusLabel($test['status']) ?>
                                            </span>
                                            <small class="text-muted">
                                                <?= date('Y-m-d', strtotime($test['test_date'])) ?>
                                            </small>
                                        </div>
                                        <?php if ($test['status'] === 'completed' && !empty($test['results'])): ?>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-info mt-2" 
                                                    onclick="viewLabTestResults(<?= $test['id'] ?>)">
                                                <i class="bi bi-eye me-1"></i>
                                                عرض النتائج
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Medical Record Details Modal -->
<div class="modal fade" id="medicalRecordModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل السجل الطبي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="medicalRecordDetails">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Lab Test Results Modal -->
<div class="modal fade" id="labTestModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">نتائج فحص المختبر</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="labTestResults">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
let currentSort = 'date';
let currentFilter = '';

// Filter and Search Functions
$('#recordTypeFilter').change(function() {
    currentFilter = $(this).val();
    filterRecords();
});

$('#dateFilter').change(function() {
    filterRecords();
});

$('#searchFilter').on('input', function() {
    filterRecords();
});

function filterRecords() {
    const typeFilter = $('#recordTypeFilter').val();
    const dateFilter = $('#dateFilter').val();
    const searchFilter = $('#searchFilter').val().toLowerCase();
    
    $('.record-item').each(function() {
        let show = true;
        const $item = $(this);
        const type = $item.data('type');
        const date = $item.data('date');
        const doctor = $item.data('doctor') || '';
        const text = $item.text().toLowerCase();
        
        // Type filter
        if (typeFilter && type !== typeFilter) {
            show = false;
        }
        
        // Date filter
        if (dateFilter && date !== dateFilter) {
            show = false;
        }
        
        // Search filter
        if (searchFilter && !text.includes(searchFilter)) {
            show = false;
        }
        
        $item.toggle(show);
    });
}

// Sort Functions
function sortRecords(sortBy) {
    currentSort = sortBy;
    const $list = $('#medicalRecordsList');
    const $items = $list.children('.record-item').get();
    
    $items.sort(function(a, b) {
        const $a = $(a);
        const $b = $(b);
        
        if (sortBy === 'date') {
            return new Date($b.data('date')) - new Date($a.data('date'));
        } else if (sortBy === 'doctor') {
            return ($a.data('doctor') || '').localeCompare($b.data('doctor') || '');
        } else if (sortBy === 'type') {
            return ($a.data('visit-type') || '').localeCompare($b.data('visit-type') || '');
        }
        
        return 0;
    });
    
    $list.empty().append($items);
}

// View Medical Record Details
function viewMedicalRecord(recordId) {
    $.get('<?= App::url('patient/medical-record-details') ?>/' + recordId, function(response) {
        $('#medicalRecordDetails').html(response);
        $('#medicalRecordModal').modal('show');
    }).fail(function() {
        showAlert('حدث خطأ في تحميل تفاصيل السجل الطبي', 'error');
    });
}

// View Lab Test Results
function viewLabTestResults(testId) {
    $.get('<?= App::url('patient/lab-test-results') ?>/' + testId, function(response) {
        $('#labTestResults').html(response);
        $('#labTestModal').modal('show');
    }).fail(function() {
        showAlert('حدث خطأ في تحميل نتائج الفحص', 'error');
    });
}

// Print Record
function printRecord(recordId) {
    window.open('<?= App::url('patient/medical-record-details') ?>/' + recordId + '?print=1', '_blank');
}

// Export Medical Records
function exportMedicalRecords() {
    window.open('<?= App::url('patient/export-medical-records') ?>', '_blank');
}

// Show Alert
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert
    $('.container-fluid').prepend(alertHtml);
    
    // Auto dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}

// Initialize page
$(document).ready(function() {
    // Set default sort
    sortRecords('date');
    
    // Initialize tooltips
    $('[title]').tooltip();
});
</script>

<style>
.list-group-item {
    border-left: none;
    border-right: none;
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}

.badge {
    font-size: 0.75rem;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-lg {
    max-width: 800px;
}

.btn-group .btn {
    font-size: 0.875rem;
}

@media print {
    .btn, .card-header, .modal {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}
</style> 