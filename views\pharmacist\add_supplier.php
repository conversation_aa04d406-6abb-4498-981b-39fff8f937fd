<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">إضافة مورد جديد</h1>
        <p class="text-muted">إضافة مورد جديد لقاعدة البيانات</p>
    </div>
    <div>
        <a href="<?= App::url('pharmacist/suppliers') ?>" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            العودة للموردين
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Form Card -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-plus-circle me-2"></i>
                    معلومات المورد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= App::url('pharmacist/add-supplier') ?>" id="addSupplierForm">
                    <div class="row">
                        <!-- اسم المورد -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">
                                اسم المورد <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   placeholder="أدخل اسم المورد" required>
                            <div class="invalid-feedback">
                                يرجى إدخال اسم المورد
                            </div>
                        </div>

                        <!-- الشخص المسؤول -->
                        <div class="col-md-6 mb-3">
                            <label for="contact_person" class="form-label">
                                الشخص المسؤول
                            </label>
                            <input type="text" class="form-control" id="contact_person" name="contact_person" 
                                   placeholder="أدخل اسم الشخص المسؤول">
                        </div>

                        <!-- البريد الإلكتروني -->
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                البريد الإلكتروني
                            </label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   placeholder="أدخل البريد الإلكتروني">
                            <div class="invalid-feedback">
                                يرجى إدخال بريد إلكتروني صحيح
                            </div>
                        </div>

                        <!-- رقم الهاتف -->
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">
                                رقم الهاتف
                            </label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   placeholder="أدخل رقم الهاتف">
                        </div>

                        <!-- العنوان -->
                        <div class="col-12 mb-3">
                            <label for="address" class="form-label">
                                العنوان
                            </label>
                            <textarea class="form-control" id="address" name="address" rows="3" 
                                      placeholder="أدخل العنوان الكامل"></textarea>
                        </div>

                        <!-- الموقع الإلكتروني -->
                        <div class="col-md-6 mb-3">
                            <label for="website" class="form-label">
                                الموقع الإلكتروني
                            </label>
                            <input type="url" class="form-control" id="website" name="website" 
                                   placeholder="https://example.com">
                            <div class="invalid-feedback">
                                يرجى إدخال رابط صحيح
                            </div>
                        </div>

                        <!-- الحالة -->
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">
                                الحالة
                            </label>
                            <select class="form-select" id="status" name="status">
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>

                        <!-- ملاحظات -->
                        <div class="col-12 mb-3">
                            <label for="notes" class="form-label">
                                ملاحظات
                            </label>
                            <textarea class="form-control" id="notes" name="notes" rows="4" 
                                      placeholder="أدخل أي ملاحظات إضافية"></textarea>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-secondary" onclick="history.back()">
                            <i class="bi bi-x-circle me-2"></i>
                            إلغاء
                        </button>
                        <div>
                            <button type="reset" class="btn btn-outline-secondary me-2">
                                <i class="bi bi-arrow-clockwise me-2"></i>
                                إعادة تعيين
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>
                                إضافة المورد
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Quick Tips -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightbulb me-2"></i>
                    نصائح سريعة
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        تأكد من إدخال اسم المورد بشكل صحيح
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        البريد الإلكتروني اختياري ولكن مفيد للتواصل
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        يمكنك إضافة ملاحظات للمورد لاحقاً
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        يمكن تغيير حالة المورد في أي وقت
                    </li>
                </ul>
            </div>
        </div>

        <!-- Recent Suppliers -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history me-2"></i>
                    آخر الموردين المضافين
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center text-muted">
                    <i class="bi bi-people display-4"></i>
                    <p class="mt-2">لا توجد موردين حديثة</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form Validation
document.getElementById('addSupplierForm').addEventListener('submit', function(e) {
    let isValid = true;
    
    // التحقق من اسم المورد
    const name = document.getElementById('name');
    if (!name.value.trim()) {
        name.classList.add('is-invalid');
        isValid = false;
    } else {
        name.classList.remove('is-invalid');
    }
    
    // التحقق من البريد الإلكتروني إذا تم إدخاله
    const email = document.getElementById('email');
    if (email.value && !isValidEmail(email.value)) {
        email.classList.add('is-invalid');
        isValid = false;
    } else {
        email.classList.remove('is-invalid');
    }
    
    // التحقق من الموقع الإلكتروني إذا تم إدخاله
    const website = document.getElementById('website');
    if (website.value && !isValidUrl(website.value)) {
        website.classList.add('is-invalid');
        isValid = false;
    } else {
        website.classList.remove('is-invalid');
    }
    
    if (!isValid) {
        e.preventDefault();
    }
});

// دالة التحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// دالة التحقق من صحة الرابط
function isValidUrl(url) {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

// تفعيل tooltips
var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
});
</script> 