<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-chat-dots me-2"></i>
            الرسائل
        </h1>
        <p class="text-muted">إدارة الرسائل الواردة والصادرة</p>
    </div>
    <div class="d-flex gap-2">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#composeModal">
            <i class="bi bi-pencil-square me-2"></i>
            رسالة جديدة
        </button>
        <button type="button" class="btn btn-outline-danger" onclick="deleteSelectedMessages()">
            <i class="bi bi-trash me-2"></i>
            حذف المحدد
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['inbox'] ?? 0 ?></h4>
                        <p class="mb-0">الرسائل الواردة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-inbox display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['unread'] ?? 0 ?></h4>
                        <p class="mb-0">غير مقروءة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-envelope display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['sent'] ?? 0 ?></h4>
                        <p class="mb-0">الرسائل الصادرة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-send display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['system'] ?? 0 ?></h4>
                        <p class="mb-0">رسائل النظام</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-gear display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text">
                <i class="bi bi-search"></i>
            </span>
            <input type="text" class="form-control" id="searchMessages" 
                   placeholder="البحث في الرسائل..." onkeyup="searchMessages(this.value)">
        </div>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="filterType" onchange="filterMessages()">
            <option value="">جميع الأنواع</option>
            <option value="system">النظام</option>
            <option value="notification">إشعار</option>
            <option value="reminder">تذكير</option>
            <option value="alert">تنبيه</option>
            <option value="announcement">إعلان</option>
            <option value="personal">شخصي</option>
        </select>
    </div>
    <div class="col-md-3">
        <select class="form-select" id="filterStatus" onchange="filterMessages()">
            <option value="">جميع الحالات</option>
            <option value="sent">غير مقروءة</option>
            <option value="read">مقروءة</option>
            <option value="pending">معلقة</option>
        </select>
    </div>
</div>

<!-- Message Tabs -->
<ul class="nav nav-tabs mb-4" id="messageTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="inbox-tab" data-bs-toggle="tab" data-bs-target="#inbox" type="button" role="tab">
            <i class="bi bi-inbox me-2"></i>
            الواردة
            <?php if ($stats['unread'] > 0): ?>
                <span class="badge bg-danger ms-1"><?= $stats['unread'] ?></span>
            <?php endif; ?>
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="sent-tab" data-bs-toggle="tab" data-bs-target="#sent" type="button" role="tab">
            <i class="bi bi-send me-2"></i>
            الصادرة
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">
            <i class="bi bi-chat-dots me-2"></i>
            الكل
        </button>
    </li>
</ul>

<!-- Message Content -->
<div class="tab-content" id="messageTabContent">
    <!-- Inbox Tab -->
    <div class="tab-pane fade show active" id="inbox" role="tabpanel">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-inbox me-2"></i>
                    الرسائل الواردة
                </h5>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="markAllAsRead()">
                        <i class="bi bi-check-all me-1"></i>
                        تحديد الكل كمقروء
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshMessages()">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="inboxMessages">
                    <?php if (empty($inboxMessages)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-inbox display-1 text-muted"></i>
                            <h5 class="text-muted mt-3">لا توجد رسائل واردة</h5>
                            <p class="text-muted">ستظهر هنا جميع الرسائل الواردة لك</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($inboxMessages as $message): ?>
                                <div class="list-group-item list-group-item-action message-item <?= $message['status'] === 'sent' ? 'unread' : '' ?>" 
                                     data-id="<?= $message['id'] ?>" data-type="<?= $message['type'] ?>" data-status="<?= $message['status'] ?>">
                                    <div class="d-flex align-items-start">
                                        <div class="flex-shrink-0 me-3">
                                            <input type="checkbox" class="form-check-input message-checkbox" value="<?= $message['id'] ?>">
                                        </div>
                                        <div class="flex-shrink-0 me-3">
                                            <div class="message-icon bg-<?= Message::getTypeColor($message['type']) ?> bg-opacity-10 rounded-circle p-2">
                                                <i class="bi <?= Message::getTypeIcon($message['type']) ?> text-<?= Message::getTypeColor($message['type']) ?>"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1 <?= $message['status'] === 'sent' ? 'fw-bold' : '' ?>">
                                                        <?= htmlspecialchars($message['subject']) ?>
                                                        <?php if ($message['status'] === 'sent'): ?>
                                                            <span class="badge bg-danger ms-2">جديد</span>
                                                        <?php endif; ?>
                                                        <?php if ($message['priority'] === 'high'): ?>
                                                            <span class="badge bg-warning ms-1">عالية</span>
                                                        <?php endif; ?>
                                                    </h6>
                                                    <p class="mb-1 text-muted"><?= htmlspecialchars(substr($message['content'], 0, 100)) ?>...</p>
                                                    <small class="text-muted">
                                                        <i class="bi bi-person me-1"></i>
                                                        من: <?= htmlspecialchars($message['sender_name']) ?>
                                                        <span class="mx-2">•</span>
                                                        <i class="bi bi-clock me-1"></i>
                                                        <?= Message::formatTime($message['created_at']) ?>
                                                        <span class="mx-2">•</span>
                                                        <span class="badge bg-<?= Message::getTypeColor($message['type']) ?> bg-opacity-25 text-<?= Message::getTypeColor($message['type']) ?>">
                                                            <?= Message::getTypeLabel($message['type']) ?>
                                                        </span>
                                                    </small>
                                                </div>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                                        <i class="bi bi-three-dots-vertical"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item" href="#" onclick="viewMessage(<?= $message['id'] ?>)">
                                                                <i class="bi bi-eye me-2"></i>
                                                                عرض الرسالة
                                                            </a>
                                                        </li>
                                                        <?php if ($message['status'] === 'sent'): ?>
                                                            <li>
                                                                <a class="dropdown-item" href="#" onclick="markAsRead(<?= $message['id'] ?>)">
                                                                    <i class="bi bi-check me-2"></i>
                                                                    تحديد كمقروء
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                        <li>
                                                            <a class="dropdown-item text-danger" href="#" onclick="deleteMessage(<?= $message['id'] ?>)">
                                                                <i class="bi bi-trash me-2"></i>
                                                                حذف
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Sent Tab -->
    <div class="tab-pane fade" id="sent" role="tabpanel">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-send me-2"></i>
                    الرسائل الصادرة
                </h5>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshMessages()">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="sentMessages">
                    <?php if (empty($sentMessages)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-send display-1 text-muted"></i>
                            <h5 class="text-muted mt-3">لا توجد رسائل صادرة</h5>
                            <p class="text-muted">ستظهر هنا جميع الرسائل التي أرسلتها</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($sentMessages as $message): ?>
                                <div class="list-group-item list-group-item-action message-item" 
                                     data-id="<?= $message['id'] ?>" data-type="<?= $message['type'] ?>" data-status="<?= $message['status'] ?>">
                                    <div class="d-flex align-items-start">
                                        <div class="flex-shrink-0 me-3">
                                            <input type="checkbox" class="form-check-input message-checkbox" value="<?= $message['id'] ?>">
                                        </div>
                                        <div class="flex-shrink-0 me-3">
                                            <div class="message-icon bg-<?= Message::getTypeColor($message['type']) ?> bg-opacity-10 rounded-circle p-2">
                                                <i class="bi <?= Message::getTypeIcon($message['type']) ?> text-<?= Message::getTypeColor($message['type']) ?>"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <?= htmlspecialchars($message['subject']) ?>
                                                        <?php if ($message['priority'] === 'high'): ?>
                                                            <span class="badge bg-warning ms-1">عالية</span>
                                                        <?php endif; ?>
                                                    </h6>
                                                    <p class="mb-1 text-muted"><?= htmlspecialchars(substr($message['content'], 0, 100)) ?>...</p>
                                                    <small class="text-muted">
                                                        <i class="bi bi-person me-1"></i>
                                                        إلى: <?= htmlspecialchars($message['recipient_name']) ?>
                                                        <span class="mx-2">•</span>
                                                        <i class="bi bi-clock me-1"></i>
                                                        <?= Message::formatTime($message['created_at']) ?>
                                                        <span class="mx-2">•</span>
                                                        <span class="badge bg-<?= Message::getTypeColor($message['type']) ?> bg-opacity-25 text-<?= Message::getTypeColor($message['type']) ?>">
                                                            <?= Message::getTypeLabel($message['type']) ?>
                                                        </span>
                                                    </small>
                                                </div>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                                        <i class="bi bi-three-dots-vertical"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item" href="#" onclick="viewMessage(<?= $message['id'] ?>)">
                                                                <i class="bi bi-eye me-2"></i>
                                                                عرض الرسالة
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item text-danger" href="#" onclick="deleteMessage(<?= $message['id'] ?>)">
                                                                <i class="bi bi-trash me-2"></i>
                                                                حذف
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- All Tab -->
    <div class="tab-pane fade" id="all" role="tabpanel">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-chat-dots me-2"></i>
                    جميع الرسائل
                </h5>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshMessages()">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="allMessages">
                    <?php if (empty($allMessages)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-chat-dots display-1 text-muted"></i>
                            <h5 class="text-muted mt-3">لا توجد رسائل</h5>
                            <p class="text-muted">ستظهر هنا جميع الرسائل</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($allMessages as $message): ?>
                                <div class="list-group-item list-group-item-action message-item <?= $message['status'] === 'sent' && $message['recipient_id'] == $_SESSION['user_id'] ? 'unread' : '' ?>" 
                                     data-id="<?= $message['id'] ?>" data-type="<?= $message['type'] ?>" data-status="<?= $message['status'] ?>">
                                    <div class="d-flex align-items-start">
                                        <div class="flex-shrink-0 me-3">
                                            <input type="checkbox" class="form-check-input message-checkbox" value="<?= $message['id'] ?>">
                                        </div>
                                        <div class="flex-shrink-0 me-3">
                                            <div class="message-icon bg-<?= Message::getTypeColor($message['type']) ?> bg-opacity-10 rounded-circle p-2">
                                                <i class="bi <?= Message::getTypeIcon($message['type']) ?> text-<?= Message::getTypeColor($message['type']) ?>"></i>
                                            </div>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1 <?= $message['status'] === 'sent' && $message['recipient_id'] == $_SESSION['user_id'] ? 'fw-bold' : '' ?>">
                                                        <?= htmlspecialchars($message['subject']) ?>
                                                        <?php if ($message['status'] === 'sent' && $message['recipient_id'] == $_SESSION['user_id']): ?>
                                                            <span class="badge bg-danger ms-2">جديد</span>
                                                        <?php endif; ?>
                                                        <?php if ($message['priority'] === 'high'): ?>
                                                            <span class="badge bg-warning ms-1">عالية</span>
                                                        <?php endif; ?>
                                                    </h6>
                                                    <p class="mb-1 text-muted"><?= htmlspecialchars(substr($message['content'], 0, 100)) ?>...</p>
                                                    <small class="text-muted">
                                                        <?php if ($message['sender_id'] == $_SESSION['user_id']): ?>
                                                            <i class="bi bi-person me-1"></i>
                                                            إلى: <?= htmlspecialchars($message['recipient_name']) ?>
                                                        <?php else: ?>
                                                            <i class="bi bi-person me-1"></i>
                                                            من: <?= htmlspecialchars($message['sender_name']) ?>
                                                        <?php endif; ?>
                                                        <span class="mx-2">•</span>
                                                        <i class="bi bi-clock me-1"></i>
                                                        <?= Message::formatTime($message['created_at']) ?>
                                                        <span class="mx-2">•</span>
                                                        <span class="badge bg-<?= Message::getTypeColor($message['type']) ?> bg-opacity-25 text-<?= Message::getTypeColor($message['type']) ?>">
                                                            <?= Message::getTypeLabel($message['type']) ?>
                                                        </span>
                                                    </small>
                                                </div>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                                        <i class="bi bi-three-dots-vertical"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item" href="#" onclick="viewMessage(<?= $message['id'] ?>)">
                                                                <i class="bi bi-eye me-2"></i>
                                                                عرض الرسالة
                                                            </a>
                                                        </li>
                                                        <?php if ($message['status'] === 'sent' && $message['recipient_id'] == $_SESSION['user_id']): ?>
                                                            <li>
                                                                <a class="dropdown-item" href="#" onclick="markAsRead(<?= $message['id'] ?>)">
                                                                    <i class="bi bi-check me-2"></i>
                                                                    تحديد كمقروء
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                        <li>
                                                            <a class="dropdown-item text-danger" href="#" onclick="deleteMessage(<?= $message['id'] ?>)">
                                                                <i class="bi bi-trash me-2"></i>
                                                                حذف
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Compose Modal -->
<div class="modal fade" id="composeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-pencil-square me-2"></i>
                    رسالة جديدة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="composeForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="recipient" class="form-label">إلى:</label>
                            <select class="form-select" id="recipient" name="recipient_id" required>
                                <option value="">اختر المستلم</option>
                                <optgroup label="المرضى">
                                    <?php foreach ($patients as $patient): ?>
                                        <option value="<?= $patient['id'] ?>"><?= User::getFullName($patient) ?></option>
                                    <?php endforeach; ?>
                                </optgroup>
                                <optgroup label="الأطباء">
                                    <?php foreach ($doctors as $doctor): ?>
                                        <option value="<?= $doctor['id'] ?>"><?= User::getFullName($doctor) ?></option>
                                    <?php endforeach; ?>
                                </optgroup>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="messageType" class="form-label">نوع الرسالة:</label>
                            <select class="form-select" id="messageType" name="type">
                                <option value="personal">شخصي</option>
                                <option value="notification">إشعار</option>
                                <option value="reminder">تذكير</option>
                                <option value="alert">تنبيه</option>
                                <option value="announcement">إعلان</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <label for="subject" class="form-label">الموضوع:</label>
                            <input type="text" class="form-control" id="subject" name="subject" required>
                        </div>
                        <div class="col-md-4">
                            <label for="priority" class="form-label">الأولوية:</label>
                            <select class="form-select" id="priority" name="priority">
                                <option value="normal">عادية</option>
                                <option value="high">عالية</option>
                                <option value="urgent">عاجلة</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="content" class="form-label">المحتوى:</label>
                        <textarea class="form-control" id="content" name="content" rows="6" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="sendMessage()">
                    <i class="bi bi-send me-2"></i>
                    إرسال
                </button>
            </div>
        </div>
    </div>
</div>

<!-- View Message Modal -->
<div class="modal fade" id="viewMessageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="messageSubject"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="messageDetails"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="replyToMessage()">
                    <i class="bi bi-reply me-2"></i>
                    رد
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.message-item {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.message-item:hover {
    background-color: #f8f9fa;
    border-left-color: #0d6efd;
}

.message-item.unread {
    background-color: #f0f8ff;
    border-left-color: #0d6efd;
}

.message-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.message-item.unread .message-icon {
    background-color: #0d6efd !important;
    color: white !important;
}

.message-item.unread .message-icon i {
    color: white !important;
}

.list-group-item:last-child {
    border-bottom: none;
}

#messageTabContent {
    max-height: 600px;
    overflow-y: auto;
}

@media (max-width: 768px) {
    .message-item .dropdown {
        position: static;
    }
    
    .message-item .dropdown-menu {
        position: absolute;
        right: 0;
        left: auto;
    }
}
</style>

<script>
let currentTab = 'inbox';
let searchTerm = '';
let filterType = '';
let filterStatus = '';

// تحديث الرسائل
function refreshMessages() {
    location.reload();
}

// البحث في الرسائل
function searchMessages(term) {
    searchTerm = term;
    filterMessages();
}

// فلترة الرسائل
function filterMessages() {
    filterType = document.getElementById('filterType').value;
    filterStatus = document.getElementById('filterStatus').value;
    
    const items = document.querySelectorAll('.message-item');
    let visibleCount = 0;
    
    items.forEach(item => {
        const type = item.dataset.type;
        const status = item.dataset.status;
        const text = item.textContent.toLowerCase();
        
        let show = true;
        
        // فلتر النوع
        if (filterType && type !== filterType) {
            show = false;
        }
        
        // فلتر الحالة
        if (filterStatus && status !== filterStatus) {
            show = false;
        }
        
        // فلتر البحث
        if (searchTerm && !text.includes(searchTerm.toLowerCase())) {
            show = false;
        }
        
        if (show) {
            item.style.display = 'block';
            visibleCount++;
        } else {
            item.style.display = 'none';
        }
    });
}

// تحديد رسالة كمقروءة
function markAsRead(id) {
    fetch('<?= App::url('doctor/mark-message-read') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message_id: id })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const item = document.querySelector(`[data-id="${id}"]`);
            item.classList.remove('unread');
            item.querySelector('.badge.bg-danger')?.remove();
            item.querySelector('h6').classList.remove('fw-bold');
            
            // تحديث العداد
            updateMessageCount();
        } else {
            showAlert('خطأ في تحديث الرسالة', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال', 'danger');
    });
}

// تحديد جميع الرسائل كمقروءة
function markAllAsRead() {
    if (!confirm('هل تريد تحديد جميع الرسائل كمقروءة؟')) {
        return;
    }
    
    fetch('<?= App::url('doctor/mark-all-messages-read') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.querySelectorAll('.message-item.unread').forEach(item => {
                item.classList.remove('unread');
                item.querySelector('.badge.bg-danger')?.remove();
                item.querySelector('h6').classList.remove('fw-bold');
            });
            
            updateMessageCount();
            showAlert('تم تحديد جميع الرسائل كمقروءة', 'success');
        } else {
            showAlert('خطأ في تحديث الرسائل', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال', 'danger');
    });
}

// حذف رسالة
function deleteMessage(id) {
    if (!confirm('هل تريد حذف هذه الرسالة؟')) {
        return;
    }
    
    fetch('<?= App::url('doctor/delete-message') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message_id: id })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const item = document.querySelector(`[data-id="${id}"]`);
            item.style.opacity = '0';
            setTimeout(() => {
                item.remove();
                updateMessageCount();
            }, 300);
        } else {
            showAlert('خطأ في حذف الرسالة', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال', 'danger');
    });
}

// حذف الرسائل المحددة
function deleteSelectedMessages() {
    const selectedCheckboxes = document.querySelectorAll('.message-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        showAlert('يرجى تحديد الرسائل المراد حذفها', 'warning');
        return;
    }
    
    if (!confirm(`هل تريد حذف ${selectedCheckboxes.length} رسالة محددة؟`)) {
        return;
    }
    
    const messageIds = Array.from(selectedCheckboxes).map(cb => cb.value);
    
    fetch('<?= App::url('doctor/delete-messages') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message_ids: messageIds })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            selectedCheckboxes.forEach(checkbox => {
                const item = checkbox.closest('.message-item');
                item.style.opacity = '0';
                setTimeout(() => {
                    item.remove();
                }, 300);
            });
            
            updateMessageCount();
            showAlert(`تم حذف ${selectedCheckboxes.length} رسالة بنجاح`, 'success');
        } else {
            showAlert('خطأ في حذف الرسائل', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال', 'danger');
    });
}

// إرسال رسالة
function sendMessage() {
    const form = document.getElementById('composeForm');
    const formData = new FormData(form);
    
    const data = {
        recipient_id: formData.get('recipient_id'),
        subject: formData.get('subject'),
        content: formData.get('content'),
        type: formData.get('type'),
        priority: formData.get('priority')
    };
    
    fetch('<?= App::url('doctor/send-message') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم إرسال الرسالة بنجاح', 'success');
            document.getElementById('composeModal').querySelector('.btn-close').click();
            form.reset();
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showAlert('خطأ في إرسال الرسالة', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال', 'danger');
    });
}

// عرض رسالة
function viewMessage(id) {
    fetch(`<?= App::url('doctor/get-message') ?>?id=${id}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const message = data.message;
            
            document.getElementById('messageSubject').textContent = message.subject;
            
            const details = document.getElementById('messageDetails');
            details.innerHTML = `
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>من:</strong> ${message.sender_name}
                    </div>
                    <div class="col-md-6">
                        <strong>إلى:</strong> ${message.recipient_name}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>النوع:</strong> 
                        <span class="badge bg-${getTypeColor(message.type)}">${getTypeLabel(message.type)}</span>
                    </div>
                    <div class="col-md-6">
                        <strong>الأولوية:</strong> 
                        <span class="badge bg-${getPriorityColor(message.priority)}">${message.priority}</span>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>التاريخ:</strong> ${formatTime(message.created_at)}
                    </div>
                    <div class="col-md-6">
                        <strong>الحالة:</strong> 
                        <span class="badge bg-${getStatusColor(message.status)}">${getStatusLabel(message.status)}</span>
                    </div>
                </div>
                <hr>
                <div class="message-content">
                    <h6>المحتوى:</h6>
                    <p class="text-muted">${message.content}</p>
                </div>
            `;
            
            // تحديد الرسالة كمقروءة إذا كانت غير مقروءة
            if (message.status === 'sent' && message.recipient_id == <?= $_SESSION['user_id'] ?>) {
                markAsRead(id);
            }
            
            new bootstrap.Modal(document.getElementById('viewMessageModal')).show();
        } else {
            showAlert('خطأ في تحميل الرسالة', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال', 'danger');
    });
}

// الرد على رسالة
function replyToMessage() {
    // إغلاق نافذة عرض الرسالة
    document.getElementById('viewMessageModal').querySelector('.btn-close').click();
    
    // فتح نافذة إنشاء رسالة جديدة
    setTimeout(() => {
        new bootstrap.Modal(document.getElementById('composeModal')).show();
    }, 300);
}

// تحديث عداد الرسائل
function updateMessageCount() {
    const unreadCount = document.querySelectorAll('.message-item.unread').length;
    const badge = document.querySelector('#inbox-tab .badge');
    
    if (unreadCount === 0) {
        badge?.remove();
    } else if (badge) {
        badge.textContent = unreadCount;
    } else {
        const tab = document.getElementById('inbox-tab');
        const newBadge = document.createElement('span');
        newBadge.className = 'badge bg-danger ms-1';
        newBadge.textContent = unreadCount;
        tab.appendChild(newBadge);
    }
}

// دوال مساعدة
function getTypeColor(type) {
    const colors = {
        'system': 'secondary',
        'notification': 'info',
        'reminder': 'warning',
        'alert': 'danger',
        'announcement': 'success',
        'personal': 'primary'
    };
    return colors[type] || 'secondary';
}

function getTypeLabel(type) {
    const labels = {
        'system': 'النظام',
        'notification': 'إشعار',
        'reminder': 'تذكير',
        'alert': 'تنبيه',
        'announcement': 'إعلان',
        'personal': 'شخصي'
    };
    return labels[type] || type;
}

function getPriorityColor(priority) {
    const colors = {
        'low': 'success',
        'normal': 'info',
        'high': 'warning',
        'urgent': 'danger'
    };
    return colors[priority] || 'info';
}

function getStatusColor(status) {
    const colors = {
        'pending': 'warning',
        'sent': 'info',
        'read': 'success',
        'deleted': 'danger'
    };
    return colors[status] || 'secondary';
}

function getStatusLabel(status) {
    const labels = {
        'pending': 'معلقة',
        'sent': 'مرسلة',
        'read': 'مقروءة',
        'deleted': 'محذوفة'
    };
    return labels[status] || status;
}

function formatTime(createdAt) {
    const time = new Date(createdAt);
    const now = new Date();
    const diff = now - time;
    
    if (diff < 60000) {
        return 'الآن';
    } else if (diff < 3600000) {
        const minutes = Math.floor(diff / 60000);
        return `منذ ${minutes} دقيقة`;
    } else if (diff < 86400000) {
        const hours = Math.floor(diff / 3600000);
        return `منذ ${hours} ساعة`;
    } else if (diff < 604800000) {
        const days = Math.floor(diff / 86400000);
        return `منذ ${days} يوم`;
    } else {
        return time.toLocaleDateString('ar-SA');
    }
}

// إظهار تنبيه
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحديث عداد الرسائل
    updateMessageCount();
    
    // إضافة مستمع للبحث
    const searchInput = document.getElementById('searchMessages');
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            searchMessages(this.value);
        }, 300);
    });
    
    // إضافة مستمع لتبديل التبويبات
    const tabs = document.querySelectorAll('[data-bs-toggle="tab"]');
    tabs.forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(e) {
            currentTab = e.target.id.replace('-tab', '');
            filterMessages();
        });
    });
});
</script> 