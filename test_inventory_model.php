<?php
require_once 'config.php';
require_once 'app/models/Inventory.php';

echo "<h1>اختبار نموذج المخزون</h1>";

try {
    $inventoryModel = new Inventory();
    
    echo "<h2>1. اختبار دالة getAll</h2>";
    $allItems = $inventoryModel->getAll(5);
    echo "✅ عدد الأدوية: " . count($allItems) . "<br>";
    
    if (!empty($allItems)) {
        echo "أول دواء: " . $allItems[0]['name'] . "<br>";
    }
    
    echo "<h2>2. اختبار دالة getStats</h2>";
    $stats = $inventoryModel->getStats();
    echo "✅ الإحصائيات: " . json_encode($stats, JSON_UNESCAPED_UNICODE) . "<br>";
    
    echo "<h2>3. اختبار دالة getLowStock</h2>";
    $lowStock = $inventoryModel->getLowStock(5);
    echo "✅ الأدوية منخفضة المخزون: " . count($lowStock) . "<br>";
    
    echo "<h2>4. اختبار دالة getExpired</h2>";
    $expired = $inventoryModel->getExpired(5);
    echo "✅ الأدوية منتهية الصلاحية: " . count($expired) . "<br>";
    
    echo "<h2>5. اختبار دالة getExpiringSoon</h2>";
    $expiringSoon = $inventoryModel->getExpiringSoon(30, 5);
    echo "✅ الأدوية التي تنتهي قريباً: " . count($expiringSoon) . "<br>";
    
    echo "<h2>6. اختبار دالة getCategories</h2>";
    $categories = $inventoryModel->getCategories();
    echo "✅ الفئات: " . count($categories) . "<br>";
    
    echo "<h2>✅ انتهى الاختبار بنجاح</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 