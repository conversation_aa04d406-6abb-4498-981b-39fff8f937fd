<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'لوحة تحكم المدير' ?> - HealthKey</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= App::url('public/css/style.css') ?>?v=<?= time() ?>" rel="stylesheet">
    <link href="<?= App::url('public/css/admin.css') ?>?v=<?= time() ?>" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= App::getBaseUrl() ?>/img/favicon.ico">
</head>
<body class="admin-layout">


    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <i class="bi bi-shield-check text-primary"></i>
                <span class="brand-text">HealthKey</span>
            </div>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="bi bi-x-lg"></i>
            </button>
        </div>

        <!-- User Info -->
        <div class="sidebar-user">
            <div class="user-avatar">
                <i class="bi bi-person-circle"></i>
            </div>
            <div class="user-info">
                <div class="user-name"><?= isset($currentUser) ? $currentUser['first_name'] . ' ' . $currentUser['last_name'] : 'مدير النظام' ?></div>
                <div class="user-role">مدير النظام</div>
            </div>
        </div>

        <!-- Navigation Menu -->
        <nav class="sidebar-nav">
            <ul class="nav flex-column">
                <!-- Dashboard Section -->
                <li class="nav-section">
                    <span class="nav-section-title">الرئيسية</span>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link <?= App::isCurrentPath('admin/dashboard') ? 'active' : '' ?>"
                       href="<?= App::url('admin/dashboard') ?>" data-bs-toggle="tooltip" title="لوحة التحكم">
                        <i class="bi bi-speedometer2"></i>
                        <span>لوحة التحكم</span>
                        <span class="nav-badge">جديد</span>
                    </a>
                </li>

                <!-- Management Section -->
                <li class="nav-section">
                    <span class="nav-section-title">الإدارة</span>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= App::isCurrentPath('admin/users') ? 'active' : '' ?>"
                       href="<?= App::url('admin/users') ?>" data-bs-toggle="tooltip" title="إدارة المستخدمين">
                        <i class="bi bi-people"></i>
                        <span>المستخدمين</span>
                        <span class="nav-badge bg-success"><?= isset($stats['users']['total']) ? $stats['users']['total'] : 0 ?></span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= App::isCurrentPath('admin/appointments') ? 'active' : '' ?>"
                       href="<?= App::url('admin/appointments') ?>" data-bs-toggle="tooltip" title="إدارة المواعيد">
                        <i class="bi bi-calendar-check"></i>
                        <span>المواعيد</span>
                        <span class="nav-badge bg-info"><?= isset($stats['appointments']['total']) ? $stats['appointments']['total'] : 0 ?></span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= App::isCurrentPath('admin/prescriptions') ? 'active' : '' ?>"
                       href="<?= App::url('admin/prescriptions') ?>" data-bs-toggle="tooltip" title="إدارة الوصفات">
                        <i class="bi bi-prescription2"></i>
                        <span>الوصفات</span>
                        <span class="nav-badge bg-warning"><?= isset($stats['prescriptions']['total']) ? $stats['prescriptions']['total'] : 0 ?></span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= App::isCurrentPath('admin/medical-records') ? 'active' : '' ?>"
                       href="<?= App::url('admin/medical-records') ?>" data-bs-toggle="tooltip" title="السجلات الطبية">
                        <i class="bi bi-file-earmark-medical"></i>
                        <span>السجلات الطبية</span>
                    </a>
                </li>

                <!-- Communication Section -->
                <li class="nav-section">
                    <span class="nav-section-title">التواصل</span>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= App::isCurrentPath('admin/notifications') ? 'active' : '' ?>"
                       href="<?= App::url('admin/notifications') ?>" data-bs-toggle="tooltip" title="إدارة الإشعارات">
                        <i class="bi bi-bell"></i>
                        <span>الإشعارات</span>
                        <?php if (isset($unreadNotifications) && $unreadNotifications > 0): ?>
                            <span class="nav-badge bg-danger"><?= $unreadNotifications ?></span>
                        <?php endif; ?>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= App::isCurrentPath('admin/messages') ? 'active' : '' ?>"
                       href="<?= App::url('admin/messages') ?>" data-bs-toggle="tooltip" title="الرسائل">
                        <i class="bi bi-chat-dots"></i>
                        <span>الرسائل</span>
                    </a>
                </li>

                <!-- Analytics Section -->
                <li class="nav-section">
                    <span class="nav-section-title">التحليلات</span>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= App::isCurrentPath('admin/reports') ? 'active' : '' ?>"
                       href="<?= App::url('admin/reports') ?>" data-bs-toggle="tooltip" title="التقارير والإحصائيات">
                        <i class="bi bi-graph-up"></i>
                        <span>التقارير</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= App::isCurrentPath('admin/analytics') ? 'active' : '' ?>"
                       href="<?= App::url('admin/analytics') ?>" data-bs-toggle="tooltip" title="التحليلات المتقدمة">
                        <i class="bi bi-bar-chart"></i>
                        <span>التحليلات</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= App::isCurrentPath('admin/activity-log') ? 'active' : '' ?>"
                       href="<?= App::url('admin/activity-log') ?>" data-bs-toggle="tooltip" title="سجل النشاطات">
                        <i class="bi bi-clock-history"></i>
                        <span>سجل النشاطات</span>
                    </a>
                </li>

                <!-- System Section -->
                <li class="nav-section">
                    <span class="nav-section-title">النظام</span>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= App::isCurrentPath('admin/settings') ? 'active' : '' ?>"
                       href="<?= App::url('admin/settings') ?>" data-bs-toggle="tooltip" title="إعدادات النظام">
                        <i class="bi bi-gear"></i>
                        <span>الإعدادات</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= App::isCurrentPath('admin/backup') ? 'active' : '' ?>"
                       href="<?= App::url('admin/backup') ?>" data-bs-toggle="tooltip" title="النسخ الاحتياطي">
                        <i class="bi bi-shield-check"></i>
                        <span>النسخ الاحتياطي</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= App::isCurrentPath('admin/logs') ? 'active' : '' ?>"
                       href="<?= App::url('admin/logs') ?>" data-bs-toggle="tooltip" title="سجلات النظام">
                        <i class="bi bi-file-text"></i>
                        <span>سجلات النظام</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link <?= App::isCurrentPath('admin/maintenance') ? 'active' : '' ?>"
                       href="<?= App::url('admin/maintenance') ?>" data-bs-toggle="tooltip" title="وضع الصيانة">
                        <i class="bi bi-tools"></i>
                        <span>الصيانة</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Sidebar Footer -->
        <div class="sidebar-footer">
            <div class="system-status">
                <div class="status-item">
                    <i class="bi bi-circle-fill text-success"></i>
                    <span>النظام متصل</span>
                </div>
                <div class="status-item">
                    <i class="bi bi-clock"></i>
                    <span><?= date('H:i') ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Mobile Toggle Button -->
        <div class="mobile-toggle-container d-lg-none">
            <button class="btn btn-primary mobile-toggle-btn" onclick="toggleSidebar()">
                <i class="bi bi-list"></i>
            </button>
        </div>
        
        <div class="container-fluid">
            <!-- Flash Messages -->
            <?php if (isset($flashMessages)): ?>
                <?php foreach ($flashMessages as $key => $flash): ?>
                    <div class="alert alert-<?= $flash['type'] === 'error' ? 'danger' : $flash['type'] ?> alert-dismissible fade show" role="alert">
                        <i class="bi bi-<?= $flash['type'] === 'success' ? 'check-circle' : ($flash['type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?> me-2"></i>
                        <?= htmlspecialchars($flash['message']) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>

            <!-- Page Content -->
            <?= $content ?>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <div class="mt-2">جاري التحميل...</div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
</script>
    
    <script>
        // Global functions
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                <i class="bi bi-${type === 'success' ? 'check-circle' : (type === 'error' ? 'exclamation-triangle' : 'info-circle')} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.main-content .container-fluid');
            container.insertBefore(alertDiv, container.firstChild);
            
            // Auto dismiss after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const toggleButton = document.querySelector('.navbar-toggler');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(event.target) &&
                !toggleButton?.contains(event.target)) {
                sidebar.classList.remove('show');
            }
        });

        // Auto-refresh notifications count
        function updateNotificationsCount() {
            fetch('<?= App::url('api/notifications/count') ?>')
                .then(response => response.json())
                .then(data => {
                    const badge = document.querySelector('.navbar .badge');
                    if (data.count > 0) {
                        if (badge) {
                            badge.textContent = data.count;
                        } else {
                            // Create badge if it doesn't exist
                            const notificationLink = document.getElementById('notificationsDropdown');
                            const newBadge = document.createElement('span');
                            newBadge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger';
                            newBadge.textContent = data.count;
                            notificationLink.appendChild(newBadge);
                        }
                    } else if (badge) {
                        badge.remove();
                    }
                })
                .catch(error => console.log('Error updating notifications:', error));
        }

        // Update notifications count every 30 seconds
        setInterval(updateNotificationsCount, 30000);

        // Enhanced sidebar functionality
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.querySelector('.main-content');
            
            if (window.innerWidth <= 768) {
                sidebar.classList.toggle('show');
                document.body.classList.toggle('sidebar-open');
            } else {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            }
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const toggleButton = document.querySelector('.mobile-toggle-btn');
            
            if (window.innerWidth <= 768 && 
                !sidebar.contains(event.target) && 
                !toggleButton?.contains(event.target)) {
                sidebar.classList.remove('show');
                document.body.classList.remove('sidebar-open');
            }
        });

        // Auto-hide sidebar on mobile when clicking a link
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.addEventListener('click', function() {
                if (window.innerWidth <= 768) {
                    document.getElementById('sidebar').classList.remove('show');
                    document.body.classList.remove('sidebar-open');
                }
            });
        });

        // Update sidebar width based on screen size
        function updateSidebarLayout() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.querySelector('.main-content');
            
            if (window.innerWidth <= 768) {
                sidebar.style.width = '280px';
                mainContent.style.marginRight = '0';
            } else {
                sidebar.style.width = '280px';
                mainContent.style.marginRight = '280px';
            }
        }

        // Initialize sidebar layout
        updateSidebarLayout();
        window.addEventListener('resize', updateSidebarLayout);

        // Confirm before leaving page with unsaved changes
        let hasUnsavedChanges = false;

        function markUnsavedChanges() {
            hasUnsavedChanges = true;
        }

        function markSavedChanges() {
            hasUnsavedChanges = false;
        }

        window.addEventListener('beforeunload', function(e) {
            if (hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = '';
            }
        });

        // Form validation helper
        function validateForm(formId) {
            const form = document.getElementById(formId);
            if (!form) return false;

            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            return isValid;
        }

        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
</body>
</html>
