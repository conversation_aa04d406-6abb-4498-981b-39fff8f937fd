<?php
/**
 * إنشاء جدول ساعات العمل
 */

require_once 'config.php';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إنشاء جدول ساعات العمل
    $sql = "CREATE TABLE IF NOT EXISTS working_hours (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        user_type ENUM('doctor', 'pharmacist', 'nurse') NOT NULL,
        day ENUM('sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday') NOT NULL,
        start_time TIME NOT NULL,
        end_time TIME NOT NULL,
        is_working TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_day (user_id, user_type, day),
        INDEX idx_user_type (user_type),
        INDEX idx_day (day)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    
    echo "✅ تم إنشاء جدول working_hours بنجاح<br>";
    
    // إضافة بعض البيانات التجريبية للصيادلة
    $testPharmacists = [1, 2, 3]; // أرقام الصيادلة التجريبية
    
    foreach ($testPharmacists as $pharmacistId) {
        $days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
        
        foreach ($days as $day) {
            // التحقق من عدم وجود بيانات مكررة
            $checkSql = "SELECT id FROM working_hours WHERE user_id = ? AND user_type = 'pharmacist' AND day = ?";
            $checkStmt = $pdo->prepare($checkSql);
            $checkStmt->execute([$pharmacistId, $day]);
            
            if (!$checkStmt->fetch()) {
                $insertSql = "INSERT INTO working_hours (user_id, user_type, day, start_time, end_time, is_working) 
                              VALUES (?, 'pharmacist', ?, ?, ?, ?)";
                $insertStmt = $pdo->prepare($insertSql);
                
                // تحديد ساعات عمل مختلفة لكل يوم
                switch ($day) {
                    case 'friday':
                        $startTime = '09:00';
                        $endTime = '15:00';
                        $isWorking = 1;
                        break;
                    case 'saturday':
                        $startTime = '10:00';
                        $endTime = '16:00';
                        $isWorking = 1;
                        break;
                    default:
                        $startTime = '08:00';
                        $endTime = '17:00';
                        $isWorking = 1;
                        break;
                }
                
                $insertStmt->execute([$pharmacistId, $day, $startTime, $endTime, $isWorking]);
            }
        }
    }
    
    echo "✅ تم إضافة بيانات تجريبية لساعات العمل<br>";
    
    // عرض هيكل الجدول
    echo "<h3>هيكل جدول working_hours:</h3>";
    $describeSql = "DESCRIBE working_hours";
    $describeStmt = $pdo->query($describeSql);
    $columns = $describeStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>الحقل</th><th>النوع</th><th>NULL</th><th>المفتاح</th><th>الافتراضي</th><th>إضافي</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // عرض البيانات المضافة
    echo "<h3>البيانات المضافة:</h3>";
    $selectSql = "SELECT * FROM working_hours WHERE user_type = 'pharmacist' ORDER BY user_id, day";
    $selectStmt = $pdo->query($selectSql);
    $data = $selectStmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($data)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>User ID</th><th>User Type</th><th>Day</th><th>Start Time</th><th>End Time</th><th>Is Working</th></tr>";
        
        foreach ($data as $row) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['user_id'] . "</td>";
            echo "<td>" . $row['user_type'] . "</td>";
            echo "<td>" . $row['day'] . "</td>";
            echo "<td>" . $row['start_time'] . "</td>";
            echo "<td>" . $row['end_time'] . "</td>";
            echo "<td>" . ($row['is_working'] ? 'نعم' : 'لا') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<br><strong>✅ تم إنشاء وإعداد جدول ساعات العمل بنجاح!</strong>";
    
} catch (PDOException $e) {
    echo "❌ خطأ في إنشاء الجدول: " . $e->getMessage();
}
?> 