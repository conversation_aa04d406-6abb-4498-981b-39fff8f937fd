<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-capsule me-2"></i>
            الأدوية
        </h1>
        <p class="text-muted">استكشف الأدوية المتاحة والمعلومات الطبية</p>
    </div>
    <div>
        <a href="<?= App::url('patient/dashboard') ?>" class="btn btn-outline-primary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['total_medications'] ?? 0 ?></h4>
                        <p class="mb-0">إجمالي الأدوية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-capsule display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['active_medications'] ?? 0 ?></h4>
                        <p class="mb-0">الأدوية النشطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['otc_medications'] ?? 0 ?></h4>
                        <p class="mb-0">أدوية بدون وصفة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-bag display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['prescription_required'] ?? 0 ?></h4>
                        <p class="mb-0">تحتاج وصفة طبية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-prescription display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?= App::url('patient/medications') ?>" id="medicationSearchForm">
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label">البحث:</label>
                    <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" 
                           placeholder="ابحث في الأدوية...">
                </div>
                <div class="col-md-3">
                    <label class="form-label">الفئة:</label>
                    <select class="form-select" name="category">
                        <option value="">جميع الفئات</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?= htmlspecialchars($cat['category']) ?>" 
                                    <?= $selectedCategory === $cat['category'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($cat['category']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">نوع الدواء:</label>
                    <select class="form-select" name="prescription_required">
                        <option value="">جميع الأنواع</option>
                        <option value="0" <?= $selectedPrescriptionRequired === '0' ? 'selected' : '' ?>>بدون وصفة طبية</option>
                        <option value="1" <?= $selectedPrescriptionRequired === '1' ? 'selected' : '' ?>>تحتاج وصفة طبية</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search me-2"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Popular Medications -->
<?php if (!empty($popularMedications)): ?>
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-star me-2"></i>
            الأدوية الشائعة
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <?php foreach ($popularMedications as $medication): ?>
                <div class="col-lg-4 col-md-6 mb-3">
                    <div class="card h-100 medication-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="card-title mb-0"><?= htmlspecialchars($medication['name']) ?></h6>
                                <span class="badge bg-<?= $medication['prescription_required'] ? 'warning' : 'success' ?>">
                                    <?= $medication['prescription_required'] ? 'وصفة طبية' : 'بدون وصفة' ?>
                                </span>
                            </div>
                            <p class="card-text text-muted small mb-2">
                                <?= htmlspecialchars($medication['generic_name']) ?>
                            </p>
                            <p class="card-text small mb-3">
                                <strong>القوة:</strong> <?= htmlspecialchars($medication['strength']) ?><br>
                                <strong>الشكل:</strong> <?= htmlspecialchars($medication['dosage_form']) ?>
                            </p>
                            <a href="<?= App::url('patient/view-medication/' . $medication['id']) ?>" 
                               class="btn btn-outline-primary btn-sm">
                                <i class="bi bi-eye me-1"></i>
                                عرض التفاصيل
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Over the Counter Medications -->
<?php if (!empty($otcMedications)): ?>
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-bag me-2"></i>
            الأدوية المتاحة بدون وصفة طبية
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <?php foreach ($otcMedications as $medication): ?>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="card h-100 medication-card">
                        <div class="card-body">
                            <h6 class="card-title"><?= htmlspecialchars($medication['name']) ?></h6>
                            <p class="card-text text-muted small mb-2">
                                <?= htmlspecialchars($medication['generic_name']) ?>
                            </p>
                            <p class="card-text small mb-3">
                                <strong>القوة:</strong> <?= htmlspecialchars($medication['strength']) ?>
                            </p>
                            <a href="<?= App::url('patient/view-medication/' . $medication['id']) ?>" 
                               class="btn btn-outline-success btn-sm">
                                <i class="bi bi-eye me-1"></i>
                                عرض التفاصيل
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- All Medications -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-capsule me-2"></i>
            جميع الأدوية
        </h5>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="sortMedications('name')">
                <i class="bi bi-sort-alpha-down me-1"></i>
                الاسم
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="sortMedications('category')">
                <i class="bi bi-tags me-1"></i>
                الفئة
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="sortMedications('prescription')">
                <i class="bi bi-prescription me-1"></i>
                نوع الوصفة
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($medications)): ?>
            <!-- Empty State -->
            <div class="text-center py-5">
                <i class="bi bi-capsule display-1 text-muted"></i>
                <h4 class="mt-3 text-muted">لا توجد أدوية</h4>
                <p class="text-muted">لم يتم العثور على أدوية تطابق معايير البحث</p>
                <a href="<?= App::url('patient/medications') ?>" class="btn btn-primary">
                    <i class="bi bi-arrow-clockwise me-2"></i>
                    إعادة تعيين البحث
                </a>
            </div>
        <?php else: ?>
            <!-- Medications List -->
            <div class="list-group list-group-flush" id="medicationsList">
                <?php foreach ($medications as $medication): ?>
                    <div class="list-group-item medication-item" 
                         data-name="<?= htmlspecialchars($medication['name']) ?>" 
                         data-category="<?= htmlspecialchars($medication['category']) ?>" 
                         data-prescription="<?= $medication['prescription_required'] ? '1' : '0' ?>">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="mb-1">
                                        <a href="<?= App::url('patient/view-medication/' . $medication['id']) ?>" 
                                           class="text-decoration-none">
                                            <?= htmlspecialchars($medication['name']) ?>
                                        </a>
                                    </h6>
                                    <span class="badge bg-<?= $medication['prescription_required'] ? 'warning' : 'success' ?>">
                                        <?= $medication['prescription_required'] ? 'وصفة طبية' : 'بدون وصفة' ?>
                                    </span>
                                </div>
                                <p class="mb-1 text-muted small">
                                    <strong>الاسم العلمي:</strong> <?= htmlspecialchars($medication['generic_name']) ?>
                                </p>
                                <p class="mb-1 text-muted small">
                                    <strong>الفئة:</strong> <?= htmlspecialchars($medication['category']) ?> |
                                    <strong>القوة:</strong> <?= htmlspecialchars($medication['strength']) ?> |
                                    <strong>الشكل:</strong> <?= htmlspecialchars($medication['dosage_form']) ?>
                                </p>
                                <?php if (!empty($medication['description'])): ?>
                                    <p class="mb-0 text-muted small">
                                        <?= htmlspecialchars(substr($medication['description'], 0, 100)) ?>...
                                    </p>
                                <?php endif; ?>
                            </div>
                            <div class="ms-3">
                                <a href="<?= App::url('patient/view-medication/' . $medication['id']) ?>" 
                                   class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                    <i class="bi bi-eye"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
let currentSort = 'name';

// Sort Medications
function sortMedications(sortBy) {
    currentSort = sortBy;
    const items = document.querySelectorAll('.medication-item');
    const itemsArray = Array.from(items);
    
    itemsArray.sort((a, b) => {
        let aValue, bValue;
        
        switch(sortBy) {
            case 'name':
                aValue = a.dataset.name.toLowerCase();
                bValue = b.dataset.name.toLowerCase();
                break;
            case 'category':
                aValue = a.dataset.category.toLowerCase();
                bValue = b.dataset.category.toLowerCase();
                break;
            case 'prescription':
                aValue = a.dataset.prescription;
                bValue = b.dataset.prescription;
                break;
            default:
                return 0;
        }
        
        if (aValue < bValue) return -1;
        if (aValue > bValue) return 1;
        return 0;
    });
    
    const container = document.getElementById('medicationsList');
    itemsArray.forEach(item => container.appendChild(item));
    
    // Update button states
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
}

// Initialize page
$(document).ready(function() {
    // Set default sort
    sortMedications('name');
    
    // Initialize tooltips
    $('[title]').tooltip();
    
    // Auto-submit form on filter change
    $('select[name="category"], select[name="prescription_required"]').change(function() {
        $('#medicationSearchForm').submit();
    });
});
</script>

<style>
.medication-card {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.medication-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.list-group-item {
    border-left: none;
    border-right: none;
    border-top: none;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.list-group-item:last-child {
    border-bottom: none;
}

.badge {
    font-size: 0.75rem;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.btn-group .btn.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}
</style> 