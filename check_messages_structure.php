<?php
/**
 * التحقق من هيكل جدول الرسائل
 */

require_once 'config.php';

try {
    $db = new PDO(
        'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4',
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n";
    
    // التحقق من وجود الجدول
    $stmt = $db->query("SHOW TABLES LIKE 'messages'");
    if ($stmt->rowCount() > 0) {
        echo "✅ جدول الرسائل موجود\n";
        
        // عرض هيكل الجدول
        echo "\n📋 هيكل جدول الرسائل:\n";
        $stmt = $db->query("DESCRIBE messages");
        while ($row = $stmt->fetch()) {
            echo "- {$row['Field']}: {$row['Type']} ({$row['Null']}) {$row['Key']}\n";
        }
        
        // عرض عدد السجلات
        $stmt = $db->query("SELECT COUNT(*) as count FROM messages");
        $result = $stmt->fetch();
        echo "\n📊 عدد الرسائل: {$result['count']}\n";
        
        // عرض عينة من البيانات
        echo "\n📋 عينة من الرسائل:\n";
        $stmt = $db->query("SELECT * FROM messages LIMIT 3");
        while ($row = $stmt->fetch()) {
            echo "- ID: {$row['id']}, Subject: {$row['subject']}, Type: {$row['type']}, Status: {$row['status']}\n";
        }
        
    } else {
        echo "❌ جدول الرسائل غير موجود\n";
    }
    
} catch (PDOException $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}
?> 