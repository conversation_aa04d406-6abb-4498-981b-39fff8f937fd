<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">الملف الشخصي</h1>
        <p class="text-muted">إدارة معلومات الصيدلي والصيدلية</p>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-person-gear me-2"></i>
                    المعلومات الشخصية
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= App::url('pharmacist/profile') ?>">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">الاسم الأول</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" 
                                   value="<?= htmlspecialchars($pharmacist['first_name']) ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">الاسم الأخير</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" 
                                   value="<?= htmlspecialchars($pharmacist['last_name']) ?>" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?= htmlspecialchars($pharmacist['email']) ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="<?= htmlspecialchars($pharmacist['phone']) ?>" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="date_of_birth" class="form-label">تاريخ الميلاد</label>
                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" 
                                   value="<?= htmlspecialchars($pharmacist['date_of_birth'] ?? '') ?>">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="gender" class="form-label">الجنس</label>
                            <select class="form-select" id="gender" name="gender">
                                <option value="">اختر الجنس</option>
                                <option value="male" <?= ($pharmacist['gender'] ?? '') === 'male' ? 'selected' : '' ?>>ذكر</option>
                                <option value="female" <?= ($pharmacist['gender'] ?? '') === 'female' ? 'selected' : '' ?>>أنثى</option>
                            </select>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3"><?= htmlspecialchars($pharmacist['address'] ?? '') ?></textarea>
                    </div>

                    <hr>

                    <h6 class="text-primary mb-3">معلومات الصيدلية</h6>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="pharmacy_name" class="form-label">اسم الصيدلية</label>
                            <input type="text" class="form-control" id="pharmacy_name" name="pharmacy_name" 
                                   value="<?= htmlspecialchars($pharmacist['pharmacy_name'] ?? '') ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="license_number" class="form-label">رقم الترخيص</label>
                            <input type="text" class="form-control" id="license_number" name="license_number" 
                                   value="<?= htmlspecialchars($pharmacist['pharmacist_license'] ?? '') ?>" readonly>
                            <div class="form-text">رقم الترخيص لا يمكن تعديله</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="pharmacy_address" class="form-label">عنوان الصيدلية</label>
                        <textarea class="form-control" id="pharmacy_address" name="pharmacy_address" rows="3"><?= htmlspecialchars($pharmacist['pharmacy_address'] ?? '') ?></textarea>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-1"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Profile Summary -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-person-circle me-2"></i>
                    ملخص الملف الشخصي
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="bi bi-person-circle display-1 text-primary"></i>
                </div>
                <h5><?= htmlspecialchars($pharmacist['first_name'] . ' ' . $pharmacist['last_name']) ?></h5>
                <p class="text-muted">صيدلي</p>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <h6 class="text-primary"><?= htmlspecialchars($pharmacist['pharmacy_name'] ?? 'غير محدد') ?></h6>
                        <small class="text-muted">اسم الصيدلية</small>
                    </div>
                    <div class="col-6">
                        <h6 class="text-primary"><?= htmlspecialchars($pharmacist['pharmacist_license'] ?? 'غير محدد') ?></h6>
                        <small class="text-muted">رقم الترخيص</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?= App::url('pharmacist/dashboard') ?>" class="btn btn-outline-primary">
                        <i class="bi bi-speedometer2 me-2"></i>
                        لوحة التحكم
                    </a>
                    <a href="<?= App::url('pharmacist/prescriptions') ?>" class="btn btn-outline-success">
                        <i class="bi bi-search me-2"></i>
                        البحث في الوصفات
                    </a>
                    <a href="<?= App::url('pharmacist/dispensing-report') ?>" class="btn btn-outline-info">
                        <i class="bi bi-file-earmark-text me-2"></i>
                        تقرير الصرف
                    </a>
                    <a href="<?= App::url('pharmacist/notifications') ?>" class="btn btn-outline-warning">
                        <i class="bi bi-bell me-2"></i>
                        الإشعارات
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تغيير كلمة المرور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                        <input type="password" class="form-control" id="current_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                        <input type="password" class="form-control" id="new_password" required minlength="8">
                        <div class="form-text">يجب أن تكون 8 أحرف على الأقل</div>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                        <input type="password" class="form-control" id="confirm_password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmChangePassword">
                    <i class="bi bi-shield-check me-1"></i>
                    تغيير كلمة المرور
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const changePasswordModal = new bootstrap.Modal(document.getElementById('changePasswordModal'));
    const changePasswordForm = document.getElementById('changePasswordForm');
    const confirmChangePasswordBtn = document.getElementById('confirmChangePassword');

    // تغيير كلمة المرور
    confirmChangePasswordBtn.addEventListener('click', function() {
        const currentPassword = document.getElementById('current_password').value;
        const newPassword = document.getElementById('new_password').value;
        const confirmPassword = document.getElementById('confirm_password').value;

        if (!currentPassword || !newPassword || !confirmPassword) {
            alert('يرجى ملء جميع الحقول');
            return;
        }

        if (newPassword !== confirmPassword) {
            alert('كلمات المرور الجديدة غير متطابقة');
            return;
        }

        if (newPassword.length < 8) {
            alert('كلمة المرور الجديدة يجب أن تكون 8 أحرف على الأقل');
            return;
        }

        // إرسال طلب تغيير كلمة المرور
        fetch('<?= App::url('pharmacist/change-password') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `current_password=${encodeURIComponent(currentPassword)}&new_password=${encodeURIComponent(newPassword)}&confirm_password=${encodeURIComponent(confirmPassword)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم تغيير كلمة المرور بنجاح');
                changePasswordModal.hide();
                changePasswordForm.reset();
            } else {
                alert('فشل في تغيير كلمة المرور: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في تغيير كلمة المرور');
        });
    });

    // التحقق من صحة النموذج
    const form = document.querySelector('form[method="POST"]');
    form.addEventListener('submit', function(e) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
        }
    });

    // التحقق من صحة البريد الإلكتروني
    const emailField = document.getElementById('email');
    emailField.addEventListener('blur', function() {
        const email = this.value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
        }
    });

    // التحقق من صحة رقم الهاتف
    const phoneField = document.getElementById('phone');
    phoneField.addEventListener('blur', function() {
        const phone = this.value;
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
        
        if (phone && !phoneRegex.test(phone)) {
            this.classList.add('is-invalid');
        } else {
            this.classList.remove('is-invalid');
        }
    });
});
</script> 