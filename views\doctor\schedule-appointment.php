<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-calendar-plus me-2"></i>
            حجز موعد جديد
        </h1>
        <p class="text-muted">حجز موعد جديد في الجدول الزمني</p>
    </div>
    <div>
        <a href="<?= App::url('doctor/schedule') ?>" class="btn btn-outline-primary">
            <i class="bi bi-calendar-week me-2"></i>
            الجدول الزمني
        </a>
        <a href="<?= App::url('doctor/appointments') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للمواعيد
        </a>
    </div>
</div>

<!-- Appointment Form -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-calendar-check me-2"></i>
                    تفاصيل الموعد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= App::url('doctor/schedule-appointment') ?>" id="appointmentForm">
                    <div class="row">
                        <!-- اختيار المريض -->
                        <div class="col-md-6 mb-3">
                            <label for="patient_id" class="form-label">اختر المريض <span class="text-danger">*</span></label>
                            <select class="form-select" id="patient_id" name="patient_id" required>
                                <option value="">اختر المريض...</option>
                                <?php foreach ($patients as $patient): ?>
                                    <option value="<?= $patient['id'] ?>" 
                                            <?= (isset($selectedPatient) && $selectedPatient == $patient['id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($patient['first_name'] . ' ' . $patient['last_name']) ?>
                                        (<?= htmlspecialchars($patient['phone']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1"></i>
                                اختر المريض من القائمة أو ابحث عن مريض جديد
                            </div>
                        </div>

                        <!-- نوع الموعد -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_type" class="form-label">نوع الموعد <span class="text-danger">*</span></label>
                            <select class="form-select" id="appointment_type" name="appointment_type" required>
                                <option value="">اختر نوع الموعد...</option>
                                <option value="consultation" <?= (isset($appointmentType) && $appointmentType == 'consultation') ? 'selected' : '' ?>>استشارة طبية</option>
                                <option value="follow_up" <?= (isset($appointmentType) && $appointmentType == 'follow_up') ? 'selected' : '' ?>>متابعة حالة</option>
                                <option value="emergency" <?= (isset($appointmentType) && $appointmentType == 'emergency') ? 'selected' : '' ?>>حالة طارئة</option>
                                <option value="routine_check" <?= (isset($appointmentType) && $appointmentType == 'routine_check') ? 'selected' : '' ?>>فحص روتيني</option>
                                <option value="lab_test" <?= (isset($appointmentType) && $appointmentType == 'lab_test') ? 'selected' : '' ?>>فحص مخبري</option>
                                <option value="prescription_review" <?= (isset($appointmentType) && $appointmentType == 'prescription_review') ? 'selected' : '' ?>>مراجعة وصفة طبية</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- التاريخ -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_date" class="form-label">التاريخ <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="appointment_date" name="appointment_date" 
                                   value="<?= htmlspecialchars($selectedDate ?? date('Y-m-d')) ?>" 
                                   min="<?= date('Y-m-d') ?>" required>
                            <div class="form-text">
                                <i class="bi bi-calendar me-1"></i>
                                اختر التاريخ المناسب للموعد
                            </div>
                        </div>

                        <!-- الوقت -->
                        <div class="col-md-6 mb-3">
                            <label for="appointment_time" class="form-label">الوقت <span class="text-danger">*</span></label>
                            <select class="form-select" id="appointment_time" name="appointment_time" required>
                                <option value="">اختر الوقت...</option>
                                <?php foreach ($availableTimeSlots as $timeSlot): ?>
                                    <option value="<?= $timeSlot ?>" 
                                            <?= (isset($selectedTime) && $selectedTime == $timeSlot) ? 'selected' : '' ?>>
                                        <?= $timeSlot ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">
                                <i class="bi bi-clock me-1"></i>
                                الأوقات المتاحة في هذا اليوم
                            </div>
                        </div>
                    </div>

                    <!-- مدة الموعد -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="duration" class="form-label">مدة الموعد</label>
                            <select class="form-select" id="duration" name="duration">
                                <option value="15" <?= (isset($duration) && $duration == '15') ? 'selected' : '' ?>>15 دقيقة</option>
                                <option value="30" <?= (isset($duration) && $duration == '30') ? 'selected' : '' ?>>30 دقيقة</option>
                                <option value="45" <?= (isset($duration) && $duration == '45') ? 'selected' : '' ?>>45 دقيقة</option>
                                <option value="60" <?= (isset($duration) && $duration == '60') ? 'selected' : '' ?>>ساعة واحدة</option>
                                <option value="90" <?= (isset($duration) && $duration == '90') ? 'selected' : '' ?>>ساعة ونصف</option>
                            </select>
                        </div>

                        <!-- الأولوية -->
                        <div class="col-md-6 mb-3">
                            <label for="priority" class="form-label">الأولوية</label>
                            <select class="form-select" id="priority" name="priority">
                                <option value="normal" <?= (isset($priority) && $priority == 'normal') ? 'selected' : '' ?>>عادية</option>
                                <option value="high" <?= (isset($priority) && $priority == 'high') ? 'selected' : '' ?>>عالية</option>
                                <option value="urgent" <?= (isset($priority) && $priority == 'urgent') ? 'selected' : '' ?>>عاجلة</option>
                            </select>
                        </div>
                    </div>

                    <!-- سبب الموعد -->
                    <div class="mb-3">
                        <label for="reason" class="form-label">سبب الموعد <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" 
                                  placeholder="اكتب سبب الموعد والأعراض التي يعاني منها المريض..." required><?= htmlspecialchars($reason ?? '') ?></textarea>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            وصف مفصل لسبب الموعد يساعد في التحضير الجيد
                        </div>
                    </div>

                    <!-- ملاحظات إضافية -->
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2" 
                                  placeholder="أي ملاحظات إضافية أو تعليمات خاصة..."><?= htmlspecialchars($notes ?? '') ?></textarea>
                    </div>

                    <!-- إعدادات إضافية -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="send_notification" name="send_notification" checked>
                                <label class="form-check-label" for="send_notification">
                                    إرسال إشعار للمريض
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="send_reminder" name="send_reminder" checked>
                                <label class="form-check-label" for="send_reminder">
                                    إرسال تذكير قبل الموعد
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-secondary" onclick="history.back()">
                            <i class="bi bi-arrow-right me-2"></i>
                            إلغاء
                        </button>
                        <div>
                            <button type="button" class="btn btn-outline-primary me-2" id="previewBtn">
                                <i class="bi bi-eye me-2"></i>
                                معاينة
                            </button>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="bi bi-calendar-check me-2"></i>
                                حجز الموعد
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- معلومات المريض المختار -->
        <div class="card mb-3" id="patientInfoCard" style="display: none;">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-person me-2"></i>
                    معلومات المريض
                </h6>
            </div>
            <div class="card-body" id="patientInfo">
                <!-- سيتم تحميل معلومات المريض هنا -->
            </div>
        </div>

        <!-- الأوقات المتاحة -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-clock me-2"></i>
                    الأوقات المتاحة
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <label for="date_selector" class="form-label">اختر التاريخ:</label>
                    <input type="date" class="form-control" id="date_selector" 
                           value="<?= htmlspecialchars($selectedDate ?? date('Y-m-d')) ?>">
                </div>
                <div id="availableSlots">
                    <?php foreach ($availableTimeSlots as $timeSlot): ?>
                        <button type="button" class="btn btn-outline-success btn-sm m-1 time-slot-btn" 
                                data-time="<?= $timeSlot ?>">
                            <?= $timeSlot ?>
                        </button>
                    <?php endforeach; ?>
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        الأوقات المتاحة في اليوم المختار
                    </small>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    إحصائيات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-2">
                        <div class="border rounded p-2">
                            <h6 class="mb-0 text-primary"><?= $stats['today_appointments'] ?? 0 ?></h6>
                            <small class="text-muted">مواعيد اليوم</small>
                        </div>
                    </div>
                    <div class="col-6 mb-2">
                        <div class="border rounded p-2">
                            <h6 class="mb-0 text-success"><?= $stats['available_slots'] ?? 0 ?></h6>
                            <small class="text-muted">أوقات متاحة</small>
                        </div>
                    </div>
                    <div class="col-6 mb-2">
                        <div class="border rounded p-2">
                            <h6 class="mb-0 text-info"><?= $stats['total_patients'] ?? 0 ?></h6>
                            <small class="text-muted">إجمالي المرضى</small>
                        </div>
                    </div>
                    <div class="col-6 mb-2">
                        <div class="border rounded p-2">
                            <h6 class="mb-0 text-warning"><?= $stats['pending_appointments'] ?? 0 ?></h6>
                            <small class="text-muted">مواعيد معلقة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-eye me-2"></i>
                    معاينة الموعد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- سيتم تحميل المعاينة هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="submitAppointment()">
                    <i class="bi bi-calendar-check me-2"></i>
                    تأكيد الحجز
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add New Patient Modal -->
<div class="modal fade" id="addPatientModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person-plus me-2"></i>
                    إضافة مريض جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addPatientForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاسم الأول <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="first_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاسم الأخير <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="last_name" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" name="email" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control" name="phone" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تاريخ الميلاد</label>
                            <input type="date" class="form-control" name="date_of_birth">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الجنس</label>
                            <select class="form-select" name="gender">
                                <option value="">اختر الجنس...</option>
                                <option value="male">ذكر</option>
                                <option value="female">أنثى</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">العنوان</label>
                        <textarea class="form-control" name="address" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="addNewPatient()">
                    <i class="bi bi-save me-2"></i>
                    إضافة المريض
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.time-slot-btn {
    transition: all 0.3s ease;
}

.time-slot-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.time-slot-btn.selected {
    background-color: #28a745;
    color: white;
    border-color: #28a745;
}

#patientInfoCard {
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}
</style>

<script>
let selectedPatientId = null;

// تحميل معلومات المريض عند اختياره
document.getElementById('patient_id').addEventListener('change', function() {
    const patientId = this.value;
    if (patientId) {
        loadPatientInfo(patientId);
        selectedPatientId = patientId;
    } else {
        document.getElementById('patientInfoCard').style.display = 'none';
        selectedPatientId = null;
    }
});

// تحميل معلومات المريض
function loadPatientInfo(patientId) {
    fetch(`<?= App::url('doctor/get-patient-info/') ?>${patientId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('patientInfo').innerHTML = data.html;
                document.getElementById('patientInfoCard').style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

// تحديث الأوقات المتاحة عند تغيير التاريخ
document.getElementById('date_selector').addEventListener('change', function() {
    const selectedDate = this.value;
    const appointmentDate = document.getElementById('appointment_date');
    
    // تحديث تاريخ الموعد
    appointmentDate.value = selectedDate;
    
    // تحميل الأوقات المتاحة
    loadAvailableSlots(selectedDate);
});

// تحميل الأوقات المتاحة
function loadAvailableSlots(date) {
    fetch(`<?= App::url('doctor/get-available-slots') ?>?date=${encodeURIComponent(date)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const slotsContainer = document.getElementById('availableSlots');
                slotsContainer.innerHTML = '';
                
                data.slots.forEach(slot => {
                    const button = document.createElement('button');
                    button.type = 'button';
                    button.className = 'btn btn-outline-success btn-sm m-1 time-slot-btn';
                    button.textContent = slot;
                    button.setAttribute('data-time', slot);
                    button.onclick = function() {
                        selectTimeSlot(slot);
                    };
                    slotsContainer.appendChild(button);
                });
                
                // تحديث قائمة الأوقات في النموذج
                updateTimeSelect(data.slots);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

// اختيار وقت محدد
function selectTimeSlot(time) {
    // إزالة التحديد من جميع الأزرار
    document.querySelectorAll('.time-slot-btn').forEach(btn => {
        btn.classList.remove('selected');
    });
    
    // تحديد الزر المختار
    event.target.classList.add('selected');
    
    // تحديث حقل الوقت في النموذج
    document.getElementById('appointment_time').value = time;
}

// تحديث قائمة الأوقات في النموذج
function updateTimeSelect(slots) {
    const timeSelect = document.getElementById('appointment_time');
    timeSelect.innerHTML = '<option value="">اختر الوقت...</option>';
    
    slots.forEach(slot => {
        const option = document.createElement('option');
        option.value = slot;
        option.textContent = slot;
        timeSelect.appendChild(option);
    });
}

// معاينة الموعد
document.getElementById('previewBtn').addEventListener('click', function() {
    const formData = new FormData(document.getElementById('appointmentForm'));
    
    // التحقق من صحة البيانات
    if (!validateForm()) {
        return;
    }
    
    // إنشاء معاينة
    const previewContent = createPreviewContent(formData);
    document.getElementById('previewContent').innerHTML = previewContent;
    
    new bootstrap.Modal(document.getElementById('previewModal')).show();
});

// التحقق من صحة النموذج
function validateForm() {
    const requiredFields = ['patient_id', 'appointment_type', 'appointment_date', 'appointment_time', 'reason'];
    
    for (const field of requiredFields) {
        const element = document.getElementById(field);
        if (!element.value.trim()) {
            alert(`يرجى ملء حقل ${element.previousElementSibling.textContent.replace('*', '').trim()}`);
            element.focus();
            return false;
        }
    }
    
    return true;
}

// إنشاء محتوى المعاينة
function createPreviewContent(formData) {
    const patientSelect = document.getElementById('patient_id');
    const selectedPatient = patientSelect.options[patientSelect.selectedIndex].text;
    
    return `
        <div class="border rounded p-3">
            <h6>تفاصيل الموعد الجديد</h6>
            <hr>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>المريض:</strong> ${selectedPatient}</p>
                    <p><strong>نوع الموعد:</strong> ${formData.get('appointment_type')}</p>
                    <p><strong>التاريخ:</strong> ${formData.get('appointment_date')}</p>
                    <p><strong>الوقت:</strong> ${formData.get('appointment_time')}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>المدة:</strong> ${formData.get('duration')} دقيقة</p>
                    <p><strong>الأولوية:</strong> ${formData.get('priority')}</p>
                    <p><strong>سبب الموعد:</strong></p>
                    <div class="border rounded p-2 bg-light">
                        ${formData.get('reason')}
                    </div>
                </div>
            </div>
            ${formData.get('notes') ? `<hr><p><strong>ملاحظات إضافية:</strong><br>${formData.get('notes')}</p>` : ''}
        </div>
    `;
}

// تأكيد حجز الموعد
function submitAppointment() {
    document.getElementById('appointmentForm').submit();
}

// إضافة مريض جديد
function addNewPatient() {
    const formData = new FormData(document.getElementById('addPatientForm'));
    
    fetch('<?= App::url('doctor/add-patient') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إضافة المريض الجديد إلى القائمة
            const patientSelect = document.getElementById('patient_id');
            const option = document.createElement('option');
            option.value = data.patient_id;
            option.textContent = data.patient_name;
            patientSelect.appendChild(option);
            
            // اختيار المريض الجديد
            patientSelect.value = data.patient_id;
            patientSelect.dispatchEvent(new Event('change'));
            
            // إغلاق النافذة
            bootstrap.Modal.getInstance(document.getElementById('addPatientModal')).hide();
            
            // إعادة تعيين النموذج
            document.getElementById('addPatientForm').reset();
            
            alert('تم إضافة المريض بنجاح');
        } else {
            alert('فشل في إضافة المريض: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء إضافة المريض');
    });
}

// تحسين التفاعل
document.addEventListener('DOMContentLoaded', function() {
    // تحميل الأوقات المتاحة عند تحميل الصفحة
    const selectedDate = document.getElementById('date_selector').value;
    if (selectedDate) {
        loadAvailableSlots(selectedDate);
    }
    
    // إضافة تأثيرات بصرية
    const formElements = document.querySelectorAll('.form-control, .form-select');
    formElements.forEach(element => {
        element.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        element.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
});
</script> 