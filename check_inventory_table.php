<?php
require_once 'config.php';

try {
    $db = Database::getInstance();
    
    // التحقق من وجود الجدول
    $result = $db->select("SHOW TABLES LIKE 'inventory'");
    
    if (count($result) > 0) {
        echo "✅ جدول المخزون موجود\n";
        
        // التحقق من البيانات
        $count = $db->selectOne("SELECT COUNT(*) as count FROM inventory");
        echo "عدد الأدوية في المخزون: " . $count['count'] . "\n";
        
        // عرض بعض البيانات
        $items = $db->select("SELECT name, quantity, unit_price FROM inventory LIMIT 5");
        echo "\nأول 5 أدوية:\n";
        foreach ($items as $item) {
            echo "- " . $item['name'] . " (الكمية: " . $item['quantity'] . ", السعر: " . $item['unit_price'] . " ريال)\n";
        }
        
    } else {
        echo "❌ جدول المخزون غير موجود\n";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}
?> 