# صفحة إدارة الموردين - HealthKey

## نظرة عامة

تم إنشاء صفحة إدارة الموردين للصيادلة في نظام HealthKey. هذه الصفحة تتيح للصيادلة إدارة موردي الأدوية والمواد الطبية بشكل شامل.

## المميزات المتوفرة

### 1. عرض الموردين
- عرض قائمة شاملة لجميع الموردين
- إحصائيات مفصلة (إجمالي الموردين، النشطين، غير النشطين، الجدد)
- عرض أفضل الموردين
- فلترة حسب الفئة والحالة

### 2. البحث والفلترة
- البحث في اسم المورد أو الشخص المسؤول أو البريد الإلكتروني
- فلترة حسب فئة المورد
- عرض نتائج البحث بشكل منظم

### 3. إدارة الموردين
- إضافة مورد جديد
- تعديل معلومات المورد
- حذف مورد
- تغيير حالة المورد (نشط/غير نشط)

### 4. معلومات المورد
- اسم المورد
- الشخص المسؤول
- معلومات الاتصال (بريد إلكتروني، هاتف)
- العنوان
- الفئة
- الموقع الإلكتروني
- الملاحظات
- الحالة

## الملفات المنشأة

### 1. النموذج (Model)
**الملف:** `app/models/Supplier.php`

**الدوال المتوفرة:**
- `getAll()` - الحصول على جميع الموردين
- `findById()` - الحصول على مورد بواسطة المعرف
- `search()` - البحث في الموردين
- `create()` - إضافة مورد جديد
- `update()` - تحديث معلومات المورد
- `delete()` - حذف مورد
- `getActive()` - الحصول على الموردين النشطين
- `getStats()` - الحصول على إحصائيات الموردين
- `getCategories()` - الحصول على فئات الموردين
- `getByCategory()` - الحصول على الموردين حسب الفئة
- `searchByLocation()` - البحث حسب المنطقة
- `getTopSuppliers()` - الحصول على أفضل الموردين

### 2. المتحكم (Controller)
**الملف:** `app/controllers/PharmacistController.php`

**الدوال المضافة:**
- `suppliers()` - عرض صفحة الموردين
- `addSupplier()` - إضافة مورد جديد
- `editSupplier($id)` - تعديل معلومات المورد
- `deleteSupplier($id)` - حذف مورد

### 3. الصفحات (Views)
**الملفات:**
- `views/pharmacist/suppliers.php` - الصفحة الرئيسية للموردين
- `views/pharmacist/add_supplier.php` - صفحة إضافة مورد جديد
- `views/pharmacist/edit_supplier.php` - صفحة تعديل المورد

### 4. قاعدة البيانات
**الملف:** `create_suppliers_table.php`

**هيكل الجدول:**
```sql
CREATE TABLE suppliers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    address TEXT,
    category VARCHAR(100),
    website VARCHAR(255),
    notes TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_email (email)
);
```

## المسارات (Routes)

تم إضافة المسارات التالية إلى `app/core/App.php`:

```php
// مسارات الموردين
'pharmacist/suppliers' => ['controller' => 'PharmacistController', 'method' => 'suppliers'],
'pharmacist/add-supplier' => ['controller' => 'PharmacistController', 'method' => 'addSupplier'],
'pharmacist/edit-supplier' => ['controller' => 'PharmacistController', 'method' => 'editSupplier'],
'pharmacist/delete-supplier' => ['controller' => 'PharmacistController', 'method' => 'deleteSupplier'],
```

## البيانات التجريبية

تم إضافة 10 موردين تجريبيين:

1. **شركة الأدوية المتحدة** - أدوية عامة
2. **مؤسسة الصحة المتقدمة** - أدوية متخصصة
3. **شركة المعدات الطبية الحديثة** - معدات طبية
4. **مؤسسة المستلزمات الطبية** - مستلزمات طبية
5. **شركة الفيتامينات الطبيعية** - فيتامينات ومكملات
6. **مؤسسة الأدوية البيطرية** - أدوية بيطرية
7. **شركة المواد الكيميائية الطبية** - مواد كيميائية
8. **مؤسسة الصيدلة المتكاملة** - أدوية عامة (غير نشط)
9. **شركة التقنيات الطبية المتقدمة** - معدات طبية
10. **مؤسسة الرعاية الصحية الشاملة** - أخرى

## فئات الموردين

- أدوية عامة
- أدوية متخصصة
- معدات طبية
- مستلزمات طبية
- فيتامينات ومكملات
- أدوية بيطرية
- مواد كيميائية
- أخرى

## كيفية الوصول

يمكن الوصول لصفحة الموردين عبر:
```
http://localhost/HealthKey/pharmacist/suppliers
```

## الشريط الجانبي

تم إضافة رابط الموردين إلى الشريط الجانبي للصيدلي في `views/partials/sidebar_pharmacist.php`:

```php
<li class="nav-item">
    <a class="nav-link <?= App::isCurrentPath('pharmacist/suppliers') ? 'active' : '' ?>" 
       href="<?= App::url('pharmacist/suppliers') ?>" data-bs-toggle="tooltip" title="الموردين">
        <i class="bi bi-truck"></i>
        <span>الموردين</span>
    </a>
</li>
```

## الإحصائيات

الصفحة تعرض الإحصائيات التالية:
- إجمالي عدد الموردين
- عدد الموردين النشطين
- عدد الموردين غير النشطين
- عدد الموردين الجدد هذا الشهر

## البحث والفلترة

### البحث
- البحث في اسم المورد
- البحث في اسم الشخص المسؤول
- البحث في البريد الإلكتروني
- البحث في رقم الهاتف

### الفلترة
- فلترة حسب فئة المورد
- فلترة حسب الحالة (نشط/غير نشط)

## الأمان

- التحقق من تسجيل الدخول
- التحقق من نوع المستخدم (صيدلي فقط)
- حماية من SQL Injection
- التحقق من صحة البيانات المدخلة
- تأكيد الحذف قبل تنفيذه

## الاختبار

تم إنشاء ملف اختبار `test_suppliers.php` للتأكد من:
- إنشاء نموذج الموردين
- عمل جميع الدوال
- إنشاء متحكم الصيدلي
- عمل دالة suppliers

## ملاحظات تقنية

1. **جدول الطلبات**: تم تعليق دالة `getTopSuppliers()` مؤقتاً حتى يتم إنشاء جدول `supplier_orders`
2. **الفهارس**: تم إضافة فهارس على الأعمدة المهمة لتحسين الأداء
3. **التشفير**: جميع البيانات محمية من SQL Injection
4. **التحقق**: تم إضافة تحقق من صحة البيانات المدخلة

## التطوير المستقبلي

1. **جدول الطلبات**: إنشاء جدول `supplier_orders` لتتبع طلبات الموردين
2. **التقارير**: إضافة تقارير مفصلة عن الموردين
3. **التقييم**: إضافة نظام تقييم للموردين
4. **الإشعارات**: إضافة إشعارات للموردين الجدد أو التغييرات
5. **التصدير**: إضافة ميزة تصدير بيانات الموردين

## الاستخدام

### إضافة مورد جديد
1. انتقل إلى صفحة الموردين
2. انقر على "إضافة مورد جديد"
3. املأ النموذج بالمعلومات المطلوبة
4. انقر على "إضافة المورد"

### تعديل مورد
1. انتقل إلى صفحة الموردين
2. انقر على أيقونة التعديل بجانب المورد
3. عدل المعلومات المطلوبة
4. انقر على "حفظ التغييرات"

### حذف مورد
1. انتقل إلى صفحة الموردين
2. انقر على أيقونة الحذف بجانب المورد
3. أكد الحذف

### البحث في الموردين
1. استخدم حقل البحث في أعلى الصفحة
2. اختر الفئة من القائمة المنسدلة
3. انقر على "بحث"

## الدعم

في حالة وجود أي مشاكل أو استفسارات، يرجى التواصل مع فريق التطوير. 