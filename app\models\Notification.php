<?php

/**
 * نموذج الإشعارات
 * يتعامل مع جدول الإشعارات وجميع العمليات المتعلقة بها
 */
class Notification
{
    private $db;
    private $table = 'notifications';

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * الحصول على إشعار بواسطة ID
     */
    public function findById($id)
    {
        $query = "SELECT n.*, 
                         CONCAT(u.first_name, ' ', u.last_name) as user_name
                  FROM {$this->table} n
                  LEFT JOIN users u ON n.user_id = u.id
                  WHERE n.id = :id";
        
        return $this->db->selectOne($query, [':id' => $id]);
    }

    /**
     * إنشاء إشعار جديد
     */
    public function create($userId, $title, $message, $type = 'general')
    {
        $query = "INSERT INTO {$this->table} (user_id, title, message, type, created_at) 
                  VALUES (:user_id, :title, :message, :type, NOW())";

        $params = [
            ':user_id' => $userId,
            ':title' => $title,
            ':message' => $message,
            ':type' => $type
        ];

        return $this->db->insert($query, $params);
    }

    /**
     * إنشاء إشعارات متعددة
     */
    public function createBulk($userIds, $title, $message, $type = 'general')
    {
        $results = [];
        foreach ($userIds as $userId) {
            $results[] = $this->create($userId, $title, $message, $type);
        }
        return $results;
    }

    /**
     * الحصول على إشعارات المستخدم
     */
    public function getByUser($userId, $unreadOnly = false, $limit = null)
    {
        $query = "SELECT * FROM {$this->table} WHERE user_id = :user_id";
        $params = [':user_id' => $userId];

        if ($unreadOnly) {
            $query .= " AND is_read = 0";
        }

        $query .= " ORDER BY created_at DESC";

        if ($limit) {
            $query .= " LIMIT " . (int)$limit;
        }

        return $this->db->select($query, $params);
    }

    /**
     * تسجيل الإشعار كمقروء
     */
    public function markAsRead($id)
    {
        $query = "UPDATE {$this->table} SET is_read = 1 WHERE id = :id";
        return $this->db->update($query, [':id' => $id]) > 0;
    }

    /**
     * تسجيل جميع إشعارات المستخدم كمقروءة
     */
    public function markAllAsRead($userId)
    {
        $query = "UPDATE {$this->table} SET is_read = 1 WHERE user_id = :user_id AND is_read = 0";
        return $this->db->update($query, [':user_id' => $userId]);
    }

    /**
     * حذف الإشعار
     */
    public function delete($id)
    {
        $query = "DELETE FROM {$this->table} WHERE id = :id";
        return $this->db->delete($query, [':id' => $id]) > 0;
    }

    /**
     * حذف الإشعارات القديمة
     */
    public function deleteOld($days = 30)
    {
        $query = "DELETE FROM {$this->table} WHERE created_at < DATE_SUB(NOW(), INTERVAL :days DAY)";
        return $this->db->delete($query, [':days' => $days]);
    }

    /**
     * عدد الإشعارات غير المقروءة للمستخدم
     */
    public function getUnreadCount($userId)
    {
        $query = "SELECT COUNT(*) as count FROM {$this->table} WHERE user_id = :user_id AND is_read = 0";
        $result = $this->db->selectOne($query, [':user_id' => $userId]);
        return (int)$result['count'];
    }

    /**
     * إشعار موعد جديد
     */
    public function notifyNewAppointment($patientId, $doctorId, $appointmentDate, $appointmentTime)
    {
        // إشعار للمريض
        $this->create(
            $patientId,
            'موعد جديد',
            "تم حجز موعد جديد لك في تاريخ $appointmentDate الساعة $appointmentTime",
            'appointment'
        );

        // إشعار للطبيب
        $patient = $this->db->selectOne(
            "SELECT CONCAT(first_name, ' ', last_name) as name FROM users WHERE id = :id",
            [':id' => $patientId]
        );

        $this->create(
            $doctorId,
            'موعد جديد',
            "تم حجز موعد جديد مع المريض {$patient['name']} في تاريخ $appointmentDate الساعة $appointmentTime",
            'appointment'
        );
    }

    /**
     * إشعار إلغاء موعد
     */
    public function notifyAppointmentCancellation($patientId, $doctorId, $appointmentDate, $appointmentTime)
    {
        // إشعار للمريض
        $this->create(
            $patientId,
            'إلغاء موعد',
            "تم إلغاء موعدك المحدد في تاريخ $appointmentDate الساعة $appointmentTime",
            'appointment'
        );

        // إشعار للطبيب
        $patient = $this->db->selectOne(
            "SELECT CONCAT(first_name, ' ', last_name) as name FROM users WHERE id = :id",
            [':id' => $patientId]
        );

        $this->create(
            $doctorId,
            'إلغاء موعد',
            "تم إلغاء الموعد مع المريض {$patient['name']} المحدد في تاريخ $appointmentDate الساعة $appointmentTime",
            'appointment'
        );
    }

    /**
     * إشعار وصفة طبية جديدة
     */
    public function notifyNewPrescription($patientId, $doctorName, $prescriptionCode)
    {
        $this->create(
            $patientId,
            'وصفة طبية جديدة',
            "تم إنشاء وصفة طبية جديدة لك من الدكتور $doctorName. رقم الوصفة: $prescriptionCode",
            'prescription'
        );
    }

    /**
     * إشعار صرف دواء
     */
    public function notifyMedicationDispensed($patientId, $medicationName, $pharmacyName)
    {
        $this->create(
            $patientId,
            'صرف دواء',
            "تم صرف دواء $medicationName من صيدلية $pharmacyName",
            'prescription'
        );
    }

    /**
     * إشعار تذكير بالموعد
     */
    public function notifyAppointmentReminder($patientId, $doctorName, $appointmentDate, $appointmentTime)
    {
        $this->create(
            $patientId,
            'تذكير بالموعد',
            "تذكير: لديك موعد مع الدكتور $doctorName غداً في تاريخ $appointmentDate الساعة $appointmentTime",
            'appointment'
        );
    }

    /**
     * إشعار نتائج فحص مخبري
     */
    public function notifyLabResults($patientId, $testName, $doctorName)
    {
        $this->create(
            $patientId,
            'نتائج فحص مخبري',
            "تم إضافة نتائج فحص $testName من الدكتور $doctorName إلى سجلك الطبي",
            'general'
        );
    }

    /**
     * إشعار انتهاء صلاحية وصفة
     */
    public function notifyPrescriptionExpiry($patientId, $prescriptionCode, $expiryDate)
    {
        $this->create(
            $patientId,
            'انتهاء صلاحية وصفة',
            "ستنتهي صلاحية الوصفة رقم $prescriptionCode في تاريخ $expiryDate",
            'prescription'
        );
    }

    /**
     * إشعار تقييم الموعد
     */
    public function notifyAppointmentRating($patientId, $doctorId, $rating)
    {
        $this->create(
            $doctorId,
            'تقييم جديد للموعد',
            "تم استلام تقييم جديد من المريض. التقييم: $rating/5 نجوم.",
            'appointment'
        );
    }

    /**
     * إرسال إشعارات تذكير المواعيد (يتم تشغيلها يومياً)
     */
    public function sendAppointmentReminders()
    {
        // الحصول على المواعيد غداً
        $tomorrow = date('Y-m-d', strtotime('+1 day'));
        
        $appointments = $this->db->select(
            "SELECT a.*, 
                    CONCAT(patient.first_name, ' ', patient.last_name) as patient_name,
                    CONCAT(doctor.first_name, ' ', doctor.last_name) as doctor_name
             FROM appointments a
             LEFT JOIN users patient ON a.patient_id = patient.id
             LEFT JOIN users doctor ON a.doctor_id = doctor.id
             WHERE a.appointment_date = :tomorrow 
             AND a.status IN ('scheduled', 'confirmed')",
            [':tomorrow' => $tomorrow]
        );

        foreach ($appointments as $appointment) {
            $this->notifyAppointmentReminder(
                $appointment['patient_id'],
                $appointment['doctor_name'],
                $appointment['appointment_date'],
                $appointment['appointment_time']
            );
        }

        return count($appointments);
    }

    /**
     * إرسال إشعارات انتهاء صلاحية الوصفات
     */
    public function sendPrescriptionExpiryNotifications()
    {
        // الوصفات التي ستنتهي خلال 3 أيام
        $threeDaysLater = date('Y-m-d', strtotime('+3 days'));
        
        $prescriptions = $this->db->select(
            "SELECT p.*, 
                    CONCAT(patient.first_name, ' ', patient.last_name) as patient_name
             FROM prescriptions p
             LEFT JOIN users patient ON p.patient_id = patient.id
             WHERE p.expiry_date = :expiry_date 
             AND p.status = 'active'",
            [':expiry_date' => $threeDaysLater]
        );

        foreach ($prescriptions as $prescription) {
            $this->notifyPrescriptionExpiry(
                $prescription['patient_id'],
                $prescription['prescription_code'],
                $prescription['expiry_date']
            );
        }

        return count($prescriptions);
    }

    /**
     * الحصول على إحصائيات الإشعارات
     */
    public function getStats($userId = null)
    {
        $stats = [];
        $whereClause = $userId ? "WHERE user_id = :user_id" : "";
        $params = $userId ? [':user_id' => $userId] : [];

        // إجمالي الإشعارات
        $total = $this->db->selectOne("SELECT COUNT(*) as count FROM {$this->table} $whereClause", $params);
        $stats['total'] = (int)$total['count'];

        // الإشعارات غير المقروءة
        $unread = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM {$this->table} 
             WHERE is_read = 0 " . ($userId ? "AND user_id = :user_id" : ""),
            $params
        );
        $stats['unread'] = (int)$unread['count'];

        // الإشعارات حسب النوع
        $byType = $this->db->select(
            "SELECT type, COUNT(*) as count FROM {$this->table} $whereClause GROUP BY type",
            $params
        );

        foreach ($byType as $type) {
            $stats[$type['type']] = (int)$type['count'];
        }

        return $stats;
    }

    /**
     * البحث في الإشعارات
     */
    public function search($term, $userId = null)
    {
        $query = "SELECT * FROM {$this->table} 
                  WHERE (title LIKE :term OR message LIKE :term)";
        
        $params = [':term' => "%$term%"];

        if ($userId) {
            $query .= " AND user_id = :user_id";
            $params[':user_id'] = $userId;
        }

        $query .= " ORDER BY created_at DESC LIMIT 20";

        return $this->db->select($query, $params);
    }

    /**
     * الحصول على نوع الإشعار باللغة العربية
     */
    public static function getTypeLabel($type)
    {
        $types = [
            'appointment' => 'موعد',
            'prescription' => 'وصفة طبية',
            'general' => 'عام',
            'system' => 'نظام'
        ];

        return $types[$type] ?? $type;
    }

    /**
     * الحصول على أيقونة نوع الإشعار
     */
    public static function getTypeIcon($type)
    {
        $icons = [
            'appointment' => 'bi-calendar-check',
            'prescription' => 'bi-prescription2',
            'general' => 'bi-info-circle',
            'system' => 'bi-gear'
        ];

        return $icons[$type] ?? 'bi-bell';
    }

    /**
     * الحصول على لون نوع الإشعار
     */
    public static function getTypeColor($type)
    {
        $colors = [
            'appointment' => 'primary',
            'prescription' => 'success',
            'general' => 'info',
            'system' => 'warning'
        ];

        return $colors[$type] ?? 'secondary';
    }

    /**
     * تنسيق وقت الإشعار
     */
    public static function formatTime($createdAt)
    {
        $time = strtotime($createdAt);
        $now = time();
        $diff = $now - $time;

        if ($diff < 60) {
            return 'الآن';
        } elseif ($diff < 3600) {
            $minutes = floor($diff / 60);
            return "منذ $minutes دقيقة";
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return "منذ $hours ساعة";
        } elseif ($diff < 604800) {
            $days = floor($diff / 86400);
            return "منذ $days يوم";
        } else {
            return date('Y-m-d', $time);
        }
    }

    /**
     * تنظيف الإشعارات القديمة (يتم تشغيلها دورياً)
     */
    public function cleanup($days = 90)
    {
        // حذف الإشعارات المقروءة الأقدم من المدة المحددة
        $query = "DELETE FROM {$this->table} 
                  WHERE is_read = 1 
                  AND created_at < DATE_SUB(NOW(), INTERVAL :days DAY)";
        
        return $this->db->delete($query, [':days' => $days]);
    }

    /**
     * إرسال إشعار للمديرين
     */
    public function notifyAdmins($title, $message, $type = 'system')
    {
        $admins = $this->db->select(
            "SELECT id FROM users WHERE user_type = 'admin' AND is_active = 1"
        );

        $adminIds = array_column($admins, 'id');
        return $this->createBulk($adminIds, $title, $message, $type);
    }

    /**
     * إرسال إشعار لجميع الأطباء
     */
    public function notifyAllDoctors($title, $message, $type = 'general')
    {
        $doctors = $this->db->select(
            "SELECT id FROM users WHERE user_type = 'doctor' AND is_active = 1"
        );

        $doctorIds = array_column($doctors, 'id');
        return $this->createBulk($doctorIds, $title, $message, $type);
    }

    /**
     * إرسال إشعار لجميع المرضى
     */
    public function notifyAllPatients($title, $message, $type = 'general')
    {
        $patients = $this->db->select(
            "SELECT id FROM users WHERE user_type = 'patient' AND is_active = 1"
        );

        $patientIds = array_column($patients, 'id');
        return $this->createBulk($patientIds, $title, $message, $type);
    }
}
