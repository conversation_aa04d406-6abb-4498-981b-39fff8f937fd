<div class="medical-record-details">
    <div class="row">
        <div class="col-md-8">
            <!-- Visit Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-calendar-check me-2"></i>
                        معلومات الزيارة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">تاريخ الزيارة:</label>
                                <p class="mb-0"><?= date('Y-m-d', strtotime($record['visit_date'])) ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">نوع الزيارة:</label>
                                <p class="mb-0">
                                    <span class="badge bg-<?= MedicalRecord::getVisitTypeColor($record['visit_type']) ?>">
                                        <?= MedicalRecord::getVisitTypeLabel($record['visit_type']) ?>
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الطبيب:</label>
                                <p class="mb-0"><?= htmlspecialchars($record['doctor_name']) ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">تاريخ الإنشاء:</label>
                                <p class="mb-0"><?= date('Y-m-d H:i', strtotime($record['created_at'])) ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Chief Complaint -->
            <?php if (!empty($record['chief_complaint'])): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-chat-text me-2"></i>
                            الشكوى الرئيسية
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-0"><?= nl2br(htmlspecialchars($record['chief_complaint'])) ?></p>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Vital Signs -->
            <?php if (!empty($record['vital_signs'])): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-heart-pulse me-2"></i>
                            العلامات الحيوية
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php if (!empty($record['vital_signs']['temperature'])): ?>
                                <div class="col-md-3 mb-3">
                                    <div class="text-center">
                                        <div class="bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-2" style="width: 60px; height: 60px;">
                                            <i class="bi bi-thermometer-half text-primary"></i>
                                        </div>
                                        <h6 class="mb-1">درجة الحرارة</h6>
                                        <p class="mb-0 text-primary fw-bold"><?= $record['vital_signs']['temperature'] ?>°C</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($record['vital_signs']['blood_pressure_systolic'])): ?>
                                <div class="col-md-3 mb-3">
                                    <div class="text-center">
                                        <div class="bg-success bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-2" style="width: 60px; height: 60px;">
                                            <i class="bi bi-droplet text-success"></i>
                                        </div>
                                        <h6 class="mb-1">ضغط الدم</h6>
                                        <p class="mb-0 text-success fw-bold">
                                            <?= $record['vital_signs']['blood_pressure_systolic'] ?>/<?= $record['vital_signs']['blood_pressure_diastolic'] ?> mmHg
                                        </p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($record['vital_signs']['heart_rate'])): ?>
                                <div class="col-md-3 mb-3">
                                    <div class="text-center">
                                        <div class="bg-danger bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-2" style="width: 60px; height: 60px;">
                                            <i class="bi bi-heart text-danger"></i>
                                        </div>
                                        <h6 class="mb-1">نبض القلب</h6>
                                        <p class="mb-0 text-danger fw-bold"><?= $record['vital_signs']['heart_rate'] ?> bpm</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($record['vital_signs']['weight'])): ?>
                                <div class="col-md-3 mb-3">
                                    <div class="text-center">
                                        <div class="bg-warning bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-2" style="width: 60px; height: 60px;">
                                            <i class="bi bi-weight text-warning"></i>
                                        </div>
                                        <h6 class="mb-1">الوزن</h6>
                                        <p class="mb-0 text-warning fw-bold"><?= $record['vital_signs']['weight'] ?> kg</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($record['vital_signs']['height'])): ?>
                                <div class="col-md-3 mb-3">
                                    <div class="text-center">
                                        <div class="bg-info bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-2" style="width: 60px; height: 60px;">
                                            <i class="bi bi-arrows-expand text-info"></i>
                                        </div>
                                        <h6 class="mb-1">الطول</h6>
                                        <p class="mb-0 text-info fw-bold"><?= $record['vital_signs']['height'] ?> cm</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($record['vital_signs']['respiratory_rate'])): ?>
                                <div class="col-md-3 mb-3">
                                    <div class="text-center">
                                        <div class="bg-secondary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-2" style="width: 60px; height: 60px;">
                                            <i class="bi bi-lungs text-secondary"></i>
                                        </div>
                                        <h6 class="mb-1">معدل التنفس</h6>
                                        <p class="mb-0 text-secondary fw-bold"><?= $record['vital_signs']['respiratory_rate'] ?> /min</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Diagnosis -->
            <?php if (!empty($record['diagnosis'])): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-search me-2"></i>
                            التشخيص
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-0"><?= nl2br(htmlspecialchars($record['diagnosis'])) ?></p>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Treatment Plan -->
            <?php if (!empty($record['treatment_plan'])): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-clipboard2-pulse me-2"></i>
                            خطة العلاج
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-0"><?= nl2br(htmlspecialchars($record['treatment_plan'])) ?></p>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Notes -->
            <?php if (!empty($record['notes'])): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-sticky me-2"></i>
                            ملاحظات
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-0"><?= nl2br(htmlspecialchars($record['notes'])) ?></p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="col-md-4">
            <!-- Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-gear me-2"></i>
                        الإجراءات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                            <i class="bi bi-printer me-2"></i>
                            طباعة السجل
                        </button>
                        <a href="<?= App::url('patient/medical-record') ?>" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-left me-2"></i>
                            العودة للسجلات
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Timeline -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-clock-history me-2"></i>
                        الجدول الزمني
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم إنشاء السجل</h6>
                                <small class="text-muted">
                                    <?= date('Y-m-d H:i', strtotime($record['created_at'])) ?>
                                </small>
                            </div>
                        </div>
                        
                        <?php if (!empty($record['updated_at']) && $record['updated_at'] !== $record['created_at']): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">تم تحديث السجل</h6>
                                    <small class="text-muted">
                                        <?= date('Y-m-d H:i', strtotime($record['updated_at'])) ?>
                                    </small>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-content {
    padding-left: 10px;
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.timeline-content small {
    font-size: 0.8rem;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

@media print {
    .btn, .card-header {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}
</style> 