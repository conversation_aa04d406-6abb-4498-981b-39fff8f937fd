<?php
/**
 * ملف اختبار تصحيح مشكلة الرسائل
 */

// تضمين ملفات النظام
require_once 'app/core/App.php';
require_once 'app/helpers/SessionHelper.php';
require_once 'app/models/User.php';
require_once 'app/models/Message.php';

// محاكاة تسجيل دخول الطبيب
session_start();
$_SESSION['user_id'] = 2;
$_SESSION['user_type'] = 'doctor';
$_SESSION['user_name'] = 'د. أحمد محمد';

// تحميل النماذج
$userModel = new User();
$messageModel = new Message();

echo "<h1>اختبار نظام الرسائل</h1>";

try {
    echo "<h2>1. اختبار الحصول على بيانات الطبيب</h2>";
    $doctor = $userModel->findById(2);
    if ($doctor) {
        echo "✅ تم الحصول على بيانات الطبيب: " . User::getFullName($doctor) . "<br>";
    } else {
        echo "❌ فشل في الحصول على بيانات الطبيب<br>";
    }

    echo "<h2>2. اختبار الحصول على المرضى</h2>";
    $patients = $userModel->getPatientsByDoctor(2);
    echo "✅ عدد المرضى: " . count($patients) . "<br>";

    echo "<h2>3. اختبار الحصول على الأطباء</h2>";
    $doctors = $userModel->getAllDoctors();
    echo "✅ عدد الأطباء: " . count($doctors) . "<br>";

    echo "<h2>4. اختبار الحصول على الرسائل الواردة</h2>";
    $inboxMessages = $messageModel->getInbox(2, false, 5);
    echo "✅ عدد الرسائل الواردة: " . count($inboxMessages) . "<br>";

    echo "<h2>5. اختبار الحصول على الرسائل الصادرة</h2>";
    $sentMessages = $messageModel->getSent(2, 5);
    echo "✅ عدد الرسائل الصادرة: " . count($sentMessages) . "<br>";

    echo "<h2>6. اختبار الحصول على إحصائيات الرسائل</h2>";
    $stats = $messageModel->getStats(2);
    echo "✅ الإحصائيات: " . json_encode($stats, JSON_UNESCAPED_UNICODE) . "<br>";

    echo "<h2>7. اختبار عدد الرسائل غير المقروءة</h2>";
    $unreadCount = $messageModel->getUnreadCount(2);
    echo "✅ عدد الرسائل غير المقروءة: " . $unreadCount . "<br>";

    echo "<h2>8. اختبار إنشاء رسالة جديدة</h2>";
    $messageId = $messageModel->create(2, 1, 'رسالة اختبار', 'هذه رسالة اختبار من الطبيب', 'personal', 'normal');
    if ($messageId) {
        echo "✅ تم إنشاء رسالة جديدة برقم: " . $messageId . "<br>";
        
        // إرسال الرسالة
        if ($messageModel->send($messageId)) {
            echo "✅ تم إرسال الرسالة بنجاح<br>";
        } else {
            echo "❌ فشل في إرسال الرسالة<br>";
        }
    } else {
        echo "❌ فشل في إنشاء رسالة جديدة<br>";
    }

    echo "<h2>✅ جميع الاختبارات تمت بنجاح!</h2>";

} catch (Exception $e) {
    echo "<h2>❌ خطأ في الاختبار</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 