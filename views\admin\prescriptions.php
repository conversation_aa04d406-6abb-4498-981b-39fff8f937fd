<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-prescription2 me-2"></i>
            إدارة الوصفات الطبية
        </h1>
        <p class="text-muted">إدارة جميع الوصفات الطبية والتحكم في حالتها</p>
    </div>
    <div class="d-flex gap-2">
        <button id="bulkActionsButton" class="btn btn-outline-warning" style="display: none;" 
                data-bs-toggle="modal" data-bs-target="#bulkActionsModal">
            <i class="bi bi-gear"></i>
            إجراءات جماعية
        </button>
        <button class="btn btn-outline-info" onclick="exportPrescriptions()">
            <i class="bi bi-download"></i>
            تصدير البيانات
        </button>
        <button class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#addPrescriptionModal">
            <i class="bi bi-plus-circle"></i>
            إضافة وصفة جديدة
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-0"><?= is_array($prescriptions) ? count($prescriptions) : 0 ?></h4>
                        <small>إجمالي الوصفات</small>
                    </div>
                    <i class="bi bi-prescription2 fs-1 opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-gradient-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-0"><?= is_array($prescriptions) ? count(array_filter($prescriptions, fn($p) => $p['status'] === 'active')) : 0 ?></h4>
                        <small>الوصفات النشطة</small>
                    </div>
                    <i class="bi bi-check-circle-fill fs-1 opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-gradient-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-0"><?= is_array($prescriptions) ? count(array_filter($prescriptions, fn($p) => $p['status'] === 'dispensed')) : 0 ?></h4>
                        <small>الوصفات الموزعة</small>
                    </div>
                    <i class="bi bi-box-seam-fill fs-1 opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-gradient-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-0"><?= is_array($prescriptions) ? count(array_filter($prescriptions, fn($p) => $p['status'] === 'expired')) : 0 ?></h4>
                        <small>الوصفات المنتهية</small>
                    </div>
                    <i class="bi bi-exclamation-triangle-fill fs-1 opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-funnel me-2"></i>
            فلاتر البحث
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="<?= App::url('admin/prescriptions') ?>" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?= htmlspecialchars($filters['search'] ?? '') ?>" 
                       placeholder="كود الوصفة، اسم المريض، أو الطبيب">
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="active" <?= ($filters['status'] ?? '') === 'active' ? 'selected' : '' ?>>نشطة</option>
                    <option value="dispensed" <?= ($filters['status'] ?? '') === 'dispensed' ? 'selected' : '' ?>>موزعة</option>
                    <option value="expired" <?= ($filters['status'] ?? '') === 'expired' ? 'selected' : '' ?>>منتهية</option>
                    <option value="cancelled" <?= ($filters['status'] ?? '') === 'cancelled' ? 'selected' : '' ?>>ملغية</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="doctor_id" class="form-label">الطبيب</label>
                <select class="form-select" id="doctor_id" name="doctor_id">
                    <option value="">جميع الأطباء</option>
                    <?php foreach ($doctors as $doctor): ?>
                        <option value="<?= $doctor['id'] ?>" <?= ($filters['doctor_id'] ?? '') == $doctor['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="date_from" name="date_from" 
                       value="<?= htmlspecialchars($filters['date_from'] ?? '') ?>">
            </div>
            <div class="col-md-2">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="date_to" name="date_to" 
                       value="<?= htmlspecialchars($filters['date_to'] ?? '') ?>">
            </div>
            <div class="col-12">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="bi bi-search me-1"></i>
                    بحث
                </button>
                <a href="<?= App::url('admin/prescriptions') ?>" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise me-1"></i>
                    إعادة تعيين
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Prescriptions Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-list-ul me-2"></i>
            قائمة الوصفات الطبية
        </h5>
        <div class="d-flex gap-2">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="selectAll">
                <label class="form-check-label" for="selectAll">
                    تحديد الكل
                </label>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th width="50">
                            <input type="checkbox" class="form-check-input" id="selectAllCheckbox">
                        </th>
                        <th>كود الوصفة</th>
                        <th>المريض</th>
                        <th>الطبيب</th>
                        <th>التشخيص</th>
                        <th>تاريخ الإصدار</th>
                        <th>تاريخ الانتهاء</th>
                        <th>الحالة</th>
                        <th width="150">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!is_array($prescriptions) || empty($prescriptions)): ?>
                        <tr>
                            <td colspan="9" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-inbox fs-1 d-block mb-2"></i>
                                    لا توجد وصفات طبية
                                </div>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($prescriptions as $prescription): ?>
                            <tr data-prescription-id="<?= $prescription['id'] ?>">
                                <td>
                                    <input type="checkbox" class="form-check-input prescription-checkbox" 
                                           value="<?= $prescription['id'] ?>">
                                </td>
                                <td>
                                    <span class="badge bg-primary"><?= htmlspecialchars($prescription['prescription_code']) ?></span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-2">
                                            <i class="bi bi-person text-primary"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?= htmlspecialchars($prescription['patient_name']) ?></div>
                                            <small class="text-muted">ID: <?= $prescription['patient_id'] ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-success bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-2">
                                            <i class="bi bi-person-badge text-success"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?= htmlspecialchars($prescription['doctor_name']) ?></div>
                                            <small class="text-muted"><?= htmlspecialchars($prescription['doctor_specialization'] ?? 'عام') ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if (!empty($prescription['diagnosis'])): ?>
                                        <span class="text-truncate d-inline-block" style="max-width: 150px;" 
                                              title="<?= htmlspecialchars($prescription['diagnosis']) ?>">
                                            <?= htmlspecialchars($prescription['diagnosis']) ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?= date('Y-m-d', strtotime($prescription['issue_date'])) ?></span>
                                </td>
                                <td>
                                    <?php 
                                    $expiryDate = strtotime($prescription['expiry_date']);
                                    $isExpired = $expiryDate < time();
                                    $isNearExpiry = $expiryDate < strtotime('+7 days');
                                    ?>
                                    <span class="badge <?= $isExpired ? 'bg-danger' : ($isNearExpiry ? 'bg-warning' : 'bg-success') ?>">
                                        <?= date('Y-m-d', $expiryDate) ?>
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    $statusClass = match($prescription['status']) {
                                        'active' => 'bg-success',
                                        'dispensed' => 'bg-info',
                                        'expired' => 'bg-danger',
                                        'cancelled' => 'bg-secondary',
                                        default => 'bg-secondary'
                                    };
                                    $statusText = match($prescription['status']) {
                                        'active' => 'نشطة',
                                        'dispensed' => 'موزعة',
                                        'expired' => 'منتهية',
                                        'cancelled' => 'ملغية',
                                        default => 'غير محدد'
                                    };
                                    ?>
                                    <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary" 
                                                onclick="viewPrescription(<?= $prescription['id'] ?>)"
                                                title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-success" 
                                                onclick="editPrescription(<?= $prescription['id'] ?>)"
                                                title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deletePrescription(<?= $prescription['id'] ?>)"
                                                title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Pagination -->
<?php if (isset($pagination) && !empty($pagination)): ?>
    <div class="d-flex justify-content-center mt-4">
        <nav aria-label="صفحات الوصفات">
            <ul class="pagination">
                <?php if ($pagination['prev']): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?= $pagination['prev'] ?>">السابق</a>
                    </li>
                <?php endif; ?>
                
                <?php foreach ($pagination['pages'] as $page): ?>
                    <li class="page-item <?= $page['current'] ? 'active' : '' ?>">
                        <a class="page-link" href="<?= $page['url'] ?>"><?= $page['number'] ?></a>
                    </li>
                <?php endforeach; ?>
                
                <?php if ($pagination['next']): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?= $pagination['next'] ?>">التالي</a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
<?php endif; ?>

<!-- Add Prescription Modal -->
<div class="modal fade" id="addPrescriptionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة وصفة طبية جديدة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addPrescriptionForm" method="POST" action="<?= App::url('admin/prescriptions/add') ?>">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="patient_id" class="form-label">المريض *</label>
                                <select class="form-select" id="patient_id" name="patient_id" required>
                                    <option value="">اختر المريض</option>
                                    <!-- سيتم تحميل المرضى عبر AJAX -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="doctor_id" class="form-label">الطبيب *</label>
                                <select class="form-select" id="doctor_id" name="doctor_id" required>
                                    <option value="">اختر الطبيب</option>
                                    <?php foreach ($doctors as $doctor): ?>
                                        <option value="<?= $doctor['id'] ?>">
                                            <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="diagnosis" class="form-label">التشخيص</label>
                        <textarea class="form-control" id="diagnosis" name="diagnosis" rows="3" 
                                  placeholder="أدخل التشخيص الطبي"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="أدخل أي ملاحظات إضافية"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="issue_date" class="form-label">تاريخ الإصدار</label>
                                <input type="date" class="form-control" id="issue_date" name="issue_date" 
                                       value="<?= date('Y-m-d') ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="expiry_date" class="form-label">تاريخ الانتهاء</label>
                                <input type="date" class="form-control" id="expiry_date" name="expiry_date" 
                                       value="<?= date('Y-m-d', strtotime('+30 days')) ?>">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>
                        إضافة الوصفة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Actions Modal -->
<div class="modal fade" id="bulkActionsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-gear me-2"></i>
                    إجراءات جماعية
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>سيتم تطبيق الإجراء المحدد على <span id="selectedCount">0</span> وصفة طبية.</p>
                <div class="mb-3">
                    <label for="bulkAction" class="form-label">اختر الإجراء:</label>
                    <select class="form-select" id="bulkAction">
                        <option value="">اختر الإجراء</option>
                        <option value="activate">تفعيل</option>
                        <option value="deactivate">إلغاء التفعيل</option>
                        <option value="delete">حذف</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="executeBulkAction()">
                    <i class="bi bi-check-circle me-1"></i>
                    تطبيق الإجراء
                </button>
            </div>
        </div>
    </div>
</div>

<!-- View Prescription Modal -->
<div class="modal fade" id="viewPrescriptionModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-eye me-2"></i>
                    تفاصيل الوصفة الطبية
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="prescriptionDetails">
                <!-- سيتم تحميل التفاصيل عبر AJAX -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="printPrescription()">
                    <i class="bi bi-printer me-1"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// تحديد الكل
document.getElementById('selectAllCheckbox').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.prescription-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateBulkActionsButton();
});

// تحديث زر الإجراءات الجماعية
document.querySelectorAll('.prescription-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateBulkActionsButton);
});

function updateBulkActionsButton() {
    const selectedCheckboxes = document.querySelectorAll('.prescription-checkbox:checked');
    const bulkButton = document.getElementById('bulkActionsButton');
    const selectedCount = document.getElementById('selectedCount');
    
    if (selectedCheckboxes.length > 0) {
        bulkButton.style.display = 'inline-block';
        selectedCount.textContent = selectedCheckboxes.length;
    } else {
        bulkButton.style.display = 'none';
    }
}

// عرض تفاصيل الوصفة
function viewPrescription(prescriptionId) {
    fetch(`<?= App::url('admin/prescriptions/view') ?>/${prescriptionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('prescriptionDetails').innerHTML = data.html;
                new bootstrap.Modal(document.getElementById('viewPrescriptionModal')).show();
            } else {
                showAlert('خطأ في تحميل تفاصيل الوصفة', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('خطأ في تحميل تفاصيل الوصفة', 'danger');
        });
}

// تعديل الوصفة
function editPrescription(prescriptionId) {
    window.location.href = `<?= App::url('admin/prescriptions/edit') ?>/${prescriptionId}`;
}

// حذف الوصفة
function deletePrescription(prescriptionId) {
    if (confirm('هل أنت متأكد من حذف هذه الوصفة الطبية؟')) {
        fetch(`<?= App::url('admin/prescriptions/delete') ?>/${prescriptionId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم حذف الوصفة بنجاح', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showAlert(data.message || 'خطأ في حذف الوصفة', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('خطأ في حذف الوصفة', 'danger');
        });
    }
}

// تنفيذ الإجراء الجماعي
function executeBulkAction() {
    const action = document.getElementById('bulkAction').value;
    const selectedIds = Array.from(document.querySelectorAll('.prescription-checkbox:checked'))
        .map(checkbox => checkbox.value);
    
    if (!action) {
        showAlert('يرجى اختيار إجراء', 'warning');
        return;
    }
    
    if (selectedIds.length === 0) {
        showAlert('يرجى اختيار وصفات', 'warning');
        return;
    }
    
    fetch('<?= App::url('admin/prescriptions/bulk-action') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: action,
            prescription_ids: selectedIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showAlert(data.message || 'خطأ في تنفيذ الإجراء', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في تنفيذ الإجراء', 'danger');
    });
}

// تصدير البيانات
function exportPrescriptions() {
    const filters = new URLSearchParams(window.location.search);
    window.location.href = `<?= App::url('admin/prescriptions/export') ?>?${filters.toString()}`;
}

// طباعة الوصفة
function printPrescription() {
    const printWindow = window.open('', '_blank');
    const prescriptionContent = document.getElementById('prescriptionDetails').innerHTML;
    
    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <title>وصفة طبية</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                body { font-family: 'Arial', sans-serif; }
                @media print { .no-print { display: none; } }
            </style>
        </head>
        <body>
            <div class="container py-4">
                ${prescriptionContent}
            </div>
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.print();
}

// تحميل المرضى للقائمة المنسدلة
function loadPatients() {
    fetch('<?= App::url('admin/users/patients') ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('patient_id');
                data.patients.forEach(patient => {
                    const option = document.createElement('option');
                    option.value = patient.id;
                    option.textContent = `${patient.first_name} ${patient.last_name} - ${patient.national_id}`;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading patients:', error);
        });
}

// تحميل المرضى عند فتح النموذج
document.getElementById('addPrescriptionModal').addEventListener('show.bs.modal', loadPatients);

// إرسال نموذج إضافة الوصفة
document.getElementById('addPrescriptionForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch(this.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم إضافة الوصفة بنجاح', 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showAlert(data.message || 'خطأ في إضافة الوصفة', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في إضافة الوصفة', 'danger');
    });
});

// عرض رسائل التنبيه
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script> 