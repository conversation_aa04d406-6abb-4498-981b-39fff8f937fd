<?php
/**
 * اختبار بسيط لوظيفة إغلاق الشريط الجانبي
 */

// محاكاة تسجيل دخول المدير
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'admin';
$_SESSION['user_name'] = 'مدير النظام';

$currentUser = [
    'id' => 1,
    'first_name' => 'أحمد',
    'last_name' => 'محمد',
    'user_type' => 'admin',
    'email' => '<EMAIL>'
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إغلاق الشريط الجانبي - HealthKey</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="public/css/style.css" rel="stylesheet">
    <link href="public/css/sidebar.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .test-content { min-height: 100vh; padding: 2rem; }
        .test-button { margin: 0.5rem; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <?php
                $sidebarFile = "../views/partials/sidebar_{$currentUser['user_type']}.php";
                if (file_exists($sidebarFile)) {
                    include $sidebarFile;
                }
                ?>
            </div>
            <div class="col-md-9 col-lg-10">
                <main class="main-content p-4">
                    <!-- Mobile Sidebar Toggle Button -->
                    <div class="mobile-sidebar-toggle d-lg-none mb-3">
                        <button class="btn btn-primary" onclick="openSidebarMobile()">
                            <i class="bi bi-list me-2"></i>
                            فتح القائمة
                        </button>
                    </div>
                    
                    <!-- Test Content -->
                    <div class="test-content">
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h4 class="mb-0">
                                            <i class="bi bi-gear me-2"></i>
                                            اختبار إغلاق الشريط الجانبي
                                        </h4>
                                    </div>
                                    <div class="card-body">
                                        <h5>تعليمات الاختبار:</h5>
                                        <ol>
                                            <li>غير حجم النافذة إلى أقل من 768px (للموبايل)</li>
                                            <li>انقر على "فتح القائمة" لفتح الشريط الجانبي</li>
                                            <li>انقر على زر الإغلاق (X) في أعلى الشريط الجانبي</li>
                                            <li>تأكد من أن الشريط الجانبي يغلق ولا يظهر مرة أخرى</li>
                                        </ol>
                                        
                                        <hr>
                                        
                                        <h5>أزرار الاختبار:</h5>
                                        <div class="d-flex flex-wrap">
                                            <button class="btn btn-success test-button" onclick="openSidebarMobile()">
                                                <i class="bi bi-list me-2"></i>
                                                فتح الشريط الجانبي
                                            </button>
                                            <button class="btn btn-warning test-button" onclick="closeSidebar()">
                                                <i class="bi bi-x-circle me-2"></i>
                                                إغلاق الشريط الجانبي
                                            </button>
                                            <button class="btn btn-info test-button" onclick="toggleSidebar()">
                                                <i class="bi bi-arrow-left-right me-2"></i>
                                                تبديل الشريط الجانبي
                                            </button>
                                        </div>
                                        
                                        <hr>
                                        
                                        <h5>حالة الشريط الجانبي:</h5>
                                        <div id="status" class="alert alert-info">
                                            جاري التحقق من حالة الشريط الجانبي...
                                        </div>
                                        
                                        <h5>سجل الأحداث:</h5>
                                        <div id="log" class="bg-light p-3 rounded" style="max-height: 200px; overflow-y: auto;">
                                            <small class="text-muted">سيتم عرض سجل الأحداث هنا...</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Sidebar JS -->
    <script src="public/js/sidebar.js"></script>
    
    <script>
    // إضافة سجل الأحداث
    function addLog(message) {
        const logElement = document.getElementById('log');
        const timestamp = new Date().toLocaleTimeString();
        logElement.innerHTML += `<div><small>[${timestamp}] ${message}</small></div>`;
        logElement.scrollTop = logElement.scrollHeight;
    }
    
    // تحديث الحالة
    function updateStatus() {
        const statusElement = document.getElementById('status');
        const sidebar = document.querySelector('.sidebar-admin, .sidebar-doctor, .sidebar-patient, .sidebar-pharmacist');
        
        if (!sidebar) {
            statusElement.className = 'alert alert-danger';
            statusElement.innerHTML = '<i class="bi bi-exclamation-triangle me-2"></i>الشريط الجانبي غير موجود';
            return;
        }
        
        let status = '';
        let alertClass = '';
        
        if (window.innerWidth <= 768) {
            if (sidebar.classList.contains('show')) {
                status = 'الشريط الجانبي مفتوح (الهاتف)';
                alertClass = 'alert-success';
            } else {
                status = 'الشريط الجانبي مغلق (الهاتف)';
                alertClass = 'alert-warning';
            }
        } else {
            if (sidebar.classList.contains('collapsed')) {
                status = 'الشريط الجانبي مطوي (سطح المكتب)';
                alertClass = 'alert-warning';
            } else {
                status = 'الشريط الجانبي مفتوح (سطح المكتب)';
                alertClass = 'alert-success';
            }
        }
        
        statusElement.className = `alert ${alertClass}`;
        statusElement.innerHTML = `<i class="bi bi-info-circle me-2"></i>${status}`;
    }
    
    // مراقبة تغييرات الشريط الجانبي
    const sidebar = document.querySelector('.sidebar-admin, .sidebar-doctor, .sidebar-patient, .sidebar-pharmacist');
    if (sidebar) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    addLog(`تغيير في كلاسات الشريط الجانبي: ${sidebar.className}`);
                    updateStatus();
                }
            });
        });
        
        observer.observe(sidebar, {
            attributes: true,
            attributeFilter: ['class']
        });
    }
    
    // تحديث الحالة كل ثانية
    setInterval(updateStatus, 1000);
    
    // تحديث عند التحميل
    document.addEventListener('DOMContentLoaded', function() {
        addLog('تم تحميل الصفحة');
        updateStatus();
    });
    
    // إضافة سجل للأحداث
    window.addEventListener('click', function(e) {
        if (e.target.closest('.sidebar-toggle')) {
            addLog('تم النقر على زر الإغلاق');
        }
        if (e.target.closest('.mobile-sidebar-toggle')) {
            addLog('تم النقر على زر فتح القائمة');
        }
    });
    </script>
</body>
</html> 