<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-prescription2 me-2"></i>
            وصفاتي الطبية
        </h1>
        <p class="text-muted">إدارة وعرض جميع الوصفات الطبية الخاصة بك</p>
    </div>
    <div>
        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
            <i class="bi bi-printer me-2"></i>
            طباعة
        </button>
        <button type="button" class="btn btn-outline-success" onclick="exportPrescriptions()">
            <i class="bi bi-download me-2"></i>
            تصدير
        </button>
        <a href="<?= App::url('patient/dashboard') ?>" class="btn btn-outline-primary ms-2">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count($prescriptions) ?></h4>
                        <p class="mb-0">إجمالي الوصفات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-prescription2 display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count(array_filter($prescriptions, fn($p) => $p['status'] === 'active')) ?></h4>
                        <p class="mb-0">الوصفات النشطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count(array_filter($prescriptions, fn($p) => $p['status'] === 'dispensed')) ?></h4>
                        <p class="mb-0">الوصفات المكتملة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-box-seam display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count(array_filter($prescriptions, fn($p) => $p['status'] === 'expired')) ?></h4>
                        <p class="mb-0">الوصفات المنتهية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-exclamation-triangle display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <label class="form-label">حالة الوصفة:</label>
                <select class="form-select" id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشطة</option>
                    <option value="dispensed">مكتملة</option>
                    <option value="expired">منتهية</option>
                    <option value="cancelled">ملغية</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">التاريخ:</label>
                <input type="date" class="form-control" id="dateFilter">
            </div>
            <div class="col-md-4">
                <label class="form-label">البحث:</label>
                <input type="text" class="form-control" id="searchFilter" placeholder="ابحث في الوصفات...">
            </div>
        </div>
    </div>
</div>

<!-- Prescriptions Section -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-prescription2 me-2"></i>
            الوصفات الطبية
        </h5>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="sortPrescriptions('date')">
                <i class="bi bi-calendar me-1"></i>
                التاريخ
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="sortPrescriptions('doctor')">
                <i class="bi bi-person-badge me-1"></i>
                الطبيب
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="sortPrescriptions('status')">
                <i class="bi bi-tag me-1"></i>
                الحالة
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($prescriptions)): ?>
            <!-- Empty State -->
            <div class="text-center py-5">
                <i class="bi bi-prescription2 display-1 text-muted"></i>
                <h4 class="mt-3 text-muted">لا توجد وصفات طبية</h4>
                <p class="text-muted">لم يتم إنشاء أي وصفات طبية لك بعد</p>
            </div>
        <?php else: ?>
            <!-- Prescriptions List -->
            <div class="list-group list-group-flush" id="prescriptionsList">
                <?php foreach ($prescriptions as $prescription): ?>
                    <div class="list-group-item prescription-item" 
                         data-status="<?= $prescription['status'] ?>" 
                                                                 data-date="<?= $prescription['issue_date'] ?? '' ?>" 
                                                                 data-doctor="<?= htmlspecialchars($prescription['doctor_name'] ?? '') ?>" 
                         data-medications="<?= htmlspecialchars(implode(', ', array_column($prescription['medications'] ?? [], 'medication_name'))) ?>">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0">
                                        <i class="bi bi-prescription2 text-primary me-2"></i>
                                        وصفة بتاريخ: <?= $prescription['issue_date'] ? date('Y-m-d', strtotime($prescription['issue_date'])) : 'غير محدد' ?>
                                    </h6>
                                    <div>
                                        <span class="badge bg-<?= Prescription::getStatusColor($prescription['status']) ?>">
                                            <?= Prescription::getStatusLabel($prescription['status']) ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="row mb-2">
                                    <div class="col-md-6">
                                        <small class="text-muted">
                                            <i class="bi bi-person-badge me-1"></i>
                                            الطبيب: <?= htmlspecialchars($prescription['doctor_name'] ?? 'غير محدد') ?>
                                        </small>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">
                                            <i class="bi bi-calendar me-1"></i>
                                            تاريخ الانتهاء: <?= $prescription['expiry_date'] ? date('Y-m-d', strtotime($prescription['expiry_date'])) : 'غير محدد' ?>
                                        </small>
                                    </div>
                                </div>
                                
                                <?php if (!empty($prescription['diagnosis'])): ?>
                                    <div class="mb-2">
                                        <strong>التشخيص:</strong>
                                        <p class="mb-1 text-muted"><?= htmlspecialchars(substr($prescription['diagnosis'], 0, 100)) ?><?= strlen($prescription['diagnosis']) > 100 ? '...' : '' ?></p>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($prescription['medications'])): ?>
                                    <div class="mb-2">
                                        <strong>الأدوية:</strong>
                                        <div class="row">
                                            <?php foreach (array_slice($prescription['medications'], 0, 3) as $medication): ?>
                                                <div class="col-md-4">
                                                    <small class="text-muted">
                                                        <i class="bi bi-capsule me-1"></i>
                                                        <?= htmlspecialchars($medication['medication_name']) ?>
                                                        (<?= $medication['quantity'] ?> <?= $medication['unit'] ?>)
                                                    </small>
                                                </div>
                                            <?php endforeach; ?>
                                            <?php if (count($prescription['medications']) > 3): ?>
                                                <div class="col-md-4">
                                                    <small class="text-muted">
                                                        <i class="bi bi-plus-circle me-1"></i>
                                                        و<?= count($prescription['medications']) - 3 ?> دواء آخر
                                                    </small>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($prescription['notes'])): ?>
                                    <div class="mb-2">
                                        <strong>ملاحظات:</strong>
                                        <p class="mb-1 text-muted"><?= htmlspecialchars(substr($prescription['notes'], 0, 100)) ?><?= strlen($prescription['notes']) > 100 ? '...' : '' ?></p>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">
                                            <i class="bi bi-clock me-1"></i>
                                            تاريخ الإنشاء: <?= $prescription['created_at'] ? date('Y-m-d H:i', strtotime($prescription['created_at'])) : 'غير محدد' ?>
                                        </small>
                                    </div>
                                    <div class="col-md-6">
                                        <?php if ($prescription['status'] === 'active'): ?>
                                            <small class="text-success">
                                                <i class="bi bi-check-circle me-1"></i>
                                                صالحة للاستخدام
                                            </small>
                                        <?php elseif ($prescription['status'] === 'expired'): ?>
                                            <small class="text-danger">
                                                <i class="bi bi-exclamation-triangle me-1"></i>
                                                منتهية الصلاحية
                                            </small>
                                        <?php elseif ($prescription['status'] === 'dispensed'): ?>
                                            <small class="text-info">
                                                <i class="bi bi-box-seam me-1"></i>
                                                تم صرفها
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="ms-3">
                                <button type="button" 
                                        class="btn btn-sm btn-outline-info" 
                                        onclick="viewPrescription(<?= $prescription['id'] ?>)" 
                                        title="عرض التفاصيل">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button type="button" 
                                        class="btn btn-sm btn-outline-secondary" 
                                        onclick="printPrescription(<?= $prescription['id'] ?>)" 
                                        title="طباعة">
                                    <i class="bi bi-printer"></i>
                                </button>
                                <?php if ($prescription['status'] === 'active'): ?>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-success" 
                                            onclick="downloadPrescription(<?= $prescription['id'] ?>)" 
                                            title="تحميل">
                                        <i class="bi bi-download"></i>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Prescription Details Modal -->
<div class="modal fade" id="prescriptionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الوصفة الطبية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="prescriptionDetails">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
let currentSort = 'date';
let currentFilter = '';

// Filter and Search Functions
$('#statusFilter').change(function() {
    currentFilter = $(this).val();
    filterPrescriptions();
});

$('#dateFilter').change(function() {
    filterPrescriptions();
});

$('#searchFilter').on('input', function() {
    filterPrescriptions();
});

function filterPrescriptions() {
    const statusFilter = $('#statusFilter').val();
    const dateFilter = $('#dateFilter').val();
    const searchFilter = $('#searchFilter').val().toLowerCase();
    
    $('.prescription-item').each(function() {
        let show = true;
        const $item = $(this);
        const status = $item.data('status');
        const date = $item.data('date');
        const doctor = $item.data('doctor') || '';
        const medications = $item.data('medications') || '';
        const text = $item.text().toLowerCase();
        
        // Status filter
        if (statusFilter && status !== statusFilter) {
            show = false;
        }
        
        // Date filter
        if (dateFilter && date !== dateFilter) {
            show = false;
        }
        
        // Search filter
        if (searchFilter && !text.includes(searchFilter) && !doctor.toLowerCase().includes(searchFilter) && !medications.toLowerCase().includes(searchFilter)) {
            show = false;
        }
        
        $item.toggle(show);
    });
}

// Sort Functions
function sortPrescriptions(sortBy) {
    currentSort = sortBy;
    const $list = $('#prescriptionsList');
    const $items = $list.children('.prescription-item').get();
    
    $items.sort(function(a, b) {
        const $a = $(a);
        const $b = $(b);
        
        if (sortBy === 'date') {
            return new Date($b.data('date')) - new Date($a.data('date'));
        } else if (sortBy === 'doctor') {
            return ($a.data('doctor') || '').localeCompare($b.data('doctor') || '');
        } else if (sortBy === 'status') {
            return ($a.data('status') || '').localeCompare($b.data('status') || '');
        }
        
        return 0;
    });
    
    $list.empty().append($items);
}

// View Prescription Details
function viewPrescription(prescriptionId) {
    $.get('<?= App::url('patient/view-prescription') ?>/' + prescriptionId, function(response) {
        $('#prescriptionDetails').html(response);
        $('#prescriptionModal').modal('show');
    }).fail(function() {
        showAlert('حدث خطأ في تحميل تفاصيل الوصفة', 'error');
    });
}

// Print Prescription
function printPrescription(prescriptionId) {
    window.open('<?= App::url('patient/view-prescription') ?>/' + prescriptionId + '?print=1', '_blank');
}

// Download Prescription
function downloadPrescription(prescriptionId) {
    window.open('<?= App::url('patient/download-prescription') ?>/' + prescriptionId, '_blank');
}

// Export Prescriptions
function exportPrescriptions() {
    window.open('<?= App::url('patient/export-prescriptions') ?>', '_blank');
}

// Show Alert
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert
    $('.container-fluid').prepend(alertHtml);
    
    // Auto dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}

// Initialize page
$(document).ready(function() {
    // Set default sort
    sortPrescriptions('date');
    
    // Initialize tooltips
    $('[title]').tooltip();
});
</script>

<style>
.list-group-item {
    border-left: none;
    border-right: none;
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}

.badge {
    font-size: 0.75rem;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-lg {
    max-width: 800px;
}

.btn-group .btn {
    font-size: 0.875rem;
}

@media print {
    .btn, .card-header, .modal {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}
</style> 