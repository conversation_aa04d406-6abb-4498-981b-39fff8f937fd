/* ========================================
   ملف CSS للوحة تحكم المدير - HealthKey
   ======================================== */

/* ===== إعدادات أساسية ===== */
:root {
    --primary-color: #2c5aa0;
    --primary-dark: #1e3f73;
    --primary-light: #4a7bc8;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --white: #ffffff;
    
    /* تدرجات لونية */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
    --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    --danger-gradient: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    --info-gradient: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
    --warning-gradient: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    
    /* قيم التصميم */
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    --box-shadow-lg: 0 8px 30px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== إعدادات الخط والاتجاه ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

body {
    font-family: 'Cairo', 'Tajawal', 'Segoe UI', sans-serif;
    direction: rtl;
    text-align: right;
    line-height: 1.7;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: var(--dark-color);
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* ===== تخطيط لوحة التحكم ===== */
.admin-layout {
    padding-top: 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    font-family: 'Cairo', 'Tajawal', 'Segoe UI', sans-serif;
    position: relative;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
    width: 100%;
}

/* إخفاء أي شريط علوي */
.navbar,
.navbar-expand-lg,
.navbar-dark,
.bg-primary,
.fixed-top,
.navbar-brand,
.navbar-nav,
.navbar-toggler {
    display: none !important;
}

/* إزالة أي مساحة في الأعلى */
body {
    padding-top: 0 !important;
    margin-top: 0 !important;
}

.admin-layout {
    padding-top: 0 !important;
    margin-top: 0 !important;
}

/* ===== تحسينات إضافية للوحة التحكم ===== */
.dashboard-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

/* إزالة الفراغ من container-fluid */
.container-fluid {
    padding-left: 0;
    padding-right: 0;
    margin-left: 0;
    margin-right: 0;
    width: 100%;
    max-width: none;
}

/* إزالة الفراغ من الصفوف */
.row {
    margin-left: 0;
    margin-right: 0;
}

/* إزالة الفراغ من الأعمدة */
.col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
.col-sm, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12,
.col-md, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12,
.col-lg, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12,
.col-xl, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12 {
    padding-left: 0;
    padding-right: 0;
}

/* ===== الشريط الجانبي ===== */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    z-index: 1000;
    overflow-y: auto;
    box-shadow: -4px 0 20px rgba(0,0,0,0.15);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5);
}

/* Sidebar Header */
.sidebar-header {
    padding: 1.5rem 1rem 1rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.sidebar-brand i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.brand-text {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--white);
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: rgba(255,255,255,0.7);
    font-size: 1.1rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.sidebar-toggle:hover {
    color: var(--white);
    background: rgba(255,255,255,0.1);
}

/* User Info */
.sidebar-user {
    padding: 1rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.25rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.user-info {
    flex: 1;
}

.user-name {
    font-weight: 600;
    color: var(--white);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.user-role {
    font-size: 0.75rem;
    color: rgba(255,255,255,0.7);
}

/* Navigation Sections */
.nav-section {
    padding: 0.75rem 1rem 0.5rem;
    margin-top: 0.5rem;
}

.nav-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: rgba(255,255,255,0.5);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: 0.5rem;
}

/* Navigation Items */
.sidebar-nav {
    flex: 1;
    padding: 0.5rem 0;
}

.sidebar .nav-item {
    margin: 0.125rem 0.75rem;
}

.sidebar .nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.sidebar .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1));
    transition: width 0.3s ease;
}

.sidebar .nav-link:hover::before {
    width: 100%;
}

.sidebar .nav-link:hover {
    color: var(--white);
    background: rgba(255,255,255,0.1);
    transform: translateX(-4px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.sidebar .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    box-shadow: 0 4px 12px rgba(44, 90, 160, 0.4);
    transform: translateX(-4px);
}

.sidebar .nav-link.active::before {
    display: none;
}

.sidebar .nav-link i {
    font-size: 1.1rem;
    min-width: 20px;
    text-align: center;
}

.sidebar .nav-link span {
    flex: 1;
    font-weight: 500;
}

/* Navigation Badges */
.nav-badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.7rem;
    font-weight: 600;
    border-radius: 12px;
    background: rgba(255,255,255,0.2);
    color: var(--white);
    min-width: 20px;
    text-align: center;
}

.nav-badge.bg-success {
    background: var(--success-color) !important;
}

.nav-badge.bg-info {
    background: var(--info-color) !important;
}

.nav-badge.bg-warning {
    background: var(--warning-color) !important;
    color: var(--dark-color);
}

.nav-badge.bg-danger {
    background: var(--danger-color) !important;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid rgba(255,255,255,0.1);
    background: rgba(0,0,0,0.1);
}

.system-status {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: rgba(255,255,255,0.8);
}

.status-item i {
    font-size: 0.7rem;
}

.status-item .text-success {
    color: #28a745 !important;
}

/* ===== المحتوى الرئيسي ===== */
.main-content {
    margin-right: 280px;
    padding: 30px;
    min-height: 100vh;
    transition: margin-right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin-top: 0;
    width: calc(100% - 280px);
    box-sizing: border-box;
    position: relative;
    left: 0;
}

.main-content.expanded {
    margin-right: 0;
    width: 100%;
}

/* إزالة الفراغ من المحتوى الرئيسي */
.main-content .container-fluid {
    padding-left: 0;
    padding-right: 0;
    margin-left: 0;
    margin-right: 0;
    width: 100%;
}

/* إزالة الفراغ من البطاقات */
.main-content .card {
    margin-left: 0;
    margin-right: 0;
    border-radius: 0;
}

/* إزالة الفراغ من الصفوف في المحتوى الرئيسي */
.main-content .row {
    margin-left: 0;
    margin-right: 0;
}

/* إزالة الفراغ من الأعمدة في المحتوى الرئيسي */
.main-content .col,
.main-content .col-1, .main-content .col-2, .main-content .col-3, .main-content .col-4, .main-content .col-5, .main-content .col-6, .main-content .col-7, .main-content .col-8, .main-content .col-9, .main-content .col-10, .main-content .col-11, .main-content .col-12 {
    padding-left: 0;
    padding-right: 0;
}

/* Mobile Toggle Button */
.mobile-toggle-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1060;
    opacity: 0;
    visibility: hidden;
    transform: translateX(100px);
    transition: all 0.3s ease;
}

/* إظهار زر التبديل فقط عندما يكون الشريط الجانبي مخفياً */
.sidebar.collapsed + .main-content .mobile-toggle-container,
.main-content.expanded .mobile-toggle-container {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

/* إخفاء زر التبديل في الشاشات الكبيرة عندما يكون الشريط الجانبي ظاهراً */
@media (min-width: 769px) {
    .mobile-toggle-container {
        display: none;
    }
    
    /* إظهار الزر فقط عندما يكون الشريط الجانبي مخفياً */
    .sidebar.collapsed ~ .mobile-toggle-container,
    .main-content.expanded .mobile-toggle-container {
        display: block;
    }
}

.mobile-toggle-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.mobile-toggle-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0,0,0,0.2);
}

/* ===== البطاقات ===== */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0 !important;
    padding: 20px;
    font-weight: 600;
}

.card-body {
    padding: 25px;
}

/* ===== البطاقات الإحصائية ===== */
.stats-card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 2rem;
}

.stats-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
    pointer-events: none;
}

.stats-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.stats-card .card-body {
    position: relative;
    z-index: 1;
    padding: 2rem;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 800;
    line-height: 1;
    margin-bottom: 0.25rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.stats-label {
    font-size: 0.9rem;
    font-weight: 600;
    opacity: 0.9;
    margin-bottom: 0;
}

.stats-icon {
    width: 70px;
    height: 70px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.stats-content {
    flex: 1;
}

.stats-card:hover .stats-icon {
    transform: rotate(10deg) scale(1.1);
    background: rgba(255, 255, 255, 0.3);
}

/* ===== خلفيات متدرجة للبطاقات الإحصائية ===== */
.bg-gradient-primary {
    background: var(--primary-gradient) !important;
}

.bg-gradient-success {
    background: var(--success-gradient) !important;
}

.bg-gradient-info {
    background: var(--info-gradient) !important;
}

.bg-gradient-warning {
    background: var(--warning-gradient) !important;
}

.bg-gradient-danger {
    background: var(--danger-gradient) !important;
}

/* ===== الأزرار ===== */
.btn {
    border-radius: var(--border-radius);
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    text-transform: none;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
}

.btn-success {
    background: var(--success-gradient);
    color: white;
}

.btn-warning {
    background: var(--warning-gradient);
    color: white;
}

.btn-danger {
    background: var(--danger-gradient);
    color: white;
}

.btn-info {
    background: var(--info-gradient);
    color: white;
}

/* ===== الجداول ===== */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border: none;
}

.table thead th {
    background: var(--primary-gradient);
    color: white;
    border: none;
    font-weight: 600;
    padding: 18px 15px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

.table tbody td {
    padding: 15px;
    vertical-align: middle;
    border-color: #f1f3f4;
}

/* ===== الشارات ===== */
.badge {
    padding: 8px 12px;
    font-size: 0.75rem;
    border-radius: 20px;
    font-weight: 500;
}

/* ===== الصور الرمزية ===== */
.avatar-sm {
    width: 35px;
    height: 35px;
}

.avatar-md {
    width: 50px;
    height: 50px;
}

/* ===== شاشة التحميل ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    text-align: center;
    color: white;
}

/* ===== التنبيهات ===== */
.alert {
    border: none;
    border-radius: var(--border-radius-lg);
    padding: 20px 25px;
    margin-bottom: 25px;
    border-left: 5px solid;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
    pointer-events: none;
}

.alert-success {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
    color: #155724;
    border-left-color: #28a745;
}

.alert-danger {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%);
    color: #721c24;
    border-left-color: #dc3545;
}

.alert-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
    color: #856404;
    border-left-color: #ffc107;
}

.alert-info {
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(23, 162, 184, 0.05) 100%);
    color: #0c5460;
    border-left-color: #17a2b8;
}



/* ===== القوائم المنسدلة ===== */
.dropdown-menu {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    padding: 10px 0;
}

.dropdown-item {
    padding: 10px 20px;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: translateX(5px);
}

/* ===== ترقيم الصفحات ===== */
.pagination .page-link {
    border: none;
    border-radius: var(--border-radius-sm);
    margin: 0 2px;
    padding: 10px 15px;
    color: #6c757d;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background: var(--primary-gradient);
    color: white;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: var(--primary-gradient);
    border: none;
}

/* ===== النماذج ===== */
.form-control {
    border-radius: var(--border-radius-sm);
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
    transform: translateY(-1px);
}

.form-select {
    border-radius: var(--border-radius-sm);
    border: 2px solid #e9ecef;
    padding: 12px 15px;
}

/* ===== التأثيرات الحركية ===== */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ===== تأثيرات خاصة للوحة التحكم ===== */
.stats-card {
    animation: fadeInUp 0.6s ease-out;
}

.stats-card:nth-child(1) { animation-delay: 0.1s; }
.stats-card:nth-child(2) { animation-delay: 0.2s; }
.stats-card:nth-child(3) { animation-delay: 0.3s; }
.stats-card:nth-child(4) { animation-delay: 0.4s; }

/* ===== فئات مساعدة ===== */
.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-soft {
    box-shadow: var(--box-shadow) !important;
}

.shadow-hover {
    transition: var(--transition);
}

.shadow-hover:hover {
    box-shadow: var(--box-shadow-lg) !important;
    transform: translateY(-2px);
}

/* ===== حاويات الرسوم البيانية ===== */
.chart-container {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    padding: 2rem;
    margin-bottom: 2rem;
    border: none;
}

/* ===== جداول لوحة التحكم ===== */
.table-dashboard {
    background: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--box-shadow);
    border: none;
}

.table-dashboard thead th {
    background: var(--primary-gradient);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1.5rem 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.table-dashboard tbody td {
    padding: 1.25rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    vertical-align: middle;
    transition: all 0.3s ease;
}

.table-dashboard tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

/* ===== شريط التمرير المخصص ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ===== التجاوب مع الشاشات الصغيرة ===== */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        width: 280px;
        position: fixed;
        top: 0;
        right: 0;
        z-index: 1050;
    }
    
    .sidebar.show {
        transform: translateX(0);
        box-shadow: -4px 0 20px rgba(0,0,0,0.3);
    }
    
    .sidebar.collapsed {
        transform: translateX(100%);
    }
    
    .main-content {
        margin-right: 0;
        padding: 20px 15px;
        width: 100%;
    }
    
    .main-content.expanded {
        margin-right: 0;
        width: 100%;
    }
    
    .card-body {
        padding: 20px;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
    
    .stats-card {
        padding: 1.5rem;
        gap: 1rem;
    }
    
    .stats-card .card-body {
        padding: 1.5rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
    
    .stats-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    /* إخفاء زر التبديل عندما يكون الشريط الجانبي ظاهراً */
    .sidebar.show ~ .mobile-toggle-container,
    .sidebar:not(.collapsed) ~ .mobile-toggle-container {
        opacity: 0;
        visibility: hidden;
        transform: translateX(100px);
    }
    
    /* إظهار زر التبديل عندما يكون الشريط الجانبي مخفياً */
    .sidebar.collapsed ~ .mobile-toggle-container,
    .sidebar:not(.show) ~ .mobile-toggle-container {
        opacity: 1;
        visibility: visible;
        transform: translateX(0);
    }
    
    /* Mobile sidebar overlay */
    body.sidebar-open::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        z-index: 1040;
        backdrop-filter: blur(2px);
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: 15px 10px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .btn {
        padding: 8px 16px;
        font-size: 0.875rem;
    }
    
    .stats-card {
        padding: 1rem;
        gap: 0.75rem;
    }
    
    .stats-number {
        font-size: 1.75rem;
    }
    
    .stats-icon {
        width: 45px;
        height: 45px;
        font-size: 1.25rem;
    }
}

/* ===== تحسينات للطباعة ===== */
@media print {
    .stats-card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .btn, .alert-dismissible .btn-close {
        display: none !important;
    }
    
    .sidebar {
        display: none !important;
    }
    
    .main-content {
        margin-right: 0 !important;
    }
}
