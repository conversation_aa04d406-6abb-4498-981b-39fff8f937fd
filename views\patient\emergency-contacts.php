<?php
/**
 * صفحة جهات الاتصال في الطوارئ للمريض
 */
?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-telephone-forward me-2"></i>
            جهات الاتصال في الطوارئ
        </h1>
        <p class="text-muted">إدارة جهات الاتصال في حالات الطوارئ</p>
    </div>
    <div>
        <a href="<?= App::url('patient/profile') ?>" class="btn btn-outline-primary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للملف الشخصي
        </a>
    </div>
</div>

<!-- رسائل النجاح والخطأ -->
<?php if (SessionHelper::hasFlash()): ?>
    <?php 
    $flashMessages = SessionHelper::getAllFlash();
    foreach ($flashMessages as $key => $flashMessage): 
    ?>
    <div class="alert alert-<?= $flashMessage['type'] ?> alert-dismissible fade show" role="alert">
        <?= $flashMessage['message'] ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endforeach; ?>
<?php endif; ?>

<div class="row">
    <!-- إعدادات جهات الاتصال -->
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-person-plus me-2"></i>
                    جهات الاتصال في الطوارئ
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="emergencyForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="emergency_contact" class="form-label">اسم جهة الاتصال <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="emergency_contact" name="emergency_contact" 
                                   value="<?= htmlspecialchars($patient['emergency_contact'] ?? '') ?>" 
                                   placeholder="مثال: أبو أحمد، أم فاطمة" required>
                            <?php if (SessionHelper::getValidationError('emergency_contact')): ?>
                                <div class="text-danger small"><?= SessionHelper::getValidationError('emergency_contact') ?></div>
                            <?php endif; ?>
                            <div class="form-text">
                                اسم الشخص الذي يمكن الاتصال به في حالة الطوارئ
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="emergency_phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control" id="emergency_phone" name="emergency_phone" 
                                   value="<?= htmlspecialchars($patient['emergency_phone'] ?? '') ?>" 
                                   placeholder="مثال: **********" required>
                            <?php if (SessionHelper::getValidationError('emergency_phone')): ?>
                                <div class="text-danger small"><?= SessionHelper::getValidationError('emergency_phone') ?></div>
                            <?php endif; ?>
                            <div class="form-text">
                                رقم هاتف جهة الاتصال في الطوارئ
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="relationship" class="form-label">العلاقة</label>
                        <select class="form-select" id="relationship" name="relationship">
                            <option value="">اختر العلاقة</option>
                            <option value="spouse" <?= ($patient['relationship'] ?? '') === 'spouse' ? 'selected' : '' ?>>زوج/زوجة</option>
                            <option value="parent" <?= ($patient['relationship'] ?? '') === 'parent' ? 'selected' : '' ?>>أب/أم</option>
                            <option value="child" <?= ($patient['relationship'] ?? '') === 'child' ? 'selected' : '' ?>>ابن/ابنة</option>
                            <option value="sibling" <?= ($patient['relationship'] ?? '') === 'sibling' ? 'selected' : '' ?>>أخ/أخت</option>
                            <option value="friend" <?= ($patient['relationship'] ?? '') === 'friend' ? 'selected' : '' ?>>صديق/صديقة</option>
                            <option value="other" <?= ($patient['relationship'] ?? '') === 'other' ? 'selected' : '' ?>>أخرى</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="أي معلومات إضافية مهمة..."><?= htmlspecialchars($patient['emergency_notes'] ?? '') ?></textarea>
                        <div class="form-text">
                            يمكنك إضافة أي معلومات إضافية مهمة مثل أوقات التوفر أو تعليمات خاصة
                        </div>
                    </div>

                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-save me-2"></i>
                            حفظ جهات الاتصال
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- معلومات إضافية للطوارئ -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    معلومات إضافية للطوارئ
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="blood_type" class="form-label">فصيلة الدم</label>
                        <select class="form-select" id="blood_type" name="blood_type">
                            <option value="">غير محدد</option>
                            <option value="A+" <?= ($patient['blood_type'] ?? '') === 'A+' ? 'selected' : '' ?>>A+</option>
                            <option value="A-" <?= ($patient['blood_type'] ?? '') === 'A-' ? 'selected' : '' ?>>A-</option>
                            <option value="B+" <?= ($patient['blood_type'] ?? '') === 'B+' ? 'selected' : '' ?>>B+</option>
                            <option value="B-" <?= ($patient['blood_type'] ?? '') === 'B-' ? 'selected' : '' ?>>B-</option>
                            <option value="AB+" <?= ($patient['blood_type'] ?? '') === 'AB+' ? 'selected' : '' ?>>AB+</option>
                            <option value="AB-" <?= ($patient['blood_type'] ?? '') === 'AB-' ? 'selected' : '' ?>>AB-</option>
                            <option value="O+" <?= ($patient['blood_type'] ?? '') === 'O+' ? 'selected' : '' ?>>O+</option>
                            <option value="O-" <?= ($patient['blood_type'] ?? '') === 'O-' ? 'selected' : '' ?>>O-</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="allergies_emergency" class="form-label">الحساسيات المهمة</label>
                        <textarea class="form-control" id="allergies_emergency" name="allergies_emergency" rows="3" 
                                  placeholder="مثال: البنسلين، المكسرات، اللاتكس..."><?= htmlspecialchars($patient['allergies_emergency'] ?? '') ?></textarea>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="medical_conditions" class="form-label">الحالات الطبية المهمة</label>
                        <textarea class="form-control" id="medical_conditions" name="medical_conditions" rows="3" 
                                  placeholder="مثال: السكري، الضغط، أمراض القلب..."><?= htmlspecialchars($patient['medical_conditions'] ?? '') ?></textarea>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="medications_emergency" class="form-label">الأدوية المهمة</label>
                        <textarea class="form-control" id="medications_emergency" name="medications_emergency" rows="3" 
                                  placeholder="مثال: الأنسولين، الوارفارين..."><?= htmlspecialchars($patient['medications_emergency'] ?? '') ?></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات مهمة -->
    <div class="col-lg-4">
        <!-- معلومات الطوارئ الحالية -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    معلومات الطوارئ الحالية
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($patient['emergency_contact']) || !empty($patient['emergency_phone'])): ?>
                    <div class="alert alert-info">
                        <h6 class="alert-heading">جهة الاتصال الحالية:</h6>
                        <?php if (!empty($patient['emergency_contact'])): ?>
                            <p class="mb-1"><strong>الاسم:</strong> <?= htmlspecialchars($patient['emergency_contact']) ?></p>
                        <?php endif; ?>
                        <?php if (!empty($patient['emergency_phone'])): ?>
                            <p class="mb-0"><strong>الهاتف:</strong> 
                                <a href="tel:<?= htmlspecialchars($patient['emergency_phone']) ?>" class="text-decoration-none">
                                    <?= htmlspecialchars($patient['emergency_phone']) ?>
                                </a>
                            </p>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">تحذير!</h6>
                        <p class="mb-0">لم يتم تحديد جهة اتصال في الطوارئ. يرجى إضافة معلومات جهة الاتصال.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- أرقام الطوارئ المهمة -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-telephone me-2"></i>
                    أرقام الطوارئ المهمة
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">الشرطة</h6>
                            <small class="text-muted">للحالات الأمنية</small>
                        </div>
                        <a href="tel:999" class="btn btn-sm btn-outline-danger">
                            <i class="bi bi-telephone me-1"></i>
                            999
                        </a>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">الإسعاف</h6>
                            <small class="text-muted">للحالات الطبية الطارئة</small>
                        </div>
                        <a href="tel:997" class="btn btn-sm btn-outline-danger">
                            <i class="bi bi-telephone me-1"></i>
                            997
                        </a>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">المطافئ</h6>
                            <small class="text-muted">للحالات الطارئة</small>
                        </div>
                        <a href="tel:998" class="btn btn-sm btn-outline-danger">
                            <i class="bi bi-telephone me-1"></i>
                            998
                        </a>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">المرور</h6>
                            <small class="text-muted">للحوادث المرورية</small>
                        </div>
                        <a href="tel:993" class="btn btn-sm btn-outline-danger">
                            <i class="bi bi-telephone me-1"></i>
                            993
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- نصائح للطوارئ -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightbulb me-2"></i>
                    نصائح للطوارئ
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-light">
                    <h6>نصائح مهمة:</h6>
                    <ul class="mb-0 small">
                        <li>تأكد من تحديث معلومات جهة الاتصال بانتظام</li>
                        <li>احتفظ بنسخة من الأدوية المهمة</li>
                        <li>أخبر جهة الاتصال عن أي تغييرات في الحالة الصحية</li>
                        <li>احتفظ بقائمة الحساسيات في مكان واضح</li>
                        <li>تأكد من أن جهة الاتصال تعرف كيفية الوصول للمستشفى</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    معلومات إضافية
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-primary mb-1">
                                <?= !empty($patient['emergency_contact']) ? '✅' : '❌' ?>
                            </h4>
                            <small class="text-muted">جهة الاتصال</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-success mb-1">
                                <?= !empty($patient['emergency_phone']) ? '✅' : '❌' ?>
                            </h4>
                            <small class="text-muted">رقم الهاتف</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-info mb-1">
                                <?= !empty($patient['blood_type']) ? '✅' : '❌' ?>
                            </h4>
                            <small class="text-muted">فصيلة الدم</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-warning mb-1">
                                <?= !empty($patient['allergies_emergency']) ? '✅' : '❌' ?>
                            </h4>
                            <small class="text-muted">الحساسيات</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// التحقق من صحة البيانات قبل الإرسال
document.getElementById('emergencyForm').addEventListener('submit', function(e) {
    const emergencyContact = document.getElementById('emergency_contact').value.trim();
    const emergencyPhone = document.getElementById('emergency_phone').value.trim();
    
    if (!emergencyContact) {
        e.preventDefault();
        alert('يرجى إدخال اسم جهة الاتصال');
        return false;
    }
    
    if (!emergencyPhone) {
        e.preventDefault();
        alert('يرجى إدخال رقم الهاتف');
        return false;
    }
    
    // التحقق من صحة رقم الهاتف
    const phoneRegex = /^[0-9+\-\s()]+$/;
    if (!phoneRegex.test(emergencyPhone)) {
        e.preventDefault();
        alert('يرجى إدخال رقم هاتف صحيح');
        return false;
    }
    
    // تأكيد الحفظ
    if (!confirm('هل أنت متأكد من حفظ معلومات جهة الاتصال في الطوارئ؟')) {
        e.preventDefault();
        return false;
    }
});

// اختبار الاتصال
function testCall(phoneNumber) {
    if (confirm('هل تريد الاتصال برقم ' + phoneNumber + '؟')) {
        window.location.href = 'tel:' + phoneNumber;
    }
}

// طباعة معلومات الطوارئ
function printEmergencyInfo() {
    const printContent = `
        <div style="font-family: Arial, sans-serif; padding: 20px;">
            <h2>معلومات الطوارئ</h2>
            <hr>
            <h3>جهة الاتصال في الطوارئ:</h3>
            <p><strong>الاسم:</strong> ${document.getElementById('emergency_contact').value || 'غير محدد'}</p>
            <p><strong>الهاتف:</strong> ${document.getElementById('emergency_phone').value || 'غير محدد'}</p>
            <hr>
            <h3>أرقام الطوارئ المهمة:</h3>
            <p><strong>الشرطة:</strong> 999</p>
            <p><strong>الإسعاف:</strong> 997</p>
            <p><strong>المطافئ:</strong> 998</p>
            <p><strong>المرور:</strong> 993</p>
        </div>
    `;
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
}
</script> 