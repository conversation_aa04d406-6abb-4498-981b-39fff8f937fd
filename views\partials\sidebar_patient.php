<!-- Sidebar for Patient -->
<div class="sidebar-patient">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <div class="sidebar-brand">
            <i class="bi bi-heart text-primary"></i>
            <span class="brand-text">HealthKey</span>
        </div>
        <button class="sidebar-toggle d-lg-none">
            <i class="bi bi-x-lg"></i>
        </button>
    </div>

    <!-- User Info -->
    <div class="sidebar-user">
        <div class="user-avatar">
            <i class="bi bi-person-circle"></i>
        </div>
        <div class="user-info">
            <div class="user-name"><?= $currentUser['first_name'] . ' ' . $currentUser['last_name'] ?></div>
            <div class="user-role">مريض</div>
        </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="sidebar-nav">
        <ul class="nav flex-column">
            <!-- Dashboard Section -->
            <li class="nav-section">
                <span class="nav-section-title">الرئيسية</span>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('patient/dashboard') ? 'active' : '' ?>" 
                   href="<?= App::url('patient/dashboard') ?>" data-bs-toggle="tooltip" title="لوحة التحكم">
                    <i class="bi bi-speedometer2"></i>
                    <span>لوحة التحكم</span>
                </a>
            </li>

            <!-- Appointments Section -->
            <li class="nav-section">
                <span class="nav-section-title">المواعيد</span>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('patient/appointments') ? 'active' : '' ?>" 
                   href="<?= App::url('patient/appointments') ?>" data-bs-toggle="tooltip" title="مواعيدي">
                    <i class="bi bi-calendar-check"></i>
                    <span>مواعيدي</span>
                    <span class="nav-badge bg-info"><?= isset($stats['appointments']['upcoming']) ? $stats['appointments']['upcoming'] : 0 ?></span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('patient/book-appointment') ? 'active' : '' ?>" 
                   href="<?= App::url('patient/book-appointment') ?>" data-bs-toggle="tooltip" title="حجز موعد">
                    <i class="bi bi-calendar-plus"></i>
                    <span>حجز موعد</span>
                </a>
            </li>

            <!-- Medical Records Section -->
            <li class="nav-section">
                <span class="nav-section-title">السجلات الطبية</span>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('patient/medical-records') ? 'active' : '' ?>" 
                   href="<?= App::url('patient/medical-records') ?>" data-bs-toggle="tooltip" title="سجلاتي الطبية">
                    <i class="bi bi-file-earmark-medical"></i>
                    <span>سجلاتي الطبية</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('patient/lab-results') ? 'active' : '' ?>" 
                   href="<?= App::url('patient/lab-results') ?>" data-bs-toggle="tooltip" title="نتائج المختبر">
                    <i class="bi bi-clipboard-data"></i>
                    <span>نتائج المختبر</span>
                </a>
            </li>

            <!-- Prescriptions Section -->
            <li class="nav-section">
                <span class="nav-section-title">الوصفات الطبية</span>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('patient/prescriptions') ? 'active' : '' ?>" 
                   href="<?= App::url('patient/prescriptions') ?>" data-bs-toggle="tooltip" title="وصفاتي الطبية">
                    <i class="bi bi-prescription2"></i>
                    <span>وصفاتي الطبية</span>
                    <span class="nav-badge bg-warning"><?= isset($stats['prescriptions']['active']) ? $stats['prescriptions']['active'] : 0 ?></span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('patient/medications') ? 'active' : '' ?>" 
                   href="<?= App::url('patient/medications') ?>" data-bs-toggle="tooltip" title="الأدوية">
                    <i class="bi bi-capsule"></i>
                    <span>الأدوية</span>
                </a>
            </li>

            <!-- Communication Section -->
            <li class="nav-section">
                <span class="nav-section-title">التواصل</span>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('patient/notifications') ? 'active' : '' ?>" 
                   href="<?= App::url('patient/notifications') ?>" data-bs-toggle="tooltip" title="الإشعارات">
                    <i class="bi bi-bell"></i>
                    <span>الإشعارات</span>
                    <?php if (SessionHelper::get('unread_notifications', 0) > 0): ?>
                        <span class="nav-badge bg-danger"><?= SessionHelper::get('unread_notifications') ?></span>
                    <?php endif; ?>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('patient/messages') ? 'active' : '' ?>" 
                   href="<?= App::url('patient/messages') ?>" data-bs-toggle="tooltip" title="الرسائل">
                    <i class="bi bi-chat-dots"></i>
                    <span>الرسائل</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('patient/contact-doctor') ? 'active' : '' ?>" 
                   href="<?= App::url('patient/contact-doctor') ?>" data-bs-toggle="tooltip" title="تواصل مع الطبيب">
                    <i class="bi bi-telephone"></i>
                    <span>تواصل مع الطبيب</span>
                </a>
            </li>

            <!-- Health Section -->
            <li class="nav-section">
                <span class="nav-section-title">الصحة</span>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('patient/health-tracker') ? 'active' : '' ?>" 
                   href="<?= App::url('patient/health-tracker') ?>" data-bs-toggle="tooltip" title="متابعة الصحة">
                    <i class="bi bi-heart-pulse"></i>
                    <span>متابعة الصحة</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('patient/vitals') ? 'active' : '' ?>" 
                   href="<?= App::url('patient/vitals') ?>" data-bs-toggle="tooltip" title="العلامات الحيوية">
                    <i class="bi bi-activity"></i>
                    <span>العلامات الحيوية</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('patient/allergies') ? 'active' : '' ?>" 
                   href="<?= App::url('patient/allergies') ?>" data-bs-toggle="tooltip" title="الحساسية">
                    <i class="bi bi-exclamation-triangle"></i>
                    <span>الحساسية</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('patient/profile') ? 'active' : '' ?>" 
                   href="<?= App::url('patient/profile') ?>" data-bs-toggle="tooltip" title="الملف الشخصي">
                    <i class="bi bi-person-gear"></i>
                    <span>الملف الشخصي</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('patient/emergency-contacts') ? 'active' : '' ?>" 
                   href="<?= App::url('patient/emergency-contacts') ?>" data-bs-toggle="tooltip" title="جهات الاتصال في الطوارئ">
                    <i class="bi bi-telephone-forward"></i>
                    <span>جهات الاتصال في الطوارئ</span>
                </a>
            </li>

            <!-- Logout Section -->
            <li class="nav-section">
                <span class="nav-section-title">الحساب</span>
            </li>

            <li class="nav-item">
                <a class="nav-link logout-link" href="<?= App::url('auth/logout') ?>" 
                   data-bs-toggle="tooltip" title="تسجيل الخروج" onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                    <i class="bi bi-box-arrow-right"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
        <div class="system-status">
            <div class="status-item">
                <i class="bi bi-circle-fill text-success"></i>
                <span>متصل</span>
            </div>
            <div class="status-item">
                <i class="bi bi-clock"></i>
                <span><?= date('H:i') ?></span>
            </div>
        </div>
    </div>
</div>

<style>
/* Sidebar Patient Styles */
.sidebar-patient {
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    height: 100vh;
    width: 280px;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    box-shadow: -4px 0 20px rgba(0,0,0,0.15);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Sidebar Header */
.sidebar-header {
    padding: 1.5rem 1rem 1rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.sidebar-brand i {
    font-size: 1.5rem;
    color: #27ae60;
}

.brand-text {
    font-size: 1.25rem;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: rgba(255,255,255,0.7);
    font-size: 1.1rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.sidebar-toggle:hover {
    color: #ffffff;
    background: rgba(255,255,255,0.1);
}

/* User Info */
.sidebar-user {
    padding: 1rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 1.25rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.user-info {
    flex: 1;
}

.user-name {
    font-weight: 600;
    color: #ffffff;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.user-role {
    font-size: 0.75rem;
    color: rgba(255,255,255,0.7);
}

/* Navigation Sections */
.nav-section {
    padding: 0.75rem 1rem 0.5rem;
    margin-top: 0.5rem;
}

.nav-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: rgba(255,255,255,0.5);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: 0.5rem;
}

/* Navigation Items */
.sidebar-nav {
    flex: 1;
    padding: 0.5rem 0;
}

.sidebar-patient .nav-item {
    margin: 0.125rem 0.75rem;
}

.sidebar-patient .nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.sidebar-patient .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1));
    transition: width 0.3s ease;
}

.sidebar-patient .nav-link:hover::before {
    width: 100%;
}

.sidebar-patient .nav-link:hover {
    color: #ffffff;
    background: rgba(255,255,255,0.1);
    transform: translateX(-4px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.sidebar-patient .nav-link.active {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.4);
    transform: translateX(-4px);
}

.sidebar-patient .nav-link.active::before {
    display: none;
}

.sidebar-patient .nav-link i {
    font-size: 1.1rem;
    min-width: 20px;
    text-align: center;
}

.sidebar-patient .nav-link span {
    flex: 1;
    font-weight: 500;
}

/* Navigation Badges */
.nav-badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.7rem;
    font-weight: 600;
    border-radius: 12px;
    background: rgba(255,255,255,0.2);
    color: #ffffff;
    min-width: 20px;
    text-align: center;
}

.nav-badge.bg-success {
    background: #198754 !important;
}

.nav-badge.bg-info {
    background: #0dcaf0 !important;
}

.nav-badge.bg-warning {
    background: #ffc107 !important;
    color: #212529;
}

.nav-badge.bg-danger {
    background: #dc3545 !important;
}

/* Logout Link */
.logout-link {
    color: #e74c3c !important;
    transition: all 0.3s ease;
}

.logout-link:hover {
    color: #c0392b !important;
    background: rgba(231, 76, 60, 0.1) !important;
}

.logout-link i {
    color: #e74c3c;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid rgba(255,255,255,0.1);
    background: rgba(0,0,0,0.1);
}

.system-status {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: rgba(255,255,255,0.8);
}

.status-item i {
    font-size: 0.7rem;
}

.status-item .text-success {
    color: #28a745 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar-patient {
        transform: translateX(100%);
        width: 280px;
        position: fixed;
        top: 0;
        right: 0;
        z-index: 1050;
    }
    
    .sidebar-patient.show {
        transform: translateX(0);
        box-shadow: -4px 0 20px rgba(0,0,0,0.3);
    }
    
    .sidebar-patient.collapsed {
        transform: translateX(100%);
    }
}

/* Scrollbar Styling */
.sidebar-patient::-webkit-scrollbar {
    width: 6px;
}

.sidebar-patient::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
    border-radius: 3px;
}

.sidebar-patient::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
}

.sidebar-patient::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5);
}
</style>

<script>
// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Auto-hide sidebar on mobile when clicking a link
document.querySelectorAll('.sidebar-patient .nav-link').forEach(link => {
    link.addEventListener('click', function() {
        if (window.innerWidth <= 768) {
            document.querySelector('.sidebar-patient').classList.remove('show');
            document.body.classList.remove('sidebar-open');
        }
    });
});

// Update time every minute
setInterval(function() {
    const timeElement = document.querySelector('.status-item:last-child span');
    if (timeElement) {
        timeElement.textContent = new Date().toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}, 60000);
</script> 