<?php
/**
 * اختبار صفحة صرف الأدوية للصيدلي
 */

require_once 'config.php';
require_once 'app/controllers/PharmacistController.php';

echo "<h1>اختبار صفحة صرف الأدوية</h1>";

try {
    // محاكاة تسجيل دخول الصيدلي
    $_SESSION['user_id'] = 2; // افتراض أن المستخدم رقم 2 هو صيدلي
    $_SESSION['user_type'] = 'pharmacist';
    
    $controller = new PharmacistController();
    
    echo "<h2>اختبار دالة dispensing</h2>";
    
    // محاكاة طلب GET
    $_GET['search'] = '';
    $_GET['status'] = 'active';
    
    // استدعاء الدالة
    $controller->dispensing();
    
    echo "✅ تم تنفيذ دالة dispensing بنجاح<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>اختبار المسارات</h2>";
echo "رابط الصفحة: <a href='http://localhost/HealthKey/pharmacist/dispensing' target='_blank'>http://localhost/HealthKey/pharmacist/dispensing</a><br>";
echo "رابط لوحة التحكم: <a href='http://localhost/HealthKey/pharmacist/dashboard' target='_blank'>http://localhost/HealthKey/pharmacist/dashboard</a><br>";
echo "رابط الوصفات: <a href='http://localhost/HealthKey/pharmacist/prescriptions' target='_blank'>http://localhost/HealthKey/pharmacist/prescriptions</a><br>";

echo "<h2>✅ انتهى الاختبار</h2>";
?> 