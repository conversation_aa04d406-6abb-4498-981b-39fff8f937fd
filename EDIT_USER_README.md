# صفحة تعديل المستخدم - HealthKey

## نظرة عامة

صفحة تعديل المستخدم هي واجهة شاملة تتيح للمدير تعديل بيانات أي مستخدم في النظام مع إمكانية إجراء عمليات إضافية مثل إعادة تعيين كلمة المرور وحذف المستخدم.

## الميزات الرئيسية

### ✅ البيانات الأساسية
- تعديل الاسم الأول والأخير
- تغيير البريد الإلكتروني
- تحديث رقم الهاتف
- تعديل نوع المستخدم (مريض، طبيب، صيدلي، مدير)

### ✅ البيانات الإضافية
- رقم الهوية الوطنية
- تاريخ الميلاد
- الجنس
- العنوان الكامل
- بيانات الطوارئ (جهة الاتصال والهاتف)

### ✅ إدارة الحالة
- تفعيل/إلغاء تفعيل المستخدم
- عرض حالة النشاط الحالية
- تحديث حالة النشاط

### ✅ الإجراءات الإضافية
- إعادة تعيين كلمة المرور
- عرض نشاط المستخدم
- إرسال إشعار للمستخدم
- تصدير بيانات المستخدم
- حذف المستخدم

## الوصول للصفحة

### رابط مباشر
```
http://localhost/HealthKey/admin/edit-user/{user_id}
```

### من قائمة المستخدمين
1. انتقل إلى `http://localhost/HealthKey/admin/users`
2. انقر على أيقونة التعديل بجانب المستخدم المطلوب

### من لوحة التحكم
1. انتقل إلى `http://localhost/HealthKey/admin/dashboard`
2. انقر على "إدارة المستخدمين"
3. اختر المستخدم المطلوب تعديله

## واجهة المستخدم

### البطاقة العلوية - معلومات المستخدم
- صورة المستخدم (أيقونة افتراضية)
- الاسم الكامل
- معرف المستخدم
- نوع المستخدم مع لون مميز
- البريد الإلكتروني
- تاريخ التسجيل
- آخر تحديث
- حالة النشاط

### نموذج التعديل
- **البيانات الأساسية**: الاسم، البريد الإلكتروني، الهاتف
- **البيانات الإضافية**: نوع المستخدم، الهوية، تاريخ الميلاد، الجنس
- **العنوان**: نص طويل للعنوان الكامل
- **بيانات الطوارئ**: جهة الاتصال وهاتف الطوارئ
- **حالة النشاط**: مربع اختيار للتفعيل/إلغاء التفعيل

### الإجراءات الإضافية
- **عرض النشاط**: فتح صفحة منفصلة لنشاط المستخدم
- **إرسال إشعار**: إرسال رسالة مباشرة للمستخدم
- **تصدير البيانات**: تحميل ملف CSV ببيانات المستخدم
- **حذف المستخدم**: حذف نهائي للمستخدم (مع تأكيد)

## التحقق من صحة البيانات

### الحقول المطلوبة
- الاسم الأول والأخير
- البريد الإلكتروني (مع التحقق من التفرد)
- نوع المستخدم

### التحقق من البريد الإلكتروني
- يجب أن يكون صيغة بريد إلكتروني صحيحة
- يجب أن يكون فريداً (لا يمكن تكراره)
- استثناء: يمكن للمستخدم تعديل بريده الإلكتروني الخاص

### التحقق من رقم الهوية
- اختياري ولكن يجب أن يكون فريداً إذا تم إدخاله
- يجب أن يكون أرقام فقط

## إعادة تعيين كلمة المرور

### العملية
1. انقر على "إعادة تعيين كلمة المرور"
2. تأكيد العملية في النافذة المنبثقة
3. إنشاء كلمة مرور عشوائية جديدة
4. تحديث كلمة المرور في قاعدة البيانات
5. إرسال كلمة المرور الجديدة للمستخدم (في التطبيق الحقيقي)

### كلمة المرور الجديدة
- طول: 8 أحرف
- تحتوي على: أحرف كبيرة وصغيرة، أرقام، رموز خاصة
- مثال: `Kj9#mP2x`

## الأمان والحماية

### التحقق من الصلاحيات
- فقط المديرين يمكنهم الوصول للصفحة
- التحقق من وجود المستخدم قبل التعديل
- منع حذف المدير الحالي

### حماية البيانات
- تنظيف جميع المدخلات
- التحقق من صحة البيانات
- منع حقن SQL

### تأكيد الإجراءات الخطرة
- تأكيد حذف المستخدم
- تأكيد إعادة تعيين كلمة المرور
- رسائل تحذير واضحة

## API Endpoints

### تعديل المستخدم
```
POST /admin/edit-user/{user_id}
```

### إعادة تعيين كلمة المرور
```
POST /admin/reset-user-password
Body: user_id={user_id}
```

### حذف المستخدم
```
POST /admin/delete-user
Body: user_id={user_id}
```

## رسائل النجاح والخطأ

### رسائل النجاح
- "تم تحديث المستخدم بنجاح"
- "تم إعادة تعيين كلمة المرور بنجاح"
- "تم حذف المستخدم بنجاح"

### رسائل الخطأ
- "المستخدم غير موجود"
- "البريد الإلكتروني مستخدم بالفعل"
- "فشل في تحديث المستخدم"
- "معرف المستخدم مطلوب"

## التخصيص والتطوير

### إضافة حقول جديدة
1. أضف الحقل في قاعدة البيانات
2. أضف الحقل في نموذج التعديل
3. أضف التحقق من صحة البيانات
4. أضف الحقل في دالة التحديث

### إضافة إجراءات جديدة
1. أضف الزر في الواجهة
2. أضف الدالة في AdminController
3. أضف المسار في App.php
4. أضف JavaScript للتفاعل

### تخصيص التصميم
- تعديل الألوان في ملف CSS
- إضافة أيقونات جديدة
- تغيير تخطيط الصفحة

## استكشاف الأخطاء

### مشاكل شائعة

#### الخطأ: "المستخدم غير موجود"
- **السبب**: معرف المستخدم غير صحيح
- **الحل**: التحقق من معرف المستخدم في الرابط

#### الخطأ: "البريد الإلكتروني مستخدم بالفعل"
- **السبب**: البريد الإلكتروني موجود لمستخدم آخر
- **الحل**: استخدام بريد إلكتروني مختلف

#### الخطأ: "فشل في تحديث المستخدم"
- **السبب**: مشكلة في قاعدة البيانات
- **الحل**: التحقق من اتصال قاعدة البيانات

### سجلات الأخطاء
- فحص ملفات السجل في `/logs/`
- فحص أخطاء PHP في `/error_log`
- فحص أخطاء قاعدة البيانات

## الاختبار

### ملف الاختبار
```
http://localhost/HealthKey/test_edit_user.php
```

### سيناريوهات الاختبار
1. **تعديل البيانات الأساسية**
   - تغيير الاسم
   - تغيير البريد الإلكتروني
   - تغيير نوع المستخدم

2. **إعادة تعيين كلمة المرور**
   - تأكيد العملية
   - التحقق من إنشاء كلمة مرور جديدة

3. **حذف المستخدم**
   - تأكيد الحذف
   - التحقق من عدم حذف المدير الحالي

4. **التحقق من صحة البيانات**
   - إدخال بيانات غير صحيحة
   - التحقق من رسائل الخطأ

## الدعم والمساعدة

### للمطورين
- راجع كود المصدر في `views/admin/edit_user.php`
- راجع منطق الأعمال في `app/controllers/AdminController.php`
- راجع نموذج البيانات في `app/models/User.php`

### للمستخدمين
- تأكد من وجود صلاحيات المدير
- تحقق من صحة البيانات المدخلة
- استخدم زر "العودة" للرجوع للصفحة السابقة

## التحديثات المستقبلية

### الميزات المخطط لها
- [ ] رفع صورة المستخدم
- [ ] إضافة حقول مخصصة حسب نوع المستخدم
- [ ] سجل التغييرات
- [ ] إشعارات فورية للمستخدم
- [ ] تصدير البيانات بصيغ إضافية

### التحسينات المقترحة
- [ ] واجهة أكثر تفاعلية
- [ ] حفظ المسودات
- [ ] مقارنة التغييرات
- [ ] إعادة التراجع عن التغييرات

---

**آخر تحديث**: <?= date('Y-m-d H:i') ?>
**الإصدار**: 1.0
**المطور**: نظام HealthKey 