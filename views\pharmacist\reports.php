<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">التقارير الشاملة</h1>
        <p class="text-muted">نظرة شاملة على أداء الصيدلية والإحصائيات</p>
    </div>
    <div>
        <button class="btn btn-outline-success" onclick="exportComprehensiveReport()">
            <i class="bi bi-download me-1"></i>
            تصدير التقرير الشامل
        </button>
    </div>
</div>

<!-- Date Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-calendar-range me-2"></i>
            تحديد الفترة الزمنية
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="<?= App::url('pharmacist/reports') ?>" class="row g-3">
            <div class="col-md-4">
                <label for="start_date" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="start_date" name="start_date" 
                       value="<?= htmlspecialchars($startDate) ?>" required>
            </div>
            <div class="col-md-4">
                <label for="end_date" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="end_date" name="end_date" 
                       value="<?= htmlspecialchars($endDate) ?>" required>
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search me-1"></i>
                        تطبيق الفترة
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Performance Metrics -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['performance_metrics']['total_prescriptions'] ?></h4>
                        <p class="mb-0">إجمالي الوصفات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-prescription2 display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['performance_metrics']['total_medications'] ?></h4>
                        <p class="mb-0">الأدوية المصروفة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-capsule display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['performance_metrics']['completion_rate'] ?>%</h4>
                        <p class="mb-0">معدل الإنجاز</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['performance_metrics']['avg_processing_time'] ?></h4>
                        <p class="mb-0">متوسط وقت المعالجة (دقيقة)</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics -->
<div class="row mb-4">
    <!-- Daily Dispensing Chart -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    الصرف اليومي
                </h5>
            </div>
            <div class="card-body">
                <canvas id="dailyDispensingChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Top Medications -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-list-ol me-2"></i>
                    الأدوية الأكثر صرفاً
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($stats['top_medications'])): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach (array_slice($stats['top_medications'], 0, 5) as $index => $medication): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1"><?= htmlspecialchars($medication['name']) ?></h6>
                                    <small class="text-muted"><?= htmlspecialchars($medication['generic_name']) ?></small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-primary rounded-pill"><?= $medication['dispensed_count'] ?></span>
                                    <br>
                                    <small class="text-muted"><?= $medication['total_quantity'] ?> وحدة</small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p class="text-muted text-center">لا توجد بيانات متاحة</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Monthly Trends -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-calendar-month me-2"></i>
                    الاتجاهات الشهرية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="monthlyTrendsChart" height="100"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-activity me-2"></i>
                    النشاط الأخير
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($stats['recent_activity'])): ?>
                    <div class="timeline">
                        <?php foreach ($stats['recent_activity'] as $activity): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker <?= $activity['type'] === 'dispensing' ? 'bg-success' : 'bg-info' ?>"></div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between">
                                        <h6 class="mb-1"><?= htmlspecialchars($activity['description']) ?></h6>
                                        <small class="text-muted"><?= DateHelper::formatDate($activity['date'], 'Y-m-d H:i') ?></small>
                                    </div>
                                    <p class="mb-0 text-muted"><?= htmlspecialchars($activity['action']) ?></p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <p class="text-muted text-center">لا توجد نشاطات حديثة</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Inventory Summary -->
<div class="row mb-4">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-box-seam me-2"></i>
                    ملخص المخزون
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h4 class="text-primary"><?= $stats['inventory']['total_items'] ?? 0 ?></h4>
                        <small class="text-muted">إجمالي العناصر</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-warning"><?= $stats['inventory']['low_stock'] ?? 0 ?></h4>
                        <small class="text-muted">منخفض المخزون</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-danger"><?= $stats['inventory']['expired'] ?? 0 ?></h4>
                        <small class="text-muted">منتهي الصلاحية</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    إحصائيات الوصفات
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h4 class="text-success"><?= $stats['prescriptions']['total'] ?? 0 ?></h4>
                        <small class="text-muted">إجمالي الوصفات</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-warning"><?= $stats['prescriptions']['pending'] ?? 0 ?></h4>
                        <small class="text-muted">في الانتظار</small>
                    </div>
                    <div class="col-4">
                        <h4 class="text-info"><?= $stats['prescriptions']['completed'] ?? 0 ?></h4>
                        <small class="text-muted">مكتملة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="<?= App::url('pharmacist/dispensing-report') ?>" class="btn btn-outline-primary w-100">
                            <i class="bi bi-clipboard-data me-2"></i>
                            تقرير الصرف التفصيلي
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= App::url('pharmacist/prescriptions') ?>" class="btn btn-outline-success w-100">
                            <i class="bi bi-prescription2 me-2"></i>
                            إدارة الوصفات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= App::url('pharmacist/inventory') ?>" class="btn btn-outline-warning w-100">
                            <i class="bi bi-box-seam me-2"></i>
                            إدارة المخزون
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?= App::url('pharmacist/dispensing') ?>" class="btn btn-outline-info w-100">
                            <i class="bi bi-capsule me-2"></i>
                            صرف الأدوية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Charts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Daily Dispensing Chart
const dailyCtx = document.getElementById('dailyDispensingChart').getContext('2d');
const dailyChart = new Chart(dailyCtx, {
    type: 'line',
    data: {
        labels: <?= json_encode(array_column($stats['daily_dispensing'], 'date')) ?>,
        datasets: [{
            label: 'عدد الوصفات',
            data: <?= json_encode(array_column($stats['daily_dispensing'], 'prescriptions_count')) ?>,
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }, {
            label: 'إجمالي الكمية',
            data: <?= json_encode(array_column($stats['daily_dispensing'], 'total_quantity')) ?>,
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'الصرف اليومي'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Monthly Trends Chart
const monthlyCtx = document.getElementById('monthlyTrendsChart').getContext('2d');
const monthlyChart = new Chart(monthlyCtx, {
    type: 'bar',
    data: {
        labels: <?= json_encode(array_column($stats['monthly_trends'], 'month')) ?>,
        datasets: [{
            label: 'عدد الوصفات',
            data: <?= json_encode(array_column($stats['monthly_trends'], 'prescriptions_count')) ?>,
            backgroundColor: 'rgba(54, 162, 235, 0.8)',
            borderColor: 'rgb(54, 162, 235)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'الاتجاهات الشهرية'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Export Comprehensive Report
function exportComprehensiveReport() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    
    // إنشاء رابط التصدير
    const exportUrl = `<?= App::url('pharmacist/export-comprehensive-report') ?>?start_date=${startDate}&end_date=${endDate}`;
    
    // فتح الرابط في نافذة جديدة
    window.open(exportUrl, '_blank');
}

// تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e9ecef;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-left: 10px;
}

.timeline-content h6 {
    margin-bottom: 5px;
    color: #495057;
}

.timeline-content p {
    margin-bottom: 0;
    font-size: 0.875rem;
}
</style> 