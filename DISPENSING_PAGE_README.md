# صفحة صرف الأدوية للصيدلي

## نظرة عامة
تم إنشاء صفحة صرف الأدوية للصيدلي في المسار `http://localhost/HealthKey/pharmacist/dispensing` لتوفير واجهة شاملة لإدارة صرف الأدوية للمرضى.

## المميزات

### 1. لوحة الإحصائيات
- **الوصفات المعلقة**: عدد الوصفات التي تحتاج إلى صرف
- **الوصفات النشطة**: إجمالي الوصفات النشطة
- **مصروف اليوم**: عدد الأدوية المصروفة اليوم
- **إجمالي المصروف**: إجمالي الأدوية المصروفة بواسطة الصيدلي

### 2. البحث والتصفية
- البحث بالكود أو اسم المريض أو الطبيب
- تصفية حسب الحالة (نشطة، معلقة، مصروفة)
- إعادة تعيين الفلاتر

### 3. قائمة الوصفات
- عرض تفصيلي للوصفات مع:
  - كود الوصفة
  - معلومات المريض والطبيب
  - التشخيص
  - نسبة إنجاز صرف الأدوية
  - تواريخ الإصدار والانتهاء
  - حالة الوصفة

### 4. إجراءات الصرف
- عرض تفاصيل الوصفة
- صرف الأدوية من خلال نافذة منبثقة
- تتبع حالة الصرف

## الملفات المضافة/المعدلة

### 1. المتحكم (Controller)
**الملف**: `app/controllers/PharmacistController.php`

#### الدوال المضافة:
- `dispensing()`: عرض صفحة صرف الأدوية
- `getDispensingStats()`: الحصول على إحصائيات الصرف

### 2. النموذج (Model)
**الملف**: `app/models/Prescription.php`

#### الدوال المضافة:
- `searchForDispensing()`: البحث في الوصفات للصرف
- `getActiveForDispensing()`: الحصول على الوصفات النشطة للصرف
- `getPendingForDispensing()`: الحصول على الوصفات المعلقة للصرف
- `getDispensedByPharmacist()`: الحصول على الوصفات المصروفة

### 3. صفحة العرض (View)
**الملف**: `views/pharmacist/dispensing.php`

#### المميزات:
- تصميم حديث ومتجاوب
- بطاقات إحصائيات ملونة
- جدول تفاعلي للوصفات
- نافذة منبثقة لصرف الأدوية
- JavaScript للتفاعل

### 4. التوجيه (Routing)
**الملف**: `app/core/App.php`

#### المسارات المضافة:
- `pharmacist/dispensing`
- `pharmacist/prescription-details`
- `pharmacist/dispense-medication`
- `pharmacist/dispensing-report`
- `pharmacist/notifications`
- `pharmacist/profile`

## كيفية الاستخدام

### 1. الوصول للصفحة
```
http://localhost/HealthKey/pharmacist/dispensing
```

### 2. البحث في الوصفات
- استخدم حقل البحث للبحث بالكود أو الأسماء
- اختر الحالة المطلوبة من القائمة المنسدلة
- اضغط "بحث" أو "إعادة تعيين"

### 3. صرف الدواء
- اضغط على أيقونة الكبسولة بجانب الوصفة
- ستفتح نافذة منبثقة مع تفاصيل الوصفة
- أدخل الكمية المصروفة
- اضغط "صرف الدواء"

### 4. عرض التفاصيل
- اضغط على أيقونة العين لعرض تفاصيل الوصفة
- يمكن الوصول لصفحة التفاصيل الكاملة

## الأمان والتحقق

### 1. التحقق من الصلاحيات
- التحقق من تسجيل الدخول
- التحقق من نوع المستخدم (صيدلي)
- التحقق من ملكية البيانات

### 2. التحقق من صحة البيانات
- التحقق من وجود الوصفة
- التحقق من صلاحية الوصفة
- التحقق من عدم صرف الدواء مسبقاً

### 3. تسجيل العمليات
- تسجيل عملية الصرف
- إرسال إشعارات للمرضى
- تحديث حالة الوصفة

## التقنيات المستخدمة

### 1. الواجهة الأمامية
- Bootstrap 5 (RTL)
- Bootstrap Icons
- JavaScript (ES6+)
- CSS3 مع دعم RTL

### 2. الخلفية
- PHP 7.4+
- MySQL
- PDO للاتصال بقاعدة البيانات
- جلسات PHP للأمان

### 3. المكتبات المساعدة
- DateHelper: لتنسيق التواريخ بالعربية
- ValidationHelper: للتحقق من صحة البيانات
- SessionHelper: لإدارة الجلسات

## اختبار الصفحة

### ملف الاختبار
**الملف**: `test_dispensing_page.php`

#### كيفية التشغيل:
```
http://localhost/HealthKey/test_dispensing_page.php
```

#### ما يختبره:
- إنشاء متحكم الصيدلي
- استدعاء دالة dispensing
- التحقق من عدم وجود أخطاء
- عرض روابط الصفحات

## الصيانة والتطوير

### 1. إضافة ميزات جديدة
- يمكن إضافة تصفية إضافية
- يمكن إضافة تصدير البيانات
- يمكن إضافة طباعة الوصفات

### 2. تحسين الأداء
- إضافة فهرسة لقاعدة البيانات
- تحسين استعلامات SQL
- إضافة التخزين المؤقت

### 3. تحسين الأمان
- إضافة CSRF protection
- تحسين التحقق من الصلاحيات
- إضافة تسجيل الأحداث

## استكشاف الأخطاء

### 1. مشاكل شائعة
- **خطأ 404**: تأكد من وجود المسار في App.php
- **خطأ في قاعدة البيانات**: تأكد من وجود الجداول المطلوبة
- **خطأ في العرض**: تأكد من وجود ملف dispensing.php

### 2. خطوات التشخيص
1. تحقق من سجلات الأخطاء
2. تأكد من تضمين جميع الملفات المطلوبة
3. تحقق من إعدادات قاعدة البيانات
4. اختبر الاتصال بقاعدة البيانات

## الدعم والمساعدة

للمساعدة أو الإبلاغ عن مشاكل:
1. راجع ملفات README الأخرى
2. تحقق من سجلات الأخطاء
3. اختبر الصفحة باستخدام ملف الاختبار
4. تأكد من تحديث جميع الملفات المطلوبة 