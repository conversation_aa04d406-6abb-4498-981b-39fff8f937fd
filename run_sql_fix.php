<?php
/**
 * تشغيل ملف SQL لإضافة الأعمدة المفقودة
 */

require_once 'config.php';

echo "<h1>إضافة الأعمدة المفقودة لجدول المواعيد</h1>";

try {
    $db = Database::getInstance();
    
    echo "<h2>1. قراءة ملف SQL</h2>";
    
    $sqlFile = 'add_missing_columns.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("ملف SQL غير موجود: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    echo "✅ تم قراءة ملف SQL بنجاح<br>";
    
    echo "<h2>2. تنفيذ الاستعلامات</h2>";
    
    // تقسيم الاستعلامات
    $queries = explode(';', $sql);
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($queries as $query) {
        $query = trim($query);
        if (!empty($query) && !preg_match('/^(USE|SELECT)/i', $query)) {
            try {
                $result = $db->update($query);
                echo "✅ تم تنفيذ: " . substr($query, 0, 50) . "...<br>";
                $successCount++;
            } catch (Exception $e) {
                // تجاهل أخطاء الأعمدة الموجودة مسبقاً
                if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                    echo "⚠️ العمود موجود مسبقاً: " . substr($query, 0, 50) . "...<br>";
                } else {
                    echo "❌ خطأ في: " . substr($query, 0, 50) . "...<br>";
                    echo "الخطأ: " . $e->getMessage() . "<br>";
                    $errorCount++;
                }
            }
        }
    }
    
    echo "<h2>3. نتائج التنفيذ</h2>";
    echo "✅ الاستعلامات الناجحة: $successCount<br>";
    echo "❌ الاستعلامات الفاشلة: $errorCount<br>";
    
    echo "<h2>4. فحص الأعمدة الجديدة</h2>";
    
    // فحص الأعمدة الجديدة
    $columnsQuery = "DESCRIBE appointments";
    $columns = $db->select($columnsQuery);
    
    $newColumns = ['rating', 'duration', 'appointment_type', 'priority', 'room_number', 'insurance_info'];
    $foundColumns = [];
    
    foreach ($columns as $column) {
        if (in_array($column['Field'], $newColumns)) {
            $foundColumns[] = $column['Field'];
        }
    }
    
    echo "<h3>الأعمدة الجديدة الموجودة:</h3>";
    if (!empty($foundColumns)) {
        echo "<ul>";
        foreach ($foundColumns as $column) {
            echo "<li>✅ $column</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>❌ لم يتم العثور على أي من الأعمدة الجديدة</p>";
    }
    
    echo "<h2>5. اختبار صفحة التحليلات</h2>";
    
    // محاكاة تسجيل دخول طبيب
    $_SESSION['user_id'] = 2;
    $_SESSION['user_type'] = 'doctor';
    $_SESSION['user_name'] = 'د. أحمد محمد';
    
    echo "<p><a href='public/index.php?url=doctor/analytics' target='_blank'>فتح صفحة التحليلات</a></p>";
    echo "<p><a href='public/index.php?url=doctor/reports' target='_blank'>فتح صفحة التقارير</a></p>";
    
    echo "<h2>✅ انتهى الإصلاح</h2>";
    echo "<p>إذا تم إضافة الأعمدة بنجاح وفتحت الصفحات بدون أخطاء، فهذا يعني أن المشكلة قد تم حلها.</p>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ عام</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 