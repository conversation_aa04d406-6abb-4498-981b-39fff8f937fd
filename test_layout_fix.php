<?php
// اختبار إصلاح مشكلة التخطيط
echo "<h1>اختبار إصلاح مشكلة التخطيط</h1>";

// تعريف الثوابت المطلوبة
define('DB_HOST', 'localhost');
define('DB_NAME', 'healthkey');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');
define('APP_DEBUG', true);
define('APP_URL', 'http://localhost/HealthKey');

// محاكاة جلسة المستخدم
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'pharmacist';
$_SESSION['user'] = [
    'id' => 1,
    'first_name' => 'أحمد',
    'last_name' => 'محمد',
    'email' => '<EMAIL>',
    'user_type' => 'pharmacist'
];

try {
    require_once 'app/core/Database.php';
    require_once 'app/core/Controller.php';
    require_once 'app/core/App.php';
    require_once 'app/controllers/PharmacistController.php';
    
    echo "<h2>✅ تم تحميل الملفات بنجاح</h2>";
    
    // اختبار إنشاء متحكم الصيدلي
    $controller = new PharmacistController();
    echo "<h2>✅ تم إنشاء متحكم الصيدلي بنجاح</h2>";
    
    // اختبار دالة addInventory
    echo "<h3>اختبار دالة addInventory:</h3>";
    try {
        $controller->addInventory();
        echo "<p style='color: green;'>✅ تم تنفيذ دالة addInventory بنجاح</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في دالة addInventory: " . $e->getMessage() . "</p>";
    }
    
    // اختبار وجود ملف التخطيط
    echo "<h3>اختبار ملف التخطيط:</h3>";
    if (file_exists('views/layouts/pharmacist.php')) {
        echo "<p style='color: green;'>✅ ملف التخطيط موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ ملف التخطيط غير موجود</p>";
    }
    
    // اختبار وجود ملف الشريط الجانبي
    echo "<h3>اختبار ملف الشريط الجانبي:</h3>";
    if (file_exists('views/partials/sidebar_pharmacist.php')) {
        echo "<p style='color: green;'>✅ ملف الشريط الجانبي موجود</p>";
    } else {
        echo "<p style='color: red;'>❌ ملف الشريط الجانبي غير موجود</p>";
    }
    
    // اختبار المسار في ملف التخطيط
    echo "<h3>اختبار المسار في ملف التخطيط:</h3>";
    $layoutContent = file_get_contents('views/layouts/pharmacist.php');
    if (strpos($layoutContent, '../views/partials/sidebar_pharmacist.php') !== false) {
        echo "<p style='color: green;'>✅ المسار صحيح في ملف التخطيط</p>";
            } else {
        echo "<p style='color: red;'>❌ المسار غير صحيح في ملف التخطيط</p>";
    }
    
    echo "<h2>🎉 تم إصلاح مشكلة التخطيط بنجاح!</h2>";
    echo "<p style='color: #28a745; font-weight: bold;'>جميع الملفات موجودة والمسارات صحيحة.</p>";
    
    // روابط للاختبار
    echo "<h3>روابط للاختبار:</h3>";
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<ul>";
    echo "<li><a href='http://localhost:8000/index.php?url=pharmacist/add-inventory' target='_blank' style='color: #007bff; text-decoration: none;'>➕ صفحة إضافة دواء جديد</a></li>";
    echo "<li><a href='http://localhost:8000/index.php?url=pharmacist/inventory' target='_blank' style='color: #007bff; text-decoration: none;'>🏥 صفحة إدارة المخزون</a></li>";
    echo "<li><a href='http://localhost:8000/index.php?url=pharmacist/suppliers' target='_blank' style='color: #007bff; text-decoration: none;'>🏢 صفحة إدارة الموردين</a></li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في الاختبار:</h2>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "<p>الملف: " . $e->getFile() . "</p>";
    echo "<p>السطر: " . $e->getLine() . "</p>";
}
?> 