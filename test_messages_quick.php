<?php
/**
 * اختبار سريع لصفحة الرسائل
 */

// تضمين ملفات النظام
require_once 'app/core/App.php';
require_once 'app/helpers/SessionHelper.php';
require_once 'app/models/User.php';
require_once 'app/models/Message.php';

// محاكاة تسجيل دخول الطبيب
session_start();
$_SESSION['user_id'] = 2;
$_SESSION['user_type'] = 'doctor';
$_SESSION['user_name'] = 'د. أحمد محمد';

// تحميل النماذج
$userModel = new User();
$messageModel = new Message();

// اختبار الحصول على البيانات
try {
    $doctorId = $_SESSION['user_id'];
    
    // الحصول على قوائم المستخدمين
    $patients = $userModel->getPatientsByDoctor($doctorId);
    $doctors = $userModel->getAllDoctors();
    
    // الحصول على الرسائل
    $inboxMessages = $messageModel->getInbox($doctorId, false, 10);
    $sentMessages = $messageModel->getSent($doctorId, 10);
    $unreadCount = $messageModel->getUnreadCount($doctorId);
    $stats = $messageModel->getStats($doctorId);
    
    echo "✅ اختبار صفحة الرسائل نجح!\n\n";
    echo "📊 الإحصائيات:\n";
    echo "- الرسائل الواردة: " . count($inboxMessages) . "\n";
    echo "- الرسائل الصادرة: " . count($sentMessages) . "\n";
    echo "- الرسائل غير المقروءة: " . $unreadCount . "\n";
    echo "- إجمالي الرسائل: " . ($stats['inbox'] + $stats['sent']) . "\n";
    echo "- عدد المرضى: " . count($patients) . "\n";
    echo "- عدد الأطباء: " . count($doctors) . "\n\n";
    
    echo "🔗 يمكنك الآن الوصول إلى صفحة الرسائل عبر:\n";
    echo "http://localhost/HealthKey/doctor/messages\n\n";
    
    echo "📝 لاختبار شامل، استخدم:\n";
    echo "http://localhost/HealthKey/test_doctor_messages.php\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في اختبار صفحة الرسائل:\n";
    echo $e->getMessage() . "\n";
    echo "في الملف: " . $e->getFile() . "\n";
    echo "في السطر: " . $e->getLine() . "\n";
}
?> 