<?php
/**
 * اختبار نهائي لصفحة ساعات العمل
 */

// محاكاة تسجيل دخول الصيدلي
session_start();
$_SESSION['user_id'] = 2;
$_SESSION['user_type'] = 'pharmacist';
$_SESSION['user'] = [
    'id' => 2,
    'first_name' => 'أحمد',
    'last_name' => 'محمد',
    'email' => '<EMAIL>'
];

echo "<h1>اختبار صفحة ساعات العمل للصيدلي</h1>";

echo "<h2>الروابط المتاحة:</h2>";
echo "<ul>";
echo "<li><a href='index.php?url=pharmacist/working-hours' target='_blank'>صفحة ساعات العمل</a></li>";
echo "<li><a href='http://localhost/HealthKey/index.php?url=pharmacist/working-hours' target='_blank'>صفحة ساعات العمل (localhost)</a></li>";
echo "<li><a href='index.php?url=pharmacist/dashboard' target='_blank'>لوحة تحكم الصيدلي</a></li>";
echo "</ul>";

echo "<h2>معلومات الجلسة:</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>اختبار قاعدة البيانات:</h2>";

try {
    require_once 'config.php';
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // التحقق من وجود جدول working_hours
    $tableExists = $pdo->query("SHOW TABLES LIKE 'working_hours'")->rowCount() > 0;
    echo "✅ جدول working_hours موجود: " . ($tableExists ? 'نعم' : 'لا') . "<br>";
    
    if ($tableExists) {
        // عرض ساعات عمل الصيدلي رقم 2
        $stmt = $pdo->prepare("SELECT * FROM working_hours WHERE user_id = ? AND user_type = 'pharmacist' ORDER BY day");
        $stmt->execute([2]);
        $hours = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "✅ عدد ساعات العمل للصيدلي رقم 2: " . count($hours) . "<br>";
        
        if (!empty($hours)) {
            echo "<h3>ساعات العمل:</h3>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>اليوم</th><th>وقت البداية</th><th>وقت النهاية</th><th>يوم عمل</th></tr>";
            
            $daysNames = [
                'sunday' => 'الأحد',
                'monday' => 'الاثنين',
                'tuesday' => 'الثلاثاء',
                'wednesday' => 'الأربعاء',
                'thursday' => 'الخميس',
                'friday' => 'الجمعة',
                'saturday' => 'السبت'
            ];
            
            foreach ($hours as $hour) {
                echo "<tr>";
                echo "<td>" . $daysNames[$hour['day']] . "</td>";
                echo "<td>" . $hour['start_time'] . "</td>";
                echo "<td>" . $hour['end_time'] . "</td>";
                echo "<td>" . ($hour['is_working'] ? 'نعم' : 'لا') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
}

echo "<h2>✅ انتهى الاختبار</h2>";
echo "<p>إذا كانت جميع الاختبارات ناجحة، يمكنك الوصول لصفحة ساعات العمل عبر الروابط أعلاه.</p>";
?> 