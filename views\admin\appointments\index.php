<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="fw-bold text-primary mb-1">
            <i class="bi bi-calendar-check-fill me-2"></i>
            إدارة المواعيد
        </h2>
        <p class="text-muted mb-0">إدارة ومتابعة جميع المواعيد الطبية في النظام</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAppointmentModal">
            <i class="bi bi-plus-circle-fill me-2"></i>
            إضافة موعد جديد
        </button>
        <button class="btn btn-outline-secondary" onclick="exportAppointments()">
            <i class="bi bi-download me-2"></i>
            تصدير
        </button>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card bg-gradient-primary text-white">
            <div class="stats-icon">
                <i class="bi bi-calendar-check"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number"><?= $appointmentStats['total'] ?? 0 ?></div>
                <div class="stats-label">إجمالي المواعيد</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-gradient-success text-white">
            <div class="stats-icon">
                <i class="bi bi-check-circle"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number"><?= $appointmentStats['confirmed'] ?? 0 ?></div>
                <div class="stats-label">مواعيد مؤكدة</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-gradient-warning text-white">
            <div class="stats-icon">
                <i class="bi bi-clock"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number"><?= $appointmentStats['scheduled'] ?? 0 ?></div>
                <div class="stats-label">في الانتظار</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-gradient-info text-white">
            <div class="stats-icon">
                <i class="bi bi-calendar-day"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number"><?= $appointmentStats['today'] ?? 0 ?></div>
                <div class="stats-label">مواعيد اليوم</div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label fw-semibold">البحث</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="text" class="form-control" name="search" 
                           value="<?= htmlspecialchars($filters['search'] ?? '') ?>" 
                           placeholder="البحث بالمريض أو الطبيب">
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label fw-semibold">الحالة</label>
                <select class="form-select" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="scheduled" <?= ($filters['status'] ?? '') === 'scheduled' ? 'selected' : '' ?>>مجدول</option>
                    <option value="confirmed" <?= ($filters['status'] ?? '') === 'confirmed' ? 'selected' : '' ?>>مؤكد</option>
                    <option value="completed" <?= ($filters['status'] ?? '') === 'completed' ? 'selected' : '' ?>>مكتمل</option>
                    <option value="cancelled" <?= ($filters['status'] ?? '') === 'cancelled' ? 'selected' : '' ?>>ملغي</option>
                    <option value="no_show" <?= ($filters['status'] ?? '') === 'no_show' ? 'selected' : '' ?>>لم يحضر</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label fw-semibold">التاريخ من</label>
                <input type="date" class="form-control" name="date_from" 
                       value="<?= htmlspecialchars($filters['date_from'] ?? '') ?>">
            </div>
            <div class="col-md-2">
                <label class="form-label fw-semibold">التاريخ إلى</label>
                <input type="date" class="form-control" name="date_to" 
                       value="<?= htmlspecialchars($filters['date_to'] ?? '') ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label fw-semibold">الطبيب</label>
                <select class="form-select" name="doctor_id">
                    <option value="">جميع الأطباء</option>
                    <?php if (isset($doctors)): ?>
                        <?php foreach ($doctors as $doctor): ?>
                            <option value="<?= $doctor['id'] ?>" <?= ($filters['doctor_id'] ?? '') == $doctor['id'] ? 'selected' : '' ?>>
                                د. <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                            </option>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </select>
            </div>
            <div class="col-md-12">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-funnel-fill me-2"></i>
                    تطبيق الفلاتر
                </button>
                <a href="<?= App::url('admin/appointments') ?>" class="btn btn-outline-secondary">
                    <i class="bi bi-x-circle me-2"></i>
                    إعادة تعيين
                </a>
            </div>
        </form>
    </div>
</div>

<!-- جدول المواعيد -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-table me-2"></i>
            قائمة المواعيد
            <span class="badge bg-primary ms-2"><?= $totalAppointments ?></span>
        </h5>
        <div class="d-flex gap-2">
            <button class="btn btn-sm btn-outline-primary" onclick="refreshTable()">
                <i class="bi bi-arrow-clockwise"></i>
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>المريض</th>
                        <th>الطبيب</th>
                        <th>التاريخ والوقت</th>
                        <th>السبب</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($appointments)): ?>
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-calendar-x display-4 d-block mb-3"></i>
                                    لا توجد مواعيد مطابقة للبحث
                                </div>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($appointments as $appointment): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar me-3">
                                            <div class="avatar-initial bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                                <i class="bi bi-person-fill"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="fw-semibold"><?= htmlspecialchars($appointment['patient_name'] ?? 'غير محدد') ?></div>
                                            <small class="text-muted">مريض</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar me-3">
                                            <div class="avatar-initial bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                                                <i class="bi bi-heart-pulse-fill"></i>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="fw-semibold">د. <?= htmlspecialchars($appointment['doctor_name'] ?? 'غير محدد') ?></div>
                                            <small class="text-muted"><?= htmlspecialchars($appointment['doctor_specialization'] ?? 'طبيب عام') ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="fw-semibold"><?= DateHelper::formatArabic($appointment['appointment_date']) ?></div>
                                        <small class="text-muted">
                                            <i class="bi bi-clock me-1"></i>
                                            <?= date('H:i', strtotime($appointment['appointment_time'])) ?>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <span class="text-truncate d-inline-block" style="max-width: 150px;" title="<?= htmlspecialchars($appointment['reason'] ?? '') ?>">
                                        <?= htmlspecialchars($appointment['reason'] ?? 'استشارة عامة') ?>
                                    </span>
                                </td>
                                <td>
                                    <?php
                                    $statusColors = [
                                        'scheduled' => 'warning',
                                        'confirmed' => 'success',
                                        'completed' => 'primary',
                                        'cancelled' => 'danger',
                                        'no_show' => 'secondary'
                                    ];
                                    $statusNames = [
                                        'scheduled' => 'مجدول',
                                        'confirmed' => 'مؤكد',
                                        'completed' => 'مكتمل',
                                        'cancelled' => 'ملغي',
                                        'no_show' => 'لم يحضر'
                                    ];
                                    $color = $statusColors[$appointment['status']] ?? 'secondary';
                                    $name = $statusNames[$appointment['status']] ?? $appointment['status'];
                                    ?>
                                    <span class="badge bg-<?= $color ?>"><?= $name ?></span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewAppointment(<?= $appointment['id'] ?>)" title="عرض">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editAppointment(<?= $appointment['id'] ?>)" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <?php if ($appointment['status'] === 'scheduled'): ?>
                                            <button class="btn btn-sm btn-outline-success" onclick="confirmAppointment(<?= $appointment['id'] ?>)" title="تأكيد">
                                                <i class="bi bi-check"></i>
                                            </button>
                                        <?php endif; ?>
                                        <?php if (in_array($appointment['status'], ['scheduled', 'confirmed'])): ?>
                                            <button class="btn btn-sm btn-outline-info" onclick="completeAppointment(<?= $appointment['id'] ?>)" title="إكمال">
                                                <i class="bi bi-check-circle"></i>
                                            </button>
                                        <?php endif; ?>
                                        <button class="btn btn-sm btn-outline-danger" onclick="cancelAppointment(<?= $appointment['id'] ?>)" title="إلغاء">
                                            <i class="bi bi-x"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- التنقل بين الصفحات -->
    <?php if (isset($totalPages) && $totalPages > 1): ?>
        <div class="card-footer">
            <nav aria-label="تنقل الصفحات">
                <ul class="pagination justify-content-center mb-0">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page - 1 ?>&<?= http_build_query($filters) ?>">
                                <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                            <a class="page-link" href="?page=<?= $i ?>&<?= http_build_query($filters) ?>"><?= $i ?></a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $page + 1 ?>&<?= http_build_query($filters) ?>">
                                <i class="bi bi-chevron-left"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    <?php endif; ?>
</div>

<!-- Modal إكمال الموعد -->
<div class="modal fade" id="completeAppointmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إكمال الموعد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="completeAppointmentForm">
                    <input type="hidden" id="completeAppointmentId" name="appointment_id">
                    <div class="mb-3">
                        <label for="completionNotes" class="form-label">ملاحظات الإكمال</label>
                        <textarea class="form-control" id="completionNotes" name="notes" rows="3" placeholder="أضف ملاحظات حول الموعد..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="submitCompleteAppointment()">إكمال الموعد</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal إلغاء الموعد -->
<div class="modal fade" id="cancelAppointmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إلغاء الموعد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="cancelAppointmentForm">
                    <input type="hidden" id="cancelAppointmentId" name="appointment_id">
                    <div class="mb-3">
                        <label for="cancelReason" class="form-label">سبب الإلغاء</label>
                        <textarea class="form-control" id="cancelReason" name="reason" rows="3" placeholder="أضف سبب إلغاء الموعد..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" onclick="submitCancelAppointment()">إلغاء الموعد</button>
            </div>
        </div>
    </div>
</div>

<script>
// عرض تفاصيل الموعد
function viewAppointment(appointmentId) {
    // تنفيذ عرض تفاصيل الموعد
    console.log('عرض الموعد:', appointmentId);
}

// تعديل الموعد
function editAppointment(appointmentId) {
    window.location.href = `<?= App::url('admin/appointments/edit/') ?>${appointmentId}`;
}

// تأكيد الموعد
function confirmAppointment(appointmentId) {
    if (confirm('هل أنت متأكد من تأكيد هذا الموعد؟')) {
        showLoading();
        fetch(`<?= App::url('admin/appointments/confirm/') ?>${appointmentId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showAlert('تم تأكيد الموعد بنجاح', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert(data.message || 'حدث خطأ', 'danger');
            }
        })
        .catch(error => {
            hideLoading();
            showAlert('حدث خطأ في الاتصال', 'danger');
        });
    }
}

// إكمال الموعد
function completeAppointment(appointmentId) {
    document.getElementById('completeAppointmentId').value = appointmentId;
    document.getElementById('completionNotes').value = '';
    new bootstrap.Modal(document.getElementById('completeAppointmentModal')).show();
}

// إلغاء الموعد
function cancelAppointment(appointmentId) {
    document.getElementById('cancelAppointmentId').value = appointmentId;
    document.getElementById('cancelReason').value = '';
    new bootstrap.Modal(document.getElementById('cancelAppointmentModal')).show();
}

// إرسال إكمال الموعد
function submitCompleteAppointment() {
    const appointmentId = document.getElementById('completeAppointmentId').value;
    const notes = document.getElementById('completionNotes').value;
    
    showLoading();
    fetch(`<?= App::url('admin/appointments/complete/') ?>${appointmentId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notes: notes })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showAlert('تم إكمال الموعد بنجاح', 'success');
            bootstrap.Modal.getInstance(document.getElementById('completeAppointmentModal')).hide();
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert(data.message || 'حدث خطأ', 'danger');
        }
    })
    .catch(error => {
        hideLoading();
        showAlert('حدث خطأ في الاتصال', 'danger');
    });
}

// إرسال إلغاء الموعد
function submitCancelAppointment() {
    const appointmentId = document.getElementById('cancelAppointmentId').value;
    const reason = document.getElementById('cancelReason').value;
    
    showLoading();
    fetch(`<?= App::url('admin/appointments/cancel/') ?>${appointmentId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason: reason })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showAlert('تم إلغاء الموعد بنجاح', 'success');
            bootstrap.Modal.getInstance(document.getElementById('cancelAppointmentModal')).hide();
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert(data.message || 'حدث خطأ', 'danger');
        }
    })
    .catch(error => {
        hideLoading();
        showAlert('حدث خطأ في الاتصال', 'danger');
    });
}

// تصدير المواعيد
function exportAppointments() {
    const currentUrl = new URL(window.location.href);
    const exportUrl = '<?= App::url('admin/appointments/export') ?>' + currentUrl.search;
    window.open(exportUrl, '_blank');
}

// تحديث الجدول
function refreshTable() {
    location.reload();
}

// عرض التنبيهات
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
