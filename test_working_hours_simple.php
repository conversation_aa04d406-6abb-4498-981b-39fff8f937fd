<?php
/**
 * اختبار مبسط لصفحة ساعات العمل
 */

require_once 'config.php';

echo "<h1>اختبار صفحة ساعات العمل للصيدلي</h1>";

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // التحقق من وجود جدول working_hours
    $tableExists = $pdo->query("SHOW TABLES LIKE 'working_hours'")->rowCount() > 0;
    
    if ($tableExists) {
        echo "✅ جدول working_hours موجود<br>";
        
        // عرض عدد السجلات
        $count = $pdo->query("SELECT COUNT(*) FROM working_hours")->fetchColumn();
        echo "✅ عدد السجلات في الجدول: " . $count . "<br>";
        
        // عرض ساعات عمل الصيدلي رقم 2
        $stmt = $pdo->prepare("SELECT * FROM working_hours WHERE user_id = ? AND user_type = 'pharmacist' ORDER BY day");
        $stmt->execute([2]);
        $hours = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($hours)) {
            echo "<h3>ساعات عمل الصيدلي رقم 2:</h3>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>اليوم</th><th>وقت البداية</th><th>وقت النهاية</th><th>يوم عمل</th></tr>";
            
            $daysNames = [
                'sunday' => 'الأحد',
                'monday' => 'الاثنين',
                'tuesday' => 'الثلاثاء',
                'wednesday' => 'الأربعاء',
                'thursday' => 'الخميس',
                'friday' => 'الجمعة',
                'saturday' => 'السبت'
            ];
            
            foreach ($hours as $hour) {
                echo "<tr>";
                echo "<td>" . $daysNames[$hour['day']] . "</td>";
                echo "<td>" . $hour['start_time'] . "</td>";
                echo "<td>" . $hour['end_time'] . "</td>";
                echo "<td>" . ($hour['is_working'] ? 'نعم' : 'لا') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // اختبار تحديث ساعات العمل
        echo "<h3>اختبار تحديث ساعات العمل:</h3>";
        
        // تحديث ساعات العمل ليوم الأحد
        $updateStmt = $pdo->prepare("UPDATE working_hours SET start_time = ?, end_time = ?, is_working = ? WHERE user_id = ? AND user_type = 'pharmacist' AND day = 'sunday'");
        $updateStmt->execute(['09:00', '18:00', 1, 2]);
        
        echo "✅ تم تحديث ساعات العمل ليوم الأحد<br>";
        
        // عرض الساعات المحدثة
        $stmt = $pdo->prepare("SELECT * FROM working_hours WHERE user_id = ? AND user_type = 'pharmacist' AND day = 'sunday'");
        $stmt->execute([2]);
        $updatedHour = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($updatedHour) {
            echo "✅ الساعات المحدثة ليوم الأحد: " . $updatedHour['start_time'] . " - " . $updatedHour['end_time'] . "<br>";
        }
        
    } else {
        echo "❌ جدول working_hours غير موجود<br>";
    }
    
    echo "<h3>رابط صفحة ساعات العمل:</h3>";
    echo "<a href='index.php?url=pharmacist/working-hours' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>فتح صفحة ساعات العمل</a>";
    
    echo "<h2>✅ انتهى الاختبار بنجاح</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في الاختبار</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
}
?> 