<?php
$pageTitle = 'التحليلات والإحصائيات';
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => App::url('admin/dashboard')],
    ['title' => 'التحليلات', 'url' => '#']
];

// بدء تخزين المحتوى
ob_start();
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">التحليلات والإحصائيات</h1>
            <p class="text-muted">تحليل شامل لأداء النظام والمستخدمين</p>
        </div>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-outline-primary" onclick="exportAnalytics()">
                <i class="bi bi-download"></i> تصدير التقرير
            </button>
            <button type="button" class="btn btn-primary" onclick="refreshAnalytics()">
                <i class="bi bi-arrow-clockwise"></i> تحديث البيانات
            </button>
        </div>
    </div>

    <!-- فلاتر التاريخ -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label for="dateRange" class="form-label">الفترة الزمنية</label>
                    <select id="dateRange" class="form-select" onchange="updateDateInputs()">
                        <option value="7">آخر 7 أيام</option>
                        <option value="30" selected>آخر 30 يوم</option>
                        <option value="90">آخر 3 أشهر</option>
                        <option value="365">آخر سنة</option>
                        <option value="custom">فترة مخصصة</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="startDate" class="form-label">من تاريخ</label>
                    <input type="date" id="startDate" class="form-control" onchange="loadAnalytics()">
                </div>
                <div class="col-md-3">
                    <label for="endDate" class="form-label">إلى تاريخ</label>
                    <input type="date" id="endDate" class="form-control" onchange="loadAnalytics()">
                </div>
                <div class="col-md-3">
                    <label for="analyticsType" class="form-label">نوع التحليل</label>
                    <select id="analyticsType" class="form-select" onchange="loadAnalytics()">
                        <option value="overview" selected>نظرة عامة</option>
                        <option value="users">المستخدمين</option>
                        <option value="appointments">المواعيد</option>
                        <option value="prescriptions">الوصفات الطبية</option>
                        <option value="performance">الأداء</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- البطاقات الإحصائية -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي المستخدمين
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalUsers">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-people fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                المواعيد النشطة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="activeAppointments">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-calendar-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                الوصفات الطبية
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="totalPrescriptions">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-prescription fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                معدل النمو
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="growthRate">0%</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-graph-up fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row mb-4">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">نشاط النظام</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown">
                            <i class="bi bi-three-dots-vertical"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in">
                            <a class="dropdown-item" href="#" onclick="exportChart('activity')">تصدير الرسم البياني</a>
                            <a class="dropdown-item" href="#" onclick="printChart('activity')">طباعة</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="activityChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">توزيع المستخدمين</h6>
                </div>
                <div class="card-body">
                    <canvas id="usersChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- جداول البيانات -->
    <div class="row">
        <div class="col-xl-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">أفضل الأطباء</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="topDoctorsTable">
                            <thead>
                                <tr>
                                    <th>الطبيب</th>
                                    <th>عدد المواعيد</th>
                                    <th>التقييم</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="3" class="text-center">جاري التحميل...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">أكثر الأمراض شيوعاً</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="commonDiseasesTable">
                            <thead>
                                <tr>
                                    <th>المرض</th>
                                    <th>عدد الحالات</th>
                                    <th>النسبة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td colspan="3" class="text-center">جاري التحميل...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- مؤشرات الأداء -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">مؤشرات الأداء الرئيسية (KPIs)</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="kpi-item">
                                <div class="kpi-value" id="avgAppointmentTime">0</div>
                                <div class="kpi-label">متوسط وقت الموعد (دقيقة)</div>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="kpi-item">
                                <div class="kpi-value" id="patientSatisfaction">0%</div>
                                <div class="kpi-label">رضا المرضى</div>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="kpi-item">
                                <div class="kpi-value" id="systemUptime">0%</div>
                                <div class="kpi-label">وقت تشغيل النظام</div>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="kpi-item">
                                <div class="kpi-value" id="responseTime">0ms</div>
                                <div class="kpi-label">وقت الاستجابة</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال تفاصيل التحليل -->
<div class="modal fade" id="analyticsDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="analyticsDetailsTitle">تفاصيل التحليل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="analyticsDetailsContent">
                <!-- سيتم تحميل المحتوى ديناميكياً -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="exportDetails()">تصدير</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let activityChart, usersChart;
let currentAnalyticsData = {};

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateDateInputs();
    loadAnalytics();
    setupEventListeners();
});

// إعداد مستمعي الأحداث
function setupEventListeners() {
    const dateRange = document.getElementById('dateRange');
    if (dateRange) {
        dateRange.addEventListener('change', updateDateInputs);
    }
}

// تحديث حقول التاريخ
function updateDateInputs() {
    const dateRange = document.getElementById('dateRange');
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    
    if (!dateRange || !startDate || !endDate) return;
    
    const days = parseInt(dateRange.value);
    const end = new Date();
    const start = new Date();
    
    if (dateRange.value === 'custom') {
        startDate.disabled = false;
        endDate.disabled = false;
        return;
    }
    
    startDate.disabled = true;
    endDate.disabled = true;
    
    start.setDate(end.getDate() - days);
    
    startDate.value = start.toISOString().split('T')[0];
    endDate.value = end.toISOString().split('T')[0];
    
    loadAnalytics();
}

// تحميل البيانات التحليلية
function loadAnalytics() {
    const startDate = document.getElementById('startDate')?.value;
    const endDate = document.getElementById('endDate')?.value;
    const analyticsType = document.getElementById('analyticsType')?.value;
    
    if (!startDate || !endDate) return;
    
    // عرض مؤشر التحميل
    showLoading();
    
    fetch(`<?= App::url('admin/analytics') ?>?action=data&start_date=${startDate}&end_date=${endDate}&type=${analyticsType}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return response.json();
        } else {
            // إذا لم يكن JSON، نقرأ النص ونحاول تحليله
            return response.text().then(text => {
                console.log('Response text:', text);
                throw new Error('Server returned HTML instead of JSON');
            });
        }
    })
    .then(data => {
        console.log('Analytics data:', data);
        if (data && data.success) {
            currentAnalyticsData = data;
            updateDashboard(data);
            renderCharts(data);
            renderTables(data);
        } else {
            console.error('Server error:', data);
            showAlert(data?.message || 'خطأ في تحميل البيانات التحليلية', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال بالخادم: ' + error.message, 'error');
    })
    .finally(() => {
        hideLoading();
    });
}

// تحديث لوحة المعلومات
function updateDashboard(data) {
    const stats = data.stats || {};
    
    // تحديث البطاقات الإحصائية
    updateElement('totalUsers', stats.totalUsers || 0);
    updateElement('activeAppointments', stats.activeAppointments || 0);
    updateElement('totalPrescriptions', stats.totalPrescriptions || 0);
    updateElement('growthRate', (stats.growthRate || 0) + '%');
    
    // تحديث مؤشرات الأداء
    updateElement('avgAppointmentTime', stats.avgAppointmentTime || 0);
    updateElement('patientSatisfaction', (stats.patientSatisfaction || 0) + '%');
    updateElement('systemUptime', (stats.systemUptime || 0) + '%');
    updateElement('responseTime', (stats.responseTime || 0) + 'ms');
}

// تحديث عنصر في الصفحة
function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

// رسم الرسوم البيانية
function renderCharts(data) {
    renderActivityChart(data.activityData || []);
    renderUsersChart(data.usersData || {});
}

// رسم مخطط النشاط
function renderActivityChart(data) {
    const ctx = document.getElementById('activityChart');
    if (!ctx) return;
    
    if (activityChart) {
        activityChart.destroy();
    }
    
    const labels = data.map(item => item.date);
    const values = data.map(item => item.count);
    
    activityChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'النشاط اليومي',
                data: values,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: true,
                    text: 'نشاط النظام اليومي'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// رسم مخطط المستخدمين
function renderUsersChart(data) {
    const ctx = document.getElementById('usersChart');
    if (!ctx) return;
    
    if (usersChart) {
        usersChart.destroy();
    }
    
    const labels = Object.keys(data);
    const values = Object.values(data);
    const colors = [
        'rgba(255, 99, 132, 0.8)',
        'rgba(54, 162, 235, 0.8)',
        'rgba(255, 205, 86, 0.8)',
        'rgba(75, 192, 192, 0.8)'
    ];
    
    usersChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: values,
                backgroundColor: colors,
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                },
                title: {
                    display: true,
                    text: 'توزيع المستخدمين'
                }
            }
        }
    });
}

// عرض الجداول
function renderTables(data) {
    renderTopDoctorsTable(data.topDoctors || []);
    renderCommonDiseasesTable(data.commonDiseases || []);
}

// عرض جدول أفضل الأطباء
function renderTopDoctorsTable(doctors) {
    const tbody = document.querySelector('#topDoctorsTable tbody');
    if (!tbody) return;
    
    if (doctors.length === 0) {
        tbody.innerHTML = '<tr><td colspan="3" class="text-center">لا توجد بيانات</td></tr>';
        return;
    }
    
    tbody.innerHTML = doctors.map(doctor => `
        <tr>
            <td>${doctor.name || 'غير محدد'}</td>
            <td>${doctor.appointments || 0}</td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="progress flex-grow-1 me-2" style="height: 6px;">
                        <div class="progress-bar" style="width: ${doctor.rating || 0}%"></div>
                    </div>
                    <span class="small">${doctor.rating || 0}%</span>
                </div>
            </td>
        </tr>
    `).join('');
}

// عرض جدول الأمراض الشائعة
function renderCommonDiseasesTable(diseases) {
    const tbody = document.querySelector('#commonDiseasesTable tbody');
    if (!tbody) return;
    
    if (diseases.length === 0) {
        tbody.innerHTML = '<tr><td colspan="3" class="text-center">لا توجد بيانات</td></tr>';
        return;
    }
    
    tbody.innerHTML = diseases.map(disease => `
        <tr>
            <td>${disease.name || 'غير محدد'}</td>
            <td>${disease.count || 0}</td>
            <td>${disease.percentage || 0}%</td>
        </tr>
    `).join('');
}

// تحديث البيانات
function refreshAnalytics() {
    loadAnalytics();
}

// تصدير التحليلات
function exportAnalytics() {
    const startDate = document.getElementById('startDate')?.value;
    const endDate = document.getElementById('endDate')?.value;
    const analyticsType = document.getElementById('analyticsType')?.value;
    
    if (!startDate || !endDate) {
        showAlert('يرجى تحديد الفترة الزمنية', 'warning');
        return;
    }
    
    const url = `<?= App::url('admin/analytics') ?>?action=export&start_date=${startDate}&end_date=${endDate}&type=${analyticsType}`;
    window.open(url, '_blank');
}

// تصدير الرسم البياني
function exportChart(chartType) {
    let chart;
    switch(chartType) {
        case 'activity':
            chart = activityChart;
            break;
        case 'users':
            chart = usersChart;
            break;
        default:
            return;
    }
    
    if (chart) {
        const link = document.createElement('a');
        link.download = `${chartType}_chart.png`;
        link.href = chart.toBase64Image();
        link.click();
    }
}

// طباعة الرسم البياني
function printChart(chartType) {
    let chart;
    switch(chartType) {
        case 'activity':
            chart = activityChart;
            break;
        case 'users':
            chart = usersChart;
            break;
        default:
            return;
    }
    
    if (chart) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>${chartType} Chart</title>
                </head>
                <body>
                    <img src="${chart.toBase64Image()}" style="max-width: 100%;">
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
    }
}

// عرض تفاصيل التحليل
function showAnalyticsDetails(type, data) {
    const modal = document.getElementById('analyticsDetailsModal');
    const title = document.getElementById('analyticsDetailsTitle');
    const content = document.getElementById('analyticsDetailsContent');
    
    if (!modal || !title || !content) return;
    
    title.textContent = `تفاصيل ${getAnalyticsTypeName(type)}`;
    
    let detailsHtml = '';
    switch(type) {
        case 'users':
            detailsHtml = renderUsersDetails(data);
            break;
        case 'appointments':
            detailsHtml = renderAppointmentsDetails(data);
            break;
        case 'prescriptions':
            detailsHtml = renderPrescriptionsDetails(data);
            break;
        default:
            detailsHtml = '<p>لا توجد تفاصيل متاحة</p>';
    }
    
    content.innerHTML = detailsHtml;
    
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
}

// عرض تفاصيل المستخدمين
function renderUsersDetails(data) {
    return `
        <div class="row">
            <div class="col-md-6">
                <h6>إحصائيات التسجيل</h6>
                <ul class="list-unstyled">
                    <li>إجمالي المستخدمين: ${data.total || 0}</li>
                    <li>المستخدمين النشطين: ${data.active || 0}</li>
                    <li>المستخدمين الجدد: ${data.new || 0}</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>توزيع المستخدمين</h6>
                <ul class="list-unstyled">
                    <li>المرضى: ${data.patients || 0}</li>
                    <li>الأطباء: ${data.doctors || 0}</li>
                    <li>الصيادلة: ${data.pharmacists || 0}</li>
                </ul>
            </div>
        </div>
    `;
}

// عرض تفاصيل المواعيد
function renderAppointmentsDetails(data) {
    return `
        <div class="row">
            <div class="col-md-6">
                <h6>حالة المواعيد</h6>
                <ul class="list-unstyled">
                    <li>المواعيد المكتملة: ${data.completed || 0}</li>
                    <li>المواعيد المعلقة: ${data.pending || 0}</li>
                    <li>المواعيد الملغية: ${data.cancelled || 0}</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>إحصائيات إضافية</h6>
                <ul class="list-unstyled">
                    <li>متوسط وقت الموعد: ${data.avgTime || 0} دقيقة</li>
                    <li>معدل الحضور: ${data.attendanceRate || 0}%</li>
                </ul>
            </div>
        </div>
    `;
}

// عرض تفاصيل الوصفات الطبية
function renderPrescriptionsDetails(data) {
    return `
        <div class="row">
            <div class="col-md-6">
                <h6>إحصائيات الوصفات</h6>
                <ul class="list-unstyled">
                    <li>إجمالي الوصفات: ${data.total || 0}</li>
                    <li>الوصفات المكتملة: ${data.completed || 0}</li>
                    <li>الوصفات المعلقة: ${data.pending || 0}</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>أكثر الأدوية استخداماً</h6>
                <ul class="list-unstyled">
                    ${(data.topMedicines || []).map(medicine => 
                        `<li>${medicine.name}: ${medicine.count} مرة</li>`
                    ).join('')}
                </ul>
            </div>
        </div>
    `;
}

// الحصول على اسم نوع التحليل
function getAnalyticsTypeName(type) {
    const names = {
        'users': 'المستخدمين',
        'appointments': 'المواعيد',
        'prescriptions': 'الوصفات الطبية',
        'performance': 'الأداء'
    };
    return names[type] || type;
}

// تصدير التفاصيل
function exportDetails() {
    const startDate = document.getElementById('startDate')?.value;
    const endDate = document.getElementById('endDate')?.value;
    const analyticsType = document.getElementById('analyticsType')?.value;
    
    if (!startDate || !endDate) {
        showAlert('يرجى تحديد الفترة الزمنية', 'warning');
        return;
    }
    
    const url = `<?= App::url('admin/analytics') ?>?action=export_details&start_date=${startDate}&end_date=${endDate}&type=${analyticsType}`;
    window.open(url, '_blank');
}

// عرض رسالة تحميل
function showLoading() {
    // يمكن إضافة مؤشر تحميل هنا
}

// إخفاء رسالة التحميل
function hideLoading() {
    // يمكن إخفاء مؤشر التحميل هنا
}

// عرض رسالة تنبيه
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
        
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}
</script>

<style>
.kpi-item {
    padding: 20px;
    border-radius: 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-bottom: 20px;
}

.kpi-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.kpi-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.border-left-primary {
    border-left: 4px solid #4e73df !important;
}

.border-left-success {
    border-left: 4px solid #1cc88a !important;
}

.border-left-info {
    border-left: 4px solid #36b9cc !important;
}

.border-left-warning {
    border-left: 4px solid #f6c23e !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-xs {
    font-size: 0.7rem;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-uppercase {
    text-transform: uppercase !important;
}
</style>

<?php
// إنهاء تخزين المحتوى
$content = ob_get_clean();

// تضمين قالب المدير
include __DIR__ . '/../layouts/admin.php';
?> 