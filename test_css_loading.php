<?php
// اختبار تحميل ملفات CSS و JavaScript
echo "<h1>اختبار تحميل ملفات CSS و JavaScript</h1>";

// تعريف الثوابت المطلوبة
define('DB_HOST', 'localhost');
define('DB_NAME', 'healthkey');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');
define('APP_DEBUG', true);
define('APP_URL', 'http://localhost/HealthKey');

// محاكاة جلسة المستخدم
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'pharmacist';
$_SESSION['user'] = [
    'id' => 1,
    'first_name' => 'أحمد',
    'last_name' => 'محمد',
    'email' => '<EMAIL>',
    'user_type' => 'pharmacist'
];

try {
    require_once 'app/core/Database.php';
    require_once 'app/core/Controller.php';
    require_once 'app/core/App.php';
    require_once 'app/controllers/PharmacistController.php';
    
    echo "<h2>✅ تم تحميل الملفات بنجاح</h2>";
    
    // اختبار وجود ملفات CSS
    echo "<h3>اختبار ملفات CSS:</h3>";
    $cssFiles = [
        'public/css/style.css',
        'public/css/sidebar.css',
        'public/css/admin.css'
    ];
    
    foreach ($cssFiles as $cssFile) {
        if (file_exists($cssFile)) {
            $size = filesize($cssFile);
            echo "<p style='color: green;'>✅ $cssFile موجود (الحجم: " . number_format($size / 1024, 2) . " KB)</p>";
        } else {
            echo "<p style='color: red;'>❌ $cssFile غير موجود</p>";
        }
    }
    
    // اختبار وجود ملفات JavaScript
    echo "<h3>اختبار ملفات JavaScript:</h3>";
    $jsFiles = [
        'public/js/main.js',
        'public/js/sidebar.js'
    ];
    
    foreach ($jsFiles as $jsFile) {
        if (file_exists($jsFile)) {
            $size = filesize($jsFile);
            echo "<p style='color: green;'>✅ $jsFile موجود (الحجم: " . number_format($size / 1024, 2) . " KB)</p>";
        } else {
            echo "<p style='color: red;'>❌ $jsFile غير موجود</p>";
        }
    }
    
    // اختبار المسارات في ملف التخطيط
    echo "<h3>اختبار المسارات في ملف التخطيط:</h3>";
    if (file_exists('views/layouts/pharmacist.php')) {
        $layoutContent = file_get_contents('views/layouts/pharmacist.php');
        
        if (strpos($layoutContent, '/HealthKey/public/css/style.css') !== false) {
            echo "<p style='color: green;'>✅ مسار style.css صحيح</p>";
        } else {
            echo "<p style='color: red;'>❌ مسار style.css غير صحيح</p>";
        }
        
        if (strpos($layoutContent, '/HealthKey/public/css/sidebar.css') !== false) {
            echo "<p style='color: green;'>✅ مسار sidebar.css صحيح</p>";
        } else {
            echo "<p style='color: red;'>❌ مسار sidebar.css غير صحيح</p>";
        }
        
        if (strpos($layoutContent, '/HealthKey/public/css/admin.css') !== false) {
            echo "<p style='color: green;'>✅ مسار admin.css صحيح</p>";
        } else {
            echo "<p style='color: red;'>❌ مسار admin.css غير صحيح</p>";
        }
        
        if (strpos($layoutContent, '/HealthKey/public/js/main.js') !== false) {
            echo "<p style='color: green;'>✅ مسار main.js صحيح</p>";
        } else {
            echo "<p style='color: red;'>❌ مسار main.js غير صحيح</p>";
        }
        
        if (strpos($layoutContent, '/HealthKey/public/js/sidebar.js') !== false) {
            echo "<p style='color: green;'>✅ مسار sidebar.js صحيح</p>";
        } else {
            echo "<p style='color: red;'>❌ مسار sidebar.js غير صحيح</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ ملف التخطيط غير موجود</p>";
    }
    
    echo "<h2>🎉 تم إصلاح مسارات ملفات CSS و JavaScript بنجاح!</h2>";
    echo "<p style='color: #28a745; font-weight: bold;'>جميع المسارات تم تحديثها لتعمل مع المسار الصحيح.</p>";
    
    // روابط للاختبار
    echo "<h3>روابط للاختبار:</h3>";
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>🎨 الصفحات مع التصميم المحسن:</h4>";
    echo "<ul>";
    echo "<li><a href='http://localhost:8000/index.php?url=pharmacist/inventory' target='_blank' style='color: #007bff; text-decoration: none;'>🏥 صفحة إدارة المخزون مع التصميم</a></li>";
    echo "<li><a href='http://localhost:8000/index.php?url=pharmacist/add-inventory' target='_blank' style='color: #007bff; text-decoration: none;'>➕ صفحة إضافة دواء جديد</a></li>";
    echo "<li><a href='http://localhost:8000/index.php?url=pharmacist/suppliers' target='_blank' style='color: #007bff; text-decoration: none;'>🏢 صفحة إدارة الموردين</a></li>";
    echo "</ul>";
    
    echo "<h4>📁 ملفات CSS و JavaScript:</h4>";
    echo "<ul>";
    echo "<li><a href='http://localhost:8000/HealthKey/public/css/style.css' target='_blank' style='color: #007bff; text-decoration: none;'>🎨 ملف style.css</a></li>";
    echo "<li><a href='http://localhost:8000/HealthKey/public/css/sidebar.css' target='_blank' style='color: #007bff; text-decoration: none;'>🎨 ملف sidebar.css</a></li>";
    echo "<li><a href='http://localhost:8000/HealthKey/public/css/admin.css' target='_blank' style='color: #007bff; text-decoration: none;'>🎨 ملف admin.css</a></li>";
    echo "<li><a href='http://localhost:8000/HealthKey/public/js/main.js' target='_blank' style='color: #007bff; text-decoration: none;'>⚡ ملف main.js</a></li>";
    echo "<li><a href='http://localhost:8000/HealthKey/public/js/sidebar.js' target='_blank' style='color: #007bff; text-decoration: none;'>⚡ ملف sidebar.js</a></li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>🔧 الإصلاحات المطبقة:</h3>";
    echo "<div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>✅ تم إصلاح جميع المسارات:</h4>";
    echo "<ul>";
    echo "<li>تم تغيير مسارات CSS من App::url() إلى مسارات مباشرة</li>";
    echo "<li>تم تغيير مسارات JavaScript من App::url() إلى مسارات مباشرة</li>";
    echo "<li>جميع المسارات تستخدم الآن /HealthKey/ كمسار أساسي</li>";
    echo "<li>تم التأكد من وجود جميع الملفات المطلوبة</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في الاختبار:</h2>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "<p>الملف: " . $e->getFile() . "</p>";
    echo "<p>السطر: " . $e->getLine() . "</p>";
}
?> 