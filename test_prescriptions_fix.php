<?php
/**
 * ملف اختبار بسيط لفحص صفحة الوصفات الطبية
 * يمكن الوصول إليه عبر: http://localhost/HealthKey/test_prescriptions_fix.php
 */

// تضمين ملفات النظام
require_once 'app/core/App.php';
require_once 'app/helpers/SessionHelper.php';
require_once 'app/models/User.php';
require_once 'app/models/Prescription.php';

// محاكاة تسجيل دخول المدير
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'admin';
$_SESSION['user_name'] = 'مدير النظام';

// اختبار النماذج
$userModel = new User();
$prescriptionModel = new Prescription();

// اختبار الحصول على الأطباء
$doctors = $userModel->getActiveDoctors();
echo "<h3>اختبار الحصول على الأطباء:</h3>";
echo "<pre>";
print_r($doctors);
echo "</pre>";

// اختبار الحصول على المرضى
$patients = $userModel->getActivePatients();
echo "<h3>اختبار الحصول على المرضى:</h3>";
echo "<pre>";
print_r($patients);
echo "</pre>";

// اختبار البحث في الوصفات
$prescriptions = $prescriptionModel->search('', []);
echo "<h3>اختبار البحث في الوصفات:</h3>";
echo "<pre>";
print_r($prescriptions);
echo "</pre>";

// اختبار إحصائيات الوصفات
$stats = $prescriptionModel->getStats();
echo "<h3>اختبار إحصائيات الوصفات:</h3>";
echo "<pre>";
print_r($stats);
echo "</pre>";

echo "<h3>نتيجة الاختبار:</h3>";
echo "<ul>";
echo "<li>عدد الأطباء: " . (is_array($doctors) ? count($doctors) : 'خطأ') . "</li>";
echo "<li>عدد المرضى: " . (is_array($patients) ? count($patients) : 'خطأ') . "</li>";
echo "<li>عدد الوصفات: " . (is_array($prescriptions) ? count($prescriptions) : 'خطأ') . "</li>";
echo "<li>الإحصائيات: " . (is_array($stats) ? 'متوفرة' : 'خطأ') . "</li>";
echo "</ul>";

echo "<h3>روابط الاختبار:</h3>";
echo "<ul>";
echo "<li><a href='admin/prescriptions'>صفحة الوصفات الطبية</a></li>";
echo "<li><a href='test_admin_prescriptions.php'>صفحة الاختبار</a></li>";
echo "</ul>";
?> 