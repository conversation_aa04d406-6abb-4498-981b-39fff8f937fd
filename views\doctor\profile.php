<?php
/**
 * صفحة الملف الشخصي للطبيب
 */
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-user-md text-primary"></i>
                الملف الشخصي
            </h1>
            <p class="text-muted">تعديل المعلومات الشخصية والبيانات المهنية</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary" onclick="window.print()">
                <i class="fas fa-print"></i>
                طباعة الملف
            </button>
            <button class="btn btn-primary" onclick="saveProfile()">
                <i class="fas fa-save"></i>
                حفظ التغييرات
            </button>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ -->
    <?php if (SessionHelper::hasFlash()): ?>
        <?php 
        $flashMessages = SessionHelper::getAllFlash();
        foreach ($flashMessages as $key => $flashMessage): 
        ?>
        <div class="alert alert-<?= $flashMessage['type'] ?> alert-dismissible fade show" role="alert">
            <?= $flashMessage['message'] ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endforeach; ?>
    <?php endif; ?>

    <div class="row">
        <!-- المعلومات الشخصية -->
        <div class="col-xl-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user"></i>
                        المعلومات الشخصية
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" id="profileForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="first_name" class="form-label">الاسم الأول *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="<?= htmlspecialchars($doctor['first_name'] ?? '') ?>" required>
                                <?php if (SessionHelper::getValidationError('first_name')): ?>
                                    <div class="text-danger small"><?= SessionHelper::getValidationError('first_name') ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="last_name" class="form-label">الاسم الأخير *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="<?= htmlspecialchars($doctor['last_name'] ?? '') ?>" required>
                                <?php if (SessionHelper::getValidationError('last_name')): ?>
                                    <div class="text-danger small"><?= SessionHelper::getValidationError('last_name') ?></div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?= htmlspecialchars($doctor['email'] ?? '') ?>" required>
                                <?php if (SessionHelper::getValidationError('email')): ?>
                                    <div class="text-danger small"><?= SessionHelper::getValidationError('email') ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف *</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?= htmlspecialchars($doctor['phone'] ?? '') ?>" required>
                                <?php if (SessionHelper::getValidationError('phone')): ?>
                                    <div class="text-danger small"><?= SessionHelper::getValidationError('phone') ?></div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <textarea class="form-control" id="address" name="address" rows="3"><?= htmlspecialchars($doctor['address'] ?? '') ?></textarea>
                            <?php if (SessionHelper::getValidationError('address')): ?>
                                <div class="text-danger small"><?= SessionHelper::getValidationError('address') ?></div>
                            <?php endif; ?>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="date_of_birth" class="form-label">تاريخ الميلاد</label>
                                <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" 
                                       value="<?= htmlspecialchars($doctor['date_of_birth'] ?? '') ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="gender" class="form-label">الجنس</label>
                                <select class="form-select" id="gender" name="gender">
                                    <option value="">اختر الجنس</option>
                                    <option value="male" <?= ($doctor['gender'] ?? '') === 'male' ? 'selected' : '' ?>>ذكر</option>
                                    <option value="female" <?= ($doctor['gender'] ?? '') === 'female' ? 'selected' : '' ?>>أنثى</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- المعلومات المهنية -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-stethoscope"></i>
                        المعلومات المهنية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="specialization" class="form-label">التخصص *</label>
                            <input type="text" class="form-control" id="specialization" name="specialization" 
                                   value="<?= htmlspecialchars($doctor['specialization'] ?? '') ?>" required>
                            <?php if (SessionHelper::getValidationError('specialization')): ?>
                                <div class="text-danger small"><?= SessionHelper::getValidationError('specialization') ?></div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="license_number" class="form-label">رقم الترخيص *</label>
                            <input type="text" class="form-control" id="license_number" name="license_number" 
                                   value="<?= htmlspecialchars($doctor['doctor_license'] ?? '') ?>" required>
                            <?php if (SessionHelper::getValidationError('license_number')): ?>
                                <div class="text-danger small"><?= SessionHelper::getValidationError('license_number') ?></div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="experience_years" class="form-label">سنوات الخبرة</label>
                            <input type="number" class="form-control" id="experience_years" name="experience_years" 
                                   value="<?= htmlspecialchars($doctor['experience_years'] ?? '') ?>" min="0" max="50">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="qualifications" class="form-label">المؤهلات العلمية</label>
                            <input type="text" class="form-control" id="qualifications" name="qualifications" 
                                   value="<?= htmlspecialchars($doctor['qualifications'] ?? '') ?>">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="bio" class="form-label">نبذة مختصرة</label>
                        <textarea class="form-control" id="bio" name="bio" rows="4"><?= htmlspecialchars($doctor['bio'] ?? '') ?></textarea>
                    </div>
                </div>
            </div>

            <!-- تغيير كلمة المرور -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-lock"></i>
                        تغيير كلمة المرور
                    </h6>
                </div>
                <div class="card-body">
                    <form method="POST" id="passwordForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                                <input type="password" class="form-control" id="current_password" name="current_password">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                                <input type="password" class="form-control" id="new_password" name="new_password">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password">
                            </div>
                            <div class="col-md-6 d-flex align-items-end">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-key"></i>
                                    تغيير كلمة المرور
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- الجانب الأيمن -->
        <div class="col-xl-4">
            <!-- الصورة الشخصية -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-camera"></i>
                        الصورة الشخصية
                    </h6>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <img src="<?= $doctor['profile_image'] ?? 'public/img/default-avatar.png' ?>" 
                             alt="الصورة الشخصية" class="rounded-circle" style="width: 150px; height: 150px; object-fit: cover;">
                    </div>
                    <div class="mb-3">
                        <input type="file" class="form-control" id="profile_image" name="profile_image" accept="image/*">
                    </div>
                    <button type="button" class="btn btn-outline-primary" onclick="uploadImage()">
                        <i class="fas fa-upload"></i>
                        رفع صورة جديدة
                    </button>
                </div>
            </div>

            <!-- الإحصائيات -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar"></i>
                        إحصائيات سريعة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>إجمالي المواعيد</span>
                            <span class="badge bg-primary"><?= number_format($stats['appointments']['total'] ?? 0) ?></span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>الوصفات الطبية</span>
                            <span class="badge bg-success"><?= number_format($stats['prescriptions']['total'] ?? 0) ?></span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>المرضى النشطين</span>
                            <span class="badge bg-info"><?= number_format($stats['patients']['total'] ?? 0) ?></span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>معدل رضا المرضى</span>
                            <span class="badge bg-warning"><?= number_format($stats['satisfaction']['rate'] ?? 0, 1) ?>%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات الاتصال -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-address-book"></i>
                        معلومات الاتصال
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>البريد الإلكتروني:</strong><br>
                        <span class="text-muted"><?= htmlspecialchars($doctor['email'] ?? '') ?></span>
                    </div>
                    <div class="mb-3">
                        <strong>رقم الهاتف:</strong><br>
                        <span class="text-muted"><?= htmlspecialchars($doctor['phone'] ?? '') ?></span>
                    </div>
                    <div class="mb-3">
                        <strong>العنوان:</strong><br>
                        <span class="text-muted"><?= htmlspecialchars($doctor['address'] ?? 'غير محدد') ?></span>
                    </div>
                    <div class="mb-3">
                        <strong>تاريخ الانضمام:</strong><br>
                        <span class="text-muted"><?= date('Y-m-d', strtotime($doctor['created_at'] ?? 'now')) ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function saveProfile() {
    document.getElementById('profileForm').submit();
}

function uploadImage() {
    const fileInput = document.getElementById('profile_image');
    const file = fileInput.files[0];
    
    if (!file) {
        alert('يرجى اختيار صورة أولاً');
        return;
    }
    
    const formData = new FormData();
    formData.append('profile_image', file);
    
    fetch('<?= App::url('doctor/upload-image') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم رفع الصورة بنجاح');
            location.reload();
        } else {
            alert('فشل في رفع الصورة: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء رفع الصورة');
    });
}

// التحقق من تطابق كلمة المرور
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (newPassword !== confirmPassword) {
        this.setCustomValidity('كلمة المرور غير متطابقة');
    } else {
        this.setCustomValidity('');
    }
});

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إخفاء رسائل التنبيه بعد 5 ثواني
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
});
</script> 