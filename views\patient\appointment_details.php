<div class="appointment-details">
    <div class="row">
        <div class="col-md-8">
            <!-- Appointment Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-calendar-check me-2"></i>
                        تفاصيل الموعد
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الطبيب:</label>
                                <p class="mb-0"><?= htmlspecialchars($appointment['doctor_name']) ?></p>
                                <?php if (!empty($appointment['doctor_specialization'])): ?>
                                    <small class="text-muted"><?= htmlspecialchars($appointment['doctor_specialization']) ?></small>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">التاريخ:</label>
                                <p class="mb-0"><?= date('Y-m-d', strtotime($appointment['appointment_date'])) ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الوقت:</label>
                                <p class="mb-0"><?= date('H:i', strtotime($appointment['appointment_time'])) ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الحالة:</label>
                                <p class="mb-0">
                                    <?php
                                    $statusLabel = Appointment::getStatusLabel($appointment['status']);
                                    $statusColor = Appointment::getStatusColor($appointment['status']);
                                    ?>
                                    <span class="badge bg-<?= $statusColor ?>">
                                        <i class="bi bi-circle-fill me-1"></i>
                                        <?= $statusLabel ?>
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <?php if (!empty($appointment['reason'])): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold">سبب الموعد:</label>
                            <p class="mb-0"><?= htmlspecialchars($appointment['reason']) ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($appointment['notes'])): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold">ملاحظات:</label>
                            <p class="mb-0"><?= nl2br(htmlspecialchars($appointment['notes'])) ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($appointment['rating'])): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold">التقييم:</label>
                            <div class="d-flex align-items-center">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="bi bi-star<?= $i <= $appointment['rating'] ? '-fill' : '' ?> text-warning me-1"></i>
                                <?php endfor; ?>
                                <span class="ms-2"><?= $appointment['rating'] ?>/5</span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Additional Information -->
            <?php if (!empty($appointment['appointment_type']) || !empty($appointment['priority']) || !empty($appointment['duration'])): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            معلومات إضافية
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php if (!empty($appointment['appointment_type'])): ?>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">نوع الموعد:</label>
                                        <p class="mb-0">
                                            <?php
                                            $typeLabels = [
                                                'consultation' => 'استشارة',
                                                'examination' => 'فحص',
                                                'follow_up' => 'متابعة'
                                            ];
                                            echo $typeLabels[$appointment['appointment_type']] ?? $appointment['appointment_type'];
                                            ?>
                                        </p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($appointment['priority'])): ?>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">الأولوية:</label>
                                        <p class="mb-0">
                                            <?php
                                            $priorityLabels = [
                                                'normal' => 'عادية',
                                                'urgent' => 'عاجلة',
                                                'emergency' => 'طوارئ'
                                            ];
                                            $priorityColors = [
                                                'normal' => 'success',
                                                'urgent' => 'warning',
                                                'emergency' => 'danger'
                                            ];
                                            $color = $priorityColors[$appointment['priority']] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?= $color ?>">
                                                <?= $priorityLabels[$appointment['priority']] ?? $appointment['priority'] ?>
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($appointment['duration'])): ?>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold">المدة المتوقعة:</label>
                                        <p class="mb-0"><?= $appointment['duration'] ?> دقيقة</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <?php if (!empty($appointment['room_number'])): ?>
                            <div class="mb-3">
                                <label class="form-label fw-bold">رقم الغرفة:</label>
                                <p class="mb-0"><?= htmlspecialchars($appointment['room_number']) ?></p>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($appointment['insurance_info'])): ?>
                            <div class="mb-3">
                                <label class="form-label fw-bold">معلومات التأمين:</label>
                                <p class="mb-0"><?= htmlspecialchars($appointment['insurance_info']) ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="col-md-4">
            <!-- Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-gear me-2"></i>
                        الإجراءات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if (in_array($appointment['status'], ['scheduled', 'confirmed'])): ?>
                            <button type="button" 
                                    class="btn btn-outline-danger" 
                                    onclick="cancelAppointment(<?= $appointment['id'] ?>)">
                                <i class="bi bi-x-circle me-2"></i>
                                إلغاء الموعد
                            </button>
                        <?php endif; ?>
                        
                        <?php if ($appointment['status'] === 'completed' && empty($appointment['rating'])): ?>
                            <button type="button" 
                                    class="btn btn-outline-warning" 
                                    onclick="rateAppointment(<?= $appointment['id'] ?>)">
                                <i class="bi bi-star me-2"></i>
                                تقييم الموعد
                            </button>
                        <?php endif; ?>
                        
                        <a href="<?= App::url('patient/appointments') ?>" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-2"></i>
                            العودة للمواعيد
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Timeline -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-clock-history me-2"></i>
                        الجدول الزمني
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم إنشاء الموعد</h6>
                                <small class="text-muted">
                                    <?= date('Y-m-d H:i', strtotime($appointment['created_at'])) ?>
                                </small>
                            </div>
                        </div>
                        
                        <?php if ($appointment['status'] === 'confirmed'): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">تم تأكيد الموعد</h6>
                                    <small class="text-muted">تم التأكيد من قبل الطبيب</small>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($appointment['status'] === 'completed'): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">تم إكمال الموعد</h6>
                                    <small class="text-muted">تم إكمال الموعد بنجاح</small>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($appointment['status'] === 'cancelled'): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-danger"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">تم إلغاء الموعد</h6>
                                    <small class="text-muted">تم إلغاء الموعد</small>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($appointment['status'] === 'no_show'): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-secondary"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">لم يحضر المريض</h6>
                                    <small class="text-muted">لم يحضر المريض للموعد</small>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-content {
    padding-left: 10px;
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.timeline-content small {
    font-size: 0.8rem;
}
</style> 