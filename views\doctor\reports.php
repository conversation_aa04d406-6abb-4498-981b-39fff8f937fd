<?php
/**
 * صفحة التقارير للطبيب
 */
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-chart-bar text-primary"></i>
                التقارير والإحصائيات
            </h1>
            <p class="text-muted">عرض التقارير والإحصائيات الخاصة بك</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary" onclick="exportReport()">
                <i class="fas fa-download"></i>
                تصدير التقرير
            </button>
            <button class="btn btn-primary" onclick="printReport()">
                <i class="fas fa-print"></i>
                طباعة
            </button>
        </div>
    </div>

    <!-- فلتر التاريخ -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" 
                           value="<?= htmlspecialchars($startDate) ?>">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" 
                           value="<?= htmlspecialchars($endDate) ?>">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i>
                        تحديث
                    </button>
                    <a href="<?= App::url('doctor/reports') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- الإحصائيات العامة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي المواعيد
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['appointments']['total'] ?? 0) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                الوصفات الطبية
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['prescriptions']['total'] ?? 0) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-prescription fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                السجلات الطبية
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['medical_records']['total'] ?? 0) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-medical fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                المرضى النشطين
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['appointments']['unique_patients'] ?? 0) ?>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تقرير المواعيد -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-calendar-alt"></i>
                        تقرير المواعيد
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($appointmentReport)): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered" id="appointmentsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>الوقت</th>
                                        <th>المريض</th>
                                        <th>نوع الموعد</th>
                                        <th>الحالة</th>
                                        <th>الملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($appointmentReport as $appointment): ?>
                                        <tr>
                                            <td><?= date('Y-m-d', strtotime($appointment['appointment_date'])) ?></td>
                                            <td><?= $appointment['appointment_time'] ?></td>
                                            <td>
                                                <a href="<?= App::url('doctor/view-patient/' . $appointment['patient_id']) ?>" 
                                                   class="text-decoration-none">
                                                    <?= htmlspecialchars($appointment['patient_name']) ?>
                                                </a>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClass = '';
                                                $statusText = '';
                                                switch ($appointment['status']) {
                                                    case 'scheduled':
                                                        $statusClass = 'bg-warning';
                                                        $statusText = 'مجدول';
                                                        break;
                                                    case 'confirmed':
                                                        $statusClass = 'bg-info';
                                                        $statusText = 'مؤكد';
                                                        break;
                                                    case 'completed':
                                                        $statusClass = 'bg-success';
                                                        $statusText = 'مكتمل';
                                                        break;
                                                    case 'cancelled':
                                                        $statusClass = 'bg-danger';
                                                        $statusText = 'ملغي';
                                                        break;
                                                    case 'no_show':
                                                        $statusClass = 'bg-secondary';
                                                        $statusText = 'لم يحضر';
                                                        break;
                                                    default:
                                                        $statusClass = 'bg-secondary';
                                                        $statusText = 'غير محدد';
                                                }
                                                ?>
                                                <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClass = '';
                                                $statusText = '';
                                                switch ($appointment['status']) {
                                                    case 'scheduled':
                                                        $statusClass = 'bg-warning';
                                                        $statusText = 'مجدول';
                                                        break;
                                                    case 'completed':
                                                        $statusClass = 'bg-success';
                                                        $statusText = 'مكتمل';
                                                        break;
                                                    case 'cancelled':
                                                        $statusClass = 'bg-danger';
                                                        $statusText = 'ملغي';
                                                        break;
                                                    default:
                                                        $statusClass = 'bg-secondary';
                                                        $statusText = 'غير محدد';
                                                }
                                                ?>
                                                <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                                            </td>
                                            <td><?= htmlspecialchars($appointment['notes'] ?? '') ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد مواعيد في الفترة المحددة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- تقرير الوصفات الطبية -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-prescription"></i>
                        تقرير الوصفات الطبية
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($prescriptionReport)): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered" id="prescriptionsTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>المريض</th>
                                        <th>التشخيص</th>
                                        <th>عدد الأدوية</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($prescriptionReport as $prescription): ?>
                                        <tr>
                                            <td><?= date('Y-m-d', strtotime($prescription['created_at'])) ?></td>
                                            <td>
                                                <a href="<?= App::url('doctor/view-patient/' . $prescription['patient_id']) ?>" 
                                                   class="text-decoration-none">
                                                    <?= htmlspecialchars($prescription['patient_name']) ?>
                                                </a>
                                            </td>
                                            <td><?= htmlspecialchars($prescription['diagnosis']) ?></td>
                                            <td>
                                                <span class="badge bg-info">
                                                    <?= $prescription['medications_count'] ?? 0 ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClass = $prescription['status'] === 'active' ? 'bg-success' : 'bg-secondary';
                                                $statusText = $prescription['status'] === 'active' ? 'نشط' : 'منتهي';
                                                ?>
                                                <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                                            </td>
                                            <td>
                                                <a href="<?= App::url('doctor/view-prescription/' . $prescription['id']) ?>" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                    عرض
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-prescription-bottle fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد وصفات طبية في الفترة المحددة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportReport() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    
    // إنشاء رابط التصدير
    const exportUrl = '<?= App::url('doctor/reports/export') ?>' + 
                     '?start_date=' + startDate + 
                     '&end_date=' + endDate;
    
    // فتح الرابط في نافذة جديدة
    window.open(exportUrl, '_blank');
}

function printReport() {
    window.print();
}

// تهيئة الجداول
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة جدول المواعيد
    if (document.getElementById('appointmentsTable')) {
        new DataTable('#appointmentsTable', {
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
            },
            pageLength: 10,
            order: [[0, 'desc']]
        });
    }
    
    // تهيئة جدول الوصفات الطبية
    if (document.getElementById('prescriptionsTable')) {
        new DataTable('#prescriptionsTable', {
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
            },
            pageLength: 10,
            order: [[0, 'desc']]
        });
    }
});
</script> 