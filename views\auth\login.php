<div class="text-center mb-4">
    <div class="auth-logo mb-3">
        <i class="bi bi-heart-pulse-fill text-primary" style="font-size: 3rem;"></i>
    </div>
    <h2 class="fw-bold text-primary mb-2">مرحباً بك مرة أخرى</h2>
    <p class="text-muted">سجل دخولك للوصول إلى حسابك</p>
</div>

<?php if (isset($errors['login'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        <?= htmlspecialchars($errors['login']) ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($_GET['registered'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle-fill me-2"></i>
        تم إنشاء حسابك بنجاح! يمكنك الآن تسجيل الدخول.
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>
        
<form method="POST" action="<?= App::url('login') ?>" data-validate="true" class="needs-validation" novalidate>
    <div class="form-group mb-4">
        <label for="email" class="form-label fw-semibold">
            <i class="bi bi-envelope-fill text-primary me-2"></i>
            البريد الإلكتروني
        </label>
        <div class="input-group">
            <span class="input-group-text bg-light border-end-0">
                <i class="bi bi-person-fill text-muted"></i>
            </span>
            <input
                type="email"
                class="form-control border-start-0 <?= isset($errors['email']) ? 'is-invalid' : '' ?>"
                id="email"
                name="email"
                value="<?= htmlspecialchars($old_input['email'] ?? '') ?>"
                required
                placeholder="<EMAIL>"
                style="border-right: none;"
            >
            <?php if (isset($errors['email'])): ?>
                <div class="invalid-feedback">
                    <?= htmlspecialchars($errors['email']) ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="form-group mb-4">
        <label for="password" class="form-label fw-semibold">
            <i class="bi bi-shield-lock-fill text-primary me-2"></i>
            كلمة المرور
        </label>
        <div class="input-group">
            <span class="input-group-text bg-light border-end-0">
                <i class="bi bi-key-fill text-muted"></i>
            </span>
            <input
                type="password"
                class="form-control border-start-0 border-end-0 <?= isset($errors['password']) ? 'is-invalid' : '' ?>"
                id="password"
                name="password"
                required
                placeholder="••••••••"
                style="border-right: none; border-left: none;"
            >
            <button class="btn btn-outline-secondary border-start-0" type="button" id="togglePassword">
                <i class="bi bi-eye-fill" id="toggleIcon"></i>
            </button>
            <?php if (isset($errors['password'])): ?>
                <div class="invalid-feedback">
                    <?= htmlspecialchars($errors['password']) ?>
                </div>
            <?php endif; ?>
        </div>

    <div class="form-check mb-4">
        <input type="checkbox" class="form-check-input" id="remember" name="remember" value="1">
        <label class="form-check-label fw-semibold" for="remember">
            <i class="bi bi-clock-fill text-primary me-2"></i>
            تذكرني لمدة 30 يوم
        </label>
    </div>

    <div class="d-grid gap-3 mb-4">
        <button type="submit" class="btn btn-primary btn-lg">
            <i class="bi bi-box-arrow-in-right me-2"></i>
            تسجيل الدخول
            <div class="spinner-border spinner-border-sm ms-2 d-none" role="status" id="loginSpinner">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </button>
    </div>
</form>
<div class="text-center">
    <div class="divider my-4">
        <span class="divider-text text-muted">أو</span>
    </div>

    <p class="text-muted mb-3">ليس لديك حساب؟</p>
    <a href="<?= App::url('register') ?>" class="btn btn-outline-primary btn-lg w-100 mb-3">
        <i class="bi bi-person-plus-fill me-2"></i>
        إنشاء حساب جديد
    </a>

    <div class="mt-4">
        <a href="<?= App::url() ?>" class="text-decoration-none text-muted">
            <i class="bi bi-arrow-right-circle me-2"></i>
            العودة للصفحة الرئيسية
        </a>
    </div>
</div>

<!-- معلومات الحسابات التجريبية للاختبار -->
<?php if (defined('APP_DEBUG') && APP_DEBUG): ?>
<div class="mt-5">
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-light border-0">
            <h6 class="mb-0 text-center fw-bold">
                <i class="bi bi-info-circle-fill text-info me-2"></i>
                حسابات تجريبية للاختبار
            </h6>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-6">
                    <div class="demo-account p-3 rounded bg-primary bg-opacity-10 border border-primary border-opacity-25">
                        <div class="d-flex align-items-center mb-2">
                            <i class="bi bi-shield-fill-check text-primary me-2"></i>
                            <h6 class="mb-0 text-primary fw-bold">المدير</h6>
                        </div>
                        <small class="text-muted d-block"><EMAIL></small>
                        <small class="text-muted">password</small>
                    </div>
                </div>
                <div class="col-6">
                    <div class="demo-account p-3 rounded bg-success bg-opacity-10 border border-success border-opacity-25">
                        <div class="d-flex align-items-center mb-2">
                            <i class="bi bi-heart-pulse-fill text-success me-2"></i>
                            <h6 class="mb-0 text-success fw-bold">الطبيب</h6>
                        </div>
                        <small class="text-muted d-block"><EMAIL></small>
                        <small class="text-muted">password</small>
                    </div>
                </div>
                <div class="col-6">
                    <div class="demo-account p-3 rounded bg-info bg-opacity-10 border border-info border-opacity-25">
                        <div class="d-flex align-items-center mb-2">
                            <i class="bi bi-capsule-pill text-info me-2"></i>
                            <h6 class="mb-0 text-info fw-bold">الصيدلي</h6>
                        </div>
                        <small class="text-muted d-block"><EMAIL></small>
                        <small class="text-muted">password</small>
                    </div>
                </div>
                <div class="col-6">
                    <div class="demo-account p-3 rounded bg-warning bg-opacity-10 border border-warning border-opacity-25">
                        <div class="d-flex align-items-center mb-2">
                            <i class="bi bi-person-fill text-warning me-2"></i>
                            <h6 class="mb-0 text-warning fw-bold">المريض</h6>
                        </div>
                        <small class="text-muted d-block"><EMAIL></small>
                        <small class="text-muted">password</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<style>
.divider {
    position: relative;
    text-align: center;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #dee2e6;
}

.divider-text {
    background: white;
    padding: 0 1rem;
    font-size: 0.875rem;
}

.demo-account {
    transition: all 0.3s ease;
    cursor: pointer;
}

.demo-account:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

#togglePassword {
    border-left: 2px solid #e9ecef;
}

.input-group-text {
    border-right: 2px solid #e9ecef;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تبديل إظهار/إخفاء كلمة المرور
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');

    if (togglePassword) {
        togglePassword.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            if (type === 'text') {
                toggleIcon.className = 'bi bi-eye-slash-fill';
            } else {
                toggleIcon.className = 'bi bi-eye-fill';
            }
        });
    }

    // تأثير النقر على الحسابات التجريبية
    const demoAccounts = document.querySelectorAll('.demo-account');
    demoAccounts.forEach(account => {
        account.addEventListener('click', function() {
            const email = this.querySelector('small').textContent;
            document.getElementById('email').value = email;
            document.getElementById('password').value = 'password';

            // تأثير بصري
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'translateY(-2px)';
            }, 150);
        });
    });

    // تحسين النموذج
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');
    const spinner = document.getElementById('loginSpinner');

    form.addEventListener('submit', function() {
        submitBtn.disabled = true;
        spinner.classList.remove('d-none');
        submitBtn.querySelector('i').classList.add('d-none');
    });
});
</script>
