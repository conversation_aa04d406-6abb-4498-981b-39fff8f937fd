<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-prescription2 me-2"></i>
            الوصفات الطبية
        </h1>
        <p class="text-muted">إدارة وعرض الوصفات الطبية للمرضى</p>
    </div>
    <div>
        <a href="<?= App::url('doctor/create-prescription') ?>" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>
            إنشاء وصفة جديدة
        </a>
        <a href="<?= App::url('doctor/dashboard') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['total'] ?></h4>
                        <p class="mb-0">إجمالي الوصفات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-prescription2 display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['active'] ?></h4>
                        <p class="mb-0">وصفات نشطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['completed'] ?></h4>
                        <p class="mb-0">وصفات مكتملة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check2-all display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['cancelled'] ?></h4>
                        <p class="mb-0">وصفات ملغية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-x-circle display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?= App::url('doctor/prescriptions') ?>" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?= htmlspecialchars($search) ?>" 
                       placeholder="البحث في اسم المريض أو الدواء أو التشخيص">
            </div>
            
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="active" <?= $selectedStatus === 'active' ? 'selected' : '' ?>>نشطة</option>
                    <option value="completed" <?= $selectedStatus === 'completed' ? 'selected' : '' ?>>مكتملة</option>
                    <option value="cancelled" <?= $selectedStatus === 'cancelled' ? 'selected' : '' ?>>ملغية</option>
                </select>
            </div>
            
            <div class="col-md-3">
                <label for="date" class="form-label">التاريخ</label>
                <input type="date" class="form-control" id="date" name="date" 
                       value="<?= htmlspecialchars($selectedDate) ?>">
            </div>
            
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search me-2"></i>
                        بحث
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Prescriptions Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-table me-2"></i>
            قائمة الوصفات الطبية
        </h5>
        <div>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportPrescriptions()">
                <i class="bi bi-download me-1"></i>
                تصدير
            </button>
            <button type="button" class="btn btn-sm btn-outline-success" onclick="printPrescriptions()">
                <i class="bi bi-printer me-1"></i>
                طباعة
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($prescriptions)): ?>
            <div class="text-center py-5">
                <i class="bi bi-prescription2 display-1 text-muted"></i>
                <h4 class="mt-3 text-muted">لا توجد وصفات طبية</h4>
                <p class="text-muted">لم يتم العثور على وصفات طبية تطابق معايير البحث</p>
                <a href="<?= App::url('doctor/create-prescription') ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    إنشاء وصفة جديدة
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الوصفة</th>
                            <th>المريض</th>
                            <th>الدواء</th>
                            <th>الجرعة</th>
                            <th>التشخيص</th>
                            <th>التاريخ</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($prescriptions as $prescription): ?>
                            <tr>
                                <td>
                                    <strong>#<?= htmlspecialchars($prescription['id']) ?></strong>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                            <?= strtoupper(substr($prescription['patient_name'], 0, 1)) ?>
                                        </div>
                                        <div>
                                            <strong><?= htmlspecialchars($prescription['patient_name']) ?></strong>
                                            <br>
                                            <small class="text-muted"><?= htmlspecialchars($prescription['patient_email'] ?? '') ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <strong><?= htmlspecialchars($prescription['medication_name']) ?></strong>
                                    <?php if (!empty($prescription['dosage'])): ?>
                                        <br>
                                        <small class="text-muted"><?= htmlspecialchars($prescription['dosage']) ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($prescription['frequency'])): ?>
                                        <span class="badge bg-info"><?= htmlspecialchars($prescription['frequency']) ?></span>
                                    <?php endif; ?>
                                    <?php if (!empty($prescription['duration'])): ?>
                                        <br>
                                        <small class="text-muted"><?= htmlspecialchars($prescription['duration']) ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($prescription['diagnosis'])): ?>
                                        <span class="text-truncate d-inline-block" style="max-width: 150px;" 
                                              title="<?= htmlspecialchars($prescription['diagnosis']) ?>">
                                            <?= htmlspecialchars($prescription['diagnosis']) ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">غير محدد</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div>
                                        <strong><?= date('Y-m-d', strtotime($prescription['prescription_date'])) ?></strong>
                                        <br>
                                        <small class="text-muted"><?= date('H:i', strtotime($prescription['created_at'])) ?></small>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $statusColors = [
                                        'active' => 'success',
                                        'completed' => 'info',
                                        'cancelled' => 'danger'
                                    ];
                                    $statusLabels = [
                                        'active' => 'نشطة',
                                        'completed' => 'مكتملة',
                                        'cancelled' => 'ملغية'
                                    ];
                                    $status = $prescription['status'] ?? 'active';
                                    ?>
                                    <span class="badge bg-<?= $statusColors[$status] ?>">
                                        <?= $statusLabels[$status] ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="viewPrescription(<?= $prescription['id'] ?>)"
                                                title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                onclick="editPrescription(<?= $prescription['id'] ?>)"
                                                title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="printPrescription(<?= $prescription['id'] ?>)"
                                                title="طباعة">
                                            <i class="bi bi-printer"></i>
                                        </button>
                                        <?php if ($status === 'active'): ?>
                                            <button type="button" class="btn btn-sm btn-outline-warning" 
                                                    onclick="completePrescription(<?= $prescription['id'] ?>)"
                                                    title="إكمال">
                                                <i class="bi bi-check2-all"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="bi bi-plus-circle display-4 mb-3"></i>
                <h5>إنشاء وصفة جديدة</h5>
                <p class="mb-3">إنشاء وصفة طبية جديدة لمريض</p>
                <a href="<?= App::url('doctor/create-prescription') ?>" class="btn btn-light">
                    <i class="bi bi-plus-circle me-2"></i>
                    إنشاء وصفة
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="bi bi-file-earmark-medical display-4 mb-3"></i>
                <h5>إضافة سجل طبي</h5>
                <p class="mb-3">إضافة سجل طبي جديد</p>
                <a href="<?= App::url('doctor/add-medical-record') ?>" class="btn btn-light">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة سجل
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="bi bi-calendar-check display-4 mb-3"></i>
                <h5>إدارة المواعيد</h5>
                <p class="mb-3">عرض وإدارة المواعيد</p>
                <a href="<?= App::url('doctor/appointments') ?>" class="btn btn-light">
                    <i class="bi bi-calendar-check me-2"></i>
                    المواعيد
                </a>
            </div>
        </div>
    </div>
</div>

<!-- View Prescription Modal -->
<div class="modal fade" id="viewPrescriptionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-prescription2 me-2"></i>
                    تفاصيل الوصفة الطبية
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="prescriptionDetails">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="editPrescription()">
                    <i class="bi bi-pencil me-2"></i>
                    تعديل
                </button>
                <button type="button" class="btn btn-info" onclick="printPrescription()">
                    <i class="bi bi-printer me-2"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentPrescriptionId = null;

function viewPrescription(prescriptionId) {
    currentPrescriptionId = prescriptionId;
    
    // تحميل تفاصيل الوصفة
    fetch(`<?= App::url('doctor/view-prescription/') ?>${prescriptionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('prescriptionDetails').innerHTML = data.html;
                new bootstrap.Modal(document.getElementById('viewPrescriptionModal')).show();
            } else {
                alert('فشل في تحميل تفاصيل الوصفة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحميل التفاصيل');
        });
}

function editPrescription(prescriptionId) {
    if (prescriptionId) {
        window.location.href = `<?= App::url('doctor/edit-prescription/') ?>${prescriptionId}`;
    } else if (currentPrescriptionId) {
        window.location.href = `<?= App::url('doctor/edit-prescription/') ?>${currentPrescriptionId}`;
    }
}

function printPrescription(prescriptionId) {
    const id = prescriptionId || currentPrescriptionId;
    if (id) {
        window.open(`<?= App::url('doctor/print-prescription/') ?>${id}`, '_blank');
    }
}

function completePrescription(prescriptionId) {
    if (confirm('هل أنت متأكد من إكمال هذه الوصفة الطبية؟')) {
        fetch(`<?= App::url('doctor/complete-prescription/') ?>${prescriptionId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('فشل في إكمال الوصفة الطبية');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إكمال الوصفة');
        });
    }
}

function exportPrescriptions() {
    const search = '<?= htmlspecialchars($search) ?>';
    const status = '<?= htmlspecialchars($selectedStatus) ?>';
    const date = '<?= htmlspecialchars($selectedDate) ?>';
    
    const url = `<?= App::url('doctor/export-prescriptions') ?>?search=${encodeURIComponent(search)}&status=${encodeURIComponent(status)}&date=${encodeURIComponent(date)}`;
    
    window.open(url, '_blank');
}

function printPrescriptions() {
    window.print();
}

// تحسين التفاعل
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات بصرية للصفوف
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
    
    // تفعيل tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 14px;
}

.table th {
    border-top: none;
    font-weight: 600;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.btn-group .btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

@media print {
    .btn, .card-header, .modal {
        display: none !important;
    }
    
    .table {
        font-size: 0.875rem;
    }
}
</style> 