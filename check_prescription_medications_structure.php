<?php
/**
 * فحص هيكل جدول prescription_medications
 */

require_once 'config.php';
require_once 'app/core/Database.php';

echo "<h1>فحص هيكل جدول prescription_medications</h1>";

try {
    $db = Database::getInstance();
    
    // عرض هيكل الجدول
    echo "<h3>هيكل الجدول:</h3>";
    $columns = $db->select("DESCRIBE prescription_medications");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // عرض عدد السجلات
    $count = $db->selectOne("SELECT COUNT(*) as count FROM prescription_medications")['count'];
    echo "✅ عدد السجلات في الجدول: $count<br>";
    
    // عرض عينة من البيانات
    if ($count > 0) {
        echo "<h3>عينة من البيانات:</h3>";
        $sample = $db->select("SELECT * FROM prescription_medications LIMIT 5");
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Prescription ID</th><th>Medication ID</th><th>Quantity</th><th>Pharmacist ID</th><th>Dispensed At</th></tr>";
        foreach ($sample as $row) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['prescription_id'] . "</td>";
            echo "<td>" . $row['medication_id'] . "</td>";
            echo "<td>" . $row['quantity'] . "</td>";
            echo "<td>" . ($row['pharmacist_id'] ?? 'NULL') . "</td>";
            echo "<td>" . ($row['dispensed_at'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في فحص الجدول</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 