<?php
/**
 * إضافة بيانات تجريبية بالهيكل الصحيح
 */

require_once 'config.php';
require_once 'app/core/Database.php';

echo "<h1>إضافة بيانات تجريبية بالهيكل الصحيح</h1>";

try {
    $db = Database::getInstance();
    
    // إضافة بيانات تجريبية لجدول prescription_medications
    $sampleData = [
        [
            'prescription_id' => 1,
            'medication_name' => 'باراسيتامول',
            'generic_name' => 'Acetaminophen',
            'strength' => '500mg',
            'dosage_form' => 'أقراص',
            'quantity' => 2,
            'dosage' => 'قرص واحد',
            'frequency' => 'كل 6 ساعات',
            'duration' => '5 أيام',
            'instructions' => 'عند الحاجة للألم أو الحمى',
            'is_dispensed' => 1,
            'dispensed_quantity' => 2,
            'dispensed_by' => 2,
            'dispensed_at' => '2024-01-15 10:30:00'
        ],
        [
            'prescription_id' => 1,
            'medication_name' => 'أموكسيسيلين',
            'generic_name' => 'Amoxicillin',
            'strength' => '500mg',
            'dosage_form' => 'كبسولات',
            'quantity' => 1,
            'dosage' => 'كبسولة واحدة',
            'frequency' => 'كل 8 ساعات',
            'duration' => '7 أيام',
            'instructions' => 'يؤخذ مع الطعام',
            'is_dispensed' => 1,
            'dispensed_quantity' => 1,
            'dispensed_by' => 2,
            'dispensed_at' => '2024-01-15 10:30:00'
        ],
        [
            'prescription_id' => 2,
            'medication_name' => 'أوميبرازول',
            'generic_name' => 'Omeprazole',
            'strength' => '20mg',
            'dosage_form' => 'كبسولات',
            'quantity' => 1,
            'dosage' => 'كبسولة واحدة',
            'frequency' => 'يومياً',
            'duration' => '14 يوم',
            'instructions' => 'يؤخذ قبل الإفطار',
            'is_dispensed' => 1,
            'dispensed_quantity' => 1,
            'dispensed_by' => 2,
            'dispensed_at' => '2024-01-16 14:20:00'
        ],
        [
            'prescription_id' => 3,
            'medication_name' => 'فيتامين د',
            'generic_name' => 'Vitamin D3',
            'strength' => '1000IU',
            'dosage_form' => 'كبسولات',
            'quantity' => 1,
            'dosage' => 'كبسولة واحدة',
            'frequency' => 'يومياً',
            'duration' => '30 يوم',
            'instructions' => 'يؤخذ مع الطعام',
            'is_dispensed' => 1,
            'dispensed_quantity' => 1,
            'dispensed_by' => 2,
            'dispensed_at' => '2024-01-17 09:15:00'
        ],
        [
            'prescription_id' => 4,
            'medication_name' => 'إيبوبروفين',
            'generic_name' => 'Ibuprofen',
            'strength' => '400mg',
            'dosage_form' => 'أقراص',
            'quantity' => 2,
            'dosage' => 'قرص واحد',
            'frequency' => 'كل 8 ساعات',
            'duration' => '3 أيام',
            'instructions' => 'عند الحاجة للألم',
            'is_dispensed' => 1,
            'dispensed_quantity' => 2,
            'dispensed_by' => 2,
            'dispensed_at' => '2024-01-18 16:45:00'
        ]
    ];
    
    $insertedCount = 0;
    foreach ($sampleData as $data) {
        try {
            $insertSql = "INSERT INTO prescription_medications (
                prescription_id, medication_name, generic_name, strength, dosage_form, 
                quantity, dosage, frequency, duration, instructions, 
                is_dispensed, dispensed_quantity, dispensed_by, dispensed_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $db->insert($insertSql, [
                $data['prescription_id'],
                $data['medication_name'],
                $data['generic_name'],
                $data['strength'],
                $data['dosage_form'],
                $data['quantity'],
                $data['dosage'],
                $data['frequency'],
                $data['duration'],
                $data['instructions'],
                $data['is_dispensed'],
                $data['dispensed_quantity'],
                $data['dispensed_by'],
                $data['dispensed_at']
            ]);
            $insertedCount++;
        } catch (Exception $e) {
            echo "⚠️ خطأ في إدراج السجل: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "✅ تم إدراج $insertedCount سجل بنجاح<br>";
    
    // التحقق من عدد السجلات
    $count = $db->selectOne("SELECT COUNT(*) as count FROM prescription_medications")['count'];
    echo "✅ إجمالي عدد السجلات في الجدول: $count<br>";
    
    // عرض البيانات المضافة
    echo "<h3>البيانات المضافة:</h3>";
    $medications = $db->select("
        SELECT pm.*, p.prescription_code
        FROM prescription_medications pm
        LEFT JOIN prescriptions p ON pm.prescription_id = p.id
        ORDER BY pm.id DESC
        LIMIT 10
    ");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Prescription</th><th>Medication</th><th>Quantity</th><th>Dispensed</th><th>Dispensed At</th></tr>";
    foreach ($medications as $med) {
        echo "<tr>";
        echo "<td>" . $med['id'] . "</td>";
        echo "<td>" . htmlspecialchars($med['prescription_code'] ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($med['medication_name']) . "</td>";
        echo "<td>" . $med['quantity'] . "</td>";
        echo "<td>" . ($med['is_dispensed'] ? 'نعم' : 'لا') . "</td>";
        echo "<td>" . $med['dispensed_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>✅ تم إضافة البيانات التجريبية بنجاح!</h2>";
    echo "<p>يمكنك الآن الوصول لصفحة التقارير:</p>";
    echo "<a href='index.php?url=pharmacist/reports' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>فتح صفحة التقارير</a>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في إضافة البيانات</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 