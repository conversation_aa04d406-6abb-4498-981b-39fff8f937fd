<?php
/**
 * إنشاء جدول الأدوية وإضافة بيانات تجريبية
 */

require_once 'config.php';

try {
    $db = Database::getInstance();
    
    echo "<h1>إنشاء جدول الأدوية</h1>";
    
    // إنشاء جدول الأدوية
    $createTableSql = "
    CREATE TABLE IF NOT EXISTS medications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        generic_name VARCHAR(255) NOT NULL,
        category VARCHAR(100) NOT NULL,
        dosage_form VARCHAR(100) NOT NULL,
        strength VARCHAR(100) NOT NULL,
        description TEXT,
        side_effects TEXT,
        contraindications TEXT,
        storage_conditions TEXT,
        prescription_required TINYINT(1) DEFAULT 0,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_name (name),
        INDEX idx_generic_name (generic_name),
        INDEX idx_category (category),
        INDEX idx_status (status),
        INDEX idx_prescription_required (prescription_required)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $result = $db->getConnection()->exec($createTableSql);
    
    if ($result !== false) {
        echo "✅ تم إنشاء جدول الأدوية بنجاح<br>";
        
        // التحقق من وجود بيانات
        $checkSql = "SELECT COUNT(*) as count FROM medications";
        $result = $db->selectOne($checkSql);
        
        if ($result['count'] == 0) {
            echo "📝 إضافة بيانات تجريبية...<br>";
            
            // بيانات تجريبية للأدوية (15 entries)
            $medications = [
                [
                    'name' => 'باراسيتامول',
                    'generic_name' => 'Acetaminophen',
                    'category' => 'مسكنات',
                    'dosage_form' => 'أقراص',
                    'strength' => '500mg',
                    'description' => 'مسكن للألم وخافض للحرارة، يستخدم لعلاج الصداع والحمى',
                    'side_effects' => 'غثيان، ألم في المعدة، طفح جلدي في حالات نادرة',
                    'contraindications' => 'الحساسية للباراسيتامول، أمراض الكبد الشديدة',
                    'storage_conditions' => 'يحفظ في مكان جاف وبارد، بعيداً عن أشعة الشمس',
                    'prescription_required' => 0,
                    'status' => 'active'
                ],
                [
                    'name' => 'أموكسيسيلين',
                    'generic_name' => 'Amoxicillin',
                    'category' => 'مضادات حيوية',
                    'dosage_form' => 'كبسولات',
                    'strength' => '500mg',
                    'description' => 'مضاد حيوي واسع الطيف لعلاج الالتهابات البكتيرية',
                    'side_effects' => 'غثيان، إسهال، طفح جلدي، حساسية',
                    'contraindications' => 'الحساسية للبنسلين، أمراض الكلى الشديدة',
                    'storage_conditions' => 'يحفظ في الثلاجة، بعيداً عن الرطوبة',
                    'prescription_required' => 1,
                    'status' => 'active'
                ],
                [
                    'name' => 'أوميبرازول',
                    'generic_name' => 'Omeprazole',
                    'category' => 'مضادات الحموضة',
                    'dosage_form' => 'كبسولات',
                    'strength' => '20mg',
                    'description' => 'مثبط لمضخة البروتون لعلاج قرحة المعدة والارتجاع المريئي',
                    'side_effects' => 'صداع، إسهال، ألم في البطن، غثيان',
                    'contraindications' => 'الحساسية للأوميبرازول، الحمل في الأشهر الأولى',
                    'storage_conditions' => 'يحفظ في مكان جاف وبارد',
                    'prescription_required' => 1,
                    'status' => 'active'
                ],
                [
                    'name' => 'فيتامين سي',
                    'generic_name' => 'Vitamin C',
                    'category' => 'فيتامينات',
                    'dosage_form' => 'أقراص',
                    'strength' => '1000mg',
                    'description' => 'فيتامين مضاد للأكسدة، يقوي المناعة ويساعد في امتصاص الحديد',
                    'side_effects' => 'إسهال، غثيان، حموضة في المعدة',
                    'contraindications' => 'حصوات الكلى، مرضى السكري',
                    'storage_conditions' => 'يحفظ في مكان جاف وبارد، بعيداً عن الضوء',
                    'prescription_required' => 0,
                    'status' => 'active'
                ],
                [
                    'name' => 'أسبيرين',
                    'generic_name' => 'Aspirin',
                    'category' => 'مسكنات',
                    'dosage_form' => 'أقراص',
                    'strength' => '100mg',
                    'description' => 'مسكن للألم ومضاد للالتهاب ومضاد لتجلط الدم',
                    'side_effects' => 'غثيان، ألم في المعدة، نزيف، طفح جلدي',
                    'contraindications' => 'قرحة المعدة، النزيف، الأطفال أقل من 12 سنة',
                    'storage_conditions' => 'يحفظ في مكان جاف وبارد',
                    'prescription_required' => 0,
                    'status' => 'active'
                ],
                [
                    'name' => 'ميتفورمين',
                    'generic_name' => 'Metformin',
                    'category' => 'أدوية السكري',
                    'dosage_form' => 'أقراص',
                    'strength' => '500mg',
                    'description' => 'دواء لعلاج السكري من النوع الثاني، يخفض مستوى السكر في الدم',
                    'side_effects' => 'غثيان، إسهال، ألم في البطن، نقص فيتامين ب12',
                    'contraindications' => 'فشل الكلى، الحماض الكيتوني، الحساسية للميتفورمين',
                    'storage_conditions' => 'يحفظ في مكان جاف وبارد',
                    'prescription_required' => 1,
                    'status' => 'active'
                ],
                [
                    'name' => 'أتينولول',
                    'generic_name' => 'Atenolol',
                    'category' => 'أدوية القلب',
                    'dosage_form' => 'أقراص',
                    'strength' => '50mg',
                    'description' => 'حاصرات بيتا لعلاج ارتفاع ضغط الدم وأمراض القلب',
                    'side_effects' => 'دوار، تعب، برودة في الأطراف، بطء في ضربات القلب',
                    'contraindications' => 'فشل القلب الشديد، الربو، بطء ضربات القلب الشديد',
                    'storage_conditions' => 'يحفظ في مكان جاف وبارد',
                    'prescription_required' => 1,
                    'status' => 'active'
                ],
                [
                    'name' => 'لوراتادين',
                    'generic_name' => 'Loratadine',
                    'category' => 'أدوية الحساسية',
                    'dosage_form' => 'أقراص',
                    'strength' => '10mg',
                    'description' => 'مضاد للهيستامين لعلاج الحساسية والتهاب الأنف التحسسي',
                    'side_effects' => 'نعاس، جفاف الفم، صداع، دوار',
                    'contraindications' => 'الحساسية للوراتادين، الأطفال أقل من 6 سنوات',
                    'storage_conditions' => 'يحفظ في مكان جاف وبارد',
                    'prescription_required' => 0,
                    'status' => 'active'
                ],
                [
                    'name' => 'إيبوبروفين',
                    'generic_name' => 'Ibuprofen',
                    'category' => 'مسكنات',
                    'dosage_form' => 'أقراص',
                    'strength' => '400mg',
                    'description' => 'مسكن للألم ومضاد للالتهاب وخافض للحرارة',
                    'side_effects' => 'ألم في المعدة، غثيان، دوار، طفح جلدي',
                    'contraindications' => 'قرحة المعدة، النزيف، الربو، أمراض الكلى',
                    'storage_conditions' => 'يحفظ في مكان جاف وبارد',
                    'prescription_required' => 0,
                    'status' => 'active'
                ],
                [
                    'name' => 'سيميتيدين',
                    'generic_name' => 'Cimetidine',
                    'category' => 'مضادات الحموضة',
                    'dosage_form' => 'أقراص',
                    'strength' => '200mg',
                    'description' => 'مضاد للهيستامين H2 لعلاج قرحة المعدة والارتجاع المريئي',
                    'side_effects' => 'صداع، إسهال، دوار، طفح جلدي',
                    'contraindications' => 'الحساسية للسيميتيدين، أمراض الكلى الشديدة',
                    'storage_conditions' => 'يحفظ في مكان جاف وبارد',
                    'prescription_required' => 0,
                    'status' => 'active'
                ],
                [
                    'name' => 'فيتامين د',
                    'generic_name' => 'Vitamin D',
                    'category' => 'فيتامينات',
                    'dosage_form' => 'كبسولات',
                    'strength' => '1000IU',
                    'description' => 'فيتامين ضروري لصحة العظام والأسنان، يساعد في امتصاص الكالسيوم',
                    'side_effects' => 'غثيان، إمساك، فقدان الشهية، حصوات الكلى',
                    'contraindications' => 'فرط الكالسيوم في الدم، حصوات الكلى',
                    'storage_conditions' => 'يحفظ في مكان جاف وبارد، بعيداً عن الضوء',
                    'prescription_required' => 0,
                    'status' => 'active'
                ],
                [
                    'name' => 'رانيتيدين',
                    'generic_name' => 'Ranitidine',
                    'category' => 'مضادات الحموضة',
                    'dosage_form' => 'أقراص',
                    'strength' => '150mg',
                    'description' => 'مضاد للهيستامين H2 لعلاج قرحة المعدة والارتجاع المريئي',
                    'side_effects' => 'صداع، إسهال، دوار، طفح جلدي',
                    'contraindications' => 'الحساسية للرانيتيدين، أمراض الكلى الشديدة',
                    'storage_conditions' => 'يحفظ في مكان جاف وبارد',
                    'prescription_required' => 0,
                    'status' => 'inactive'
                ],
                [
                    'name' => 'كودين',
                    'generic_name' => 'Codeine',
                    'category' => 'مسكنات',
                    'dosage_form' => 'أقراص',
                    'strength' => '30mg',
                    'description' => 'مسكن قوي للألم، يستخدم لعلاج الألم الشديد والسعال',
                    'side_effects' => 'نعاس، إمساك، غثيان، إدمان',
                    'contraindications' => 'الحساسية للكودين، الربو، أمراض التنفس',
                    'storage_conditions' => 'يحفظ في مكان آمن، بعيداً عن متناول الأطفال',
                    'prescription_required' => 1,
                    'status' => 'active'
                ],
                [
                    'name' => 'سلفاميثوكسازول',
                    'generic_name' => 'Sulfamethoxazole',
                    'category' => 'مضادات حيوية',
                    'dosage_form' => 'أقراص',
                    'strength' => '400mg',
                    'description' => 'مضاد حيوي سلفوناميد لعلاج الالتهابات البكتيرية',
                    'side_effects' => 'غثيان، إسهال، طفح جلدي، حساسية للضوء',
                    'contraindications' => 'الحساسية للسلفوناميد، أمراض الكلى، الحمل',
                    'storage_conditions' => 'يحفظ في مكان جاف وبارد',
                    'prescription_required' => 1,
                    'status' => 'active'
                ],
                [
                    'name' => 'فيتامين ب12',
                    'generic_name' => 'Vitamin B12',
                    'category' => 'فيتامينات',
                    'dosage_form' => 'أقراص',
                    'strength' => '1000mcg',
                    'description' => 'فيتامين ضروري لتكوين خلايا الدم الحمراء والجهاز العصبي',
                    'side_effects' => 'غثيان، إسهال، طفح جلدي، حكة',
                    'contraindications' => 'الحساسية لفيتامين ب12، مرض ليبر',
                    'storage_conditions' => 'يحفظ في مكان جاف وبارد، بعيداً عن الضوء',
                    'prescription_required' => 0,
                    'status' => 'active'
                ]
            ];
            
            // إدراج البيانات
            $insertSql = "INSERT INTO medications (name, generic_name, category, dosage_form, strength, 
                           description, side_effects, contraindications, storage_conditions, 
                           prescription_required, status) 
                           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            foreach ($medications as $medication) {
                $params = [
                    $medication['name'],
                    $medication['generic_name'],
                    $medication['category'],
                    $medication['dosage_form'],
                    $medication['strength'],
                    $medication['description'],
                    $medication['side_effects'],
                    $medication['contraindications'],
                    $medication['storage_conditions'],
                    $medication['prescription_required'],
                    $medication['status']
                ];
                
                $result = $db->insert($insertSql, $params);
                if ($result) {
                    echo "✅ تم إضافة: " . $medication['name'] . "<br>";
                } else {
                    echo "❌ فشل في إضافة: " . $medication['name'] . "<br>";
                }
            }
            
            echo "<br>✅ تم إضافة جميع البيانات التجريبية بنجاح<br>";
            
        } else {
            echo "ℹ️ الجدول يحتوي بالفعل على بيانات<br>";
        }
        
        // عرض إحصائيات الجدول
        $statsSql = "SELECT 
                        COUNT(*) as total,
                        COUNT(CASE WHEN status = 'active' THEN 1 END) as active,
                        COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive,
                        COUNT(CASE WHEN prescription_required = 1 THEN 1 END) as prescription_required,
                        COUNT(CASE WHEN prescription_required = 0 THEN 1 END) as otc
                     FROM medications";
        $stats = $db->selectOne($statsSql);
        
        echo "<br><h3>إحصائيات الجدول:</h3>";
        echo "📊 إجمالي الأدوية: " . $stats['total'] . "<br>";
        echo "✅ الأدوية النشطة: " . $stats['active'] . "<br>";
        echo "⏸️ الأدوية غير النشطة: " . $stats['inactive'] . "<br>";
        echo "💊 تحتاج وصفة: " . $stats['prescription_required'] . "<br>";
        echo "🛒 بدون وصفة: " . $stats['otc'] . "<br>";
        
    } else {
        echo "❌ فشل في إنشاء جدول الأدوية<br>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 