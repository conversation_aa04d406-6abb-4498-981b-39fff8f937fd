<!-- <PERSON> Header -->
<div class="dashboard-header mb-4">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="dashboard-title">
                <i class="bi bi-box-seam text-primary me-3"></i>
                إدارة المخزون
            </h1>
            <p class="dashboard-subtitle">إدارة أدوية الصيدلية والمخزون بكفاءة عالية</p>
        </div>
        <div class="dashboard-actions">
            <a href="<?= App::url('pharmacist/add-inventory') ?>" class="btn btn-primary btn-lg">
                <i class="bi bi-plus-circle me-2"></i>
                إضافة دواء جديد
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number"><?= isset($stats['total_items']) ? $stats['total_items'] : 0 ?></div>
                        <div class="stats-label">إجمالي الأدوية</div>
                    </div>
                    <div class="stats-icon">
                        <i class="bi bi-capsule"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card bg-gradient-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number"><?= isset($stats['low_stock']) ? $stats['low_stock'] : 0 ?></div>
                        <div class="stats-label">منخفضة المخزون</div>
                    </div>
                    <div class="stats-icon">
                        <i class="bi bi-exclamation-triangle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card bg-gradient-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number"><?= isset($stats['expired']) ? $stats['expired'] : 0 ?></div>
                        <div class="stats-label">منتهية الصلاحية</div>
                    </div>
                    <div class="stats-icon">
                        <i class="bi bi-x-circle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card bg-gradient-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stats-number"><?= isset($stats['total_value']) ? number_format($stats['total_value'], 2) : '0.00' ?></div>
                        <div class="stats-label">قيمة المخزون</div>
                    </div>
                    <div class="stats-icon">
                        <i class="bi bi-currency-dollar"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- البحث والفلترة -->
    <div class="col-lg-8 mb-4">
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="bi bi-search text-primary me-2"></i>
                    البحث في المخزون
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" action="<?= App::url('pharmacist/inventory') ?>" class="row g-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">البحث في اسم الدواء</label>
                            <input type="text" name="search" class="form-control" 
                                   placeholder="اكتب اسم الدواء..." 
                                   value="<?= isset($search) ? htmlspecialchars($search) : '' ?>">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">الفئة</label>
                            <select name="category" class="form-select">
                                <option value="">جميع الفئات</option>
                                <?php if (isset($categories) && is_array($categories)): ?>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?= htmlspecialchars($category['category']) ?>" 
                                                <?= (isset($currentCategory) && $currentCategory === $category['category']) ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($category['category']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi bi-search me-2"></i>
                                بحث
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- التنبيهات -->
    <div class="col-lg-4 mb-4">
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                    التنبيهات
                </h5>
            </div>
            <div class="card-body">
                <?php if (isset($lowStock) && !empty($lowStock)): ?>
                    <div class="alert alert-warning alert-dismissible fade show mb-3">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>منخفضة المخزون:</strong> <?= count($lowStock) ?> دواء
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($expired) && !empty($expired)): ?>
                    <div class="alert alert-danger alert-dismissible fade show mb-3">
                        <i class="bi bi-x-circle me-2"></i>
                        <strong>منتهية الصلاحية:</strong> <?= count($expired) ?> دواء
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($expiringSoon) && !empty($expiringSoon)): ?>
                    <div class="alert alert-info alert-dismissible fade show mb-3">
                        <i class="bi bi-clock me-2"></i>
                        <strong>تنتهي قريباً:</strong> <?= count($expiringSoon) ?> دواء
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ((!isset($lowStock) || empty($lowStock)) && (!isset($expired) || empty($expired)) && (!isset($expiringSoon) || empty($expiringSoon))): ?>
                    <div class="alert alert-success alert-dismissible fade show mb-0">
                        <i class="bi bi-check-circle me-2"></i>
                        لا توجد تنبيهات
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- جدول المخزون -->
<div class="card dashboard-card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title">
            <i class="bi bi-list-ul text-primary me-2"></i>
            قائمة الأدوية
        </h5>
        <span class="badge bg-primary fs-6"><?= isset($inventory) ? count($inventory) : 0 ?> دواء</span>
    </div>
    <div class="card-body">
        <?php if (!isset($inventory) || empty($inventory)): ?>
            <div class="text-center py-5">
                <i class="bi bi-inbox display-1 text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد أدوية</h5>
                <p class="text-muted mb-4">لم يتم العثور على أدوية في المخزون</p>
                <a href="<?= App::url('pharmacist/add-inventory') ?>" class="btn btn-primary btn-lg">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة دواء جديد
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover dashboard-table">
                    <thead>
                        <tr>
                            <th>اسم الدواء</th>
                            <th>الفئة</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (isset($inventory) && is_array($inventory)): ?>
                            <?php foreach ($inventory as $item): ?>
                                <tr>
                                <td>
                                    <div>
                                        <strong class="text-primary"><?= htmlspecialchars($item['name']) ?></strong>
                                        <?php if (!empty($item['generic_name'])): ?>
                                            <br><small class="text-muted"><?= htmlspecialchars($item['generic_name']) ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?= htmlspecialchars($item['category']) ?></span>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2 fw-bold"><?= $item['quantity'] ?></span>
                                        <?php if ($item['quantity'] <= $item['reorder_level']): ?>
                                            <i class="bi bi-exclamation-triangle text-warning"></i>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-bold text-success"><?= number_format($item['unit_price'], 2) ?> ريال</span>
                                </td>
                                <td>
                                    <?php 
                                    $expiryDate = new DateTime($item['expiry_date']);
                                    $today = new DateTime();
                                    $daysUntilExpiry = $today->diff($expiryDate)->days;
                                    
                                    if ($expiryDate < $today): ?>
                                        <span class="text-danger fw-bold">
                                            <i class="bi bi-x-circle me-1"></i>
                                            منتهية
                                        </span>
                                    <?php elseif ($daysUntilExpiry <= 30): ?>
                                        <span class="text-warning fw-bold">
                                            <i class="bi bi-exclamation-triangle me-1"></i>
                                            <?= $daysUntilExpiry ?> يوم
                                        </span>
                                    <?php else: ?>
                                        <span class="text-success">
                                            <?= $expiryDate->format('Y-m-d') ?>
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($item['quantity'] <= $item['reorder_level']): ?>
                                        <span class="badge bg-warning">منخفضة</span>
                                    <?php elseif ($expiryDate < $today): ?>
                                        <span class="badge bg-danger">منتهية</span>
                                    <?php elseif ($daysUntilExpiry <= 30): ?>
                                        <span class="badge bg-info">تنتهي قريباً</span>
                                    <?php else: ?>
                                        <span class="badge bg-success">متوفرة</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#editModal<?= $item['id'] ?>"
                                                title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-success"
                                                data-bs-toggle="modal" 
                                                data-bs-target="#quantityModal<?= $item['id'] ?>"
                                                title="تحديث الكمية">
                                            <i class="bi bi-plus-circle"></i>
                                        </button>
                                        <a href="<?= App::url('pharmacist/delete-inventory/' . $item['id']) ?>" 
                                           class="btn btn-sm btn-outline-danger"
                                           onclick="return confirm('هل أنت متأكد من حذف هذا الدواء؟')"
                                           title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modals for editing -->
<?php if (isset($inventory) && is_array($inventory)): ?>
    <?php foreach ($inventory as $item): ?>
    <!-- Edit Modal -->
    <div class="modal fade" id="editModal<?= $item['id'] ?>" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-pencil text-primary me-2"></i>
                        تعديل معلومات الدواء
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="<?= App::url('pharmacist/edit-inventory/' . $item['id']) ?>" method="POST">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم الدواء</label>
                                <input type="text" name="name" class="form-control" 
                                       value="<?= htmlspecialchars($item['name']) ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الاسم العلمي</label>
                                <input type="text" name="generic_name" class="form-control" 
                                       value="<?= htmlspecialchars($item['generic_name']) ?>">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الفئة</label>
                                <input type="text" name="category" class="form-control" 
                                       value="<?= htmlspecialchars($item['category']) ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">شكل الجرعة</label>
                                <input type="text" name="dosage_form" class="form-control" 
                                       value="<?= htmlspecialchars($item['dosage_form']) ?>">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الجرعة</label>
                                <input type="text" name="strength" class="form-control" 
                                       value="<?= htmlspecialchars($item['strength']) ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الشركة المصنعة</label>
                                <input type="text" name="manufacturer" class="form-control" 
                                       value="<?= htmlspecialchars($item['manufacturer']) ?>">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الكمية</label>
                                <input type="number" name="quantity" class="form-control" 
                                       value="<?= $item['quantity'] ?>" min="0" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">سعر الوحدة</label>
                                <input type="number" name="unit_price" class="form-control" 
                                       value="<?= $item['unit_price'] ?>" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">مستوى إعادة الطلب</label>
                                <input type="number" name="reorder_level" class="form-control" 
                                       value="<?= $item['reorder_level'] ?>" min="0" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">تاريخ الانتهاء</label>
                                <input type="date" name="expiry_date" class="form-control" 
                                       value="<?= $item['expiry_date'] ?>" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الدفعة</label>
                                <input type="text" name="batch_number" class="form-control" 
                                       value="<?= htmlspecialchars($item['batch_number']) ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الموقع</label>
                                <input type="text" name="location" class="form-control" 
                                       value="<?= htmlspecialchars($item['location']) ?>">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">الوصف</label>
                            <textarea name="description" class="form-control" rows="3"><?= htmlspecialchars($item['description']) ?></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Quantity Update Modal -->
    <div class="modal fade" id="quantityModal<?= $item['id'] ?>" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-circle text-success me-2"></i>
                        تحديث الكمية
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">اسم الدواء</label>
                        <input type="text" class="form-control" value="<?= htmlspecialchars($item['name']) ?>" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الكمية الحالية</label>
                        <input type="number" class="form-control" value="<?= $item['quantity'] ?>" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الكمية الجديدة</label>
                        <input type="number" id="newQuantity<?= $item['id'] ?>" class="form-control" 
                               value="<?= $item['quantity'] ?>" min="0" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="updateQuantity(<?= $item['id'] ?>)">
                        <i class="bi bi-check-circle me-2"></i>
                        تحديث الكمية
                    </button>
                </div>
            </div>
        </div>
    </div>
<?php endforeach; ?>
<?php endif; ?>
<?php endif; ?>

<script>
function updateQuantity(itemId) {
    const quantityInput = document.getElementById('newQuantity' + itemId);
    if (!quantityInput) {
        alert('لم يتم العثور على حقل الكمية');
        return;
    }
    
    const quantity = quantityInput.value;
    if (!quantity || quantity < 0) {
        alert('يرجى إدخال كمية صحيحة');
        return;
    }
    
    fetch('<?= App::url('pharmacist/update-quantity') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'id=' + itemId + '&quantity=' + quantity
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('خطأ في الاستجابة من الخادم');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // إظهار رسالة نجاح
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show';
            alertDiv.innerHTML = `
                <i class="bi bi-check-circle me-2"></i>
                ${data.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            // البحث عن مكان مناسب لإدراج الرسالة
            const container = document.querySelector('.container-fluid');
            if (container) {
                const firstElement = container.querySelector('.dashboard-header') || 
                                   container.querySelector('.row') || 
                                   container.querySelector('.main-content') ||
                                   container.firstChild;
                
                if (firstElement) {
                    container.insertBefore(alertDiv, firstElement);
                } else {
                    container.appendChild(alertDiv);
                }
            } else {
                // إذا لم نجد container، نضيف في بداية body
                document.body.insertBefore(alertDiv, document.body.firstChild);
            }
            
            // إعادة تحميل الصفحة بعد ثانيتين
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            alert(data.message || 'حدث خطأ أثناء تحديث الكمية');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء تحديث الكمية: ' + error.message);
    });
}

// إضافة تأثيرات بصرية للصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثير ظهور تدريجي للبطاقات
    const cards = document.querySelectorAll('.stats-card, .dashboard-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script> 