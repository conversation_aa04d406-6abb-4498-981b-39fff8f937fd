<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="fw-bold text-primary mb-1">
            <i class="bi bi-people-fill me-2"></i>
            إدارة المستخدمين
        </h2>
        <p class="text-muted mb-0">إدارة وتنظيم حسابات المستخدمين في النظام</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
            <i class="bi bi-person-plus-fill me-2"></i>
            إضافة مستخدم جديد
        </button>
        <button class="btn btn-outline-secondary" onclick="exportUsers()">
            <i class="bi bi-download me-2"></i>
            تصدير
        </button>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card bg-primary text-white">
            <div class="stats-number"><?= $totalUsers ?></div>
            <div class="stats-label">إجمالي المستخدمين</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-success text-white">
            <div class="stats-number"><?= count(array_filter($users, fn($u) => $u['user_type'] === 'doctor')) ?></div>
            <div class="stats-label">الأطباء</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-info text-white">
            <div class="stats-number"><?= count(array_filter($users, fn($u) => $u['user_type'] === 'pharmacist')) ?></div>
            <div class="stats-label">الصيادلة</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-warning text-white">
            <div class="stats-number"><?= count(array_filter($users, fn($u) => $u['user_type'] === 'patient')) ?></div>
            <div class="stats-label">المرضى</div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label class="form-label fw-semibold">البحث</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="text" class="form-control" name="search" 
                           value="<?= htmlspecialchars($filters['search'] ?? '') ?>" 
                           placeholder="البحث بالاسم أو البريد الإلكتروني">
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label fw-semibold">نوع المستخدم</label>
                <select class="form-select" name="user_type">
                    <option value="">جميع الأنواع</option>
                    <option value="admin" <?= ($filters['user_type'] ?? '') === 'admin' ? 'selected' : '' ?>>مدير</option>
                    <option value="doctor" <?= ($filters['user_type'] ?? '') === 'doctor' ? 'selected' : '' ?>>طبيب</option>
                    <option value="pharmacist" <?= ($filters['user_type'] ?? '') === 'pharmacist' ? 'selected' : '' ?>>صيدلي</option>
                    <option value="patient" <?= ($filters['user_type'] ?? '') === 'patient' ? 'selected' : '' ?>>مريض</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label fw-semibold">الحالة</label>
                <select class="form-select" name="is_active">
                    <option value="">جميع الحالات</option>
                    <option value="1" <?= ($filters['is_active'] ?? '') === '1' ? 'selected' : '' ?>>نشط</option>
                    <option value="0" <?= ($filters['is_active'] ?? '') === '0' ? 'selected' : '' ?>>غير نشط</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-funnel-fill me-2"></i>
                        تطبيق
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- جدول المستخدمين -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-table me-2"></i>
            قائمة المستخدمين
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>المستخدم</th>
                        <th>النوع</th>
                        <th>التخصص/الصيدلية</th>
                        <th>تاريخ التسجيل</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($users)): ?>
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-inbox display-4 d-block mb-3"></i>
                                    لا توجد مستخدمين مطابقين للبحث
                                </div>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar me-3">
                                            <div class="avatar-initial bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                <?= strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)) ?>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="fw-semibold"><?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?></div>
                                            <small class="text-muted"><?= htmlspecialchars($user['email']) ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $typeColors = [
                                        'admin' => 'primary',
                                        'doctor' => 'success',
                                        'pharmacist' => 'info',
                                        'patient' => 'warning'
                                    ];
                                    $typeNames = [
                                        'admin' => 'مدير',
                                        'doctor' => 'طبيب',
                                        'pharmacist' => 'صيدلي',
                                        'patient' => 'مريض'
                                    ];
                                    $color = $typeColors[$user['user_type']] ?? 'secondary';
                                    $name = $typeNames[$user['user_type']] ?? $user['user_type'];
                                    ?>
                                    <span class="badge bg-<?= $color ?>"><?= $name ?></span>
                                </td>
                                <td>
                                    <?php if ($user['user_type'] === 'doctor' && !empty($user['specialization'])): ?>
                                        <small class="text-muted"><?= htmlspecialchars($user['specialization']) ?></small>
                                    <?php elseif ($user['user_type'] === 'pharmacist' && !empty($user['pharmacy_name'])): ?>
                                        <small class="text-muted"><?= htmlspecialchars($user['pharmacy_name']) ?></small>
                                    <?php else: ?>
                                        <small class="text-muted">-</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?= DateHelper::formatArabic($user['created_at']) ?>
                                    </small>
                                </td>
                                <td>
                                    <?php if ($user['is_active']): ?>
                                        <span class="status-badge status-active">
                                            <i class="bi bi-check-circle-fill"></i>
                                            نشط
                                        </span>
                                    <?php else: ?>
                                        <span class="status-badge status-inactive">
                                            <i class="bi bi-x-circle-fill"></i>
                                            غير نشط
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewUser(<?= $user['id'] ?>)" title="عرض">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" onclick="editUser(<?= $user['id'] ?>)" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-<?= $user['is_active'] ? 'warning' : 'success' ?>" 
                                                onclick="toggleUserStatus(<?= $user['id'] ?>, <?= $user['is_active'] ? 'false' : 'true' ?>)" 
                                                title="<?= $user['is_active'] ? 'إلغاء التفعيل' : 'تفعيل' ?>">
                                            <i class="bi bi-<?= $user['is_active'] ? 'pause' : 'play' ?>"></i>
                                        </button>
                                        <?php if ($user['id'] != $this->currentUser['id']): ?>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(<?= $user['id'] ?>)" title="حذف">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- التنقل بين الصفحات -->
    <?php if ($totalPages > 1): ?>
        <div class="card-footer">
            <nav aria-label="تنقل الصفحات">
                <ul class="pagination justify-content-center mb-0">
                    <?php if ($currentPage > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $currentPage - 1 ?>&<?= http_build_query($filters) ?>">
                                <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++): ?>
                        <li class="page-item <?= $i === $currentPage ? 'active' : '' ?>">
                            <a class="page-link" href="?page=<?= $i ?>&<?= http_build_query($filters) ?>"><?= $i ?></a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($currentPage < $totalPages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?= $currentPage + 1 ?>&<?= http_build_query($filters) ?>">
                                <i class="bi bi-chevron-left"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    <?php endif; ?>
</div>

<!-- مودال إضافة مستخدم جديد -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person-plus-fill me-2"></i>
                    إضافة مستخدم جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addUserForm" method="POST" action="<?= App::url('admin/users/store') ?>">
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label fw-semibold">الاسم الأول</label>
                            <input type="text" class="form-control" name="first_name" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-semibold">الاسم الأخير</label>
                            <input type="text" class="form-control" name="last_name" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-semibold">البريد الإلكتروني</label>
                            <input type="email" class="form-control" name="email" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-semibold">رقم الهاتف</label>
                            <input type="tel" class="form-control" name="phone" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-semibold">نوع المستخدم</label>
                            <select class="form-select" name="user_type" id="userType" required>
                                <option value="">اختر النوع</option>
                                <option value="admin">مدير</option>
                                <option value="doctor">طبيب</option>
                                <option value="pharmacist">صيدلي</option>
                                <option value="patient">مريض</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-semibold">كلمة المرور</label>
                            <input type="password" class="form-control" name="password" required>
                        </div>

                        <!-- حقول إضافية للطبيب -->
                        <div id="doctorFields" class="col-12 d-none">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-success">
                                        <i class="bi bi-heart-pulse me-2"></i>
                                        معلومات الطبيب
                                    </h6>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label class="form-label">التخصص</label>
                                            <input type="text" class="form-control" name="specialization">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">رقم الترخيص</label>
                                            <input type="text" class="form-control" name="license_number">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">سنوات الخبرة</label>
                                            <input type="number" class="form-control" name="years_of_experience" min="0">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- حقول إضافية للصيدلي -->
                        <div id="pharmacistFields" class="col-12 d-none">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-info">
                                        <i class="bi bi-capsule me-2"></i>
                                        معلومات الصيدلي
                                    </h6>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label class="form-label">اسم الصيدلية</label>
                                            <input type="text" class="form-control" name="pharmacy_name">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">رقم الترخيص</label>
                                            <input type="text" class="form-control" name="license_number">
                                        </div>
                                        <div class="col-12">
                                            <label class="form-label">عنوان الصيدلية</label>
                                            <textarea class="form-control" name="pharmacy_address" rows="2"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-lg me-2"></i>
                        إضافة المستخدم
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- مودال عرض تفاصيل المستخدم -->
<div class="modal fade" id="viewUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person-circle me-2"></i>
                    تفاصيل المستخدم
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetailsContent">
                <!-- سيتم تحميل المحتوى ديناميكياً -->
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // إظهار/إخفاء الحقول الإضافية حسب نوع المستخدم
    const userTypeSelect = document.getElementById('userType');
    const doctorFields = document.getElementById('doctorFields');
    const pharmacistFields = document.getElementById('pharmacistFields');

    userTypeSelect.addEventListener('change', function() {
        doctorFields.classList.add('d-none');
        pharmacistFields.classList.add('d-none');

        if (this.value === 'doctor') {
            doctorFields.classList.remove('d-none');
        } else if (this.value === 'pharmacist') {
            pharmacistFields.classList.remove('d-none');
        }
    });

    // معالجة إرسال النموذج
    document.getElementById('addUserForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;

        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الإضافة...';

        fetch(this.action, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم إضافة المستخدم بنجاح', 'success');
                bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert(data.message || 'حدث خطأ أثناء إضافة المستخدم', 'danger');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'danger');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });
});

// عرض تفاصيل المستخدم
function viewUser(userId) {
    const modal = new bootstrap.Modal(document.getElementById('viewUserModal'));
    const content = document.getElementById('userDetailsContent');

    content.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div></div>';
    modal.show();

    fetch(`<?= App::url('admin/users/') ?>${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                content.innerHTML = data.html;
            } else {
                content.innerHTML = '<div class="alert alert-danger">حدث خطأ في تحميل البيانات</div>';
            }
        })
        .catch(error => {
            content.innerHTML = '<div class="alert alert-danger">حدث خطأ في الاتصال</div>';
        });
}

// تعديل المستخدم
function editUser(userId) {
    window.location.href = `<?= App::url('admin/users/edit/') ?>${userId}`;
}

// تبديل حالة المستخدم
function toggleUserStatus(userId, newStatus) {
    const action = newStatus ? 'تفعيل' : 'إلغاء تفعيل';

    if (confirm(`هل أنت متأكد من ${action} هذا المستخدم؟`)) {
        fetch('<?= App::url('admin/users/toggle-status') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId,
                is_active: newStatus
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert(data.message || 'حدث خطأ', 'danger');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'danger');
        });
    }
}

// حذف المستخدم
function deleteUser(userId) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        fetch(`<?= App::url('admin/users/delete/') ?>${userId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert(data.message || 'حدث خطأ', 'danger');
            }
        })
        .catch(error => {
            showAlert('حدث خطأ في الاتصال', 'danger');
        });
    }
}

// تصدير المستخدمين
function exportUsers() {
    window.open('<?= App::url('admin/users/export') ?>', '_blank');
}

// عرض التنبيهات
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
