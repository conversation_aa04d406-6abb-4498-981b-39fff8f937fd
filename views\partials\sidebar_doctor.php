<!-- Sidebar for Doctor -->
<div class="sidebar-doctor">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <div class="sidebar-brand">
            <i class="bi bi-heart-pulse text-primary"></i>
            <span class="brand-text">HealthKey</span>
        </div>
        <button class="sidebar-toggle d-lg-none">
            <i class="bi bi-x-lg"></i>
        </button>
    </div>

    <!-- User Info -->
    <div class="sidebar-user">
        <div class="user-avatar">
            <i class="bi bi-person-circle"></i>
        </div>
        <div class="user-info">
            <div class="user-name">د. <?= $currentUser['first_name'] . ' ' . $currentUser['last_name'] ?></div>
            <div class="user-role">طبيب</div>
        </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="sidebar-nav">
        <ul class="nav flex-column">
            <!-- Dashboard Section -->
            <li class="nav-section">
                <span class="nav-section-title">الرئيسية</span>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('doctor/dashboard') ? 'active' : '' ?>" 
                   href="<?= App::url('doctor/dashboard') ?>" data-bs-toggle="tooltip" title="لوحة التحكم">
                    <i class="bi bi-speedometer2"></i>
                    <span>لوحة التحكم</span>
                </a>
            </li>

            <!-- Patient Management Section -->
            <li class="nav-section">
                <span class="nav-section-title">إدارة المرضى</span>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('doctor/patients') ? 'active' : '' ?>" 
                   href="<?= App::url('doctor/patients') ?>" data-bs-toggle="tooltip" title="قائمة المرضى">
                    <i class="bi bi-people"></i>
                    <span>المرضى</span>
                    <span class="nav-badge bg-success"><?= isset($stats['patients']['total']) ? $stats['patients']['total'] : 0 ?></span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('doctor/medical-records') ? 'active' : '' ?>" 
                   href="<?= App::url('doctor/medical-records') ?>" data-bs-toggle="tooltip" title="السجلات الطبية">
                    <i class="bi bi-file-earmark-medical"></i>
                    <span>السجلات الطبية</span>
                </a>
            </li>

            <!-- Appointments Section -->
            <li class="nav-section">
                <span class="nav-section-title">المواعيد</span>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('doctor/appointments') ? 'active' : '' ?>" 
                   href="<?= App::url('doctor/appointments') ?>" data-bs-toggle="tooltip" title="إدارة المواعيد">
                    <i class="bi bi-calendar-check"></i>
                    <span>المواعيد</span>
                    <span class="nav-badge bg-info"><?= isset($stats['appointments']['today']) ? $stats['appointments']['today'] : 0 ?></span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('doctor/schedule') ? 'active' : '' ?>" 
                   href="<?= App::url('doctor/schedule') ?>" data-bs-toggle="tooltip" title="الجدول الزمني">
                    <i class="bi bi-calendar-week"></i>
                    <span>الجدول الزمني</span>
                </a>
            </li>

            <!-- Prescriptions Section -->
            <li class="nav-section">
                <span class="nav-section-title">الوصفات الطبية</span>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('doctor/prescriptions') ? 'active' : '' ?>" 
                   href="<?= App::url('doctor/prescriptions') ?>" data-bs-toggle="tooltip" title="الوصفات الطبية">
                    <i class="bi bi-prescription2"></i>
                    <span>الوصفات</span>
                    <span class="nav-badge bg-warning"><?= isset($stats['prescriptions']['total']) ? $stats['prescriptions']['total'] : 0 ?></span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('doctor/create-prescription') ? 'active' : '' ?>" 
                   href="<?= App::url('doctor/create-prescription') ?>" data-bs-toggle="tooltip" title="إنشاء وصفة جديدة">
                    <i class="bi bi-plus-circle"></i>
                    <span>وصفة جديدة</span>
                </a>
            </li>

            <!-- Communication Section -->
            <li class="nav-section">
                <span class="nav-section-title">التواصل</span>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('doctor/notifications') ? 'active' : '' ?>" 
                   href="<?= App::url('doctor/notifications') ?>" data-bs-toggle="tooltip" title="الإشعارات">
                    <i class="bi bi-bell"></i>
                    <span>الإشعارات</span>
                    <?php if (SessionHelper::get('unread_notifications', 0) > 0): ?>
                        <span class="nav-badge bg-danger"><?= SessionHelper::get('unread_notifications') ?></span>
                    <?php endif; ?>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('doctor/messages') ? 'active' : '' ?>" 
                   href="<?= App::url('doctor/messages') ?>" data-bs-toggle="tooltip" title="الرسائل">
                    <i class="bi bi-chat-dots"></i>
                    <span>الرسائل</span>
                </a>
            </li>

            <!-- Reports Section -->
            <li class="nav-section">
                <span class="nav-section-title">التقارير</span>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('doctor/reports') ? 'active' : '' ?>" 
                   href="<?= App::url('doctor/reports') ?>" data-bs-toggle="tooltip" title="التقارير">
                    <i class="bi bi-graph-up"></i>
                    <span>التقارير</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('doctor/analytics') ? 'active' : '' ?>" 
                   href="<?= App::url('doctor/analytics') ?>" data-bs-toggle="tooltip" title="التحليلات">
                    <i class="bi bi-bar-chart"></i>
                    <span>التحليلات</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('doctor/profile') ? 'active' : '' ?>" 
                   href="<?= App::url('doctor/profile') ?>" data-bs-toggle="tooltip" title="الملف الشخصي">
                    <i class="bi bi-person-gear"></i>
                    <span>الملف الشخصي</span>
                </a>
            </li>

            <!-- Logout Section -->
            <li class="nav-section">
                <span class="nav-section-title">الحساب</span>
            </li>

            <li class="nav-item">
                <a class="nav-link logout-link" href="<?= App::url('auth/logout') ?>" 
                   data-bs-toggle="tooltip" title="تسجيل الخروج" onclick="return confirm('هل أنت متأكد من تسجيل الخروج؟')">
                    <i class="bi bi-box-arrow-right"></i>
                    <span>تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
        <div class="system-status">
            <div class="status-item">
                <i class="bi bi-circle-fill text-success"></i>
                <span>متصل</span>
            </div>
            <div class="status-item">
                <i class="bi bi-clock"></i>
                <span><?= date('H:i') ?></span>
            </div>
        </div>
    </div>
</div>

<style>
/* Sidebar Doctor Styles */
.sidebar-doctor {
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    height: 100vh;
    width: 280px;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    box-shadow: -4px 0 20px rgba(0,0,0,0.15);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Sidebar Header */
.sidebar-header {
    padding: 1.5rem 1rem 1rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.sidebar-brand i {
    font-size: 1.5rem;
    color: #e74c3c;
}

.brand-text {
    font-size: 1.25rem;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: rgba(255,255,255,0.7);
    font-size: 1.1rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.sidebar-toggle:hover {
    color: #ffffff;
    background: rgba(255,255,255,0.1);
}

/* User Info */
.sidebar-user {
    padding: 1rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 1.25rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.user-info {
    flex: 1;
}

.user-name {
    font-weight: 600;
    color: #ffffff;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.user-role {
    font-size: 0.75rem;
    color: rgba(255,255,255,0.7);
}

/* Navigation Sections */
.nav-section {
    padding: 0.75rem 1rem 0.5rem;
    margin-top: 0.5rem;
}

.nav-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: rgba(255,255,255,0.5);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: 0.5rem;
}

/* Navigation Items */
.sidebar-nav {
    flex: 1;
    padding: 0.5rem 0;
}

.sidebar-doctor .nav-item {
    margin: 0.125rem 0.75rem;
}

.sidebar-doctor .nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.sidebar-doctor .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1));
    transition: width 0.3s ease;
}

.sidebar-doctor .nav-link:hover::before {
    width: 100%;
}

.sidebar-doctor .nav-link:hover {
    color: #ffffff;
    background: rgba(255,255,255,0.1);
    transform: translateX(-4px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.sidebar-doctor .nav-link.active {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
    transform: translateX(-4px);
}

.sidebar-doctor .nav-link.active::before {
    display: none;
}

.sidebar-doctor .nav-link i {
    font-size: 1.1rem;
    min-width: 20px;
    text-align: center;
}

.sidebar-doctor .nav-link span {
    flex: 1;
    font-weight: 500;
}

/* Navigation Badges */
.nav-badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.7rem;
    font-weight: 600;
    border-radius: 12px;
    background: rgba(255,255,255,0.2);
    color: #ffffff;
    min-width: 20px;
    text-align: center;
}

.nav-badge.bg-success {
    background: #198754 !important;
}

.nav-badge.bg-info {
    background: #0dcaf0 !important;
}

.nav-badge.bg-warning {
    background: #ffc107 !important;
    color: #212529;
}

.nav-badge.bg-danger {
    background: #dc3545 !important;
}

/* Logout Link */
.logout-link {
    color: #e74c3c !important;
    transition: all 0.3s ease;
}

.logout-link:hover {
    color: #c0392b !important;
    background: rgba(231, 76, 60, 0.1) !important;
}

.logout-link i {
    color: #e74c3c;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid rgba(255,255,255,0.1);
    background: rgba(0,0,0,0.1);
}

.system-status {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: rgba(255,255,255,0.8);
}

.status-item i {
    font-size: 0.7rem;
}

.status-item .text-success {
    color: #28a745 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar-doctor {
        transform: translateX(100%);
        width: 280px;
        position: fixed;
        top: 0;
        right: 0;
        z-index: 1050;
    }
    
    .sidebar-doctor.show {
        transform: translateX(0);
        box-shadow: -4px 0 20px rgba(0,0,0,0.3);
    }
    
    .sidebar-doctor.collapsed {
        transform: translateX(100%);
    }
}

/* Scrollbar Styling */
.sidebar-doctor::-webkit-scrollbar {
    width: 6px;
}

.sidebar-doctor::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
    border-radius: 3px;
}

.sidebar-doctor::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
}

.sidebar-doctor::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5);
}
</style>

<script>
// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Auto-hide sidebar on mobile when clicking a link
document.querySelectorAll('.sidebar-doctor .nav-link').forEach(link => {
    link.addEventListener('click', function() {
        if (window.innerWidth <= 768) {
            document.querySelector('.sidebar-doctor').classList.remove('show');
            document.body.classList.remove('sidebar-open');
        }
    });
});

// Update time every minute
setInterval(function() {
    const timeElement = document.querySelector('.status-item:last-child span');
    if (timeElement) {
        timeElement.textContent = new Date().toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}, 60000);
</script> 