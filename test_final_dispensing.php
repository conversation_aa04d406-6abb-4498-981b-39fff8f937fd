<?php
/**
 * اختبار نهائي لصفحة صرف الأدوية
 */

require_once 'config.php';

echo "<h1>اختبار نهائي لصفحة صرف الأدوية</h1>";

// اختبار تضمين الملفات
echo "<h2>1. اختبار تضمين الملفات</h2>";
try {
    $pharmacistController = new PharmacistController();
    echo "✅ تم تحميل PharmacistController بنجاح<br>";
} catch (Exception $e) {
    echo "❌ خطأ في تحميل PharmacistController: " . $e->getMessage() . "<br>";
}

try {
    $prescriptionModel = new Prescription();
    echo "✅ تم تحميل Prescription Model بنجاح<br>";
} catch (Exception $e) {
    echo "❌ خطأ في تحميل Prescription Model: " . $e->getMessage() . "<br>";
}

// اختبار قاعدة البيانات
echo "<h2>2. اختبار قاعدة البيانات</h2>";
try {
    $db = Database::getInstance();
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح<br>";
    
    // اختبار استعلام بسيط
    $result = $db->selectOne("SELECT COUNT(*) as count FROM prescriptions");
    echo "✅ عدد الوصفات في قاعدة البيانات: " . ($result['count'] ?? 0) . "<br>";
} catch (Exception $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
}

// اختبار المسارات
echo "<h2>3. اختبار المسارات</h2>";
$routes = [
    'pharmacist/dispensing' => 'صفحة صرف الأدوية',
    'pharmacist/prescriptions' => 'صفحة الوصفات',
    'pharmacist/dashboard' => 'لوحة التحكم',
    'pharmacist/dispensing-report' => 'تقرير الصرف'
];

foreach ($routes as $route => $description) {
    $url = App::url($route);
    echo "✅ $description: <a href='$url' target='_blank'>$url</a><br>";
}

// اختبار الدوال الجديدة
echo "<h2>4. اختبار الدوال الجديدة</h2>";
try {
    // اختبار دالة البحث في الوصفات للصرف
    $searchResults = $prescriptionModel->searchForDispensing('', 'active', 1);
    echo "✅ دالة searchForDispensing تعمل بنجاح<br>";
    
    // اختبار دالة الوصفات النشطة للصرف
    $activeResults = $prescriptionModel->getActiveForDispensing(1);
    echo "✅ دالة getActiveForDispensing تعمل بنجاح<br>";
    
    // اختبار دالة الوصفات المعلقة للصرف
    $pendingResults = $prescriptionModel->getPendingForDispensing(1);
    echo "✅ دالة getPendingForDispensing تعمل بنجاح<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في اختبار الدوال: " . $e->getMessage() . "<br>";
}

// اختبار DateHelper
echo "<h2>5. اختبار DateHelper</h2>";
try {
    $date = DateHelper::formatArabic('2024-01-15');
    echo "✅ DateHelper يعمل بنجاح: $date<br>";
} catch (Exception $e) {
    echo "❌ خطأ في DateHelper: " . $e->getMessage() . "<br>";
}

// اختبار وجود ملف العرض
echo "<h2>6. اختبار ملفات العرض</h2>";
$viewFiles = [
    'views/pharmacist/dispensing.php' => 'صفحة صرف الأدوية',
    'views/partials/sidebar_pharmacist.php' => 'القائمة الجانبية للصيدلي'
];

foreach ($viewFiles as $file => $description) {
    if (file_exists($file)) {
        echo "✅ $description موجود: $file<br>";
    } else {
        echo "❌ $description غير موجود: $file<br>";
    }
}

// روابط الاختبار
echo "<h2>7. روابط الاختبار</h2>";
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h3>روابط مهمة:</h3>";
echo "<ul>";
echo "<li><a href='http://localhost/HealthKey/pharmacist/dispensing' target='_blank' style='color: #007bff; text-decoration: none;'>🔗 صفحة صرف الأدوية</a></li>";
echo "<li><a href='http://localhost/HealthKey/pharmacist/dashboard' target='_blank' style='color: #007bff; text-decoration: none;'>🔗 لوحة تحكم الصيدلي</a></li>";
echo "<li><a href='http://localhost/HealthKey/pharmacist/prescriptions' target='_blank' style='color: #007bff; text-decoration: none;'>🔗 صفحة الوصفات</a></li>";
echo "<li><a href='http://localhost/HealthKey/pharmacist/dispensing-report' target='_blank' style='color: #007bff; text-decoration: none;'>🔗 تقرير الصرف</a></li>";
echo "</ul>";
echo "</div>";

// تعليمات الاستخدام
echo "<h2>8. تعليمات الاستخدام</h2>";
echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #007bff;'>";
echo "<h3>📋 كيفية استخدام صفحة صرف الأدوية:</h3>";
echo "<ol>";
echo "<li>تسجيل الدخول كصيدلي</li>";
echo "<li>الانتقال إلى صفحة صرف الأدوية من القائمة الجانبية</li>";
echo "<li>استخدام البحث والتصفية للعثور على الوصفات</li>";
echo "<li>النقر على أيقونة الكبسولة لصرف الدواء</li>";
echo "<li>إدخال الكمية المصروفة في النافذة المنبثقة</li>";
echo "<li>تأكيد عملية الصرف</li>";
echo "</ol>";
echo "</div>";

// ملخص النتائج
echo "<h2>✅ ملخص النتائج</h2>";
echo "<div style='background: #d4edda; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #28a745;'>";
echo "<h3>🎉 تم إنشاء صفحة صرف الأدوية بنجاح!</h3>";
echo "<p><strong>المميزات المضافة:</strong></p>";
echo "<ul>";
echo "<li>لوحة إحصائيات تفاعلية</li>";
echo "<li>بحث وتصفية متقدم</li>";
echo "<li>قائمة وصفات مفصلة</li>";
echo "<li>نافذة منبثقة لصرف الأدوية</li>";
echo "<li>تصميم حديث ومتجاوب</li>";
echo "<li>دعم كامل للغة العربية</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🏁 انتهى الاختبار</h2>";
echo "<p>يمكنك الآن الوصول إلى صفحة صرف الأدوية عبر الرابط: <a href='http://localhost/HealthKey/pharmacist/dispensing' target='_blank' style='color: #007bff; font-weight: bold;'>http://localhost/HealthKey/pharmacist/dispensing</a></p>";
?> 