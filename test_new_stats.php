<?php
// اختبار الإحصائيات الجديدة
require_once 'config.php';
require_once 'app/core/App.php';
require_once 'app/core/Database.php';
require_once 'app/controllers/AdminController.php';

// محاكاة تسجيل دخول المدير
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'admin';
$_SESSION['is_logged_in'] = true;

echo "<h1>اختبار الإحصائيات الجديدة</h1>";

try {
    // إنشاء كائن AdminController
    $adminController = new AdminController();
    
    // اختبار الإحصائيات الجديدة
    $startDate = '2025-01-01';
    $endDate = '2025-12-31';
    
    echo "<h2>إحصائيات الأداء</h2>";
    $performanceStats = $adminController->getPerformanceStats($startDate, $endDate);
    echo "<pre>" . print_r($performanceStats, true) . "</pre>";
    
    echo "<h2>الإحصائيات المالية</h2>";
    $financialStats = $adminController->getFinancialStats($startDate, $endDate);
    echo "<pre>" . print_r($financialStats, true) . "</pre>";
    
    echo "<h2>إحصائيات الجودة</h2>";
    $qualityStats = $adminController->getQualityStats($startDate, $endDate);
    echo "<pre>" . print_r($qualityStats, true) . "</pre>";
    
    echo "<h2>إحصائيات النشاط</h2>";
    $activityStats = $adminController->getActivityStats($startDate, $endDate);
    echo "<pre>" . print_r($activityStats, true) . "</pre>";
    
    echo "<h2>بيانات الرسوم البيانية</h2>";
    $chartsData = $adminController->getChartsData($startDate, $endDate);
    echo "<pre>" . print_r($chartsData, true) . "</pre>";
    
} catch (Exception $e) {
    echo "<h2>خطأ في الاختبار</h2>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "<p>الملف: " . $e->getFile() . "</p>";
    echo "<p>السطر: " . $e->getLine() . "</p>";
}
?> 