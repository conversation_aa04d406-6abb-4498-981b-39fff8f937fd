<?php
// محاكاة جلسة المدير
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'admin';
$_SESSION['user_name'] = 'مدير النظام';

// تضمين الملفات المطلوبة
require_once 'app/core/App.php';
require_once 'app/core/Database.php';
require_once 'app/controllers/AdminController.php';

// إنشاء مثيل من AdminController
$controller = new AdminController();

// اختبار دالة analytics مع AJAX
echo "<h2>اختبار طلبات AJAX للتحليلات</h2>";

try {
    // محاكاة طلب AJAX
    $_GET['action'] = 'data';
    $_GET['start_date'] = date('Y-m-d', strtotime('-30 days'));
    $_GET['end_date'] = date('Y-m-d');
    $_GET['type'] = 'overview';
    
    // محاكاة header AJAX
    $_SERVER['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest';
    
    echo "<h3>بيانات الاختبار:</h3>";
    echo "<ul>";
    echo "<li>Action: " . $_GET['action'] . "</li>";
    echo "<li>Start Date: " . $_GET['start_date'] . "</li>";
    echo "<li>End Date: " . $_GET['end_date'] . "</li>";
    echo "<li>Type: " . $_GET['type'] . "</li>";
    echo "<li>AJAX Header: " . $_SERVER['HTTP_X_REQUESTED_WITH'] . "</li>";
    echo "</ul>";
    
    // اختبار دالة analytics
    $reflection = new ReflectionClass($controller);
    $method = $reflection->getMethod('analytics');
    $method->setAccessible(true);
    
    // التقاط output
    ob_start();
    $method->invoke($controller);
    $output = ob_get_clean();
    
    echo "<h3>Output:</h3>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
    
    echo "<h3>✅ تم اختبار طلب AJAX بنجاح!</h3>";
    
} catch (Exception $e) {
    echo "<h3>❌ خطأ في الاختبار:</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "<p>الملف: " . $e->getFile() . "</p>";
    echo "<p>السطر: " . $e->getLine() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار طلبات AJAX للتحليلات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4>اختبار طلبات AJAX للتحليلات</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5>تعليمات الاختبار:</h5>
                            <ol>
                                <li>تأكد من أن قاعدة البيانات متصلة</li>
                                <li>تأكد من وجود بيانات في الجداول</li>
                                <li>راجع النتائج المعروضة أعلاه</li>
                                <li>إذا كانت النتائج صحيحة، يمكنك الوصول إلى صفحة التحليلات</li>
                            </ol>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                            <a href="<?= App::url('admin/analytics') ?>" class="btn btn-primary">
                                <i class="bi bi-graph-up"></i> الانتقال إلى صفحة التحليلات
                            </a>
                            <a href="<?= App::url('admin/dashboard') ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> العودة إلى لوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 