/* ملف الأنماط الرئيسي لنظام HealthKey */

/* استيراد الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* إعدادات عامة */
:root {
    --primary-color: #2c5aa0;
    --primary-dark: #1e3f73;
    --primary-light: #4a7bc8;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --white: #ffffff;

    /* تدرجات لونية جميلة */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
    --success-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    --danger-gradient: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);

    /* قيم التصميم */
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    --box-shadow-lg: 0 8px 30px rgba(0, 0, 0, 0.15);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* الخطوط */
    --font-family: 'Cairo', 'Tajawal', 'Segoe UI', 'Tahoma', 'Geneva', 'Verdana', sans-serif;
    --font-size-base: 16px;
    --font-size-sm: 14px;
    --font-size-lg: 18px;
    --font-size-xl: 24px;
}

/* إعدادات الخط والاتجاه */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: var(--font-size-base);
}

body {
    font-family: var(--font-family);
    direction: rtl;
    text-align: right;
    line-height: 1.7;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: var(--dark-color);
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* تحسين النصوص */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family);
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
    line-height: 1.7;
}

/* روابط محسنة */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--primary-dark);
    text-decoration: none;
}

/* تخصيص الألوان الأساسية */
.text-primary { color: var(--primary-color) !important; }
.bg-primary { background-color: var(--primary-color) !important; }

/* أزرار محسنة */
.btn {
    font-family: var(--font-family);
    font-weight: 500;
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
    font-size: var(--font-size-base);
    line-height: 1;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: var(--primary-gradient);
    color: var(--white);
    box-shadow: 0 4px 15px rgba(44, 90, 160, 0.3);
}

.btn-primary:hover {
    background: var(--secondary-gradient);
    box-shadow: 0 6px 20px rgba(44, 90, 160, 0.4);
    color: var(--white);
}

.btn-success {
    background: var(--success-gradient);
    color: var(--white);
    box-shadow: 0 4px 15px rgba(25, 135, 84, 0.3);
}

.btn-danger {
    background: var(--danger-gradient);
    color: var(--white);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--white);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: var(--font-size-sm);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: var(--font-size-lg);
}





/* المحتوى الرئيسي */
.main-content {
    padding: 2rem;
    min-height: calc(100vh - 56px);
}

/* البطاقات المحسنة */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
    background: var(--white);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.card-header {
    background: linear-gradient(135deg, rgba(44, 90, 160, 0.1) 0%, rgba(44, 90, 160, 0.05) 100%);
    border-bottom: 1px solid rgba(44, 90, 160, 0.1);
    font-weight: 600;
    padding: 1.25rem;
    color: var(--primary-color);
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background: rgba(248, 249, 250, 0.8);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1rem 1.5rem;
}

/* بطاقات إحصائية */
.stats-card {
    background: var(--white);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border-left: 4px solid var(--primary-color);
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--box-shadow-lg);
}

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stats-card .stats-label {
    color: var(--secondary-color);
    font-weight: 500;
    font-size: var(--font-size-sm);
}

/* الجداول المحسنة */
.table {
    margin-bottom: 0;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    border: none;
    font-weight: 600;
    padding: 1rem;
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    padding: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    vertical-align: middle;
}

.table-hover tbody tr {
    transition: var(--transition);
}

.table-hover tbody tr:hover {
    background-color: rgba(44, 90, 160, 0.05);
    transform: scale(1.01);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(248, 249, 250, 0.5);
}

/* النماذج المحسنة */
.form-label {
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--dark-color);
    font-size: var(--font-size-sm);
}

.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    transition: var(--transition);
    background: var(--white);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.15);
    outline: none;
    background: var(--white);
}

.form-control:hover, .form-select:hover {
    border-color: var(--primary-light);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.15);
}

.form-control.is-valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.15);
}

.invalid-feedback {
    display: block;
    font-size: var(--font-size-sm);
    color: var(--danger-color);
    margin-top: 0.5rem;
    font-weight: 500;
}

.valid-feedback {
    display: block;
    font-size: var(--font-size-sm);
    color: var(--success-color);
    margin-top: 0.5rem;
    font-weight: 500;
}

/* مجموعات النماذج */
.form-group {
    margin-bottom: 1.5rem;
}

.input-group {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.input-group-text {
    background: var(--light-color);
    border: 2px solid #e9ecef;
    color: var(--primary-color);
    font-weight: 500;
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
}

/* التنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.25rem;
}

.alert-dismissible .btn-close {
    padding: 1.25rem 1rem;
}

/* الشارات */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
}

/* إحصائيات لوحة التحكم */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-card .stats-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

.stats-card .stats-icon {
    font-size: 2.5rem;
    opacity: 0.3;
    position: absolute;
    top: 1rem;
    left: 1rem;
}

/* قائمة المهام */
.task-list .list-group-item {
    border: none;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem;
}

.task-list .list-group-item:last-child {
    border-bottom: none;
}

.task-priority-high {
    border-right: 4px solid var(--danger-color);
}

.task-priority-medium {
    border-right: 4px solid var(--warning-color);
}

.task-priority-low {
    border-right: 4px solid var(--success-color);
}

/* التقويم */
.calendar-day {
    min-height: 100px;
    border: 1px solid #dee2e6;
    padding: 0.5rem;
}

.calendar-day.today {
    background-color: rgba(44, 90, 160, 0.1);
}

.calendar-event {
    background-color: var(--primary-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
}

/* الرسوم البيانية */
.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 1.5rem;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* تحسينات للطباعة */
@media print {
    .btn,
    .alert {
        display: none !important;
    }
    
    .main-content {
        padding: 0;
        margin: 0;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* تخصيصات إضافية */
.text-muted {
    color: #6c757d !important;
}

.border-start {
    border-right: 1px solid #dee2e6 !important;
    border-left: none !important;
}

.border-end {
    border-left: 1px solid #dee2e6 !important;
    border-right: none !important;
}

.me-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
}

.ms-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

/* تحسينات إضافية للتجاوب */
@media (max-width: 576px) {
    h1 { font-size: 1.8rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.3rem; }

    .btn {
        padding: 0.6rem 1.2rem;
        font-size: var(--font-size-sm);
    }

    .card-body {
        padding: 1rem;
    }

    .stats-card .stats-number {
        font-size: 2rem;
    }

    .table th, .table td {
        padding: 0.5rem;
        font-size: var(--font-size-sm);
    }
}

@media (min-width: 992px) {
    .main-content {
        padding: 3rem;
    }

    .card:hover {
        transform: translateY(-8px);
    }
}

/* تأثيرات حديثة */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(50px); }
    to { opacity: 1; transform: translateX(0); }
}

/* تحسينات للإشعارات */
.toast {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    border: none;
}

.toast-header {
    background: var(--primary-color);
    color: var(--white);
    border-bottom: none;
}

/* تحسينات للمودال */
.modal-content {
    border-radius: var(--border-radius-lg);
    border: none;
    box-shadow: var(--box-shadow-lg);
}

.modal-header {
    background: var(--primary-gradient);
    color: var(--white);
    border-bottom: none;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    background: rgba(248, 249, 250, 0.5);
}

/* تحسينات للتنقل */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 1.5rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "←";
    color: var(--secondary-color);
}

/* تحسينات للشارات */
.badge {
    font-weight: 500;
    border-radius: var(--border-radius-sm);
    padding: 0.5rem 0.75rem;
}

/* تحسينات للقوائم المنسدلة */
.dropdown-menu {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow-lg);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    font-weight: 500;
}

.dropdown-item:hover {
    background: rgba(44, 90, 160, 0.1);
    color: var(--primary-color);
}

/* تحسينات للتقدم */
.progress {
    height: 8px;
    border-radius: var(--border-radius);
    background: rgba(44, 90, 160, 0.1);
}

.progress-bar {
    background: var(--primary-gradient);
    border-radius: var(--border-radius);
}

/* أنماط مخصصة للنظام */
.health-icon {
    width: 24px;
    height: 24px;
    fill: currentColor;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    font-size: var(--font-size-sm);
}

.status-active {
    background: rgba(25, 135, 84, 0.1);
    color: var(--success-color);
}

.status-inactive {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.status-pending {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

/* بطاقات الإحصائيات المحسنة */
.bg-gradient-primary {
    background: var(--primary-gradient) !important;
}

.bg-gradient-success {
    background: var(--success-gradient) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
}

.stats-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stats-label {
    font-size: 1rem;
    font-weight: 500;
    opacity: 0.9;
}

/* تحسينات للجداول */
.table-dashboard {
    background: var(--white);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table-dashboard th {
    background: var(--light-color);
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem;
}

.table-dashboard td {
    padding: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    vertical-align: middle;
}

/* تحسينات للرسوم البيانية */
.chart-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.chart-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

/* تحسينات للأزرار الصغيرة */
.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: var(--border-radius-sm);
}

.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: var(--border-radius-sm);
}

/* تحسينات للتنبيهات */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
}

.alert-primary {
    background: rgba(44, 90, 160, 0.1);
    color: var(--primary-color);
}

.alert-success {
    background: rgba(25, 135, 84, 0.1);
    color: var(--success-color);
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.alert-info {
    background: rgba(13, 202, 240, 0.1);
    color: var(--info-color);
}

/* تحسينات خاصة للوحة التحكم */
.admin-dashboard {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.dashboard-header {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    padding: 2rem;
    margin-bottom: 2rem;
    border-left: 4px solid var(--primary-color);
}

.dashboard-header h1 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.dashboard-header p {
    color: var(--secondary-color);
    margin-bottom: 0;
}

/* بطاقات الإحصائيات المحسنة للوحة التحكم */
.stats-card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
    margin-bottom: 2rem;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
    pointer-events: none;
}

.stats-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

.stats-card .card-body {
    position: relative;
    z-index: 1;
    padding: 2rem;
}

.stats-number {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.stats-label {
    font-size: 1.1rem;
    font-weight: 600;
    opacity: 0.95;
    margin-bottom: 1rem;
}

.stats-icon {
    width: 70px;
    height: 70px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.stats-card:hover .stats-icon {
    transform: rotate(10deg) scale(1.1);
    background: rgba(255, 255, 255, 0.3);
}

/* تحسينات للجداول في لوحة التحكم */
.dashboard-table {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: none;
    margin-bottom: 2rem;
}

.dashboard-table .card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border: none;
    padding: 1.5rem 2rem;
}

.dashboard-table .card-header h5 {
    margin: 0;
    font-weight: 600;
}

.dashboard-table .table thead th {
    background: var(--light-color);
    color: var(--dark-color);
    border: none;
    font-weight: 600;
    padding: 1.5rem 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.dashboard-table .table tbody td {
    padding: 1.25rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    vertical-align: middle;
    transition: all 0.3s ease;
}

.dashboard-table .table tbody tr:hover {
    background: rgba(44, 90, 160, 0.05);
    transform: scale(1.01);
}

/* تحسينات للرسوم البيانية */
.chart-container {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-bottom: 2rem;
    border: none;
}

.chart-header {
    border-bottom: 2px solid rgba(44, 90, 160, 0.1);
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.chart-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .dashboard-header {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .stats-card .card-body {
        padding: 1.5rem;
    }

    .stats-number {
        font-size: 2.5rem;
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .dashboard-table .card-header {
        padding: 1rem 1.5rem;
    }

    .dashboard-table .table thead th,
    .dashboard-table .table tbody td {
        padding: 1rem 0.5rem;
        font-size: 0.875rem;
    }
}

/* تأثيرات الحركة للوحة التحكم */
.dashboard-fade-in {
    animation: dashboardFadeIn 0.8s ease-out;
}

@keyframes dashboardFadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stats-card {
    animation: statsCardSlide 0.6s ease-out;
}

@keyframes statsCardSlide {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تحسينات للطباعة */
@media print {
    .stats-card {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }

    .btn, .alert-dismissible .btn-close {
        display: none !important;
    }

    .dashboard-header {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
