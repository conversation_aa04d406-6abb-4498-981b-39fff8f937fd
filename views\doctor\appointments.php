<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-calendar-check me-2"></i>
            المواعيد
        </h1>
        <p class="text-muted">إدارة مواعيد المرضى والجدول الزمني</p>
    </div>
    <div>
        <a href="<?= App::url('doctor/dashboard') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $todayCount ?></h4>
                        <p class="mb-0">مواعيد اليوم</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-day display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count(array_filter($appointments, function($a) { return $a['status'] === 'confirmed'; })) ?></h4>
                        <p class="mb-0">مواعيد مؤكدة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count(array_filter($appointments, function($a) { return $a['status'] === 'scheduled'; })) ?></h4>
                        <p class="mb-0">مواعيد مجدولة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count(array_filter($appointments, function($a) { return $a['status'] === 'completed'; })) ?></h4>
                        <p class="mb-0">مواعيد مكتملة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check2-all display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?= App::url('doctor/appointments') ?>" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">التاريخ</label>
                <input type="date" 
                       class="form-control" 
                       name="date" 
                       value="<?= htmlspecialchars($currentDate) ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label">الحالة</label>
                <select class="form-select" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="scheduled" <?= $currentStatus === 'scheduled' ? 'selected' : '' ?>>مجدول</option>
                    <option value="confirmed" <?= $currentStatus === 'confirmed' ? 'selected' : '' ?>>مؤكد</option>
                    <option value="completed" <?= $currentStatus === 'completed' ? 'selected' : '' ?>>مكتمل</option>
                    <option value="cancelled" <?= $currentStatus === 'cancelled' ? 'selected' : '' ?>>ملغي</option>
                    <option value="no_show" <?= $currentStatus === 'no_show' ? 'selected' : '' ?>>لم يحضر</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="text" 
                           class="form-control" 
                           name="search" 
                           placeholder="البحث في المواعيد...">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search me-1"></i>
                        بحث
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="<?= App::url('doctor/appointments') ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        إعادة تعيين
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Appointments List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-list-ul me-2"></i>
            قائمة المواعيد
            <span class="badge bg-primary ms-2"><?= count($appointments) ?></span>
        </h5>
        <div>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                <i class="bi bi-printer me-1"></i>
                طباعة
            </button>
            <button type="button" class="btn btn-sm btn-outline-success" onclick="exportAppointments()">
                <i class="bi bi-download me-1"></i>
                تصدير
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($appointments)): ?>
            <!-- Empty State -->
            <div class="text-center py-5">
                <i class="bi bi-calendar-x display-1 text-muted"></i>
                <h4 class="mt-3 text-muted">لا توجد مواعيد</h4>
                <p class="text-muted">
                    <?php if (!empty($currentDate) || !empty($currentStatus)): ?>
                        لم يتم العثور على مواعيد تطابق المعايير المحددة
                    <?php else: ?>
                        لم يتم جدولة أي مواعيد بعد
                    <?php endif; ?>
                </p>
                <a href="<?= App::url('doctor/appointments') ?>" class="btn btn-primary">
                    <i class="bi bi-arrow-clockwise me-2"></i>
                    عرض جميع المواعيد
                </a>
            </div>
        <?php else: ?>
            <!-- Appointments Table -->
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>المريض</th>
                            <th>التاريخ والوقت</th>
                            <th>السبب</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($appointments as $appointment): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                            <i class="bi bi-person text-primary"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?= htmlspecialchars($appointment['patient_name']) ?></h6>
                                            <small class="text-muted">
                                                <i class="bi bi-telephone me-1"></i>
                                                <?= htmlspecialchars($appointment['patient_phone'] ?? 'غير محدد') ?>
                                            </small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="badge bg-info mb-1">
                                            <?= date('Y-m-d', strtotime($appointment['appointment_date'])) ?>
                                        </div>
                                        <br>
                                        <small class="text-muted">
                                            <?= date('H:i', strtotime($appointment['appointment_time'])) ?>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="<?= htmlspecialchars($appointment['reason']) ?>">
                                        <?= htmlspecialchars($appointment['reason'] ?? 'غير محدد') ?>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $statusLabel = Appointment::getStatusLabel($appointment['status']);
                                    $statusColor = Appointment::getStatusColor($appointment['status']);
                                    ?>
                                    <span class="badge bg-<?= $statusColor ?>">
                                        <i class="bi bi-circle-fill me-1"></i>
                                        <?= $statusLabel ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <?php if ($appointment['status'] === 'scheduled'): ?>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-success" 
                                                    onclick="confirmAppointment(<?= $appointment['id'] ?>)" 
                                                    title="تأكيد الموعد">
                                                <i class="bi bi-check-circle"></i>
                                            </button>
                                        <?php endif; ?>
                                        
                                        <?php if (in_array($appointment['status'], ['scheduled', 'confirmed'])): ?>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-primary" 
                                                    onclick="completeAppointment(<?= $appointment['id'] ?>)" 
                                                    title="إكمال الموعد">
                                                <i class="bi bi-check2-all"></i>
                                            </button>
                                        <?php endif; ?>
                                        
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-info" 
                                                onclick="viewAppointment(<?= $appointment['id'] ?>)" 
                                                title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        
                                        <?php if (in_array($appointment['status'], ['scheduled', 'confirmed'])): ?>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    onclick="cancelAppointment(<?= $appointment['id'] ?>)" 
                                                    title="إلغاء الموعد">
                                                <i class="bi bi-x-circle"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Quick Actions -->
<?php if (!empty($appointments)): ?>
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="bi bi-calendar-plus display-4 mb-3"></i>
                    <h5>حجز موعد جديد</h5>
                    <p class="mb-3">حجز موعد جديد لمريض</p>
                    <a href="<?= App::url('doctor/schedule-appointment') ?>" class="btn btn-light">
                        <i class="bi bi-plus-circle me-2"></i>
                        حجز موعد
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="bi bi-file-earmark-medical display-4 mb-3"></i>
                    <h5>إضافة سجل طبي</h5>
                    <p class="mb-3">إضافة سجل طبي جديد</p>
                    <a href="<?= App::url('doctor/add-medical-record') ?>" class="btn btn-light">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة سجل
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <i class="bi bi-prescription2 display-4 mb-3"></i>
                    <h5>إنشاء وصفة طبية</h5>
                    <p class="mb-3">إنشاء وصفة طبية جديدة</p>
                    <a href="<?= App::url('doctor/create-prescription') ?>" class="btn btn-light">
                        <i class="bi bi-plus-circle me-2"></i>
                        إنشاء وصفة
                    </a>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- View Appointment Modal -->
<div class="modal fade" id="viewAppointmentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-calendar-check me-2"></i>
                    تفاصيل الموعد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="appointmentDetails">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="editAppointment()">
                    <i class="bi bi-pencil me-2"></i>
                    تعديل
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Complete Appointment Modal -->
<div class="modal fade" id="completeAppointmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-check2-all me-2"></i>
                    إكمال الموعد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="completeAppointmentForm">
                    <input type="hidden" id="completeAppointmentId" name="appointment_id">
                    <div class="mb-3">
                        <label class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" name="notes" rows="3" placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="submitCompleteAppointment()">
                    <i class="bi bi-check2-all me-2"></i>
                    إكمال الموعد
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentAppointmentId = null;

function confirmAppointment(appointmentId) {
    if (confirm('هل أنت متأكد من تأكيد هذا الموعد؟')) {
        fetch('<?= App::url('doctor/confirm-appointment') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'appointment_id=' + appointmentId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم تأكيد الموعد بنجاح');
                location.reload();
            } else {
                alert('فشل في تأكيد الموعد: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تأكيد الموعد');
        });
    }
}

function completeAppointment(appointmentId) {
    currentAppointmentId = appointmentId;
    document.getElementById('completeAppointmentId').value = appointmentId;
    new bootstrap.Modal(document.getElementById('completeAppointmentModal')).show();
}

function submitCompleteAppointment() {
    const form = document.getElementById('completeAppointmentForm');
    const formData = new FormData(form);
    
    fetch('<?= App::url('doctor/complete-appointment') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إكمال الموعد بنجاح');
            location.reload();
        } else {
            alert('فشل في إكمال الموعد: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء إكمال الموعد');
    });
}

function cancelAppointment(appointmentId) {
    if (confirm('هل أنت متأكد من إلغاء هذا الموعد؟')) {
        fetch('<?= App::url('doctor/cancel-appointment') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'appointment_id=' + appointmentId
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إلغاء الموعد بنجاح');
                location.reload();
            } else {
                alert('فشل في إلغاء الموعد: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إلغاء الموعد');
        });
    }
}

function viewAppointment(appointmentId) {
    currentAppointmentId = appointmentId;
    
    // تحميل تفاصيل الموعد
    fetch(`<?= App::url('doctor/view-appointment/') ?>${appointmentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('appointmentDetails').innerHTML = data.html;
                new bootstrap.Modal(document.getElementById('viewAppointmentModal')).show();
            } else {
                alert('فشل في تحميل تفاصيل الموعد');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحميل التفاصيل');
        });
}

function editAppointment() {
    if (currentAppointmentId) {
        window.location.href = `<?= App::url('doctor/edit-appointment/') ?>${currentAppointmentId}`;
    }
}

function exportAppointments() {
    const date = '<?= htmlspecialchars($currentDate) ?>';
    const status = '<?= htmlspecialchars($currentStatus) ?>';
    
    const url = `<?= App::url('doctor/export-appointments') ?>?date=${encodeURIComponent(date)}&status=${encodeURIComponent(status)}`;
    
    window.open(url, '_blank');
}

// تحسين الفلاتر
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.querySelector('input[name="date"]');
    const statusSelect = document.querySelector('select[name="status"]');
    
    [dateInput, statusSelect].forEach(element => {
        if (element) {
            element.addEventListener('change', function() {
                this.form.submit();
            });
        }
    });
});
</script> 