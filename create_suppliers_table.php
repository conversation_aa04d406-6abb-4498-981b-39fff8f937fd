<?php
/**
 * إنشاء جدول suppliers
 */

require_once 'config.php';
require_once 'app/core/Database.php';

echo "<h1>إنشاء جدول suppliers</h1>";

try {
    $db = Database::getInstance();
    
    // إنشاء جدول suppliers
    $sql = "
    CREATE TABLE IF NOT EXISTS suppliers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        contact_person VARCHAR(255),
        phone VARCHAR(50),
        email VARCHAR(255),
        address TEXT,
        website VARCHAR(255),
        notes TEXT,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $db->getConnection()->exec($sql);
    echo "✅ تم إنشاء جدول suppliers بنجاح<br>";
    
    // إضافة بيانات تجريبية
    $sampleSuppliers = [
        [
            'name' => 'شركة الأدوية الوطنية',
            'contact_person' => 'أحمد محمد',
            'phone' => '0501234567',
            'email' => '<EMAIL>',
            'address' => 'شارع الملك فهد، الرياض',
            'website' => 'www.national-pharma.com',
            'notes' => 'مورد رئيسي للأدوية الأساسية'
        ],
        [
            'name' => 'شركة الأدوية المتقدمة',
            'contact_person' => 'فاطمة علي',
            'phone' => '0509876543',
            'email' => '<EMAIL>',
            'address' => 'شارع التحلية، جدة',
            'website' => 'www.advanced-pharma.com',
            'notes' => 'متخصصة في الأدوية المتقدمة'
        ],
        [
            'name' => 'شركة الفيتامينات الصحية',
            'contact_person' => 'خالد عبدالله',
            'phone' => '**********',
            'email' => '<EMAIL>',
            'address' => 'شارع العليا، الرياض',
            'website' => 'www.health-vitamins.com',
            'notes' => 'متخصصة في المكملات الغذائية'
        ]
    ];
    
    foreach ($sampleSuppliers as $supplier) {
        $insertSql = "INSERT INTO suppliers (name, contact_person, phone, email, address, website, notes) 
                      VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $db->insert($insertSql, [
            $supplier['name'],
            $supplier['contact_person'],
            $supplier['phone'],
            $supplier['email'],
            $supplier['address'],
            $supplier['website'],
            $supplier['notes']
        ]);
    }
    
    echo "✅ تم إضافة البيانات التجريبية بنجاح<br>";
    
    // التحقق من إنشاء الجدول
    $count = $db->selectOne("SELECT COUNT(*) as count FROM suppliers")['count'];
    echo "✅ عدد الموردين في الجدول: $count<br>";
    
    // عرض البيانات المضافة
    echo "<h3>الموردين المضافة:</h3>";
    $suppliers = $db->select("SELECT * FROM suppliers ORDER BY id");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Contact Person</th><th>Phone</th><th>Email</th><th>Status</th></tr>";
    foreach ($suppliers as $supplier) {
        echo "<tr>";
        echo "<td>" . $supplier['id'] . "</td>";
        echo "<td>" . htmlspecialchars($supplier['name']) . "</td>";
        echo "<td>" . htmlspecialchars($supplier['contact_person']) . "</td>";
        echo "<td>" . htmlspecialchars($supplier['phone']) . "</td>";
        echo "<td>" . htmlspecialchars($supplier['email']) . "</td>";
        echo "<td>" . $supplier['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>✅ تم إنشاء جدول suppliers بنجاح!</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في إنشاء الجدول</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 