<?php
/**
 * اختبار نهائي لنظام الرسائل
 */

// تضمين الملفات المطلوبة
require_once 'config.php';
require_once 'app/models/Message.php';
require_once 'app/models/User.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار نهائي لنظام الرسائل</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { background-color: #f8f9fa; padding: 20px; }
        .test-card { margin-bottom: 20px; }
    </style>
</head>
<body>";

echo "<div class='container'>
    <div class='row'>
        <div class='col-12'>
            <div class='card test-card'>
                <div class='card-header bg-primary text-white'>
                    <h1 class='h3 mb-0'>اختبار نهائي لنظام الرسائل</h1>
                </div>
                <div class='card-body'>";

try {
    // إنشاء النماذج
    $messageModel = new Message();
    $userModel = new User();
    
    echo "<h2>1. اختبار الحصول على الرسائل الواردة</h2>";
    $inboxMessages = $messageModel->getInbox(2, false, 5);
    echo "<div class='alert alert-success'>عدد الرسائل الواردة: " . count($inboxMessages) . "</div>";
    
    if (!empty($inboxMessages)) {
        echo "<h4>أمثلة على الرسائل الواردة:</h4>";
        echo "<ul>";
        foreach (array_slice($inboxMessages, 0, 3) as $message) {
            echo "<li><strong>" . htmlspecialchars($message['subject']) . "</strong> - من: " . htmlspecialchars($message['sender_name']) . "</li>";
        }
        echo "</ul>";
    }
    
    echo "<h2>2. اختبار الحصول على الرسائل الصادرة</h2>";
    $sentMessages = $messageModel->getSent(2, 5);
    echo "<div class='alert alert-info'>عدد الرسائل الصادرة: " . count($sentMessages) . "</div>";
    
    echo "<h2>3. اختبار إحصائيات الرسائل</h2>";
    $stats = $messageModel->getStats(2);
    echo "<div class='alert alert-warning'>";
    echo "<strong>الإحصائيات:</strong><br>";
    echo "الواردة: " . ($stats['inbox'] ?? 0) . " | ";
    echo "الصادرة: " . ($stats['sent'] ?? 0) . " | ";
    echo "غير المقروءة: " . ($stats['unread'] ?? 0);
    echo "</div>";
    
    echo "<h2>4. اختبار إنشاء رسالة جديدة</h2>";
    $messageId = $messageModel->create(2, 1, 'رسالة اختبار نهائية', 'هذه رسالة اختبار نهائية من الطبيب', 'personal', 'normal');
    if ($messageId) {
        echo "<div class='alert alert-success'>✅ تم إنشاء رسالة جديدة برقم: " . $messageId . "</div>";
        
        // إرسال الرسالة
        if ($messageModel->send($messageId)) {
            echo "<div class='alert alert-success'>✅ تم إرسال الرسالة بنجاح</div>";
        } else {
            echo "<div class='alert alert-danger'>❌ فشل في إرسال الرسالة</div>";
        }
    } else {
        echo "<div class='alert alert-danger'>❌ فشل في إنشاء رسالة جديدة</div>";
    }
    
    echo "<h2>5. اختبار الحصول على المرضى والأطباء</h2>";
    $patients = $userModel->getPatientsByDoctor(2);
    $doctors = $userModel->getAllDoctors();
    echo "<div class='alert alert-info'>";
    echo "عدد المرضى: " . count($patients) . " | ";
    echo "عدد الأطباء: " . count($doctors);
    echo "</div>";
    
    echo "<div class='alert alert-success'>
        <h3>✅ جميع الاختبارات تمت بنجاح!</h3>
        <p>نظام الرسائل يعمل بشكل صحيح ويمكن الوصول إليه عبر:</p>
        <ul>
            <li><strong>صفحة الرسائل:</strong> <a href='doctor/messages' target='_blank'>doctor/messages</a></li>
            <li><strong>اختبار الرسائل:</strong> <a href='test_doctor_messages.php' target='_blank'>test_doctor_messages.php</a></li>
        </ul>
    </div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
        <h3>❌ خطأ في الاختبار</h3>
        <p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
        <p><strong>الملف:</strong> " . htmlspecialchars($e->getFile()) . "</p>
        <p><strong>السطر:</strong> " . $e->getLine() . "</p>
        <pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>
    </div>";
}

echo "</div>
        </div>
    </div>
</div>";

echo "</body></html>";
?> 