<?php
/**
 * اختبار صفحة العلامات الحيوية للمريض
 * Test Patient Vitals Page
 */

// تضمين ملف التكوين
require_once 'config.php';

// بدء الجلسة
SessionHelper::start();

// محاكاة تسجيل دخول المريض (استبدل بمعرف المريض الفعلي)
$testPatientId = 1; // استبدل بمعرف المريض الفعلي في قاعدة البيانات

// إنشاء بيانات المريض التجريبية
$testPatient = [
    'id' => $testPatientId,
    'first_name' => 'أحمد',
    'last_name' => 'محمد',
    'email' => '<EMAIL>',
    'user_type' => 'patient',
    'is_active' => 1
];

// تسجيل المريض في الجلسة
SessionHelper::set('user_id', $testPatientId);
SessionHelper::set('user_type', 'patient');
SessionHelper::set('current_user', $testPatient);

echo "<h2>اختبار صفحة العلامات الحيوية</h2>";
echo "<p>تم تسجيل دخول المريض: " . $testPatient['first_name'] . " " . $testPatient['last_name'] . "</p>";

// اختبار الوصول للصفحة
try {
    // إنشاء متحكم المريض
    $patientController = new PatientController();
    
    // استدعاء دالة العلامات الحيوية
    echo "<h3>اختبار دالة vitals()</h3>";
    
    // محاكاة استدعاء الدالة
    $patientId = $testPatientId;
    
    // إنشاء نموذج السجلات الطبية
    $medicalRecordModel = new MedicalRecord();
    
    // الحصول على السجلات الطبية للمريض
    $medicalRecords = $medicalRecordModel->getByPatient($patientId);
    
    echo "<p>عدد السجلات الطبية: " . count($medicalRecords) . "</p>";
    
    // تصفية السجلات التي تحتوي على العلامات الحيوية
    $vitalsRecords = [];
    foreach ($medicalRecords as $record) {
        if (!empty($record['vital_signs'])) {
            $vitals = json_decode($record['vital_signs'], true);
            if (is_array($vitals) && !empty($vitals)) {
                $record['formatted_vitals'] = MedicalRecord::formatVitalSigns($vitals);
                $vitalsRecords[] = $record;
            }
        }
    }
    
    echo "<p>عدد السجلات التي تحتوي على العلامات الحيوية: " . count($vitalsRecords) . "</p>";
    
    if (!empty($vitalsRecords)) {
        echo "<h4>تفاصيل العلامات الحيوية:</h4>";
        foreach ($vitalsRecords as $index => $record) {
            echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>";
            echo "<h5>السجل رقم " . ($index + 1) . "</h5>";
            echo "<p><strong>التاريخ:</strong> " . $record['visit_date'] . "</p>";
            echo "<p><strong>الطبيب:</strong> " . ($record['doctor_name'] ?? 'غير محدد') . "</p>";
            echo "<p><strong>العلامات الحيوية:</strong></p>";
            echo "<ul>";
            foreach ($record['formatted_vitals'] as $vitalName => $vitalValue) {
                echo "<li><strong>$vitalName:</strong> $vitalValue</li>";
            }
            echo "</ul>";
            echo "</div>";
        }
    } else {
        echo "<p style='color: orange;'>لا توجد علامات حيوية مسجلة لهذا المريض.</p>";
    }
    
    echo "<h3>اختبار الرابط</h3>";
    echo "<p>رابط صفحة العلامات الحيوية: <a href='" . App::url('patient/vitals') . "' target='_blank'>فتح صفحة العلامات الحيوية</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ في اختبار الصفحة: " . $e->getMessage() . "</p>";
    echo "<p>تفاصيل الخطأ: " . $e->getTraceAsString() . "</p>";
}

echo "<hr>";
echo "<h3>معلومات إضافية</h3>";
echo "<p>معرف المريض: $testPatientId</p>";
echo "<p>نوع المستخدم: " . SessionHelper::get('user_type') . "</p>";
echo "<p>حالة تسجيل الدخول: " . (SessionHelper::isLoggedIn() ? 'مسجل دخول' : 'غير مسجل دخول') . "</p>";

// إضافة زر للعودة
echo "<p><a href='index.php' class='btn btn-primary'>العودة للصفحة الرئيسية</a></p>";
?> 