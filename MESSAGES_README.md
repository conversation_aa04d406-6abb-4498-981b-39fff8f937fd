# صفحة الرسائل للمريض - HealthKey

## نظرة عامة
تم إنشاء صفحة الرسائل للمريض (`patient/messages`) لتوفير نظام تواصل شامل بين المرضى والأطباء مع إمكانية إدارة الرسائل الواردة والصادرة.

## الميزات الرئيسية

### 1. الصفحة الرئيسية للرسائل (`patient/messages`)
- **إحصائيات شاملة**: عرض الرسائل الواردة، الصادرة، غير المقروءة، وإجمالي الرسائل
- **بحث متقدم**: إمكانية البحث في محتوى الرسائل والموضوعات
- **فلترة ذكية**: تصفية الرسائل حسب النوع (الواردة، الصادرة، جميع الرسائل)
- **ترتيب ديناميكي**: عرض الرسائل مرتبة حسب التاريخ (الأحدث أولاً)
- **تفاعل مباشر**: إمكانية تسجيل الرسائل كمقروءة وحذفها
- **ترقيم الصفحات**: دعم للصفحات المتعددة للرسائل الكثيرة

### 2. صفحة عرض الرسالة (`patient/view-message`)
- **تفاصيل شاملة**: عرض جميع معلومات الرسالة (المرسل، المستقبل، التاريخ، الحالة)
- **محتوى منسق**: عرض محتوى الرسالة بتنسيق واضح ومقروء
- **إجراءات متقدمة**: إمكانية تسجيل الرسالة كمقروءة، الرد، والحذف
- **معلومات إضافية**: عرض نوع الرسالة، الأولوية، أوقات الإرسال والقراءة

### 3. صفحة إنشاء رسالة جديدة (`patient/compose-message`)
- **نموذج شامل**: حقول لجميع المعلومات المطلوبة (المستلم، الموضوع، المحتوى)
- **قوالب جاهزة**: قوالب مسبقة للرسائل الشائعة (مواعيد، وصفات طبية، أعراض)
- **معاينة مباشرة**: إمكانية معاينة الرسالة قبل الإرسال
- **حفظ المسودات**: حفظ الرسائل كمسودات للعودة إليها لاحقاً
- **عداد النص**: عداد للأحرف والكلمات في الرسالة
- **إرشادات مفيدة**: نصائح لكتابة رسائل فعالة

## الملفات المضافة/المعدلة

### 1. المتحكم (Controller)
- `app/controllers/PatientController.php` - أضفت دوال:
  - `messages()` - عرض قائمة الرسائل
  - `viewMessage()` - عرض رسالة واحدة
  - `composeMessage()` - إنشاء رسالة جديدة
  - `markMessageRead()` - تسجيل الرسالة كمقروءة
  - `deleteMessage()` - حذف الرسالة

### 2. الطرق (Routes)
- `app/core/App.php` - أضفت مسارات:
  - `patient/messages` - الصفحة الرئيسية للرسائل
  - `patient/view-message` - عرض رسالة واحدة
  - `patient/compose-message` - إنشاء رسالة جديدة
  - `patient/mark-message-read` - تسجيل الرسالة كمقروءة
  - `patient/delete-message` - حذف الرسالة

### 3. صفحات العرض (Views)
- `views/patient/messages.php` - الصفحة الرئيسية للرسائل
- `views/patient/view_message.php` - صفحة عرض الرسالة الواحدة
- `views/patient/compose_message.php` - صفحة إنشاء رسالة جديدة

## الميزات التقنية

### 1. التفاعل الديناميكي (AJAX)
- **تسجيل الرسائل كمقروءة**: تحديث فوري لحالة الرسالة
- **حذف الرسائل**: إزالة فورية من الواجهة
- **تحديث العدادات**: تحديث أعداد الرسائل في الشريط الجانبي
- **تنبيهات فورية**: رسائل نجاح وخطأ فورية

### 2. البحث والفلترة
- **بحث نصي**: البحث في الموضوع والمحتوى
- **فلترة حسب النوع**: الواردة، الصادرة، جميع الرسائل
- **ترقيم الصفحات**: دعم للصفحات المتعددة

### 3. قوالب الرسائل
- **قوالب جاهزة**: مواعيد، وصفات طبية، أعراض، أدوية، استفسارات عامة
- **تحميل سريع**: تحميل القوالب بنقرة واحدة
- **تخصيص**: إمكانية تعديل القوالب حسب الحاجة

### 4. حفظ المسودات
- **حفظ محلي**: حفظ المسودات في localStorage
- **استرجاع تلقائي**: تحميل المسودات المحفوظة عند فتح الصفحة
- **تنظيف تلقائي**: حذف المسودات بعد الإرسال الناجح

## الأمان والتحقق

### 1. التحقق من الملكية
- **تحقق من المرسل/المستقبل**: التأكد من أن المستخدم هو صاحب الرسالة
- **حماية من الوصول غير المصرح**: منع الوصول للرسائل الخاصة بالآخرين

### 2. التحقق من المدخلات
- **تحقق من الحقول المطلوبة**: التأكد من ملء جميع الحقول المطلوبة
- **تنظيف المدخلات**: حماية من XSS والهجمات الأخرى

### 3. التحقق من الطريقة
- **POST فقط**: التأكد من استخدام طريقة POST للعمليات الحساسة
- **CSRF Protection**: حماية من هجمات CSRF

## الاستخدام

### 1. عرض الرسائل
```
http://localhost/HealthKey/patient/messages
```

### 2. عرض رسالة واحدة
```
http://localhost/HealthKey/patient/view-message?id=1
```

### 3. إنشاء رسالة جديدة
```
http://localhost/HealthKey/patient/compose-message
```

## التطوير المستقبلي

### 1. الميزات المقترحة
- **إرفاق ملفات**: إمكانية إرفاق صور أو مستندات طبية
- **رسائل صوتية**: إرسال واستقبال رسائل صوتية
- **إشعارات فورية**: إشعارات فورية عند استلام رسائل جديدة
- **بحث متقدم**: بحث في التواريخ، المرسلين، وأنواع الرسائل

### 2. التحسينات التقنية
- **Pagination محسن**: تحسين أداء ترقيم الصفحات
- **Caching**: تخزين مؤقت للرسائل المتكررة
- **Real-time updates**: تحديثات فورية باستخدام WebSockets

## الدعم والمساعدة

للمساعدة في استخدام صفحة الرسائل أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

---

**تم تطوير هذه الصفحة باستخدام PHP MVC و Bootstrap مع دعم كامل للغة العربية.** 