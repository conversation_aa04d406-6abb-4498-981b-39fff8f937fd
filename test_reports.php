<?php
/**
 * ملف اختبار صفحة التقارير
 * Test file for Reports page
 */

// تضمين ملف الإعدادات
require_once 'config.php';

// بدء الجلسة
SessionHelper::start();

// محاكاة تسجيل دخول المدير
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'admin';
$_SESSION['is_logged_in'] = true;

// إعداد بيانات المستخدم الحالي
$currentUser = [
    'id' => 1,
    'first_name' => 'مدير',
    'last_name' => 'النظام',
    'email' => '<EMAIL>',
    'user_type' => 'admin'
];
SessionHelper::set('current_user', $currentUser);

// اختبار الوصول لصفحة التقارير
echo "<h1>اختبار صفحة التقارير</h1>";

// اختبار الوصول المباشر
$url = 'admin/reports';
echo "<p>اختبار الوصول إلى: $url</p>";

// محاكاة طلب GET
$_GET['url'] = $url;
$_GET['start_date'] = '2024-01-01';
$_GET['end_date'] = '2024-12-31';
$_GET['type'] = 'overview';

try {
    // تشغيل التطبيق
    $app = new App();
    echo "<p style='color: green;'>✓ تم تشغيل التطبيق بنجاح</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في تشغيل التطبيق: " . $e->getMessage() . "</p>";
}

// اختبار تصدير التقارير
echo "<h2>اختبار تصدير التقارير</h2>";

$exportUrls = [
    'admin/reports/export?type=overview&start_date=2024-01-01&end_date=2024-12-31',
    'admin/reports/export?type=users&start_date=2024-01-01&end_date=2024-12-31',
    'admin/reports/export?type=appointments&start_date=2024-01-01&end_date=2024-12-31',
    'admin/reports/export?type=prescriptions&start_date=2024-01-01&end_date=2024-12-31'
];

foreach ($exportUrls as $exportUrl) {
    echo "<p>اختبار تصدير: $exportUrl</p>";
    $_GET['url'] = $exportUrl;
    
    try {
        $app = new App();
        echo "<p style='color: green;'>✓ تم تصدير التقرير بنجاح</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ خطأ في تصدير التقرير: " . $e->getMessage() . "</p>";
    }
}

echo "<h2>اختبار الإحصائيات</h2>";

// اختبار إحصائيات المستخدمين
try {
    $userModel = new User();
    $userStats = $userModel->getStats();
    echo "<p style='color: green;'>✓ إحصائيات المستخدمين: " . json_encode($userStats) . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في إحصائيات المستخدمين: " . $e->getMessage() . "</p>";
}

// اختبار إحصائيات المواعيد
try {
    $appointmentModel = new Appointment();
    $appointmentStats = $appointmentModel->getStats();
    echo "<p style='color: green;'>✓ إحصائيات المواعيد: " . json_encode($appointmentStats) . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في إحصائيات المواعيد: " . $e->getMessage() . "</p>";
}

// اختبار إحصائيات الوصفات
try {
    $prescriptionModel = new Prescription();
    $prescriptionStats = $prescriptionModel->getStats();
    echo "<p style='color: green;'>✓ إحصائيات الوصفات: " . json_encode($prescriptionStats) . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ خطأ في إحصائيات الوصفات: " . $e->getMessage() . "</p>";
}

echo "<h2>النتيجة النهائية</h2>";
echo "<p>تم اختبار صفحة التقارير بنجاح!</p>";
echo "<p><a href='public/index.php?url=admin/reports' target='_blank'>فتح صفحة التقارير</a></p>";
?> 