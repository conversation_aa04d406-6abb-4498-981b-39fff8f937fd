<?php
require_once 'config.php';
require_once 'app/core/Database.php';
require_once 'app/models/User.php';

$db = Database::getInstance();
$userModel = new User();

echo "=== فحص بيانات المرضى والمواعيد ===\n\n";

// فحص جدول المواعيد
echo "1. عدد المواعيد في الجدول:\n";
$appointments = $db->select("SELECT COUNT(*) as count FROM appointments");
echo "المواعيد: " . $appointments[0]['count'] . "\n\n";

// فحص جدول المستخدمين المرضى
echo "2. عدد المرضى في الجدول:\n";
$patients = $db->select("SELECT COUNT(*) as count FROM users WHERE user_type = 'patient'");
echo "المرضى: " . $patients[0]['count'] . "\n\n";

// فحص المرضى النشطين
echo "3. عدد المرضى النشطين:\n";
$activePatients = $db->select("SELECT COUNT(*) as count FROM users WHERE user_type = 'patient' AND is_active = 1");
echo "المرضى النشطين: " . $activePatients[0]['count'] . "\n\n";

// فحص المواعيد مع الأطباء
echo "4. عدد المواعيد مع الأطباء:\n";
$doctorAppointments = $db->select("SELECT COUNT(*) as count FROM appointments WHERE doctor_id IS NOT NULL");
echo "المواعيد مع الأطباء: " . $doctorAppointments[0]['count'] . "\n\n";

// فحص المرضى حسب الطبيب (اختبار مع طبيب ID = 1)
echo "5. المرضى للطبيب ID = 1:\n";
$patientsByDoctor = $userModel->getPatientsByDoctor(1);
echo "عدد المرضى للطبيب 1: " . count($patientsByDoctor) . "\n";

if (!empty($patientsByDoctor)) {
    echo "أسماء المرضى:\n";
    foreach ($patientsByDoctor as $patient) {
        echo "- " . $patient['first_name'] . " " . $patient['last_name'] . "\n";
    }
} else {
    echo "لا توجد مرضى لهذا الطبيب\n";
}

echo "\n6. تفاصيل المواعيد:\n";
$appointmentDetails = $db->select("
    SELECT a.*, u.first_name, u.last_name, d.first_name as doctor_first_name, d.last_name as doctor_last_name
    FROM appointments a
    LEFT JOIN users u ON a.patient_id = u.id
    LEFT JOIN users d ON a.doctor_id = d.id
    LIMIT 5
");

foreach ($appointmentDetails as $appointment) {
    echo "موعد ID: " . $appointment['id'] . 
         " - المريض: " . $appointment['first_name'] . " " . $appointment['last_name'] .
         " - الطبيب: " . $appointment['doctor_first_name'] . " " . $appointment['doctor_last_name'] . "\n";
}
?> 