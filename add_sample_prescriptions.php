<?php
/**
 * إضافة وصفات طبية تجريبية
 */

require_once 'config.php';
require_once 'app/core/Database.php';

echo "<h1>إضافة وصفات طبية تجريبية</h1>";

try {
    $db = Database::getInstance();
    
    // إضافة وصفات طبية تجريبية
    $samplePrescriptions = [
        [
            'prescription_code' => 'PRES-2024-001',
            'patient_id' => 1,
            'doctor_id' => 1,
            'diagnosis' => 'حمى وألم في الحلق',
            'notes' => 'مريض يعاني من أعراض البرد',
            'status' => 'active',
            'created_at' => '2024-01-15 09:00:00'
        ],
        [
            'prescription_code' => 'PRES-2024-002',
            'patient_id' => 2,
            'doctor_id' => 1,
            'diagnosis' => 'قرحة في المعدة',
            'notes' => 'مريض يعاني من ألم في المعدة',
            'status' => 'active',
            'created_at' => '2024-01-16 10:30:00'
        ],
        [
            'prescription_code' => 'PRES-2024-003',
            'patient_id' => 3,
            'doctor_id' => 1,
            'diagnosis' => 'نقص فيتامين د',
            'notes' => 'مريض يعاني من نقص فيتامين د',
            'status' => 'active',
            'created_at' => '2024-01-17 11:15:00'
        ],
        [
            'prescription_code' => 'PRES-2024-004',
            'patient_id' => 4,
            'doctor_id' => 1,
            'diagnosis' => 'ألم في المفاصل',
            'notes' => 'مريض يعاني من ألم في المفاصل',
            'status' => 'active',
            'created_at' => '2024-01-18 14:20:00'
        ]
    ];
    
    $insertedCount = 0;
    foreach ($samplePrescriptions as $prescription) {
        try {
            $insertSql = "INSERT INTO prescriptions (
                prescription_code, patient_id, doctor_id, diagnosis, notes, status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $db->insert($insertSql, [
                $prescription['prescription_code'],
                $prescription['patient_id'],
                $prescription['doctor_id'],
                $prescription['diagnosis'],
                $prescription['notes'],
                $prescription['status'],
                $prescription['created_at']
            ]);
            $insertedCount++;
        } catch (Exception $e) {
            echo "⚠️ خطأ في إدراج الوصفة: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "✅ تم إدراج $insertedCount وصفة بنجاح<br>";
    
    // التحقق من عدد الوصفات
    $count = $db->selectOne("SELECT COUNT(*) as count FROM prescriptions")['count'];
    echo "✅ إجمالي عدد الوصفات في الجدول: $count<br>";
    
    // عرض الوصفات المضافة
    echo "<h3>الوصفات المضافة:</h3>";
    $prescriptions = $db->select("SELECT * FROM prescriptions ORDER BY id DESC LIMIT 10");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Prescription Code</th><th>Patient ID</th><th>Doctor ID</th><th>Diagnosis</th><th>Status</th></tr>";
    foreach ($prescriptions as $prescription) {
        echo "<tr>";
        echo "<td>" . $prescription['id'] . "</td>";
        echo "<td>" . htmlspecialchars($prescription['prescription_code']) . "</td>";
        echo "<td>" . $prescription['patient_id'] . "</td>";
        echo "<td>" . $prescription['doctor_id'] . "</td>";
        echo "<td>" . htmlspecialchars($prescription['diagnosis']) . "</td>";
        echo "<td>" . $prescription['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>✅ تم إضافة الوصفات الطبية بنجاح!</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في إضافة الوصفات</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 