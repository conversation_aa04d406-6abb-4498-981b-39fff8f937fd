<?php
require_once 'config.php';

echo "<h1>اختبار صفحة الرسائل للصيدلي</h1>";

try {
    // اختبار إنشاء متحكم الصيدلي
    echo "<h2>1. اختبار إنشاء متحكم الصيدلي</h2>";
    
    // محاكاة تسجيل دخول صيدلي
    session_start();
    $_SESSION['user_id'] = 1;
    $_SESSION['user_type'] = 'pharmacist';
    $_SESSION['user'] = [
        'id' => 1,
        'first_name' => 'أحمد',
        'last_name' => 'محمد',
        'email' => '<EMAIL>'
    ];
    
    $controller = new PharmacistController();
    echo "✅ تم إنشاء متحكم الصيدلي بنجاح<br>";
    
    // اختبار دالة messages
    echo "<h2>2. اختبار دالة messages</h2>";
    try {
        $controller->messages();
        echo "✅ دالة messages تعمل بنجاح<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في دالة messages: " . $e->getMessage() . "<br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    // اختبار دالة composeMessage
    echo "<h2>3. اختبار دالة composeMessage</h2>";
    try {
        $controller->composeMessage();
        echo "✅ دالة composeMessage تعمل بنجاح<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في دالة composeMessage: " . $e->getMessage() . "<br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    // اختبار دالة viewMessage
    echo "<h2>4. اختبار دالة viewMessage</h2>";
    try {
        $controller->viewMessage(1);
        echo "✅ دالة viewMessage تعمل بنجاح<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في دالة viewMessage: " . $e->getMessage() . "<br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    // اختبار دالة deleteMessage
    echo "<h2>5. اختبار دالة deleteMessage</h2>";
    try {
        $controller->deleteMessage(1);
        echo "✅ دالة deleteMessage تعمل بنجاح<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في دالة deleteMessage: " . $e->getMessage() . "<br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    // اختبار دالة updateMessageStatus
    echo "<h2>6. اختبار دالة updateMessageStatus</h2>";
    try {
        $_POST['message_id'] = 1;
        $_POST['status'] = 'read';
        $controller->updateMessageStatus();
        echo "✅ دالة updateMessageStatus تعمل بنجاح<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في دالة updateMessageStatus: " . $e->getMessage() . "<br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    // اختبار نموذج الرسائل
    echo "<h2>7. اختبار نموذج الرسائل</h2>";
    try {
        $messageModel = new Message();
        echo "✅ تم إنشاء نموذج الرسائل بنجاح<br>";
        
        // اختبار دالة getStats
        $stats = $messageModel->getStats(1);
        echo "✅ دالة getStats تعمل بنجاح: " . json_encode($stats, JSON_UNESCAPED_UNICODE) . "<br>";
        
        // اختبار دالة getInbox
        $inbox = $messageModel->getInbox(1, false, 5);
        echo "✅ دالة getInbox تعمل بنجاح: " . count($inbox) . " رسائل<br>";
        
        // اختبار دالة getSent
        $sent = $messageModel->getSent(1, 5);
        echo "✅ دالة getSent تعمل بنجاح: " . count($sent) . " رسائل<br>";
        
        // اختبار دالة search
        $searchResults = $messageModel->search('رسالة', 1, 'all');
        echo "✅ دالة search تعمل بنجاح: " . count($searchResults) . " نتائج<br>";
        
    } catch (Exception $e) {
        echo "❌ خطأ في نموذج الرسائل: " . $e->getMessage() . "<br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    // اختبار المسارات
    echo "<h2>8. اختبار المسارات</h2>";
    $routes = [
        'pharmacist/messages' => 'الصفحة الرئيسية للرسائل',
        'pharmacist/compose-message' => 'إنشاء رسالة جديدة',
        'pharmacist/view-message' => 'عرض رسالة',
        'pharmacist/delete-message' => 'حذف رسالة',
        'pharmacist/update-message-status' => 'تحديث حالة الرسالة'
    ];
    
    foreach ($routes as $route => $description) {
        $url = App::url($route);
        echo "✅ مسار $description: $url<br>";
    }
    
    echo "<h2>✅ انتهى الاختبار بنجاح</h2>";
    echo "<p>جميع وظائف الرسائل تعمل بشكل صحيح!</p>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 