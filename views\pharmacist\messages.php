<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">الرسائل</h1>
        <p class="text-muted">إدارة الرسائل والمراسلات</p>
    </div>
    <div>
        <a href="<?= App::url('pharmacist/compose-message') ?>" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>
            رسالة جديدة
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['total_messages'] ?? 0 ?></h4>
                        <p class="mb-0">إجمالي الرسائل</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-envelope display-6 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['unread_messages'] ?? 0 ?></h4>
                        <p class="mb-0">رسائل غير مقروءة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-envelope-open display-6 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['sent_messages'] ?? 0 ?></h4>
                        <p class="mb-0">رسائل مرسلة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-send display-6 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['draft_messages'] ?? 0 ?></h4>
                        <p class="mb-0">مسودات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-file-earmark-text display-6 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['trash_messages'] ?? 0 ?></h4>
                        <p class="mb-0">سلة المحذوفات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-trash display-6 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-secondary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['important_messages'] ?? 0 ?></h4>
                        <p class="mb-0">رسائل مهمة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-star display-6 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- البحث والفلترة -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-search me-2"></i>
                    البحث في الرسائل
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" action="<?= App::url('pharmacist/messages') ?>" class="row g-3">
                    <div class="col-md-6">
                        <input type="text" name="search" class="form-control" 
                               placeholder="البحث في الرسائل..."
                               value="<?= htmlspecialchars($search) ?>">
                    </div>
                    <div class="col-md-4">
                        <select name="type" class="form-select">
                            <option value="inbox" <?= $currentType === 'inbox' ? 'selected' : '' ?>>الوارد</option>
                            <option value="sent" <?= $currentType === 'sent' ? 'selected' : '' ?>>المرسل</option>
                            <option value="draft" <?= $currentType === 'draft' ? 'selected' : '' ?>>المسودات</option>
                            <option value="trash" <?= $currentType === 'trash' ? 'selected' : '' ?>>سلة المحذوفات</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-search"></i>
                            بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>إجمالي الرسائل:</span>
                    <span class="badge bg-primary"><?= $stats['total_messages'] ?? 0 ?></span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>غير مقروءة:</span>
                    <span class="badge bg-success"><?= $stats['unread_messages'] ?? 0 ?></span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>مرسلة:</span>
                    <span class="badge bg-info"><?= $stats['sent_messages'] ?? 0 ?></span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>مسودات:</span>
                    <span class="badge bg-warning"><?= $stats['draft_messages'] ?? 0 ?></span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>مهمة:</span>
                    <span class="badge bg-secondary"><?= $stats['important_messages'] ?? 0 ?></span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قائمة الرسائل -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-list-ul me-2"></i>
            <?php
            switch ($currentType) {
                case 'sent':
                    echo 'الرسائل المرسلة';
                    break;
                case 'draft':
                    echo 'المسودات';
                    break;
                case 'trash':
                    echo 'سلة المحذوفات';
                    break;
                default:
                    echo 'الرسائل الواردة';
                    break;
            }
            ?>
        </h5>
        <span class="badge bg-primary"><?= $totalMessages ?> رسالة</span>
    </div>
    <div class="card-body">
        <?php if (!empty($messages)): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>المرسل/المستلم</th>
                            <th>الموضوع</th>
                            <th>النوع</th>
                            <th>الأولوية</th>
                            <th>التاريخ</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($messages as $message): ?>
                            <tr class="<?= $message['read_at'] === null ? 'table-warning' : '' ?>">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <i class="bi bi-person-circle text-primary"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-3">
                                            <strong><?= htmlspecialchars($message['sender_name'] ?? 'غير محدد') ?></strong>
                                            <br><small class="text-muted">إلى: <?= htmlspecialchars($message['recipient_name'] ?? 'غير محدد') ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong><?= htmlspecialchars($message['subject']) ?></strong>
                                        <?php if (!empty($message['content'])): ?>
                                            <br><small class="text-muted"><?= htmlspecialchars(substr($message['content'], 0, 50)) ?>...</small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?= htmlspecialchars($message['type']) ?></span>
                                </td>
                                <td>
                                    <?php
                                    $priorityClass = 'bg-success';
                                    if ($message['priority'] === 'high') $priorityClass = 'bg-danger';
                                    elseif ($message['priority'] === 'medium') $priorityClass = 'bg-warning';
                                    ?>
                                    <span class="badge <?= $priorityClass ?>"><?= htmlspecialchars($message['priority']) ?></span>
                                </td>
                                <td>
                                    <div>
                                        <strong><?= date('Y-m-d', strtotime($message['created_at'])) ?></strong>
                                        <br><small class="text-muted"><?= date('H:i', strtotime($message['created_at'])) ?></small>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($message['read_at'] === null): ?>
                                        <span class="badge bg-warning">غير مقروءة</span>
                                    <?php else: ?>
                                        <span class="badge bg-success">مقروءة</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= App::url('pharmacist/view-message/' . $message['id']) ?>" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <?php if ($currentType !== 'trash'): ?>
                                            <a href="<?= App::url('pharmacist/delete-message/' . $message['id']) ?>" 
                                               class="btn btn-sm btn-outline-danger"
                                               onclick="return confirm('هل أنت متأكد من حذف هذه الرسالة؟')">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- ترقيم الصفحات -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="ترقيم الصفحات" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($currentPage > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= App::url('pharmacist/messages') ?>?type=<?= $currentType ?>&page=<?= $currentPage - 1 ?>">
                                    السابق
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?= $i === $currentPage ? 'active' : '' ?>">
                                <a class="page-link" href="<?= App::url('pharmacist/messages') ?>?type=<?= $currentType ?>&page=<?= $i ?>">
                                    <?= $i ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        
                        <?php if ($currentPage < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= App::url('pharmacist/messages') ?>?type=<?= $currentType ?>&page=<?= $currentPage + 1 ?>">
                                    التالي
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
            
        <?php else: ?>
            <div class="text-center py-5">
                <i class="bi bi-envelope display-1 text-muted"></i>
                <h4 class="mt-3">لا توجد رسائل</h4>
                <p class="text-muted">لم يتم العثور على رسائل مطابقة لمعايير البحث</p>
                <a href="<?= App::url('pharmacist/compose-message') ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    إنشاء رسالة جديدة
                </a>
            </div>
        <?php endif; ?>
    </div>
</div> 