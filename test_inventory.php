<?php
/**
 * اختبار صفحة إدارة المخزون
 */

require_once 'config.php';
require_once 'app/core/App.php';
require_once 'app/core/Database.php';
require_once 'app/controllers/PharmacistController.php';

echo "<h1>اختبار صفحة إدارة المخزون</h1>";

try {
    // اختبار إنشاء متحكم الصيدلي
    echo "<h2>اختبار إنشاء متحكم الصيدلي</h2>";
    $controller = new PharmacistController();
    echo "✅ تم إنشاء متحكم الصيدلي بنجاح<br>";
    
    // اختبار دالة إحصائيات المخزون
    echo "<h2>اختبار إحصائيات المخزون</h2>";
    $stats = $controller->getInventoryStats();
    echo "<pre>";
    print_r($stats);
    echo "</pre>";
    echo "✅ تم الحصول على إحصائيات المخزون بنجاح<br>";
    
    // اختبار دالة الحصول على عناصر المخزون
    echo "<h2>اختبار الحصول على عناصر المخزون</h2>";
    $inventory = $controller->getInventoryItems('', '', '');
    echo "✅ تم الحصول على " . count($inventory) . " عنصر من المخزون<br>";
    
    // اختبار دالة الحصول على الفئات
    echo "<h2>اختبار الحصول على فئات المخزون</h2>";
    $categories = $controller->getInventoryCategories();
    echo "✅ تم الحصول على " . count($categories) . " فئة<br>";
    echo "<pre>";
    print_r($categories);
    echo "</pre>";
    
    echo "<h2>✅ تم اختبار جميع دوال المخزون بنجاح!</h2>";
    echo "<p>يمكنك الآن الوصول لصفحة إدارة المخزون:</p>";
    echo "<a href='index.php?url=pharmacist/inventory' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>فتح صفحة إدارة المخزون</a>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في الاختبار</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 