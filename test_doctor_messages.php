<?php
/**
 * ملف اختبار رسائل الطبيب
 * يمكن الوصول إليه عبر: http://localhost/HealthKey/test_doctor_messages.php
 */

// تضمين ملفات النظام
require_once 'app/core/App.php';
require_once 'app/helpers/SessionHelper.php';
require_once 'app/models/User.php';
require_once 'app/models/Message.php';

// محاكاة تسجيل دخول الطبيب
session_start();
$_SESSION['user_id'] = 2; // افتراض أن الطبيب له معرف 2
$_SESSION['user_type'] = 'doctor';
$_SESSION['user_name'] = 'د. أحمد محمد';

// تحميل النماذج
$userModel = new User();
$messageModel = new Message();

// الحصول على بيانات الطبيب
$doctor = $userModel->findById(2);

// إنشاء رسائل اختبار
$testMessages = [
    [
        'sender_id' => 1, // مدير النظام
        'recipient_id' => 2, // الطبيب
        'subject' => 'تحديث النظام',
        'content' => 'تم تحديث نظام HealthKey إلى الإصدار الجديد مع ميزات جديدة ومحسنة.',
        'type' => 'system',
        'priority' => 'high',
        'status' => 'sent'
    ],
    [
        'sender_id' => 1, // مدير النظام
        'recipient_id' => 2, // الطبيب
        'subject' => 'تذكير بالمواعيد',
        'content' => 'يرجى مراجعة المواعيد المعلقة لهذا الأسبوع والتأكد من تحديث السجلات الطبية.',
        'type' => 'reminder',
        'priority' => 'normal',
        'status' => 'sent'
    ],
    [
        'sender_id' => 2, // الطبيب
        'recipient_id' => 1, // مريض
        'subject' => 'نتائج الفحص',
        'content' => 'مرحباً، نتائج فحصك متاحة الآن. يرجى مراجعة الطبيب في أقرب وقت ممكن.',
        'type' => 'notification',
        'priority' => 'high',
        'status' => 'sent'
    ],
    [
        'sender_id' => 1, // مدير النظام
        'recipient_id' => 2, // الطبيب
        'subject' => 'إحصائيات الأسبوع',
        'content' => 'إحصائيات الأسبوع الماضي متاحة الآن في لوحة التحكم. يرجى مراجعتها.',
        'type' => 'announcement',
        'priority' => 'normal',
        'status' => 'read'
    ],
    [
        'sender_id' => 2, // الطبيب
        'recipient_id' => 3, // مريض آخر
        'subject' => 'تحديث الوصفة الطبية',
        'content' => 'تم تحديث وصفتك الطبية. يرجى مراجعة الصيدلية لصرف الأدوية الجديدة.',
        'type' => 'personal',
        'priority' => 'normal',
        'status' => 'sent'
    ]
];

// إنشاء رسائل اختبار في قاعدة البيانات
foreach ($testMessages as $message) {
    $messageModel->create(
        $message['sender_id'],
        $message['recipient_id'],
        $message['subject'],
        $message['content'],
        $message['type'],
        $message['priority']
    );
    
    // إرسال الرسالة إذا كانت مرسلة
    if ($message['status'] === 'sent') {
        $messageModel->send($messageModel->getLastInsertId());
    }
    
    // تحديد كمقروءة إذا كانت مقروءة
    if ($message['status'] === 'read') {
        $messageModel->markAsRead($messageModel->getLastInsertId());
    }
}

// الحصول على رسائل الطبيب
$inboxMessages = $messageModel->getInbox($_SESSION['user_id'], false, 10);
$sentMessages = $messageModel->getSent($_SESSION['user_id'], 10);
$allMessages = $messageModel->getByUser($_SESSION['user_id'], 'all', 20);
$unreadCount = $messageModel->getUnreadCount($_SESSION['user_id']);
$stats = $messageModel->getStats($_SESSION['user_id']);

// الحصول على قوائم المستخدمين
$patients = $userModel->getPatientsByDoctor($_SESSION['user_id']);
$doctors = $userModel->getAllDoctors();

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رسائل الطبيب - HealthKey</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .test-header { background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%); }
        .test-card { border: none; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
        .avatar-lg { width: 80px; height: 80px; }
        .message-preview { max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container py-4">
        <!-- Header -->
        <div class="test-header text-white p-4 rounded mb-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-2">
                        <i class="bi bi-chat-dots me-2"></i>
                        اختبار رسائل الطبيب
                    </h1>
                    <p class="mb-0 opacity-75">اختبار نظام إدارة الرسائل</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex align-items-center justify-content-md-end">
                        <div class="me-3">
                            <small class="d-block opacity-75">الطبيب المسجل</small>
                            <strong><?= $_SESSION['user_name'] ?></strong>
                        </div>
                        <div class="avatar-lg bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center">
                            <i class="bi bi-person-badge text-white fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="row">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-check-circle me-2"></i>
                            نتائج الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>معلومات الطبيب:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>الاسم:</strong> <?= $doctor ? User::getFullName($doctor) : 'غير محدد' ?></li>
                                    <li><strong>البريد الإلكتروني:</strong> <?= $doctor['email'] ?? 'غير محدد' ?></li>
                                    <li><strong>التخصص:</strong> <?= $doctor['specialization'] ?? 'غير محدد' ?></li>
                                    <li><strong>عدد المرضى:</strong> <?= count($patients) ?></li>
                                    <li><strong>الرسائل الواردة:</strong> <?= count($inboxMessages) ?></li>
                                    <li><strong>الرسائل الصادرة:</strong> <?= count($sentMessages) ?></li>
                                    <li><strong>الرسائل غير المقروءة:</strong> <?= $unreadCount ?></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>روابط الاختبار:</h6>
                                <div class="d-grid gap-2">
                                    <a href="doctor/messages" class="btn btn-success">
                                        <i class="bi bi-chat-dots me-2"></i>
                                        صفحة الرسائل
                                    </a>
                                    <a href="doctor/dashboard" class="btn btn-primary">
                                        <i class="bi bi-speedometer2 me-2"></i>
                                        لوحة التحكم
                                    </a>
                                    <a href="doctor/notifications" class="btn btn-outline-primary">
                                        <i class="bi bi-bell me-2"></i>
                                        الإشعارات
                                    </a>
                                </div>
                                
                                <hr>
                                
                                <h6>الميزات المختبرة:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle text-success me-2"></i>عرض الرسائل الواردة</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>عرض الرسائل الصادرة</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>البحث في الرسائل</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>فلترة حسب النوع والحالة</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>إنشاء رسالة جديدة</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>تحديد كمقروء</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>حذف الرسائل</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>إحصائيات الرسائل</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Messages Preview -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-inbox me-2"></i>
                            الرسائل الواردة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="message-preview">
                            <?php if (empty($inboxMessages)): ?>
                                <div class="text-center py-4">
                                    <i class="bi bi-inbox text-muted display-4"></i>
                                    <h6 class="text-muted mt-2">لا توجد رسائل واردة</h6>
                                </div>
                            <?php else: ?>
                                <div class="list-group list-group-flush">
                                    <?php foreach (array_slice($inboxMessages, 0, 5) as $message): ?>
                                        <div class="list-group-item">
                                            <div class="d-flex align-items-start">
                                                <div class="flex-shrink-0 me-3">
                                                    <div class="bg-<?= Message::getTypeColor($message['type']) ?> bg-opacity-10 rounded-circle p-2">
                                                        <i class="bi <?= Message::getTypeIcon($message['type']) ?> text-<?= Message::getTypeColor($message['type']) ?>"></i>
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1">
                                                        <?= htmlspecialchars($message['subject']) ?>
                                                        <?php if ($message['status'] === 'sent'): ?>
                                                            <span class="badge bg-danger ms-2">جديد</span>
                                                        <?php endif; ?>
                                                    </h6>
                                                    <p class="mb-1 text-muted"><?= htmlspecialchars(substr($message['content'], 0, 80)) ?>...</p>
                                                    <small class="text-muted">
                                                        من: <?= htmlspecialchars($message['sender_name']) ?>
                                                        <span class="mx-2">•</span>
                                                        <?= Message::formatTime($message['created_at']) ?>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-send me-2"></i>
                            الرسائل الصادرة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="message-preview">
                            <?php if (empty($sentMessages)): ?>
                                <div class="text-center py-4">
                                    <i class="bi bi-send text-muted display-4"></i>
                                    <h6 class="text-muted mt-2">لا توجد رسائل صادرة</h6>
                                </div>
                            <?php else: ?>
                                <div class="list-group list-group-flush">
                                    <?php foreach (array_slice($sentMessages, 0, 5) as $message): ?>
                                        <div class="list-group-item">
                                            <div class="d-flex align-items-start">
                                                <div class="flex-shrink-0 me-3">
                                                    <div class="bg-<?= Message::getTypeColor($message['type']) ?> bg-opacity-10 rounded-circle p-2">
                                                        <i class="bi <?= Message::getTypeIcon($message['type']) ?> text-<?= Message::getTypeColor($message['type']) ?>"></i>
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1"><?= htmlspecialchars($message['subject']) ?></h6>
                                                    <p class="mb-1 text-muted"><?= htmlspecialchars(substr($message['content'], 0, 80)) ?>...</p>
                                                    <small class="text-muted">
                                                        إلى: <?= htmlspecialchars($message['recipient_name']) ?>
                                                        <span class="mx-2">•</span>
                                                        <?= Message::formatTime($message['created_at']) ?>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up me-2"></i>
                            إحصائيات الرسائل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <h4 class="text-primary"><?= $stats['inbox'] ?? 0 ?></h4>
                                <small class="text-muted">الواردة</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-warning"><?= $stats['unread'] ?? 0 ?></h4>
                                <small class="text-muted">غير مقروءة</small>
                            </div>
                        </div>
                        <hr>
                        <div class="row text-center">
                            <div class="col-6">
                                <h5 class="text-success"><?= $stats['sent'] ?? 0 ?></h5>
                                <small class="text-muted">الصادرة</small>
                            </div>
                            <div class="col-6">
                                <h5 class="text-info"><?= $stats['system'] ?? 0 ?></h5>
                                <small class="text-muted">النظام</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-warning text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-gear me-2"></i>
                            إجراءات الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-success" onclick="createTestMessage()">
                                <i class="bi bi-plus-circle me-2"></i>
                                إنشاء رسالة اختبار
                            </button>
                            <button type="button" class="btn btn-primary" onclick="markAllAsRead()">
                                <i class="bi bi-check-all me-2"></i>
                                تحديد الكل كمقروء
                            </button>
                            <button type="button" class="btn btn-danger" onclick="deleteAllMessages()">
                                <i class="bi bi-trash me-2"></i>
                                حذف جميع الرسائل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            تعليمات الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>انقر على "صفحة الرسائل" لفتح صفحة الرسائل الكاملة</li>
                            <li>اختبر عرض الرسائل الواردة والصادرة في التبويبات المختلفة</li>
                            <li>اختبر البحث في الرسائل باستخدام الكلمات المفتاحية</li>
                            <li>اختبر فلترة الرسائل حسب النوع والحالة</li>
                            <li>اختبر إنشاء رسالة جديدة باستخدام النموذج</li>
                            <li>اختبر تحديد الرسائل كمقروءة</li>
                            <li>اختبر حذف الرسائل الفردية والمتعددة</li>
                            <li>اختبر عرض تفاصيل الرسالة في النافذة المنبثقة</li>
                            <li>اختبر الرد على الرسائل</li>
                            <li>تحقق من تحديث عداد الرسائل غير المقروءة</li>
                            <li>تحقق من عرض الإحصائيات بشكل صحيح</li>
                        </ol>
                        
                        <div class="alert alert-info">
                            <i class="bi bi-lightbulb me-2"></i>
                            <strong>نصيحة:</strong> يمكنك إنشاء رسائل اختبار إضافية باستخدام الأزرار في قسم "إجراءات الاختبار".
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>ملاحظة:</strong> تأكد من أن جميع الوظائف تعمل بشكل صحيح قبل الانتقال إلى الاختبار التالي.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-bell display-4 mb-3"></i>
                        <h5>الإشعارات</h5>
                        <p class="mb-3">إدارة الإشعارات والتنبيهات</p>
                        <a href="doctor/notifications" class="btn btn-light">
                            <i class="bi bi-bell me-2"></i>
                            الإشعارات
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-calendar-check display-4 mb-3"></i>
                        <h5>المواعيد</h5>
                        <p class="mb-3">إدارة مواعيد المرضى</p>
                        <a href="doctor/appointments" class="btn btn-light">
                            <i class="bi bi-calendar me-2"></i>
                            المواعيد
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-clipboard2-pulse display-4 mb-3"></i>
                        <h5>السجلات الطبية</h5>
                        <p class="mb-3">إدارة السجلات الطبية للمرضى</p>
                        <a href="doctor/medical-records" class="btn btn-light">
                            <i class="bi bi-clipboard2-pulse me-2"></i>
                            السجلات
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="text-center text-muted">
                    <small>
                        <i class="bi bi-code-slash me-1"></i>
                        نظام HealthKey - اختبار رسائل الطبيب
                        <span class="mx-2">|</span>
                        <i class="bi bi-calendar me-1"></i>
                        <?= date('Y-m-d H:i') ?>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    function createTestMessage() {
        const types = ['personal', 'notification', 'reminder', 'alert', 'announcement'];
        const subjects = [
            'رسالة اختبار شخصية',
            'إشعار اختبار جديد',
            'تذكير اختبار مهم',
            'تنبيه اختبار عاجل',
            'إعلان اختبار عام'
        ];
        const contents = [
            'هذه رسالة اختبار شخصية للطبيب.',
            'هذا إشعار اختبار جديد من النظام.',
            'هذا تذكير اختبار مهم للمتابعة.',
            'هذا تنبيه اختبار عاجل يتطلب الاهتمام.',
            'هذا إعلان اختبار عام لجميع المستخدمين.'
        ];
        
        const randomIndex = Math.floor(Math.random() * types.length);
        const type = types[randomIndex];
        const subject = subjects[randomIndex];
        const content = contents[randomIndex];
        
        fetch('test_create_message.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                sender_id: <?= $_SESSION['user_id'] ?>,
                recipient_id: 1, // مدير النظام
                subject: subject,
                content: content,
                type: type,
                priority: 'normal'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إنشاء رسالة اختبار جديدة بنجاح!');
                location.reload();
            } else {
                alert('خطأ في إنشاء الرسالة');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('خطأ في الاتصال');
        });
    }
    
    function markAllAsRead() {
        if (!confirm('هل تريد تحديد جميع الرسائل كمقروءة؟')) {
            return;
        }
        
        fetch('test_mark_all_messages_read.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: <?= $_SESSION['user_id'] ?>
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم تحديد جميع الرسائل كمقروءة بنجاح!');
                location.reload();
            } else {
                alert('خطأ في تحديث الرسائل');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('خطأ في الاتصال');
        });
    }
    
    function deleteAllMessages() {
        if (!confirm('هل تريد حذف جميع الرسائل؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            return;
        }
        
        fetch('test_delete_all_messages.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: <?= $_SESSION['user_id'] ?>
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حذف جميع الرسائل بنجاح!');
                location.reload();
            } else {
                alert('خطأ في حذف الرسائل');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('خطأ في الاتصال');
        });
    }
    </script>
</body>
</html> 