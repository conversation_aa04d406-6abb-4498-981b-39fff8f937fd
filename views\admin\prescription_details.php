<div class="prescription-details">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h4 class="mb-2">
                <i class="bi bi-prescription2 me-2 text-primary"></i>
                تفاصيل الوصفة الطبية
            </h4>
            <p class="text-muted mb-0">كود الوصفة: <strong><?= htmlspecialchars($prescription['prescription_code']) ?></strong></p>
        </div>
        <div class="col-md-4 text-md-end">
            <div class="d-flex flex-column align-items-md-end">
                <span class="badge bg-primary fs-6 mb-2"><?= htmlspecialchars($prescription['prescription_code']) ?></span>
                <small class="text-muted">تاريخ الإصدار: <?= date('Y-m-d', strtotime($prescription['issue_date'])) ?></small>
            </div>
        </div>
    </div>

    <!-- Patient and Doctor Info -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="bi bi-person me-2"></i>
                        معلومات المريض
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="avatar-lg bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                            <i class="bi bi-person text-primary fs-4"></i>
                        </div>
                        <div>
                            <h6 class="mb-1"><?= htmlspecialchars($prescription['patient_name']) ?></h6>
                            <small class="text-muted">رقم المريض: <?= $prescription['patient_id'] ?></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                        <i class="bi bi-person-badge me-2"></i>
                        معلومات الطبيب
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="avatar-lg bg-success bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                            <i class="bi bi-person-badge text-success fs-4"></i>
                        </div>
                        <div>
                            <h6 class="mb-1"><?= htmlspecialchars($prescription['doctor_name']) ?></h6>
                            <small class="text-muted"><?= htmlspecialchars($prescription['doctor_specialization'] ?? 'طب عام') ?></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Diagnosis and Notes -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-clipboard2-pulse me-2"></i>
                        التشخيص
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($prescription['diagnosis'])): ?>
                        <p class="mb-0"><?= nl2br(htmlspecialchars($prescription['diagnosis'])) ?></p>
                    <?php else: ?>
                        <p class="text-muted mb-0">لا يوجد تشخيص محدد</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-journal-text me-2"></i>
                        الملاحظات
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($prescription['notes'])): ?>
                        <p class="mb-0"><?= nl2br(htmlspecialchars($prescription['notes'])) ?></p>
                    <?php else: ?>
                        <p class="text-muted mb-0">لا توجد ملاحظات</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Dates and Status -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="bi bi-calendar-event me-2"></i>
                        تاريخ الإصدار
                    </h6>
                </div>
                <div class="card-body text-center">
                    <h5 class="mb-0"><?= date('Y-m-d', strtotime($prescription['issue_date'])) ?></h5>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="bi bi-calendar-x me-2"></i>
                        تاريخ الانتهاء
                    </h6>
                </div>
                <div class="card-body text-center">
                    <?php 
                    $expiryDate = strtotime($prescription['expiry_date']);
                    $isExpired = $expiryDate < time();
                    $isNearExpiry = $expiryDate < strtotime('+7 days');
                    ?>
                    <h5 class="mb-0 <?= $isExpired ? 'text-danger' : ($isNearExpiry ? 'text-warning' : 'text-success') ?>">
                        <?= date('Y-m-d', $expiryDate) ?>
                    </h5>
                    <?php if ($isExpired): ?>
                        <small class="text-danger">منتهية الصلاحية</small>
                    <?php elseif ($isNearExpiry): ?>
                        <small class="text-warning">تنتهي قريباً</small>
                    <?php else: ?>
                        <small class="text-success">صالحة</small>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-secondary">
                <div class="card-header bg-secondary text-white">
                    <h6 class="mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        الحالة
                    </h6>
                </div>
                <div class="card-body text-center">
                    <?php
                    $statusClass = match($prescription['status']) {
                        'active' => 'bg-success',
                        'dispensed' => 'bg-info',
                        'expired' => 'bg-danger',
                        'cancelled' => 'bg-secondary',
                        default => 'bg-secondary'
                    };
                    $statusText = match($prescription['status']) {
                        'active' => 'نشطة',
                        'dispensed' => 'موزعة',
                        'expired' => 'منتهية',
                        'cancelled' => 'ملغية',
                        default => 'غير محدد'
                    };
                    ?>
                    <span class="badge <?= $statusClass ?> fs-6"><?= $statusText ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Medications -->
    <?php if (!empty($medications)): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-capsule me-2"></i>
                    الأدوية الموصوفة
                </h6>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>اسم الدواء</th>
                                <th>الجرعة</th>
                                <th>التكرار</th>
                                <th>المدة</th>
                                <th>الكمية المطلوبة</th>
                                <th>الكمية الموزعة</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($medications as $medication): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <i class="bi bi-capsule text-primary"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?= htmlspecialchars($medication['medication_name']) ?></div>
                                                <small class="text-muted"><?= htmlspecialchars($medication['generic_name'] ?? '') ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?= htmlspecialchars($medication['dosage']) ?></td>
                                    <td><?= htmlspecialchars($medication['frequency']) ?></td>
                                    <td><?= htmlspecialchars($medication['duration']) ?></td>
                                    <td>
                                        <span class="badge bg-primary"><?= $medication['quantity'] ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?= $medication['dispensed_quantity'] ?? 0 ?></span>
                                    </td>
                                    <td>
                                        <?php
                                        $medStatusClass = match($medication['status']) {
                                            'pending' => 'bg-warning',
                                            'dispensed' => 'bg-success',
                                            'partial' => 'bg-info',
                                            'cancelled' => 'bg-danger',
                                            default => 'bg-secondary'
                                        };
                                        $medStatusText = match($medication['status']) {
                                            'pending' => 'في الانتظار',
                                            'dispensed' => 'موزعة',
                                            'partial' => 'موزعة جزئياً',
                                            'cancelled' => 'ملغية',
                                            default => 'غير محدد'
                                        };
                                        ?>
                                        <span class="badge <?= $medStatusClass ?>"><?= $medStatusText ?></span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="card mb-4">
            <div class="card-body text-center py-4">
                <i class="bi bi-capsule text-muted fs-1 d-block mb-2"></i>
                <p class="text-muted mb-0">لا توجد أدوية موصوفة في هذه الوصفة</p>
            </div>
        </div>
    <?php endif; ?>

    <!-- Actions -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <small class="text-muted">
                        <i class="bi bi-clock me-1"></i>
                        آخر تحديث: <?= date('Y-m-d H:i', strtotime($prescription['updated_at'] ?? $prescription['created_at'])) ?>
                    </small>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-primary" onclick="printPrescription()">
                        <i class="bi bi-printer me-1"></i>
                        طباعة
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="editPrescription(<?= $prescription['id'] ?>)">
                        <i class="bi bi-pencil me-1"></i>
                        تعديل
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="deletePrescription(<?= $prescription['id'] ?>)">
                        <i class="bi bi-trash me-1"></i>
                        حذف
                    </button>
                </div>
            </div>
        </div>
    </div>
</div> 