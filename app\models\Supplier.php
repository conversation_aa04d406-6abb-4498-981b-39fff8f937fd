<?php

/**
 * نموذج إدارة الموردين
 */
class Supplier
{
    private $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * الحصول على جميع الموردين
     */
    public function getAll($limit = null, $offset = 0)
    {
        $sql = "SELECT * FROM suppliers ORDER BY name ASC";
        
        if ($limit) {
            $sql .= " LIMIT $limit OFFSET $offset";
        }
        
        return $this->db->select($sql);
    }

    /**
     * الحصول على مورد بواسطة المعرف
     */
    public function findById($id)
    {
        $sql = "SELECT * FROM suppliers WHERE id = ?";
        return $this->db->selectOne($sql, [$id]);
    }

    /**
     * البحث في الموردين
     */
    public function search($query, $category = '')
    {
        $sql = "SELECT * FROM suppliers WHERE 1=1";
        $params = [];

        if (!empty($query)) {
            $sql .= " AND (name LIKE ? OR contact_person LIKE ? OR email LIKE ? OR phone LIKE ?)";
            $searchTerm = "%$query%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        if (!empty($category)) {
            $sql .= " AND category = ?";
            $params[] = $category;
        }

        $sql .= " ORDER BY name ASC";
        
        return $this->db->select($sql, $params);
    }

    /**
     * إضافة مورد جديد
     */
    public function create($data)
    {
        $sql = "INSERT INTO suppliers (name, contact_person, email, phone, address, 
                category, website, notes, status, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $params = [
            $data['name'],
            $data['contact_person'],
            $data['email'],
            $data['phone'],
            $data['address'],
            $data['category'],
            $data['website'] ?? '',
            $data['notes'] ?? '',
            $data['status'] ?? 'active'
        ];

        return $this->db->insert($sql, $params);
    }

    /**
     * تحديث معلومات المورد
     */
    public function update($id, $data)
    {
        $sql = "UPDATE suppliers SET 
                name = ?, contact_person = ?, email = ?, phone = ?, 
                address = ?, category = ?, website = ?, notes = ?, 
                status = ?, updated_at = NOW() 
                WHERE id = ?";
        
        $params = [
            $data['name'],
            $data['contact_person'],
            $data['email'],
            $data['phone'],
            $data['address'],
            $data['category'],
            $data['website'] ?? '',
            $data['notes'] ?? '',
            $data['status'] ?? 'active',
            $id
        ];

        return $this->db->update($sql, $params);
    }

    /**
     * حذف مورد
     */
    public function delete($id)
    {
        $sql = "DELETE FROM suppliers WHERE id = ?";
        return $this->db->delete($sql, [$id]);
    }

    /**
     * الحصول على الموردين النشطين
     */
    public function getActive($limit = 10)
    {
        $sql = "SELECT * FROM suppliers WHERE status = 'active' ORDER BY name ASC";
        
        if ($limit) {
            $sql .= " LIMIT $limit";
        }
        
        return $this->db->select($sql);
    }

    /**
     * الحصول على إحصائيات الموردين
     */
    public function getStats()
    {
        $stats = [];

        // إجمالي عدد الموردين
        $sql = "SELECT COUNT(*) as total FROM suppliers";
        $result = $this->db->selectOne($sql);
        $stats['total_suppliers'] = $result['total'];

        // الموردين النشطين
        $sql = "SELECT COUNT(*) as active FROM suppliers WHERE status = 'active'";
        $result = $this->db->selectOne($sql);
        $stats['active_suppliers'] = $result['active'];

        // الموردين غير النشطين
        $sql = "SELECT COUNT(*) as inactive FROM suppliers WHERE status = 'inactive'";
        $result = $this->db->selectOne($sql);
        $stats['inactive_suppliers'] = $result['inactive'];

        // الموردين الجدد هذا الشهر
        $sql = "SELECT COUNT(*) as new_this_month FROM suppliers 
                WHERE MONTH(created_at) = MONTH(CURDATE()) 
                AND YEAR(created_at) = YEAR(CURDATE())";
        $result = $this->db->selectOne($sql);
        $stats['new_this_month'] = $result['new_this_month'];

        return $stats;
    }

    /**
     * الحصول على فئات الموردين
     */
    public function getCategories()
    {
        $sql = "SELECT DISTINCT category FROM suppliers WHERE category IS NOT NULL ORDER BY category";
        return $this->db->select($sql);
    }

    /**
     * الحصول على الموردين حسب الفئة
     */
    public function getByCategory($category, $limit = 10)
    {
        $sql = "SELECT * FROM suppliers WHERE category = ? AND status = 'active' ORDER BY name ASC";
        
        if ($limit) {
            $sql .= " LIMIT $limit";
        }
        
        return $this->db->select($sql, [$category]);
    }

    /**
     * البحث في الموردين حسب المنطقة
     */
    public function searchByLocation($location)
    {
        $sql = "SELECT * FROM suppliers WHERE address LIKE ? AND status = 'active' ORDER BY name ASC";
        return $this->db->select($sql, ["%$location%"]);
    }

    /**
     * الحصول على أفضل الموردين (حسب عدد الطلبات)
     */
    public function getTopSuppliers($limit = 5)
    {
        // مؤقتاً نعيد الموردين النشطين فقط حتى يتم إنشاء جدول الطلبات
        $sql = "SELECT s.*, 0 as order_count 
                FROM suppliers s 
                WHERE s.status = 'active' 
                ORDER BY s.name ASC 
                LIMIT $limit";
        
        return $this->db->select($sql);
    }
} 