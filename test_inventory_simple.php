<?php
// اختبار بسيط لصفحة المخزون
require_once 'config.php';

try {
    $db = Database::getInstance();
    
    echo "<h1>اختبار صفحة المخزون</h1>";
    
    // اختبار الحصول على الإحصائيات
    echo "<h3>اختبار الإحصائيات:</h3>";
    $stats = [
        'total_items' => 0,
        'low_stock' => 0,
        'expired' => 0,
        'total_value' => 0
    ];

    // إجمالي العناصر
    $totalItems = $db->selectOne("SELECT COUNT(*) as count FROM inventory");
    $stats['total_items'] = $totalItems['count'] ?? 0;

    // الأدوية منخفضة المخزون
    $lowStock = $db->selectOne("SELECT COUNT(*) as count FROM inventory WHERE quantity <= reorder_level");
    $stats['low_stock'] = $lowStock['count'] ?? 0;

    // الأدوية منتهية الصلاحية
    $expired = $db->selectOne("SELECT COUNT(*) as count FROM inventory WHERE expiry_date < CURDATE()");
    $stats['expired'] = $expired['count'] ?? 0;

    // إجمالي قيمة المخزون
    $totalValue = $db->selectOne("SELECT SUM(quantity * unit_price) as total FROM inventory");
    $stats['total_value'] = $totalValue['total'] ?? 0;

    echo "<p>✅ تم الحصول على الإحصائيات بنجاح</p>";
    echo "<pre>" . print_r($stats, true) . "</pre>";
    
    // اختبار الحصول على قائمة المخزون
    echo "<h3>اختبار قائمة المخزون:</h3>";
    $inventory = $db->select("SELECT * FROM inventory ORDER BY name ASC LIMIT 5");
    echo "<p>✅ تم الحصول على قائمة المخزون بنجاح</p>";
    echo "<p>عدد العناصر: " . count($inventory) . "</p>";
    
    // اختبار الحصول على الفئات
    echo "<h3>اختبار الفئات:</h3>";
    $categories = $db->select("SELECT DISTINCT category FROM inventory WHERE category IS NOT NULL AND category != '' ORDER BY category");
    echo "<p>✅ تم الحصول على الفئات بنجاح</p>";
    echo "<p>عدد الفئات: " . count($categories) . "</p>";
    
    // اختبار الحصول على الأدوية منخفضة المخزون
    echo "<h3>اختبار الأدوية منخفضة المخزون:</h3>";
    $lowStockItems = $db->select("SELECT * FROM inventory WHERE quantity <= reorder_level ORDER BY name ASC");
    echo "<p>✅ تم الحصول على الأدوية منخفضة المخزون بنجاح</p>";
    echo "<p>عدد الأدوية منخفضة المخزون: " . count($lowStockItems) . "</p>";
    
    // اختبار الحصول على الأدوية منتهية الصلاحية
    echo "<h3>اختبار الأدوية منتهية الصلاحية:</h3>";
    $expiredItems = $db->select("SELECT * FROM inventory WHERE expiry_date < CURDATE() ORDER BY name ASC");
    echo "<p>✅ تم الحصول على الأدوية منتهية الصلاحية بنجاح</p>";
    echo "<p>عدد الأدوية منتهية الصلاحية: " . count($expiredItems) . "</p>";
    
    // اختبار الحصول على الأدوية التي تنتهي قريباً
    echo "<h3>اختبار الأدوية التي تنتهي قريباً:</h3>";
    $expiringSoonItems = $db->select("SELECT * FROM inventory WHERE expiry_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY) ORDER BY expiry_date ASC");
    echo "<p>✅ تم الحصول على الأدوية التي تنتهي قريباً بنجاح</p>";
    echo "<p>عدد الأدوية التي تنتهي قريباً: " . count($expiringSoonItems) . "</p>";
    
    echo "<h2 style='color: green;'>✅ جميع الاختبارات نجحت!</h2>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<p>تفاصيل الخطأ: " . $e->getTraceAsString() . "</p>";
}
?> 