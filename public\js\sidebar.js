/**
 * وظائف الشريط الجانبي المشتركة
 * HealthKey Sidebar Functions
 */

// تهيئة الشريط الجانبي
function initializeSidebar() {
    // تهيئة tooltips
    initializeTooltips();
    
    // إضافة مستمعي الأحداث للروابط
    addSidebarEventListeners();
    
    // تحديث الوقت كل دقيقة
    updateTimeEveryMinute();
    
    // إعداد التخطيط المتجاوب
    setupResponsiveLayout();
}

// تهيئة tooltips
function initializeTooltips() {
    document.addEventListener('DOMContentLoaded', function() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
}

// إضافة مستمعي الأحداث للشريط الجانبي
function addSidebarEventListeners() {
    // إخفاء الشريط الجانبي تلقائياً على الهاتف عند النقر على رابط
    document.querySelectorAll('.sidebar-admin .nav-link, .sidebar-doctor .nav-link, .sidebar-patient .nav-link, .sidebar-pharmacist .nav-link').forEach(link => {
        link.addEventListener('click', function() {
            if (window.innerWidth <= 768) {
                const sidebar = document.querySelector('.sidebar-admin, .sidebar-doctor, .sidebar-patient, .sidebar-pharmacist');
                if (sidebar) {
                    sidebar.classList.remove('show');
                    document.body.classList.remove('sidebar-open');
                }
            }
        });
    });

    // إضافة مستمع الأحداث لزر الإغلاق
    document.querySelectorAll('.sidebar-toggle').forEach(toggleBtn => {
        toggleBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeSidebar();
            
            // منع إعادة الفتح التلقائي لمدة ثانية
            setTimeout(() => {
                const sidebar = document.querySelector('.sidebar-admin, .sidebar-doctor, .sidebar-patient, .sidebar-pharmacist');
                if (sidebar && sidebar.classList.contains('show')) {
                    sidebar.classList.remove('show');
                    document.body.classList.remove('sidebar-open');
                }
            }, 100);
        });
    });
}

// تبديل الشريط الجانبي
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar-admin, .sidebar-doctor, .sidebar-patient, .sidebar-pharmacist');
    if (!sidebar) return;

    if (window.innerWidth <= 768) {
        // على الهاتف: إظهار/إخفاء
        sidebar.classList.toggle('show');
        document.body.classList.toggle('sidebar-open');
    } else {
        // على سطح المكتب: طي/فتح
        sidebar.classList.toggle('collapsed');
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.classList.toggle('expanded');
        }
    }
}

// إغلاق الشريط الجانبي
function closeSidebar() {
    const sidebar = document.querySelector('.sidebar-admin, .sidebar-doctor, .sidebar-patient, .sidebar-pharmacist');
    if (!sidebar) return;

    console.log('Closing sidebar...');

    if (window.innerWidth <= 768) {
        // على الهاتف: إخفاء الشريط الجانبي
        console.log('Closing sidebar on mobile');
        sidebar.classList.remove('show');
        document.body.classList.remove('sidebar-open');
        
        // إضافة تأثير fade out
        sidebar.style.opacity = '0';
        setTimeout(() => {
            sidebar.style.opacity = '';
        }, 300);
    } else {
        // على سطح المكتب: طي الشريط الجانبي
        console.log('Closing sidebar on desktop');
        sidebar.classList.add('collapsed');
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.classList.add('expanded');
        }
    }

    // إضافة تأثير للزر
    const toggleBtn = sidebar.querySelector('.sidebar-toggle');
    if (toggleBtn) {
        toggleBtn.style.transform = 'scale(0.9)';
        setTimeout(() => {
            toggleBtn.style.transform = '';
        }, 200);
    }
}

// فتح الشريط الجانبي
function openSidebar() {
    const sidebar = document.querySelector('.sidebar-admin, .sidebar-doctor, .sidebar-patient, .sidebar-pharmacist');
    if (!sidebar) return;

    if (window.innerWidth <= 768) {
        // على الهاتف: إظهار الشريط الجانبي
        sidebar.classList.add('show');
        document.body.classList.add('sidebar-open');
    } else {
        // على سطح المكتب: فتح الشريط الجانبي
        sidebar.classList.remove('collapsed');
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.classList.remove('expanded');
        }
    }
}

// فتح الشريط الجانبي للموبايل (مع debugging)
function openSidebarMobile() {
    console.log('openSidebarMobile called');
    
    const sidebar = document.querySelector('.sidebar-admin, .sidebar-doctor, .sidebar-patient, .sidebar-pharmacist');
    console.log('Sidebar found:', sidebar);
    
    if (!sidebar) {
        console.error('No sidebar found');
        return;
    }

    console.log('Window width:', window.innerWidth);
    console.log('Current sidebar classes:', sidebar.className);

    if (window.innerWidth <= 768) {
        // على الهاتف: إظهار الشريط الجانبي
        console.log('Opening sidebar on mobile');
        
        // إزالة أي تأثيرات سابقة
        sidebar.style.opacity = '';
        sidebar.style.transform = '';
        
        // إضافة الكلاسات المطلوبة
        sidebar.classList.add('show');
        document.body.classList.add('sidebar-open');
        
        // التأكد من أن الشريط الجانبي مرئي
        sidebar.style.display = 'flex';
        sidebar.style.visibility = 'visible';
        
        console.log('Sidebar classes after opening:', sidebar.className);
        console.log('Sidebar computed styles:', window.getComputedStyle(sidebar));
        
        // إضافة تأثير بصري للفتح
        setTimeout(() => {
            sidebar.style.transition = 'transform 0.3s ease';
            sidebar.style.transform = 'translateX(0)';
        }, 50);
        
    } else {
        // على سطح المكتب: فتح الشريط الجانبي
        console.log('Opening sidebar on desktop');
        sidebar.classList.remove('collapsed');
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.classList.remove('expanded');
        }
    }
}

// تحديث الوقت كل دقيقة
function updateTimeEveryMinute() {
    setInterval(function() {
        const timeElement = document.querySelector('.status-item:last-child span');
        if (timeElement) {
            timeElement.textContent = new Date().toLocaleTimeString('ar-SA', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    }, 60000);
}

// إعداد التخطيط المتجاوب
function setupResponsiveLayout() {
    // تحديث التخطيط عند تغيير حجم النافذة
    window.addEventListener('resize', function() {
        // تأخير قليل لتجنب التحديث المتكرر
        clearTimeout(window.resizeTimeout);
        window.resizeTimeout = setTimeout(() => {
            updateSidebarLayout();
        }, 100);
    });
    
    // تحديث التخطيط عند التحميل
    window.addEventListener('load', function() {
        updateSidebarLayout();
    });
}

// تحديث تخطيط الشريط الجانبي
function updateSidebarLayout() {
    const sidebar = document.querySelector('.sidebar-admin, .sidebar-doctor, .sidebar-patient, .sidebar-pharmacist');
    const mainContent = document.querySelector('.main-content');
    
    if (!sidebar || !mainContent) return;
    
    if (window.innerWidth <= 768) {
        // على الهاتف: لا نغلق الشريط الجانبي إذا كان مفتوحاً بالفعل
        // فقط نحدث margin للمحتوى
        mainContent.style.marginRight = '0';
    } else {
        // على سطح المكتب: إظهار الشريط الجانبي
        if (!sidebar.classList.contains('collapsed')) {
            mainContent.style.marginRight = '280px';
        } else {
            mainContent.style.marginRight = '0';
        }
    }
}

// إخفاء الشريط الجانبي عند النقر خارجه (على الهاتف)
function hideSidebarOnOutsideClick() {
    document.addEventListener('click', function(event) {
        const sidebar = document.querySelector('.sidebar-admin, .sidebar-doctor, .sidebar-patient, .sidebar-pharmacist');
        const sidebarToggle = document.querySelector('.sidebar-toggle, .mobile-toggle-btn');
        const mobileToggleBtn = document.querySelector('.mobile-sidebar-toggle .btn');
        
        if (!sidebar) return;
        
        // إذا كان النقر خارج الشريط الجانبي وزر التبديل
        if (!sidebar.contains(event.target) && 
            !sidebarToggle?.contains(event.target) && 
            !mobileToggleBtn?.contains(event.target)) {
            if (window.innerWidth <= 768 && sidebar.classList.contains('show')) {
                closeSidebar();
            }
        }
    });
}

// تحديث عداد الإشعارات
function updateNotificationCount(count) {
    const notificationBadges = document.querySelectorAll('.nav-badge.bg-danger');
    notificationBadges.forEach(badge => {
        if (badge.textContent.match(/^\d+$/)) {
            badge.textContent = count;
        }
    });
}

// تحديث الإحصائيات في الشريط الجانبي
function updateSidebarStats(stats) {
    // تحديث عداد المستخدمين
    const userBadges = document.querySelectorAll('.nav-badge.bg-success');
    if (stats.users && stats.users.total !== undefined) {
        userBadges.forEach(badge => {
            if (badge.closest('.nav-link').textContent.includes('المستخدمين')) {
                badge.textContent = stats.users.total;
            }
        });
    }
    
    // تحديث عداد المواعيد
    const appointmentBadges = document.querySelectorAll('.nav-badge.bg-info');
    if (stats.appointments && stats.appointments.total !== undefined) {
        appointmentBadges.forEach(badge => {
            if (badge.closest('.nav-link').textContent.includes('المواعيد')) {
                badge.textContent = stats.appointments.total;
            }
        });
    }
    
    // تحديث عداد الوصفات
    const prescriptionBadges = document.querySelectorAll('.nav-badge.bg-warning');
    if (stats.prescriptions && stats.prescriptions.total !== undefined) {
        prescriptionBadges.forEach(badge => {
            if (badge.closest('.nav-link').textContent.includes('الوصفات')) {
                badge.textContent = stats.prescriptions.total;
            }
        });
    }
}

// إضافة تأثيرات بصرية للروابط النشطة
function highlightActiveLink() {
    const currentPath = window.location.pathname.replace(/^\//, '');
    const navLinks = document.querySelectorAll('.sidebar-admin .nav-link, .sidebar-doctor .nav-link, .sidebar-patient .nav-link, .sidebar-pharmacist .nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.includes(href.replace(/^\//, ''))) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
}

// تهيئة الشريط الجانبي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeSidebar();
    hideSidebarOnOutsideClick();
    highlightActiveLink();
});

// تصدير الوظائف للاستخدام العام
window.SidebarFunctions = {
    toggleSidebar,
    openSidebar,
    closeSidebar,
    openSidebarMobile,
    updateNotificationCount,
    updateSidebarStats,
    highlightActiveLink
}; 