<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">صرف الأدوية</h1>
        <p class="text-muted">إدارة صرف الأدوية للمرضى</p>
    </div>
    <div>
        <a href="<?= App::url('pharmacist/dispensing-report') ?>" class="btn btn-outline-primary">
            <i class="bi bi-file-earmark-text"></i>
            تقرير الصرف
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['pending'] ?></h4>
                        <p class="mb-0">الوصفات المعلقة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock-history display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['active'] ?></h4>
                        <p class="mb-0">الوصفات النشطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-prescription2 display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['dispensed_today'] ?></h4>
                        <p class="mb-0">مصروف اليوم</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-capsule display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['total_dispensed'] ?></h4>
                        <p class="mb-0">إجمالي المصروف</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clipboard2-check display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?= htmlspecialchars($search) ?>" 
                       placeholder="ابحث بالكود أو اسم المريض أو الطبيب">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="active" <?= $currentStatus === 'active' ? 'selected' : '' ?>>الوصفات النشطة</option>
                    <option value="pending" <?= $currentStatus === 'pending' ? 'selected' : '' ?>>الوصفات المعلقة</option>
                    <option value="dispensed" <?= $currentStatus === 'dispensed' ? 'selected' : '' ?>>الوصفات المصروفة</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i>
                        بحث
                    </button>
                    <a href="<?= App::url('pharmacist/dispensing') ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise"></i>
                        إعادة تعيين
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Prescriptions List -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-list-ul me-2"></i>
            قائمة الوصفات
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($prescriptions)): ?>
            <div class="text-center py-5">
                <i class="bi bi-inbox display-1 text-muted"></i>
                <h4 class="mt-3 text-muted">لا توجد وصفات</h4>
                <p class="text-muted">لا توجد وصفات مطابقة للبحث المحدد</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>كود الوصفة</th>
                            <th>المريض</th>
                            <th>الطبيب</th>
                            <th>التشخيص</th>
                            <th>الأدوية</th>
                            <th>تاريخ الإصدار</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($prescriptions as $prescription): ?>
                            <tr>
                                <td>
                                    <span class="badge bg-primary"><?= htmlspecialchars($prescription['prescription_code']) ?></span>
                                </td>
                                <td>
                                    <div>
                                        <strong><?= htmlspecialchars($prescription['patient_name']) ?></strong>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong><?= htmlspecialchars($prescription['doctor_name']) ?></strong>
                                        <?php if ($prescription['doctor_specialization']): ?>
                                            <br><small class="text-muted"><?= htmlspecialchars($prescription['doctor_specialization']) ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($prescription['diagnosis']): ?>
                                        <span class="text-truncate d-inline-block" style="max-width: 150px;" 
                                              title="<?= htmlspecialchars($prescription['diagnosis']) ?>">
                                            <?= htmlspecialchars($prescription['diagnosis']) ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-info me-2">
                                            <?= $prescription['dispensed_medications'] ?>/<?= $prescription['total_medications'] ?>
                                        </span>
                                        <div class="progress flex-grow-1" style="height: 6px;">
                                            <?php 
                                            $percentage = $prescription['total_medications'] > 0 
                                                ? ($prescription['dispensed_medications'] / $prescription['total_medications']) * 100 
                                                : 0;
                                            ?>
                                            <div class="progress-bar bg-success" style="width: <?= $percentage ?>%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <small><?= DateHelper::formatArabic($prescription['issue_date']) ?></small>
                                </td>
                                <td>
                                    <?php 
                                    $isExpired = strtotime($prescription['expiry_date']) < time();
                                    $expiresSoon = strtotime($prescription['expiry_date']) < strtotime('+7 days');
                                    ?>
                                    <small class="<?= $isExpired ? 'text-danger' : ($expiresSoon ? 'text-warning' : 'text-muted') ?>">
                                        <?= DateHelper::formatArabic($prescription['expiry_date']) ?>
                                        <?php if ($isExpired): ?>
                                            <i class="bi bi-exclamation-triangle-fill text-danger"></i>
                                        <?php elseif ($expiresSoon): ?>
                                            <i class="bi bi-clock text-warning"></i>
                                        <?php endif; ?>
                                    </small>
                                </td>
                                <td>
                                    <?php if ($prescription['dispensed_medications'] == $prescription['total_medications']): ?>
                                        <span class="badge bg-success">مكتمل</span>
                                    <?php elseif ($prescription['dispensed_medications'] > 0): ?>
                                        <span class="badge bg-warning">جزئي</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">معلق</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= App::url('pharmacist/prescription-details/' . $prescription['id']) ?>" 
                                           class="btn btn-sm btn-outline-primary" title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <?php if ($prescription['dispensed_medications'] < $prescription['total_medications']): ?>
                                            <button type="button" class="btn btn-sm btn-success" 
                                                    onclick="dispensePrescription(<?= $prescription['id'] ?>)" 
                                                    title="صرف الدواء">
                                                <i class="bi bi-capsule"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Dispense Modal -->
<div class="modal fade" id="dispenseModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">صرف الدواء</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="dispenseModalBody">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
        </div>
    </div>
</div>

<script>
function dispensePrescription(prescriptionId) {
    // تحميل تفاصيل الوصفة في النافذة المنبثقة
    fetch(`<?= App::url('pharmacist/prescription-details/') ?>${prescriptionId}?modal=1`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('dispenseModalBody').innerHTML = html;
            new bootstrap.Modal(document.getElementById('dispenseModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحميل تفاصيل الوصفة');
        });
}

// دالة صرف الدواء
function dispenseMedication(medicationId, prescriptionId) {
    const quantity = document.getElementById(`quantity_${medicationId}`).value;
    
    if (!quantity || quantity <= 0) {
        alert('يرجى إدخال كمية صحيحة');
        return;
    }
    
    fetch('<?= App::url('pharmacist/dispense-medication') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            medication_id: medicationId,
            dispensed_quantity: quantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم صرف الدواء بنجاح');
            location.reload();
        } else {
            alert('خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء صرف الدواء');
    });
}

// تحديث الصفحة عند تغيير الحالة
document.getElementById('status').addEventListener('change', function() {
    this.form.submit();
});
</script> 