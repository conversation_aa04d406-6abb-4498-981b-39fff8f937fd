<?php
require_once 'config.php';
require_once 'app/models/Message.php';

echo "اختبار سريع لإصلاح مشكلة الرسائل\n";

try {
    $messageModel = new Message();
    
    echo "1. اختبار إحصائيات الرسائل...\n";
    $stats = $messageModel->getStats(2);
    echo "✅ تم الحصول على الإحصائيات: " . json_encode($stats, JSON_UNESCAPED_UNICODE) . "\n";
    
    echo "2. اختبار الرسائل الواردة...\n";
    $inbox = $messageModel->getInbox(2, false, 3);
    echo "✅ عدد الرسائل الواردة: " . count($inbox) . "\n";
    
    echo "3. اختبار الرسائل الصادرة...\n";
    $sent = $messageModel->getSent(2, 3);
    echo "✅ عدد الرسائل الصادرة: " . count($sent) . "\n";
    
    echo "✅ جميع الاختبارات تمت بنجاح!\n";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}
?> 