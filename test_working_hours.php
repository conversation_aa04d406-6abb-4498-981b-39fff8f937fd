<?php
/**
 * اختبار صفحة ساعات العمل للصيدلي
 */

require_once 'config.php';
require_once 'app/core/App.php';
require_once 'app/controllers/PharmacistController.php';

echo "<h1>اختبار صفحة ساعات العمل للصيدلي</h1>";

try {
    // إنشاء جدول ساعات العمل إذا لم يكن موجوداً
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $createTableSql = "CREATE TABLE IF NOT EXISTS working_hours (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        user_type ENUM('doctor', 'pharmacist', 'nurse') NOT NULL,
        day ENUM('sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday') NOT NULL,
        start_time TIME NOT NULL,
        end_time TIME NOT NULL,
        is_working TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_user_day (user_id, user_type, day),
        INDEX idx_user_type (user_type),
        INDEX idx_day (day)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($createTableSql);
    echo "✅ تم إنشاء جدول working_hours بنجاح<br>";
    
    // إضافة بيانات تجريبية للصيدلي رقم 2
    $pharmacistId = 2;
    $days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    
    foreach ($days as $day) {
        // التحقق من عدم وجود بيانات مكررة
        $checkSql = "SELECT id FROM working_hours WHERE user_id = ? AND user_type = 'pharmacist' AND day = ?";
        $checkStmt = $pdo->prepare($checkSql);
        $checkStmt->execute([$pharmacistId, $day]);
        
        if (!$checkStmt->fetch()) {
            $insertSql = "INSERT INTO working_hours (user_id, user_type, day, start_time, end_time, is_working) 
                          VALUES (?, 'pharmacist', ?, ?, ?, ?)";
            $insertStmt = $pdo->prepare($insertSql);
            
            // تحديد ساعات عمل مختلفة لكل يوم
            switch ($day) {
                case 'friday':
                    $startTime = '09:00';
                    $endTime = '15:00';
                    $isWorking = 1;
                    break;
                case 'saturday':
                    $startTime = '10:00';
                    $endTime = '16:00';
                    $isWorking = 1;
                    break;
                default:
                    $startTime = '08:00';
                    $endTime = '17:00';
                    $isWorking = 1;
                    break;
            }
            
            $insertStmt->execute([$pharmacistId, $day, $startTime, $endTime, $isWorking]);
        }
    }
    
    echo "✅ تم إضافة بيانات تجريبية لساعات العمل<br>";
    
    // اختبار دالة getWorkingHours
    echo "<h2>اختبار دالة getWorkingHours</h2>";
    
    // محاكاة تسجيل دخول الصيدلي
    $_SESSION['user_id'] = $pharmacistId;
    $_SESSION['user_type'] = 'pharmacist';
    $_SESSION['user'] = [
        'id' => $pharmacistId,
        'first_name' => 'أحمد',
        'last_name' => 'محمد',
        'email' => '<EMAIL>'
    ];
    
    // إنشاء متحكم الصيدلي
    $controller = new PharmacistController();
    
    // اختبار الحصول على ساعات العمل
    $workingHours = $controller->getWorkingHours($pharmacistId);
    
    echo "✅ تم الحصول على ساعات العمل: " . count($workingHours) . " يوم<br>";
    
    // عرض ساعات العمل
    echo "<h3>ساعات العمل الحالية:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>اليوم</th><th>وقت البداية</th><th>وقت النهاية</th><th>يوم عمل</th></tr>";
    
    $daysNames = [
        'sunday' => 'الأحد',
        'monday' => 'الاثنين',
        'tuesday' => 'الثلاثاء',
        'wednesday' => 'الأربعاء',
        'thursday' => 'الخميس',
        'friday' => 'الجمعة',
        'saturday' => 'السبت'
    ];
    
    foreach ($workingHours as $hour) {
        echo "<tr>";
        echo "<td>" . $daysNames[$hour['day']] . "</td>";
        echo "<td>" . $hour['start_time'] . "</td>";
        echo "<td>" . $hour['end_time'] . "</td>";
        echo "<td>" . ($hour['is_working'] ? 'نعم' : 'لا') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // اختبار تحديث ساعات العمل
    echo "<h2>اختبار تحديث ساعات العمل</h2>";
    
    // محاكاة بيانات POST
    $_POST['sunday_working'] = 1;
    $_POST['sunday_start'] = '09:00';
    $_POST['sunday_end'] = '18:00';
    
    $_POST['monday_working'] = 1;
    $_POST['monday_start'] = '08:30';
    $_POST['monday_end'] = '17:30';
    
    $_POST['tuesday_working'] = 0; // يوم راحة
    $_POST['tuesday_start'] = '08:00';
    $_POST['tuesday_end'] = '17:00';
    
    $_POST['wednesday_working'] = 1;
    $_POST['wednesday_start'] = '08:00';
    $_POST['wednesday_end'] = '17:00';
    
    $_POST['thursday_working'] = 1;
    $_POST['thursday_start'] = '08:00';
    $_POST['thursday_end'] = '17:00';
    
    $_POST['friday_working'] = 1;
    $_POST['friday_start'] = '09:00';
    $_POST['friday_end'] = '15:00';
    
    $_POST['saturday_working'] = 1;
    $_POST['saturday_start'] = '10:00';
    $_POST['saturday_end'] = '16:00';
    
    // اختبار دالة updateWorkingHours
    $controller->updateWorkingHours($pharmacistId);
    
    echo "✅ تم تحديث ساعات العمل بنجاح<br>";
    
    // عرض الساعات المحدثة
    $updatedHours = $controller->getWorkingHours($pharmacistId);
    
    echo "<h3>ساعات العمل المحدثة:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>اليوم</th><th>وقت البداية</th><th>وقت النهاية</th><th>يوم عمل</th></tr>";
    
    foreach ($updatedHours as $hour) {
        echo "<tr>";
        echo "<td>" . $daysNames[$hour['day']] . "</td>";
        echo "<td>" . $hour['start_time'] . "</td>";
        echo "<td>" . $hour['end_time'] . "</td>";
        echo "<td>" . ($hour['is_working'] ? 'نعم' : 'لا') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // اختبار الوصول للصفحة
    echo "<h2>اختبار الوصول للصفحة</h2>";
    echo "<p>يمكنك الوصول لصفحة ساعات العمل عبر الرابط التالي:</p>";
    echo "<a href='index.php?url=pharmacist/working-hours' target='_blank' class='btn btn-primary'>فتح صفحة ساعات العمل</a>";
    
    echo "<h2>✅ انتهى الاختبار بنجاح</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في الاختبار</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 