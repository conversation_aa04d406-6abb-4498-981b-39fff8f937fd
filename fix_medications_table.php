<?php
/**
 * إصلاح جدول الأدوية وإضافة الأعمدة المفقودة
 */

require_once 'config.php';

try {
    $db = Database::getInstance();
    
    echo "<h1>إصلاح جدول الأدوية</h1>";
    
    // التحقق من وجود الجدول
    $tableExists = $db->selectOne("SHOW TABLES LIKE 'medications'");
    if (!$tableExists) {
        echo "❌ جدول الأدوية غير موجود<br>";
        exit;
    }
    
    echo "✅ جدول الأدوية موجود<br>";
    
    // التحقق من الأعمدة الموجودة
    $columns = $db->select("SHOW COLUMNS FROM medications");
    $existingColumns = array_column($columns, 'Field');
    
    echo "<h3>الأعمدة الموجودة:</h3>";
    foreach ($existingColumns as $column) {
        echo "- $column<br>";
    }
    
    // إضافة الأعمدة المفقودة
    $missingColumns = [];
    
    if (!in_array('status', $existingColumns)) {
        $missingColumns[] = 'status';
    }
    
    if (!in_array('prescription_required', $existingColumns)) {
        $missingColumns[] = 'prescription_required';
    }
    
    if (!in_array('side_effects', $existingColumns)) {
        $missingColumns[] = 'side_effects';
    }
    
    if (!in_array('contraindications', $existingColumns)) {
        $missingColumns[] = 'contraindications';
    }
    
    if (!in_array('storage_conditions', $existingColumns)) {
        $missingColumns[] = 'storage_conditions';
    }
    
    if (!empty($missingColumns)) {
        echo "<h3>إضافة الأعمدة المفقودة:</h3>";
        
        // ترتيب الأعمدة حسب الترتيب المطلوب
        $orderedColumns = ['side_effects', 'contraindications', 'storage_conditions', 'status', 'prescription_required'];
        $columnsToAdd = array_intersect($orderedColumns, $missingColumns);
        
        foreach ($columnsToAdd as $column) {
            $sql = "";
            switch ($column) {
                case 'side_effects':
                    $sql = "ALTER TABLE medications ADD COLUMN side_effects TEXT AFTER description";
                    break;
                case 'contraindications':
                    $sql = "ALTER TABLE medications ADD COLUMN contraindications TEXT AFTER side_effects";
                    break;
                case 'storage_conditions':
                    $sql = "ALTER TABLE medications ADD COLUMN storage_conditions TEXT AFTER contraindications";
                    break;
                case 'status':
                    $sql = "ALTER TABLE medications ADD COLUMN status ENUM('active', 'inactive') DEFAULT 'active' AFTER storage_conditions";
                    break;
                case 'prescription_required':
                    $sql = "ALTER TABLE medications ADD COLUMN prescription_required TINYINT(1) DEFAULT 0 AFTER status";
                    break;
            }
            
            if (!empty($sql)) {
                try {
                    $result = $db->getConnection()->exec($sql);
                    if ($result !== false) {
                        echo "✅ تم إضافة العمود: $column<br>";
                    } else {
                        echo "❌ فشل في إضافة العمود: $column<br>";
                    }
                } catch (Exception $e) {
                    echo "❌ خطأ في إضافة العمود $column: " . $e->getMessage() . "<br>";
                }
            }
        }
    } else {
        echo "✅ جميع الأعمدة موجودة<br>";
    }
    
    // إضافة الفهارس المفقودة
    echo "<h3>إضافة الفهارس:</h3>";
    
    $indexes = $db->select("SHOW INDEX FROM medications");
    $existingIndexes = array_column($indexes, 'Key_name');
    
    $requiredIndexes = ['idx_name', 'idx_generic_name', 'idx_category', 'idx_status', 'idx_prescription_required'];
    
    foreach ($requiredIndexes as $index) {
        if (!in_array($index, $existingIndexes)) {
            $column = str_replace('idx_', '', $index);
            $sql = "ALTER TABLE medications ADD INDEX $index ($column)";
            $result = $db->getConnection()->exec($sql);
            if ($result !== false) {
                echo "✅ تم إضافة الفهرس: $index<br>";
            } else {
                echo "❌ فشل في إضافة الفهرس: $index<br>";
            }
        } else {
            echo "ℹ️ الفهرس موجود: $index<br>";
        }
    }
    
    // تحديث البيانات الموجودة
    echo "<h3>تحديث البيانات الموجودة:</h3>";
    
    // تحديث الأدوية الموجودة لتكون نشطة
    $updateSql = "UPDATE medications SET status = 'active' WHERE status IS NULL";
    $result = $db->getConnection()->exec($updateSql);
    echo "✅ تم تحديث حالة الأدوية<br>";
    
    // تحديث الأدوية التي تحتاج وصفة
    $prescriptionDrugs = ['أموكسيسيلين', 'أوميبرازول', 'ميتفورمين', 'أتينولول', 'كودين', 'سلفاميثوكسازول'];
    foreach ($prescriptionDrugs as $drug) {
        $updateSql = "UPDATE medications SET prescription_required = 1 WHERE name = ?";
        $db->update($updateSql, [$drug]);
    }
    echo "✅ تم تحديث الأدوية التي تحتاج وصفة<br>";
    
    // إضافة بيانات تجريبية للأعمدة الجديدة إذا كانت فارغة
    $medications = $db->select("SELECT * FROM medications WHERE side_effects IS NULL OR side_effects = '' LIMIT 5");
    
    if (!empty($medications)) {
        echo "<h3>تحديث البيانات التجريبية:</h3>";
        
        $sampleData = [
            'باراسيتامول' => [
                'side_effects' => 'غثيان، ألم في المعدة، طفح جلدي في حالات نادرة',
                'contraindications' => 'الحساسية للباراسيتامول، أمراض الكبد الشديدة',
                'storage_conditions' => 'يحفظ في مكان جاف وبارد، بعيداً عن أشعة الشمس'
            ],
            'أموكسيسيلين' => [
                'side_effects' => 'غثيان، إسهال، طفح جلدي، حساسية',
                'contraindications' => 'الحساسية للبنسلين، أمراض الكلى الشديدة',
                'storage_conditions' => 'يحفظ في الثلاجة، بعيداً عن الرطوبة'
            ],
            'أوميبرازول' => [
                'side_effects' => 'صداع، إسهال، ألم في البطن، غثيان',
                'contraindications' => 'الحساسية للأوميبرازول، الحمل في الأشهر الأولى',
                'storage_conditions' => 'يحفظ في مكان جاف وبارد'
            ],
            'فيتامين سي' => [
                'side_effects' => 'إسهال، غثيان، حموضة في المعدة',
                'contraindications' => 'حصوات الكلى، مرضى السكري',
                'storage_conditions' => 'يحفظ في مكان جاف وبارد، بعيداً عن الضوء'
            ],
            'أسبيرين' => [
                'side_effects' => 'غثيان، ألم في المعدة، نزيف، طفح جلدي',
                'contraindications' => 'قرحة المعدة، النزيف، الأطفال أقل من 12 سنة',
                'storage_conditions' => 'يحفظ في مكان جاف وبارد'
            ]
        ];
        
        foreach ($medications as $medication) {
            if (isset($sampleData[$medication['name']])) {
                $data = $sampleData[$medication['name']];
                $updateSql = "UPDATE medications SET 
                              side_effects = ?, 
                              contraindications = ?, 
                              storage_conditions = ? 
                              WHERE id = ?";
                $params = [
                    $data['side_effects'],
                    $data['contraindications'],
                    $data['storage_conditions'],
                    $medication['id']
                ];
                
                $result = $db->update($updateSql, $params);
                if ($result) {
                    echo "✅ تم تحديث: " . $medication['name'] . "<br>";
                }
            }
        }
    }
    
    // عرض الإحصائيات النهائية
    echo "<h3>الإحصائيات النهائية:</h3>";
    $statsSql = "SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN status = 'active' THEN 1 END) as active,
                    COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive,
                    COUNT(CASE WHEN prescription_required = 1 THEN 1 END) as prescription_required,
                    COUNT(CASE WHEN prescription_required = 0 THEN 1 END) as otc
                 FROM medications";
    $stats = $db->selectOne($statsSql);
    
    echo "📊 إجمالي الأدوية: " . $stats['total'] . "<br>";
    echo "✅ الأدوية النشطة: " . $stats['active'] . "<br>";
    echo "⏸️ الأدوية غير النشطة: " . $stats['inactive'] . "<br>";
    echo "💊 تحتاج وصفة: " . $stats['prescription_required'] . "<br>";
    echo "🛒 بدون وصفة: " . $stats['otc'] . "<br>";
    
    echo "<h2>✅ تم إصلاح جدول الأدوية بنجاح!</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 