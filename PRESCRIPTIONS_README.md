# صفحة إدارة الوصفات الطبية للمدير

## نظرة عامة

تم إنشاء صفحة شاملة لإدارة الوصفات الطبية في لوحة تحكم المدير. هذه الصفحة تتيح للمدير عرض وإدارة جميع الوصفات الطبية في النظام مع ميزات متقدمة للبحث والتصفية والإجراءات الجماعية.

## الملفات المنشأة

### 1. `views/admin/prescriptions.php`
الصفحة الرئيسية لإدارة الوصفات الطبية تحتوي على:
- **إحصائيات الوصفات**: عرض إجمالي الوصفات، النشطة، الموزعة، والمنتهية
- **فلاتر البحث**: البحث بالكود، المريض، الطبيب، الحالة، والتاريخ
- **جدول الوصفات**: عرض منظم للوصفات مع معلومات مفصلة
- **الإجراءات**: عرض، تعديل، حذف، وإجراءات جماعية
- **تصدير البيانات**: تصدير الوصفات بصيغة CSV

### 2. `views/admin/prescription_details.php`
ملف عرض تفاصيل الوصفة الطبية يحتوي على:
- **معلومات المريض والطبيب**: عرض تفصيلي لمعلومات الطرفين
- **التشخيص والملاحظات**: عرض التشخيص الطبي والملاحظات
- **التواريخ والحالة**: تاريخ الإصدار والانتهاء مع مؤشرات بصرية
- **الأدوية الموصوفة**: جدول تفصيلي للأدوية مع حالاتها
- **أزرار الإجراءات**: طباعة، تعديل، وحذف

### 3. `test_admin_prescriptions.php`
ملف اختبار يعرض:
- **بيانات تجريبية**: وصفات طبية تجريبية للاختبار
- **واجهة مشابهة**: واجهة مطابقة للصفحة الحقيقية
- **تعليمات الاختبار**: خطوات مفصلة لاختبار الميزات

## الميزات المضافة في AdminController

### الدوال الجديدة:

#### 1. `prescriptions()`
- عرض قائمة الوصفات الطبية
- دعم الفلترة والبحث
- ترقيم الصفحات

#### 2. `addPrescription()`
- إضافة وصفة طبية جديدة
- التحقق من صحة البيانات
- إنشاء كود فريد للوصفة

#### 3. `viewPrescription($prescriptionId)`
- عرض تفاصيل الوصفة الطبية
- عرض الأدوية المرتبطة
- دعم AJAX للعرض السريع

#### 4. `editPrescription($prescriptionId)`
- تعديل الوصفة الطبية
- تحديث البيانات والحالة
- عرض نموذج التعديل

#### 5. `deletePrescription($prescriptionId)`
- حذف الوصفة الطبية
- تأكيد الحذف
- استجابة JSON

#### 6. `bulkPrescriptionAction()`
- الإجراءات الجماعية للوصفات
- تفعيل/إلغاء تفعيل/حذف متعدد
- معالجة AJAX

#### 7. `exportPrescriptions()`
- تصدير الوصفات بصيغة CSV
- دعم الفلاتر في التصدير
- ترميز UTF-8 للدعم العربي

#### 8. `getPatients()`
- الحصول على قائمة المرضى النشطين
- دعم AJAX للقوائم المنسدلة

## الميزات المضافة في User Model

### دالة جديدة:

#### `getActivePatients()`
- الحصول على المرضى النشطين فقط
- معلومات إضافية من جدول المرضى
- ترتيب حسب الاسم

## الميزات الرئيسية

### 1. إحصائيات شاملة
- إجمالي الوصفات الطبية
- الوصفات النشطة والموزعة والمنتهية
- مؤشرات بصرية ملونة

### 2. فلاتر متقدمة
- البحث بالكود أو اسم المريض أو الطبيب
- فلترة بالحالة (نشطة، موزعة، منتهية، ملغية)
- فلترة بالطبيب المعالج
- فلترة بالتاريخ (من - إلى)

### 3. عرض تفصيلي
- معلومات المريض والطبيب
- التشخيص الطبي والملاحظات
- تواريخ الإصدار والانتهاء مع مؤشرات
- قائمة الأدوية الموصوفة

### 4. إجراءات متقدمة
- إضافة وصفة جديدة
- تعديل الوصفات الموجودة
- حذف الوصفات
- الإجراءات الجماعية
- تصدير البيانات

### 5. واجهة مستخدم حديثة
- تصميم متجاوب
- أيقونات Bootstrap
- ألوان مميزة للحالات المختلفة
- رسائل تنبيه تفاعلية

## كيفية الاستخدام

### 1. الوصول للصفحة
```
http://localhost/HealthKey/admin/prescriptions
```

### 2. اختبار الميزات
```
http://localhost/HealthKey/test_admin_prescriptions.php
```

### 3. إضافة وصفة جديدة
- انقر على "إضافة وصفة جديدة"
- املأ البيانات المطلوبة
- اختر المريض والطبيب
- أضف التشخيص والملاحظات
- حدد تواريخ الإصدار والانتهاء

### 4. البحث والفلترة
- استخدم حقل البحث للبحث السريع
- اختر الحالة من القائمة المنسدلة
- اختر الطبيب للفلترة
- حدد نطاق التاريخ

### 5. الإجراءات الجماعية
- حدد الوصفات المطلوبة
- انقر على "إجراءات جماعية"
- اختر الإجراء المطلوب
- تأكيد الإجراء

## الأمان والتحقق

### 1. التحقق من الصلاحيات
- التحقق من تسجيل دخول المدير
- التحقق من نوع المستخدم
- حماية من الوصول غير المصرح

### 2. التحقق من البيانات
- التحقق من صحة البيانات المدخلة
- التحقق من وجود الوصفة قبل التعديل/الحذف
- معالجة الأخطاء وعرض رسائل مناسبة

### 3. حماية CSRF
- استخدام tokens للحماية
- التحقق من طريقة الطلب
- حماية من الهجمات

## التطوير المستقبلي

### 1. ميزات إضافية
- طباعة الوصفات بصيغة PDF
- إرسال الوصفات عبر البريد الإلكتروني
- نظام تنبيهات للوصفات المنتهية
- تقارير إحصائية مفصلة

### 2. تحسينات الأداء
- تحسين استعلامات قاعدة البيانات
- إضافة cache للبيانات المتكررة
- تحسين سرعة التحميل

### 3. ميزات متقدمة
- نظام موافقات للوصفات
- تتبع تاريخ التعديلات
- نظام تعليقات وملاحظات
- تكامل مع أنظمة خارجية

## الدعم التقني

### للمطورين:
- الكود مكتوب بأفضل الممارسات
- تعليقات مفصلة باللغة العربية
- هيكل منظم وقابل للتوسع
- دعم كامل للغة العربية

### للمستخدمين:
- واجهة سهلة الاستخدام
- رسائل واضحة ومفيدة
- دليل استخدام شامل
- دعم فني متوفر

---

**تاريخ الإنشاء:** <?= date('Y-m-d') ?>  
**الإصدار:** 1.0  
**المطور:** نظام HealthKey 