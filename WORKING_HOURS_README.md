# صفحة ساعات العمل للصيدلي

## نظرة عامة

تم إنشاء صفحة ساعات العمل للصيدلي في نظام HealthKey، والتي تسمح للصيادلة بإدارة ساعات عملهم لكل يوم من أيام الأسبوع.

## الميزات

### 1. إدارة ساعات العمل
- تحديد ساعات العمل لكل يوم من أيام الأسبوع
- إمكانية تحديد أيام راحة
- تحديد وقت البداية والنهاية لكل يوم
- حفظ وتحديث ساعات العمل

### 2. واجهة المستخدم
- تصميم متجاوب ومريح للاستخدام
- عرض ملخص للجدول الحالي
- إحصائيات سريعة (عدد أيام العمل، إجمالي الساعات)
- نصائح مفيدة للمستخدم

### 3. التحقق من صحة البيانات
- التحقق من أن وقت النهاية بعد وقت البداية
- تعطيل حقول الوقت للأيام غير العاملة
- تحديث حالة الأيام في الوقت الفعلي

## الملفات المضافة/المعدلة

### 1. المتحكم (Controller)
**الملف:** `app/controllers/PharmacistController.php`

#### الدوال المضافة:
- `workingHours()` - عرض صفحة ساعات العمل
- `getWorkingHours($pharmacistId)` - الحصول على ساعات العمل
- `updateWorkingHours($pharmacistId)` - تحديث ساعات العمل

### 2. صفحة العرض (View)
**الملف:** `views/pharmacist/working-hours.php`

#### المكونات:
- نموذج لتحديث ساعات العمل
- ملخص للجدول الحالي
- إحصائيات سريعة
- نصائح للمستخدم
- JavaScript للتحقق من صحة البيانات

### 3. قاعدة البيانات
**الملف:** `create_working_hours_table.php`

#### جدول `working_hours`:
```sql
CREATE TABLE working_hours (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    user_type ENUM('doctor', 'pharmacist', 'nurse') NOT NULL,
    day ENUM('sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday') NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_working TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_day (user_id, user_type, day),
    INDEX idx_user_type (user_type),
    INDEX idx_day (day)
)
```

## كيفية الاستخدام

### 1. الوصول للصفحة
```
URL: /pharmacist/working-hours
```

### 2. تحديث ساعات العمل
1. تحديد الأيام التي تعمل فيها
2. تحديد وقت البداية والنهاية لكل يوم
3. الضغط على "حفظ ساعات العمل"

### 3. إعادة تعيين
- الضغط على "إعادة تعيين" لاستعادة القيم الافتراضية

## الاختبار

### ملف الاختبار
**الملف:** `test_working_hours.php`

#### ما يختبره:
- إنشاء جدول قاعدة البيانات
- إضافة بيانات تجريبية
- اختبار دالة `getWorkingHours`
- اختبار دالة `updateWorkingHours`
- عرض البيانات المحدثة

### تشغيل الاختبار
```bash
php test_working_hours.php
```

## الشريط الجانبي

تم إضافة رابط لصفحة ساعات العمل في الشريط الجانبي للصيدلي:
- **الرابط:** ساعات العمل
- **الأيقونة:** `bi-clock`
- **الموقع:** في قسم الحساب

## الأمان والتحقق

### 1. التحقق من تسجيل الدخول
- التأكد من أن المستخدم مسجل دخول
- التحقق من نوع المستخدم (صيدلي)

### 2. التحقق من صحة البيانات
- التحقق من أن وقت النهاية بعد وقت البداية
- التأكد من صحة تنسيق الوقت

### 3. حماية قاعدة البيانات
- استخدام Prepared Statements
- التحقق من صحة المدخلات

## التطوير المستقبلي

### 1. ميزات إضافية
- إضافة استثناءات للعطل الرسمية
- إمكانية تحديد فترات راحة
- دعم جداول عمل متعددة

### 2. تحسينات واجهة المستخدم
- إضافة تقويم تفاعلي
- إمكانية نسخ جدول من أسبوع لآخر
- عرض ساعات العمل للمرضى

### 3. التكامل
- ربط ساعات العمل مع نظام الحجوزات
- إرسال إشعارات عند تغيير الساعات
- تصدير الجدول بصيغ مختلفة

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في قاعدة البيانات**
   - تأكد من تشغيل `create_working_hours_table.php`
   - تحقق من صلاحيات قاعدة البيانات

2. **خطأ في عرض الصفحة**
   - تأكد من تسجيل دخول الصيدلي
   - تحقق من وجود البيانات في الجدول

3. **خطأ في حفظ البيانات**
   - تأكد من صحة تنسيق الوقت
   - تحقق من اتصال قاعدة البيانات

## الدعم

للمساعدة أو الإبلاغ عن مشاكل:
- راجع ملف الاختبار `test_working_hours.php`
- تحقق من سجلات الأخطاء
- تأكد من إعدادات قاعدة البيانات 