<!-- CSS Links -->
<link href="<?= App::url('public/css/style.css') ?>?v=<?= time() ?>" rel="stylesheet">
<link href="<?= App::url('public/css/admin.css') ?>?v=<?= time() ?>" rel="stylesheet">

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="fw-bold text-primary mb-2">
            <i class="bi bi-bell-fill me-2"></i>
            إدارة الإشعارات
        </h1>
        <p class="text-muted mb-0">إرسال إشعارات جماعية وإدارة الإشعارات النظام</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-outline-success" onclick="refreshNotifications()">
            <i class="bi bi-arrow-clockwise"></i>
            تحديث
        </button>
        <a href="<?= App::url('admin/dashboard') ?>" class="btn btn-outline-primary">
            <i class="bi bi-speedometer2"></i>
            لوحة التحكم
        </a>
    </div>
</div>

<!-- Flash Messages -->
<?php if (isset($_SESSION['flash_message'])): ?>
    <div class="alert alert-<?= $_SESSION['flash_type'] ?? 'info' ?> alert-dismissible fade show" role="alert">
        <i class="bi bi-<?= $_SESSION['flash_type'] === 'success' ? 'check-circle' : ($_SESSION['flash_type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?> me-2"></i>
        <?= $_SESSION['flash_message'] ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['flash_message'], $_SESSION['flash_type']); ?>
<?php endif; ?>

<div class="row">
    <!-- Send Notification Form -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-send me-2"></i>
                    إرسال إشعار جماعي
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= App::url('admin/notifications') ?>" id="notificationForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="title" class="form-label">
                                <i class="bi bi-type me-1"></i>
                                عنوان الإشعار
                                <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="title" name="title" 
                                   placeholder="أدخل عنوان الإشعار" required maxlength="100">
                            <div class="form-text">حد أقصى 100 حرف</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="user_type" class="form-label">
                                <i class="bi bi-people me-1"></i>
                                نوع المستخدمين
                                <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="user_type" name="user_type" required>
                                <option value="">اختر نوع المستخدمين</option>
                                <option value="all">جميع المستخدمين</option>
                                <option value="patient">المرضى فقط</option>
                                <option value="doctor">الأطباء فقط</option>
                                <option value="pharmacist">الصيادلة فقط</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notification_type" class="form-label">
                            <i class="bi bi-tag me-1"></i>
                            نوع الإشعار
                        </label>
                        <select class="form-select" id="notification_type" name="notification_type">
                            <option value="general">عام</option>
                            <option value="system">نظام</option>
                            <option value="appointment">موعد طبي</option>
                            <option value="prescription">وصفة طبية</option>
                            <option value="reminder">تذكير</option>
                            <option value="alert">تنبيه</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="message" class="form-label">
                            <i class="bi bi-chat-text me-1"></i>
                            رسالة الإشعار
                            <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control" id="message" name="message" rows="4" 
                                  placeholder="أدخل رسالة الإشعار" required maxlength="500"></textarea>
                        <div class="form-text">
                            <span id="charCount">0</span> / 500 حرف
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-send me-2"></i>
                            إرسال الإشعار
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="previewNotification()">
                            <i class="bi bi-eye me-2"></i>
                            معاينة
                        </button>
                        <button type="reset" class="btn btn-outline-danger">
                            <i class="bi bi-arrow-clockwise me-2"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Notification Statistics -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    إحصائيات الإشعارات
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="stats-item">
                            <div class="stats-number text-primary"><?= $recentNotifications ? count($recentNotifications) : 0 ?></div>
                            <div class="stats-label">إشعارات اليوم</div>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="stats-item">
                            <div class="stats-number text-success"><?= $userTypes ? count($userTypes) : 0 ?></div>
                            <div class="stats-label">أنواع المستخدمين</div>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <h6 class="mb-3">
                    <i class="bi bi-list-check me-2"></i>
                    أنواع الإشعارات
                </h6>
                <div class="notification-types">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="badge bg-primary">عام</span>
                        <small class="text-muted">إشعارات عامة</small>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="badge bg-success">نظام</span>
                        <small class="text-muted">إشعارات النظام</small>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="badge bg-info">موعد طبي</span>
                        <small class="text-muted">إشعارات المواعيد</small>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="badge bg-warning">وصفة طبية</span>
                        <small class="text-muted">إشعارات الوصفات</small>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="badge bg-secondary">تذكير</span>
                        <small class="text-muted">إشعارات التذكير</small>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="badge bg-danger">تنبيه</span>
                        <small class="text-muted">إشعارات التنبيه</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Notifications -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history me-2"></i>
                    الإشعارات الحديثة
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($recentNotifications)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>العنوان</th>
                                    <th>النوع</th>
                                    <th>المستلمون</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentNotifications as $notification): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="bi bi-<?= $notification['type'] === 'system' ? 'gear' : 'bell' ?> me-2 text-primary"></i>
                                                <div>
                                                    <div class="fw-bold"><?= htmlspecialchars($notification['title']) ?></div>
                                                    <small class="text-muted"><?= htmlspecialchars(substr($notification['message'], 0, 50)) ?>...</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $notification['type'] === 'system' ? 'success' : 'primary' ?>">
                                                <?= $notification['type'] === 'system' ? 'نظام' : 'عام' ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">جميع المستخدمين</span>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?= date('Y-m-d H:i', strtotime($notification['created_at'])) ?>
                                            </small>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">تم الإرسال</span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-bell-slash text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3">لا توجد إشعارات حديثة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-eye me-2"></i>
                    معاينة الإشعار
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="notification-preview">
                    <div class="d-flex align-items-center mb-3">
                        <div class="notification-icon bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                            <i class="bi bi-bell"></i>
                        </div>
                        <div>
                            <h6 class="mb-0" id="previewTitle">عنوان الإشعار</h6>
                            <small class="text-muted" id="previewType">نوع الإشعار</small>
                        </div>
                    </div>
                    <div class="notification-message p-3 bg-light rounded">
                        <p class="mb-0" id="previewMessage">رسالة الإشعار</p>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="bi bi-people me-1"></i>
                            سيتم إرسال هذا الإشعار إلى: <span id="previewRecipients">جميع المستخدمين</span>
                        </small>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="sendNotification()">
                    <i class="bi bi-send me-2"></i>
                    إرسال الإشعار
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Character counter for message
document.getElementById('message').addEventListener('input', function() {
    const charCount = this.value.length;
    document.getElementById('charCount').textContent = charCount;
    
    if (charCount > 450) {
        document.getElementById('charCount').classList.add('text-danger');
    } else {
        document.getElementById('charCount').classList.remove('text-danger');
    }
});

// Preview notification
function previewNotification() {
    const title = document.getElementById('title').value;
    const message = document.getElementById('message').value;
    const userType = document.getElementById('user_type').value;
    const notificationType = document.getElementById('notification_type').value;
    
    if (!title || !message || !userType) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    // Update preview content
    document.getElementById('previewTitle').textContent = title;
    document.getElementById('previewMessage').textContent = message;
    document.getElementById('previewType').textContent = getNotificationTypeLabel(notificationType);
    document.getElementById('previewRecipients').textContent = getUserTypeLabel(userType);
    
    // Show modal
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

// Get notification type label
function getNotificationTypeLabel(type) {
    const types = {
        'general': 'عام',
        'system': 'نظام',
        'appointment': 'موعد طبي',
        'prescription': 'وصفة طبية',
        'reminder': 'تذكير',
        'alert': 'تنبيه'
    };
    return types[type] || 'عام';
}

// Get user type label
function getUserTypeLabel(type) {
    const types = {
        'all': 'جميع المستخدمين',
        'patient': 'المرضى فقط',
        'doctor': 'الأطباء فقط',
        'pharmacist': 'الصيادلة فقط'
    };
    return types[type] || 'جميع المستخدمين';
}

// Send notification from preview
function sendNotification() {
    document.getElementById('notificationForm').submit();
}

// Refresh notifications
function refreshNotifications() {
    location.reload();
}

// Form validation
document.getElementById('notificationForm').addEventListener('submit', function(e) {
    const title = document.getElementById('title').value.trim();
    const message = document.getElementById('message').value.trim();
    const userType = document.getElementById('user_type').value;
    
    if (!title || !message || !userType) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return false;
    }
    
    if (title.length > 100) {
        e.preventDefault();
        alert('عنوان الإشعار يجب أن يكون أقل من 100 حرف');
        return false;
    }
    
    if (message.length > 500) {
        e.preventDefault();
        alert('رسالة الإشعار يجب أن تكون أقل من 500 حرف');
        return false;
    }
    
    // Show loading
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الإرسال...';
    submitBtn.disabled = true;
    
    // Re-enable after 3 seconds if form doesn't submit
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 3000);
});
</script>

<style>
.notification-preview {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
}

.notification-icon {
    width: 40px;
    height: 40px;
}

.stats-item {
    padding: 1rem;
    border-radius: 0.375rem;
    background-color: #f8f9fa;
}

.stats-number {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.stats-label {
    font-size: 0.875rem;
    color: #6c757d;
}

.notification-types .badge {
    font-size: 0.75rem;
}

.form-text {
    font-size: 0.875rem;
}

#charCount.text-danger {
    font-weight: bold;
}
</style> 