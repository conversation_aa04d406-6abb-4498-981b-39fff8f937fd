# صفحة حجز الموعد الجديدة - HealthKey

## نظرة عامة
تم إنشاء صفحة حجز موعد جديد للمرضى في نظام HealthKey، تتيح للمرضى حجز مواعيدهم الطبية بسهولة وكفاءة.

## المميزات الرئيسية

### 🎯 **واجهة مستخدم تفاعلية**
- نموذج حجز سهل الاستخدام
- اختيار الطبيب من قائمة الأطباء النشطين
- اختيار التاريخ والوقت المتاح
- إدخال سبب الموعد (اختياري)

### ⚡ **وظائف تفاعلية**
- **تحميل الأوقات المتاحة**: يتم تحميل الأوقات المتاحة تلقائياً عند اختيار الطبيب والتاريخ
- **معلومات الطبيب**: عرض معلومات الطبيب المختار
- **التحقق من التوفر**: التحقق الفوري من الأوقات المتاحة
- **التحقق من صحة البيانات**: تحقق شامل من البيانات المدخلة

### 🛡️ **الأمان والتحقق**
- التحقق من صحة التاريخ (لا يمكن حجز موعد في الماضي)
- التحقق من توفر الوقت المختار
- التحقق من صحة بيانات الطبيب
- حماية من الحجز المزدوج

### 📱 **تجربة مستخدم محسنة**
- رسائل تأكيد وتنبيه واضحة
- مؤشرات تحميل أثناء العمليات
- إعادة تعيين النموذج بسهولة
- نصائح مفيدة للحجز

## الملفات المضافة/المعدلة

### 1. **صفحة حجز الموعد** (`views/patient/book_appointment.php`)
```php
<!-- نموذج حجز الموعد -->
<form method="POST" action="<?= App::url('patient/book-appointment') ?>" id="bookingForm">
    <!-- اختيار الطبيب -->
    <select name="doctor_id" id="doctorSelect" required>
        <!-- قائمة الأطباء -->
    </select>
    
    <!-- اختيار التاريخ -->
    <input type="date" name="appointment_date" id="appointmentDate" required>
    
    <!-- اختيار الوقت -->
    <select name="appointment_time" id="appointmentTime" required>
        <!-- الأوقات المتاحة -->
    </select>
    
    <!-- سبب الموعد -->
    <textarea name="reason" placeholder="اشرح سبب زيارتك للطبيب..."></textarea>
</form>
```

### 2. **المسارات الجديدة** (`app/core/App.php`)
```php
'patient/book-appointment' => ['controller' => 'PatientController', 'method' => 'bookAppointment'],
'patient/get-available-slots' => ['controller' => 'PatientController', 'method' => 'getAvailableSlots'],
```

### 3. **الوظائف الجديدة** (`app/helpers/SessionHelper.php`)
```php
public static function hasValidationError($field)
{
    $errors = self::getValidationErrors();
    return isset($errors[$field]);
}
```

## الوظائف التقنية

### 🔧 **JavaScript التفاعلي**
```javascript
// تحميل الأوقات المتاحة
function loadAvailableSlots() {
    $.get('<?= App::url('patient/get-available-slots') ?>', {
        doctor_id: selectedDoctor,
        date: selectedDate
    }, function(response) {
        // تحديث قائمة الأوقات المتاحة
    });
}

// عرض معلومات الطبيب
function loadDoctorInfo() {
    // عرض تفاصيل الطبيب المختار
}

// التحقق من صحة النموذج
$('#bookingForm').submit(function(e) {
    // التحقق من البيانات قبل الإرسال
});
```

### 📊 **معالجة البيانات**
```php
// في PatientController::processBookAppointment()
$appointmentData = [
    'patient_id' => $patientId,
    'doctor_id' => $postData['doctor_id'],
    'appointment_date' => $postData['appointment_date'],
    'appointment_time' => $postData['appointment_time'],
    'reason' => $postData['reason'] ?? null,
    'status' => 'scheduled'
];

$appointmentId = $this->appointmentModel->create($appointmentData);
```

## كيفية الاستخدام

### 1. **الوصول للصفحة**
```
http://localhost:8000/patient/book-appointment
```

### 2. **خطوات الحجز**
1. **اختر الطبيب**: اختر من قائمة الأطباء النشطين
2. **اختر التاريخ**: اختر تاريخ الموعد (لا يمكن اختيار تاريخ في الماضي)
3. **اختر الوقت**: ستظهر الأوقات المتاحة تلقائياً
4. **أدخل السبب**: اكتب سبب زيارتك للطبيب (اختياري)
5. **اضغط حجز الموعد**: لتأكيد الحجز

### 3. **التحقق من التوفر**
- يتم التحقق من الأوقات المتاحة تلقائياً
- يتم عرض عدد المواعيد المتاحة
- يتم إظهار رسالة إذا لم تكن هناك مواعيد متاحة

## المميزات الإضافية

### 🎨 **التصميم المتجاوب**
- يعمل على جميع الأجهزة (الهواتف، الأجهزة اللوحية، الحواسيب)
- تصميم حديث وأنيق
- ألوان متناسقة مع باقي النظام

### 💡 **نصائح مفيدة**
- احجز موعدك مبكراً لضمان التوفر
- تأكد من وصولك قبل الموعد بـ 10 دقائق
- أحضر جميع الوثائق المطلوبة
- يمكنك إلغاء الموعد قبل 24 ساعة

### 🔔 **الإشعارات**
- إشعار للطبيب عند حجز موعد جديد
- رسائل تأكيد واضحة للمريض
- إشعارات في حالة حدوث خطأ

## الأمان والتحقق

### ✅ **التحقق من البيانات**
- التحقق من صحة معرف الطبيب
- التحقق من صحة التاريخ والوقت
- التحقق من توفر الموعد
- منع الحجز المزدوج

### 🛡️ **الحماية**
- التحقق من تسجيل دخول المريض
- حماية من الوصول غير المصرح
- التحقق من ملكية البيانات

## الاختبار

### 🧪 **اختبار الوظائف**
1. **اختبار حجز موعد صحيح**
2. **اختبار الحجز في وقت محجوز**
3. **اختبار الحجز في الماضي**
4. **اختبار الحجز بدون بيانات مطلوبة**
5. **اختبار تحميل الأوقات المتاحة**

### 📱 **اختبار التجاوب**
- اختبار على الهواتف الذكية
- اختبار على الأجهزة اللوحية
- اختبار على الحواسيب المكتبية

## التطوير المستقبلي

### 🚀 **مميزات مقترحة**
- إضافة خيار اختيار نوع الموعد (استشارة، فحص، متابعة)
- إضافة خيار تحديد الأولوية (عادية، عاجلة، طوارئ)
- إضافة خيار رفع ملفات مرفقة
- إضافة نظام تذكير بالمواعيد
- إضافة خيار إعادة جدولة المواعيد

### 🔧 **تحسينات تقنية**
- تحسين سرعة تحميل الأوقات المتاحة
- إضافة ذاكرة مؤقتة للبيانات
- تحسين تجربة المستخدم على الأجهزة المحمولة
- إضافة دعم للوضع المظلم

## الخلاصة

تم إنشاء صفحة حجز موعد شاملة ومتطورة تتيح للمرضى حجز مواعيدهم بسهولة وكفاءة. الصفحة تتميز بواجهة مستخدم حديثة ووظائف تفاعلية متقدمة مع ضمان الأمان والتحقق من صحة البيانات.

🎉 **تم إنشاء الصفحة بنجاح وجاهزة للاستخدام!** 