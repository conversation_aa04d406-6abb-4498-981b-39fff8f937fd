# الشريط الجانبي للوحة التحكم - HealthKey

## 📋 نظرة عامة

تم إنشاء نظام شريط جانبي متكامل ومتجاوب لجميع أنواع المستخدمين في نظام HealthKey. الشريط الجانبي مصمم ليكون سهل الاستخدام وجذاب بصرياً مع دعم كامل للغة العربية.

## 🎨 الميزات

### ✅ الميزات الأساسية
- **تصميم متجاوب**: يعمل بشكل مثالي على جميع الأجهزة
- **ألوان مخصصة**: كل نوع مستخدم له لون مميز
- **تنقل سلس**: انتقالات سلسة وتأثيرات بصرية
- **دعم RTL**: تصميم كامل للغة العربية
- **أيقونات Bootstrap**: استخدام أيقونات Bootstrap Icons
- **Tooltips**: تلميحات تفاعلية للروابط

### ✅ الميزات المتقدمة
- **إحصائيات حية**: عرض أعداد المستخدمين والمواعيد والوصفات
- **إشعارات**: عداد الإشعارات غير المقروءة
- **حالة النظام**: عرض حالة الاتصال والوقت الحالي
- **طي/فتح**: إمكانية طي الشريط الجانبي على سطح المكتب
- **إخفاء تلقائي**: إخفاء تلقائي على الهاتف عند النقر على رابط

## 📁 هيكل الملفات

```
views/partials/
├── sidebar_admin.php      # الشريط الجانبي للمدير
├── sidebar_doctor.php     # الشريط الجانبي للطبيب
├── sidebar_patient.php    # الشريط الجانبي للمريض
└── sidebar_pharmacist.php # الشريط الجانبي للصيدلي

public/css/
├── sidebar.css           # الأنماط المشتركة للشريط الجانبي
└── admin.css            # أنماط لوحة التحكم

public/js/
└── sidebar.js           # وظائف JavaScript للشريط الجانبي
```

## 🎯 أنواع المستخدمين والروابط

### 👨‍💼 المدير (Admin)
- **الرئيسية**: لوحة التحكم
- **الإدارة**: المستخدمين، المواعيد، الوصفات، السجلات الطبية
- **التواصل**: الإشعارات، الرسائل
- **التحليلات**: التقارير، التحليلات، سجل النشاطات
- **النظام**: الإعدادات، النسخ الاحتياطي، سجلات النظام، الصيانة

### 👨‍⚕️ الطبيب (Doctor)
- **الرئيسية**: لوحة التحكم
- **إدارة المرضى**: المرضى، السجلات الطبية
- **المواعيد**: المواعيد، الجدول الزمني
- **الوصفات الطبية**: الوصفات، وصفة جديدة
- **التواصل**: الإشعارات، الرسائل
- **التقارير**: التقارير، التحليلات
- **الإعدادات**: الملف الشخصي، الإعدادات

### 👤 المريض (Patient)
- **الرئيسية**: لوحة التحكم
- **المواعيد**: مواعيدي، حجز موعد
- **السجلات الطبية**: سجلاتي الطبية، نتائج المختبر
- **الوصفات الطبية**: وصفاتي الطبية، الأدوية
- **التواصل**: الإشعارات، الرسائل، تواصل مع الطبيب
- **الصحة**: متابعة الصحة، العلامات الحيوية، الحساسية
- **الإعدادات**: الملف الشخصي، الإعدادات، جهات الاتصال في الطوارئ

### 💊 الصيدلي (Pharmacist)
- **الرئيسية**: لوحة التحكم
- **الوصفات الطبية**: الوصفات الطبية، صرف الأدوية، تفاصيل الوصفة
- **المخزون**: إدارة المخزون، الأدوية، الموردين
- **التقارير**: التقارير، تقرير الصرف، تقرير المخزون
- **التواصل**: الإشعارات، الرسائل، تواصل مع الأطباء
- **الإعدادات**: الملف الشخصي، الإعدادات، ساعات العمل

## 🎨 الألوان والتصميم

### الألوان المخصصة
- **المدير**: أزرق (#2c5aa0)
- **الطبيب**: أحمر (#e74c3c)
- **المريض**: أخضر (#27ae60)
- **الصيدلي**: بنفسجي (#9b59b6)

### التصميم
- **خلفية**: تدرج داكن مع تأثيرات شفافة
- **الخطوط**: خطوط واضحة ومقروءة
- **الظلال**: ظلال ناعمة للعمق
- **الانتقالات**: انتقالات سلسة (0.3s cubic-bezier)

## 📱 التجاوب

### سطح المكتب (Desktop)
- عرض ثابت: 280px
- إمكانية الطي/الفتح
- تخطيط جانبي مع المحتوى الرئيسي

### الهاتف (Mobile)
- عرض كامل الشاشة
- إخفاء تلقائي عند النقر
- Overlay للخلفية
- زر إغلاق في الأعلى

## 🔧 الوظائف JavaScript

### الوظائف الأساسية
```javascript
// تبديل الشريط الجانبي
toggleSidebar()

// تحديث عداد الإشعارات
updateNotificationCount(count)

// تحديث الإحصائيات
updateSidebarStats(stats)

// تمييز الرابط النشط
highlightActiveLink()
```

### الأحداث المدعومة
- **تحميل الصفحة**: تهيئة تلقائية
- **تغيير الحجم**: تحديث التخطيط
- **النقر خارج الشريط**: إخفاء على الهاتف
- **النقر على الروابط**: إخفاء تلقائي على الهاتف

## 🎯 الاستخدام

### تضمين الشريط الجانبي
```php
<?php
$sidebarFile = "../views/partials/sidebar_{$currentUser['user_type']}.php";
if (file_exists($sidebarFile)) {
    include $sidebarFile;
}
?>
```

### التحقق من المسار النشط
```php
<?= App::isCurrentPath('admin/dashboard') ? 'active' : '' ?>
```

### عرض الإحصائيات
```php
<span class="nav-badge bg-success"><?= $stats['users']['total'] ?? 0 ?></span>
```

## 📊 الإحصائيات المدعومة

### المدير
- `$stats['users']['total']` - إجمالي المستخدمين
- `$stats['appointments']['total']` - إجمالي المواعيد
- `$stats['prescriptions']['total']` - إجمالي الوصفات

### الطبيب
- `$stats['patients']['total']` - إجمالي المرضى
- `$stats['appointments']['today']` - مواعيد اليوم
- `$stats['prescriptions']['total']` - إجمالي الوصفات

### المريض
- `$stats['appointments']['upcoming']` - المواعيد القادمة
- `$stats['prescriptions']['active']` - الوصفات النشطة

### الصيدلي
- `$stats['prescriptions']['pending']` - الوصفات المعلقة
- `$stats['inventory']['low_stock']` - المخزون المنخفض

## 🔔 الإشعارات

### عرض عداد الإشعارات
```php
<?php if (SessionHelper::get('unread_notifications', 0) > 0): ?>
    <span class="nav-badge bg-danger"><?= SessionHelper::get('unread_notifications') ?></span>
<?php endif; ?>
```

## 🎨 التخصيص

### إضافة رابط جديد
```php
<li class="nav-item">
    <a class="nav-link <?= App::isCurrentPath('admin/new-page') ? 'active' : '' ?>" 
       href="<?= App::url('admin/new-page') ?>" data-bs-toggle="tooltip" title="صفحة جديدة">
        <i class="bi bi-new-icon"></i>
        <span>صفحة جديدة</span>
        <span class="nav-badge bg-info">5</span>
    </a>
</li>
```

### تغيير الألوان
```css
.sidebar-admin .nav-link.active {
    background: linear-gradient(135deg, #your-color, #your-color-dark);
}
```

## 🚀 الأداء

### التحسينات المطبقة
- **CSS**: استخدام `will-change` للتحسين
- **JavaScript**: أحداث محسنة ومدمجة
- **الصور**: استخدام أيقونات SVG خفيفة
- **الخطوط**: خطوط النظام المحلية

### أفضل الممارسات
- استخدام `data-bs-toggle="tooltip"` للتلميحات
- التحقق من وجود الملف قبل التضمين
- استخدام `SessionHelper` للإشعارات
- تحديث الإحصائيات بشكل ديناميكي

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### الشريط الجانبي لا يظهر
```php
// تأكد من وجود الملف
$sidebarFile = "../views/partials/sidebar_{$currentUser['user_type']}.php";
if (file_exists($sidebarFile)) {
    include $sidebarFile;
} else {
    echo "<!-- Sidebar file not found: $sidebarFile -->";
}
```

#### الروابط لا تعمل
```php
// تأكد من إعداد المسارات في App.php
'admin/dashboard' => ['controller' => 'AdminController', 'method' => 'dashboard']
```

#### الإحصائيات لا تظهر
```php
// تأكد من تمرير البيانات
$stats = [
    'users' => ['total' => 150],
    'appointments' => ['total' => 45],
    'prescriptions' => ['total' => 23]
];
```

## 📝 التحديثات المستقبلية

### الميزات المخطط لها
- [ ] دعم الوضع المظلم
- [ ] إمكانية تخصيص الترتيب
- [ ] دعم الإشعارات الفورية
- [ ] تحسين الأداء أكثر
- [ ] دعم المزيد من الأيقونات

### التحسينات المقترحة
- إضافة تأثيرات بصرية أكثر
- دعم التنقل بالكيبورد
- تحسين إمكانية الوصول
- إضافة المزيد من الإحصائيات

---

**تم إنشاء هذا التوثيق بواسطة فريق تطوير HealthKey**
**آخر تحديث**: <?= date('Y-m-d H:i') ?> 