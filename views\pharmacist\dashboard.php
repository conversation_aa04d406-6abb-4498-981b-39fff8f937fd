<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">مرحباً، <?= htmlspecialchars($pharmacist['first_name']) ?></h1>
        <p class="text-muted">إليك ملخص عن نشاط الصيدلية</p>
    </div>
    <div>
        <?php if ($unreadNotifications > 0): ?>
            <a href="<?= App::url('pharmacist/notifications') ?>" class="btn btn-outline-primary position-relative">
                <i class="bi bi-bell"></i>
                الإشعارات
                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                    <?= $unreadNotifications ?>
                </span>
            </a>
        <?php endif; ?>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['active_prescriptions'] ?></h4>
                        <p class="mb-0">الوصفات النشطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-prescription2 display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['today_dispensed'] ?></h4>
                        <p class="mb-0">الأدوية المصروفة اليوم</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-capsule display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['total_dispensed'] ?></h4>
                        <p class="mb-0">إجمالي الأدوية المصروفة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clipboard2-check display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['expired_prescriptions'] ?></h4>
                        <p class="mb-0">الوصفات المنتهية الصلاحية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-exclamation-triangle display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- البحث في الوصفات -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-search me-2"></i>
                    البحث في الوصفات
                </h5>
                <a href="<?= App::url('pharmacist/prescriptions') ?>" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <form id="prescriptionSearchForm" class="mb-3">
                    <div class="input-group">
                        <input type="text" class="form-control" id="prescriptionCode" 
                               placeholder="أدخل كود الوصفة" required>
                        <button class="btn btn-primary" type="submit">
                            <i class="bi bi-search me-1"></i>
                            بحث
                        </button>
                    </div>
                </form>
                
                <div id="searchResult" class="mt-3" style="display: none;">
                    <!-- نتيجة البحث ستظهر هنا -->
                </div>
                
                <div class="text-center py-4">
                    <i class="bi bi-prescription display-4 text-muted mb-3"></i>
                    <p class="text-muted">ابحث عن الوصفات باستخدام الكود</p>
                </div>
            </div>
        </div>
    </div>

    <!-- الأدوية المصروفة حديثاً -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-capsule me-2"></i>
                    الأدوية المصروفة حديثاً
                </h5>
                <a href="<?= App::url('pharmacist/dispensing-report') ?>" class="btn btn-sm btn-outline-primary">
                    تقرير الصرف
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($recentDispensed)): ?>
                    <?php foreach ($recentDispensed as $medication): ?>
                        <div class="d-flex align-items-center mb-3 p-3 bg-light rounded">
                            <div class="flex-grow-1">
                                <h6 class="mb-1"><?= htmlspecialchars($medication['medication_name']) ?></h6>
                                <p class="mb-1 text-muted">
                                    المريض: <?= htmlspecialchars($medication['patient_name']) ?>
                                </p>
                                <p class="mb-0 text-muted">
                                    <i class="bi bi-calendar me-1"></i>
                                    <?= DateHelper::formatArabic($medication['dispensed_at']) ?>
                                </p>
                            </div>
                            <div>
                                <span class="badge bg-success">
                                    تم الصرف
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-capsule display-4 text-muted mb-3"></i>
                        <p class="text-muted">لا توجد أدوية مصروفة حديثاً</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الوصفات النشطة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-prescription2 me-2"></i>
                    الوصفات النشطة
                </h5>
                <a href="<?= App::url('pharmacist/prescriptions') ?>" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($activePrescriptions)): ?>
                    <?php foreach ($activePrescriptions as $prescription): ?>
                        <div class="d-flex align-items-center mb-3 p-3 bg-light rounded">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    <a href="<?= App::url('pharmacist/prescription/' . $prescription['id']) ?>" class="text-decoration-none">
                                        <?= htmlspecialchars($prescription['prescription_code']) ?>
                                    </a>
                                </h6>
                                <p class="mb-1 text-muted">د. <?= htmlspecialchars($prescription['doctor_name']) ?></p>
                                <p class="mb-0 text-muted">
                                    <i class="bi bi-calendar me-1"></i>
                                    انتهاء الصلاحية: <?= DateHelper::formatArabic($prescription['expiry_date']) ?>
                                </p>
                            </div>
                            <div>
                                <span class="badge bg-success">
                                    نشطة
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-prescription display-4 text-muted mb-3"></i>
                        <p class="text-muted">لا توجد وصفات نشطة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- الإشعارات الحديثة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-bell me-2"></i>
                    الإشعارات الحديثة
                </h5>
                <a href="<?= App::url('pharmacist/notifications') ?>" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($notifications)): ?>
                    <?php foreach ($notifications as $notification): ?>
                        <div class="d-flex align-items-start mb-3 p-3 bg-light rounded <?= !$notification['is_read'] ? 'border-start border-primary border-3' : '' ?>">
                            <div class="me-3">
                                <i class="bi <?= Notification::getTypeIcon($notification['type']) ?> text-<?= Notification::getTypeColor($notification['type']) ?>"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1"><?= htmlspecialchars($notification['title']) ?></h6>
                                <p class="mb-1 text-muted small"><?= htmlspecialchars($notification['message']) ?></p>
                                <p class="mb-0 text-muted small">
                                    <?= Notification::formatTime($notification['created_at']) ?>
                                </p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-bell-slash display-4 text-muted mb-3"></i>
                        <p class="text-muted">لا توجد إشعارات</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="<?= App::url('pharmacist/prescriptions') ?>" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-search display-6 mb-2"></i>
                            <span>البحث في الوصفات</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="<?= App::url('pharmacist/dispensing-report') ?>" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-file-earmark-text display-6 mb-2"></i>
                            <span>تقرير الصرف</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="<?= App::url('pharmacist/notifications') ?>" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-bell display-6 mb-2"></i>
                            <span>الإشعارات</span>
                        </a>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="<?= App::url('pharmacist/profile') ?>" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-person-gear display-6 mb-2"></i>
                            <span>الملف الشخصي</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Prescription Details -->
<div class="modal fade" id="prescriptionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الوصفة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="prescriptionModalBody">
                <!-- محتوى الوصفة سيظهر هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="dispenseBtn" style="display: none;">
                    <i class="bi bi-capsule me-1"></i>
                    صرف الدواء
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.getElementById('prescriptionSearchForm');
    const searchResult = document.getElementById('searchResult');
    const prescriptionModal = new bootstrap.Modal(document.getElementById('prescriptionModal'));
    const prescriptionModalBody = document.getElementById('prescriptionModalBody');
    const dispenseBtn = document.getElementById('dispenseBtn');

    // البحث في الوصفات
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const prescriptionCode = document.getElementById('prescriptionCode').value;
        
        if (!prescriptionCode) {
            alert('يرجى إدخال كود الوصفة');
            return;
        }

        // إظهار مؤشر التحميل
        searchResult.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p class="mt-2">جاري البحث...</p></div>';
        searchResult.style.display = 'block';

        // إرسال طلب البحث
        fetch('<?= App::url('pharmacist/search-prescription') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'prescription_code=' + encodeURIComponent(prescriptionCode)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showPrescriptionDetails(data.prescription, data.medications);
            } else {
                searchResult.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        ${data.message}
                    </div>
                `;
            }
        })
        .catch(error => {
            searchResult.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    حدث خطأ في البحث
                </div>
            `;
        });
    });

    // عرض تفاصيل الوصفة
    function showPrescriptionDetails(prescription, medications) {
        let medicationsHtml = '';
        medications.forEach(medication => {
            const dispensedStatus = medication.is_dispensed ? 
                '<span class="badge bg-success">تم الصرف</span>' : 
                '<span class="badge bg-warning">لم يتم الصرف</span>';
            
            const dispenseButton = !medication.is_dispensed ? 
                `<button class="btn btn-sm btn-primary dispense-medication" data-medication-id="${medication.id}">
                    <i class="bi bi-capsule me-1"></i>صرف الدواء
                </button>` : '';

            medicationsHtml += `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">${medication.medication_name}</h6>
                                <p class="mb-1 text-muted">الجرعة: ${medication.dosage}</p>
                                <p class="mb-1 text-muted">التكرار: ${medication.frequency}</p>
                                <p class="mb-1 text-muted">المدة: ${medication.duration}</p>
                                ${medication.instructions ? `<p class="mb-1 text-muted">التعليمات: ${medication.instructions}</p>` : ''}
                                <p class="mb-0 text-muted">الكمية: ${medication.quantity}</p>
                            </div>
                            <div class="text-end">
                                ${dispensedStatus}
                                ${dispenseButton}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        prescriptionModalBody.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">معلومات الوصفة</h6>
                    <p><strong>كود الوصفة:</strong> ${prescription.prescription_code}</p>
                    <p><strong>المريض:</strong> ${prescription.patient_name}</p>
                    <p><strong>الطبيب:</strong> ${prescription.doctor_name}</p>
                    <p><strong>تاريخ الإصدار:</strong> ${prescription.issue_date}</p>
                    <p><strong>تاريخ انتهاء الصلاحية:</strong> ${prescription.expiry_date}</p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">التشخيص</h6>
                    <p>${prescription.diagnosis || 'غير محدد'}</p>
                    ${prescription.notes ? `<h6 class="text-primary">ملاحظات</h6><p>${prescription.notes}</p>` : ''}
                </div>
            </div>
            <hr>
            <h6 class="text-primary">الأدوية</h6>
            ${medicationsHtml}
        `;

        prescriptionModal.show();
    }

    // صرف الدواء
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('dispense-medication')) {
            const medicationId = e.target.dataset.medicationId;
            const quantity = prompt('أدخل الكمية المصروفة:');
            
            if (quantity === null) return;
            
            // إرسال طلب صرف الدواء
            fetch('<?= App::url('pharmacist/dispense-medication') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `medication_id=${medicationId}&dispensed_quantity=${quantity}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم صرف الدواء بنجاح');
                    location.reload();
                } else {
                    alert('فشل في صرف الدواء: ' + data.message);
                }
            })
            .catch(error => {
                alert('حدث خطأ في صرف الدواء');
            });
        }
    });
});
</script> 