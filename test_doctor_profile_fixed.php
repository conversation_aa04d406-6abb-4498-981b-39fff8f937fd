<?php
/**
 * اختبار صفحة الملف الشخصي للطبيب - بعد الإصلاح
 */

require_once 'config.php';

echo "<h1>اختبار صفحة الملف الشخصي للطبيب - بعد الإصلاح</h1>";

try {
    // اختبار الوصول إلى صفحة الملف الشخصي
    $url = 'doctor/profile';
    
    echo "<h2>1. اختبار المسار</h2>";
    echo "<p>المسار: <code>$url</code></p>";
    
    // محاكاة تسجيل دخول طبيب
    $_SESSION['user_id'] = 2; // افتراض أن المستخدم رقم 2 هو طبيب
    $_SESSION['user_type'] = 'doctor';
    $_SESSION['user_name'] = 'د. أحمد محمد';
    
    echo "<p>✅ تم تسجيل دخول الطبيب</p>";
    
    // اختبار الوصول إلى الصفحة
    echo "<h2>2. اختبار الوصول إلى الصفحة</h2>";
    echo "<p><a href='public/index.php?url=$url' target='_blank'>فتح صفحة الملف الشخصي</a></p>";
    
    // اختبار SessionHelper
    echo "<h2>3. اختبار SessionHelper</h2>";
    
    // اختبار hasFlash
    SessionHelper::setFlash('test', 'رسالة اختبار', 'success');
    $hasFlash = SessionHelper::hasFlash();
    echo "<p>✅ hasFlash(): " . ($hasFlash ? 'true' : 'false') . "</p>";
    
    // اختبار getAllFlash
    $flashMessages = SessionHelper::getAllFlash();
    echo "<p>✅ getAllFlash(): " . count($flashMessages) . " رسائل</p>";
    
    // اختبار getValidationError
    SessionHelper::setValidationErrors(['test_field' => 'خطأ في الاختبار']);
    $validationError = SessionHelper::getValidationError('test_field');
    echo "<p>✅ getValidationError(): " . ($validationError ?: 'لا توجد أخطاء') . "</p>";
    
    // اختبار تحديث الملف الشخصي
    echo "<h2>4. اختبار تحديث الملف الشخصي</h2>";
    echo "<p>يمكن اختبار تحديث المعلومات من خلال النموذج في الصفحة</p>";
    
    // اختبار تغيير كلمة المرور
    echo "<h2>5. اختبار تغيير كلمة المرور</h2>";
    echo "<p>يمكن اختبار تغيير كلمة المرور من خلال النموذج المخصص</p>";
    
    // اختبار رفع الصورة
    echo "<h2>6. اختبار رفع الصورة الشخصية</h2>";
    echo "<p>يمكن اختبار رفع صورة شخصية جديدة</p>";
    
    echo "<h2>✅ انتهى الاختبار</h2>";
    echo "<p>إذا كانت الصفحة تفتح بدون أخطاء، فهذا يعني أن المشكلة قد تم حلها.</p>";
    
    // اختبار الروابط الأخرى
    echo "<h2>7. اختبار الروابط الأخرى</h2>";
    $testUrls = [
        'doctor/dashboard',
        'doctor/appointments',
        'doctor/patients',
        'doctor/prescriptions',
        'doctor/reports',
        'doctor/analytics'
    ];
    
    echo "<p>روابط اختبار إضافية:</p>";
    echo "<ul>";
    foreach ($testUrls as $testUrl) {
        echo "<li><a href='public/index.php?url=$testUrl' target='_blank'>$testUrl</a></li>";
    }
    echo "</ul>";
    
    // اختبار إضافي للجلسة
    echo "<h2>8. اختبار إضافي للجلسة</h2>";
    echo "<p>معرف المستخدم: " . SessionHelper::getUserId() . "</p>";
    echo "<p>نوع المستخدم: " . SessionHelper::getUserType() . "</p>";
    echo "<p>اسم المستخدم: " . SessionHelper::getUserName() . "</p>";
    echo "<p>تسجيل الدخول: " . (SessionHelper::isLoggedIn() ? 'نعم' : 'لا') . "</p>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 