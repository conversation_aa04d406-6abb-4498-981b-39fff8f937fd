<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">الإشعارات</h1>
        <p class="text-muted">إدارة الإشعارات والتنبيهات</p>
    </div>
    <div>
        <?php if ($unreadCount > 0): ?>
            <button class="btn btn-outline-primary" id="markAllReadBtn">
                <i class="bi bi-check-all me-1"></i>
                تسجيل الكل كمقروء
            </button>
        <?php endif; ?>
    </div>
</div>

<!-- Notifications List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-bell me-2"></i>
            الإشعارات
            <?php if ($unreadCount > 0): ?>
                <span class="badge bg-danger ms-2"><?= $unreadCount ?> جديد</span>
            <?php endif; ?>
        </h5>
    </div>
    <div class="card-body">
        <?php if (!empty($notifications)): ?>
            <div class="notifications-list">
                <?php foreach ($notifications as $notification): ?>
                    <div class="notification-item p-3 mb-3 rounded <?= !$notification['is_read'] ? 'bg-light border-start border-primary border-3' : 'bg-white border' ?>" 
                         data-notification-id="<?= $notification['id'] ?>">
                        <div class="d-flex align-items-start">
                            <div class="me-3">
                                <i class="bi <?= Notification::getTypeIcon($notification['type']) ?> text-<?= Notification::getTypeColor($notification['type']) ?> fs-4"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="mb-1 <?= !$notification['is_read'] ? 'fw-bold' : '' ?>">
                                        <?= htmlspecialchars($notification['title']) ?>
                                    </h6>
                                    <div class="d-flex align-items-center">
                                        <?php if (!$notification['is_read']): ?>
                                            <span class="badge bg-primary me-2">جديد</span>
                                        <?php endif; ?>
                                        <small class="text-muted">
                                            <?= Notification::formatTime($notification['created_at']) ?>
                                        </small>
                                    </div>
                                </div>
                                <p class="mb-2 text-muted"><?= htmlspecialchars($notification['message']) ?></p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="bi bi-tag me-1"></i>
                                        <?= Notification::getTypeLabel($notification['type']) ?>
                                    </small>
                                    <?php if (!$notification['is_read']): ?>
                                        <button class="btn btn-sm btn-outline-primary mark-read-btn" 
                                                data-notification-id="<?= $notification['id'] ?>">
                                            <i class="bi bi-check me-1"></i>
                                            تسجيل كمقروء
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if (count($notifications) >= 50): ?>
                <nav aria-label="صفحات الإشعارات" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <span class="page-link">السابق</span>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">1</span>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">2</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">3</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">التالي</a>
                        </li>
                    </ul>
                </nav>
            <?php endif; ?>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="bi bi-bell-slash display-4 text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد إشعارات</h5>
                <p class="text-muted">ستظهر هنا الإشعارات الجديدة عند وصولها</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Notification Types Summary -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-prescription2 display-4 text-primary mb-3"></i>
                <h5>وصفات جديدة</h5>
                <p class="text-muted">إشعارات الوصفات الطبية الجديدة</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-exclamation-triangle display-4 text-warning mb-3"></i>
                <h5>تنبيهات مهمة</h5>
                <p class="text-muted">تنبيهات حول الأدوية والتفاعلات</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-gear display-4 text-info mb-3"></i>
                <h5>إشعارات النظام</h5>
                <p class="text-muted">تحديثات وإشعارات النظام</p>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تسجيل إشعار كمقروء
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('mark-read-btn')) {
            const notificationId = e.target.dataset.notificationId;
            markNotificationAsRead(notificationId, e.target);
        }
    });

    // تسجيل جميع الإشعارات كمقروءة
    document.getElementById('markAllReadBtn')?.addEventListener('click', function() {
        if (confirm('هل تريد تسجيل جميع الإشعارات كمقروءة؟')) {
            markAllNotificationsAsRead();
        }
    });

    // دالة تسجيل إشعار كمقروء
    function markNotificationAsRead(notificationId, button) {
        fetch('<?= App::url('pharmacist/mark-notification-read') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `notification_id=${notificationId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث واجهة المستخدم
                const notificationItem = button.closest('.notification-item');
                notificationItem.classList.remove('bg-light', 'border-start', 'border-primary', 'border-3');
                notificationItem.classList.add('bg-white', 'border');
                
                // إزالة علامة "جديد"
                const newBadge = notificationItem.querySelector('.badge');
                if (newBadge) {
                    newBadge.remove();
                }
                
                // إزالة زر "تسجيل كمقروء"
                button.remove();
                
                // تحديث العداد
                updateNotificationCount();
            } else {
                alert('فشل في تحديث الإشعار: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في تحديث الإشعار');
        });
    }

    // دالة تسجيل جميع الإشعارات كمقروءة
    function markAllNotificationsAsRead() {
        fetch('<?= App::url('pharmacist/mark-all-notifications-read') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث جميع الإشعارات
                document.querySelectorAll('.notification-item').forEach(item => {
                    item.classList.remove('bg-light', 'border-start', 'border-primary', 'border-3');
                    item.classList.add('bg-white', 'border');
                    
                    // إزالة علامات "جديد"
                    const newBadges = item.querySelectorAll('.badge');
                    newBadges.forEach(badge => badge.remove());
                    
                    // إزالة أزرار "تسجيل كمقروء"
                    const markReadBtns = item.querySelectorAll('.mark-read-btn');
                    markReadBtns.forEach(btn => btn.remove());
                });
                
                // إخفاء زر "تسجيل الكل كمقروء"
                const markAllReadBtn = document.getElementById('markAllReadBtn');
                if (markAllReadBtn) {
                    markAllReadBtn.style.display = 'none';
                }
                
                // تحديث العداد
                updateNotificationCount();
                
                alert(`تم تسجيل ${data.count} إشعار كمقروء`);
            } else {
                alert('فشل في تحديث الإشعارات: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في تحديث الإشعارات');
        });
    }

    // دالة تحديث عداد الإشعارات
    function updateNotificationCount() {
        const unreadCount = document.querySelectorAll('.notification-item:not(.bg-white)').length;
        const badge = document.querySelector('.badge.bg-danger');
        
        if (badge) {
            if (unreadCount === 0) {
                badge.remove();
            } else {
                badge.textContent = unreadCount;
            }
        }
    }

    // تحديث الإشعارات تلقائياً كل دقيقة
    setInterval(function() {
        // يمكن إضافة كود لتحديث الإشعارات الجديدة هنا
    }, 60000);
});
</script>

<style>
.notification-item {
    transition: all 0.3s ease;
    cursor: pointer;
}

.notification-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.notification-item.unread {
    background-color: rgba(13, 110, 253, 0.05);
    border-left: 4px solid #0d6efd;
}

.mark-read-btn {
    transition: all 0.3s ease;
}

.mark-read-btn:hover {
    transform: scale(1.05);
}

.badge {
    font-size: 0.75rem;
}

.notifications-list {
    max-height: 600px;
    overflow-y: auto;
}

/* تخصيص شريط التمرير */
.notifications-list::-webkit-scrollbar {
    width: 6px;
}

.notifications-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.notifications-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.notifications-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style> 