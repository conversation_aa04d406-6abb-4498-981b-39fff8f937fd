<?php

/**
 * مساعد إدارة الجلسات
 * يوفر وظائف لإدارة الجلسات، رسائل Flash، والتحقق من تسجيل الدخول
 */
class SessionHelper
{
    /**
     * بدء الجلسة إذا لم تكن مبدوءة
     */
    public static function start()
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    /**
     * تعيين قيمة في الجلسة
     */
    public static function set($key, $value)
    {
        self::start();
        $_SESSION[$key] = $value;
    }

    /**
     * الحصول على قيمة من الجلسة
     */
    public static function get($key, $default = null)
    {
        self::start();
        return $_SESSION[$key] ?? $default;
    }

    /**
     * التحقق من وجود مفتاح في الجلسة
     */
    public static function has($key)
    {
        self::start();
        return isset($_SESSION[$key]);
    }

    /**
     * حذف مفتاح من الجلسة
     */
    public static function remove($key)
    {
        self::start();
        if (isset($_SESSION[$key])) {
            unset($_SESSION[$key]);
            return true;
        }
        return false;
    }

    /**
     * مسح جميع بيانات الجلسة
     */
    public static function clear()
    {
        self::start();
        $_SESSION = [];
    }

    /**
     * تدمير الجلسة بالكامل
     */
    public static function destroy()
    {
        self::start();
        
        // مسح جميع متغيرات الجلسة
        $_SESSION = [];
        
        // حذف كوكي الجلسة إذا كان موجوداً
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        // تدمير الجلسة
        session_destroy();
    }

    /**
     * إعادة إنشاء معرف الجلسة (لمنع session fixation)
     */
    public static function regenerate($deleteOldSession = true)
    {
        self::start();
        session_regenerate_id($deleteOldSession);
    }

    /**
     * تعيين رسالة flash
     */
    public static function setFlash($key, $message, $type = 'info')
    {
        self::start();
        $_SESSION['flash'][$key] = [
            'message' => $message,
            'type' => $type
        ];
    }

    /**
     * الحصول على رسالة flash وحذفها
     */
    public static function getFlash($key)
    {
        self::start();
        if (isset($_SESSION['flash'][$key])) {
            $flash = $_SESSION['flash'][$key];
            unset($_SESSION['flash'][$key]);
            return $flash;
        }
        return null;
    }

    /**
     * الحصول على جميع رسائل flash وحذفها
     */
    public static function getAllFlash()
    {
        self::start();
        $flashes = $_SESSION['flash'] ?? [];
        unset($_SESSION['flash']);
        return $flashes;
    }

    /**
     * التحقق من وجود رسائل flash
     */
    public static function hasFlash($key = null)
    {
        self::start();
        if ($key === null) {
            return !empty($_SESSION['flash']);
        }
        return isset($_SESSION['flash'][$key]);
    }

    /**
     * تعيين رسالة نجاح
     */
    public static function setSuccess($message)
    {
        self::setFlash('success', $message, 'success');
    }

    /**
     * تعيين رسالة خطأ
     */
    public static function setError($message)
    {
        self::setFlash('error', $message, 'error');
    }

    /**
     * تعيين رسالة تحذير
     */
    public static function setWarning($message)
    {
        self::setFlash('warning', $message, 'warning');
    }

    /**
     * تعيين رسالة معلومات
     */
    public static function setInfo($message)
    {
        self::setFlash('info', $message, 'info');
    }

    /**
     * الحصول على رسالة النجاح
     */
    public static function getSuccess()
    {
        return self::getFlash('success');
    }

    /**
     * الحصول على رسالة الخطأ
     */
    public static function getError()
    {
        return self::getFlash('error');
    }

    /**
     * الحصول على رسالة التحذير
     */
    public static function getWarning()
    {
        return self::getFlash('warning');
    }

    /**
     * الحصول على رسالة المعلومات
     */
    public static function getInfo()
    {
        return self::getFlash('info');
    }

    /**
     * تسجيل دخول المستخدم
     */
    public static function login($user)
    {
        self::start();
        self::regenerate(); // أمان إضافي
        
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_type'] = $user['user_type'];
        $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
    }

    /**
     * تسجيل خروج المستخدم
     */
    public static function logout()
    {
        self::destroy();
    }

    /**
     * التحقق من تسجيل الدخول
     */
    public static function isLoggedIn()
    {
        self::start();
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }

    /**
     * الحصول على معرف المستخدم الحالي
     */
    public static function getUserId()
    {
        return self::get('user_id');
    }

    /**
     * الحصول على نوع المستخدم الحالي
     */
    public static function getUserType()
    {
        return self::get('user_type');
    }

    /**
     * الحصول على اسم المستخدم الحالي
     */
    public static function getUserName()
    {
        return self::get('user_name');
    }

    /**
     * الحصول على بريد المستخدم الحالي
     */
    public static function getUserEmail()
    {
        return self::get('user_email');
    }

    /**
     * الحصول على بيانات المستخدم الحالي
     */
    public static function getCurrentUser()
    {
        if (!self::isLoggedIn()) {
            return null;
        }

        return [
            'id' => self::getUserId(),
            'user_type' => self::getUserType(),
            'first_name' => explode(' ', self::getUserName())[0] ?? '',
            'last_name' => explode(' ', self::getUserName())[1] ?? '',
            'full_name' => self::getUserName(),
            'email' => self::getUserEmail(),
            'login_time' => self::get('login_time'),
            'last_activity' => self::get('last_activity')
        ];
    }

    /**
     * التحقق من نوع المستخدم
     */
    public static function isUserType($type)
    {
        return self::getUserType() === $type;
    }

    /**
     * التحقق من كون المستخدم مدير
     */
    public static function isAdmin()
    {
        return self::isUserType('admin');
    }

    /**
     * التحقق من كون المستخدم طبيب
     */
    public static function isDoctor()
    {
        return self::isUserType('doctor');
    }

    /**
     * التحقق من كون المستخدم مريض
     */
    public static function isPatient()
    {
        return self::isUserType('patient');
    }

    /**
     * التحقق من كون المستخدم صيدلي
     */
    public static function isPharmacist()
    {
        return self::isUserType('pharmacist');
    }

    /**
     * تحديث وقت آخر نشاط
     */
    public static function updateLastActivity()
    {
        self::set('last_activity', time());
    }

    /**
     * التحقق من انتهاء صلاحية الجلسة
     */
    public static function isExpired($timeout = 3600) // ساعة واحدة افتراضياً
    {
        $lastActivity = self::get('last_activity');
        if ($lastActivity && (time() - $lastActivity) > $timeout) {
            return true;
        }
        return false;
    }

    /**
     * تعيين البيانات المؤقتة (تُحذف بعد قراءتها مرة واحدة)
     */
    public static function setTempData($key, $value, $ttl = 300) // 5 دقائق افتراضياً
    {
        self::start();
        $_SESSION['temp_data'][$key] = [
            'value' => $value,
            'expires' => time() + $ttl
        ];
    }

    /**
     * الحصول على البيانات المؤقتة
     */
    public static function getTempData($key)
    {
        self::start();
        if (isset($_SESSION['temp_data'][$key])) {
            $data = $_SESSION['temp_data'][$key];
            
            // التحقق من انتهاء الصلاحية
            if (time() > $data['expires']) {
                unset($_SESSION['temp_data'][$key]);
                return null;
            }
            
            // حذف البيانات بعد قراءتها
            unset($_SESSION['temp_data'][$key]);
            return $data['value'];
        }
        return null;
    }

    /**
     * تنظيف البيانات المؤقتة المنتهية الصلاحية
     */
    public static function cleanupTempData()
    {
        self::start();
        if (isset($_SESSION['temp_data'])) {
            $now = time();
            foreach ($_SESSION['temp_data'] as $key => $data) {
                if ($now > $data['expires']) {
                    unset($_SESSION['temp_data'][$key]);
                }
            }
        }
    }

    /**
     * حفظ البيانات القديمة للنماذج
     */
    public static function setOldInput($data)
    {
        self::setTempData('old_input', $data, 600); // 10 دقائق
    }

    /**
     * الحصول على البيانات القديمة للنماذج
     */
    public static function getOldInput($key = null, $default = null)
    {
        $oldInput = self::getTempData('old_input') ?? [];
        
        if ($key === null) {
            return $oldInput;
        }
        
        return $oldInput[$key] ?? $default;
    }

    /**
     * حفظ أخطاء التحقق
     */
    public static function setValidationErrors($errors)
    {
        self::setTempData('validation_errors', $errors, 600); // 10 دقائق
    }

    /**
     * الحصول على أخطاء التحقق
     */
    public static function getValidationErrors()
    {
        return self::getTempData('validation_errors') ?? [];
    }

    /**
     * الحصول على خطأ تحقق محدد
     */
    public static function getValidationError($field)
    {
        $errors = self::getValidationErrors();
        return $errors[$field] ?? null;
    }

    /**
     * التحقق من وجود أخطاء تحقق
     */
    public static function hasValidationErrors()
    {
        $errors = self::getValidationErrors();
        return !empty($errors);
    }

    /**
     * التحقق من وجود خطأ تحقق لحقل محدد
     */
    public static function hasValidationError($field)
    {
        $errors = self::getValidationErrors();
        return isset($errors[$field]);
    }

    /**
     * إنشاء رمز CSRF
     */
    public static function generateCSRFToken()
    {
        self::start();
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }

    /**
     * التحقق من رمز CSRF
     */
    public static function verifyCSRFToken($token)
    {
        self::start();
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }

    /**
     * الحصول على معلومات الجلسة
     */
    public static function getSessionInfo()
    {
        self::start();
        return [
            'session_id' => session_id(),
            'session_name' => session_name(),
            'login_time' => self::get('login_time'),
            'last_activity' => self::get('last_activity'),
            'user_id' => self::getUserId(),
            'user_type' => self::getUserType(),
            'user_name' => self::getUserName(),
            'is_logged_in' => self::isLoggedIn()
        ];
    }

    /**
     * تصدير بيانات الجلسة للتصحيح (في بيئة التطوير فقط)
     */
    public static function debug()
    {
        if (defined('APP_DEBUG') && APP_DEBUG) {
            self::start();
            return $_SESSION;
        }
        return ['debug' => 'disabled'];
    }
}
