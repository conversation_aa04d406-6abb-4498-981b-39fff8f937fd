<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">عرض الرسالة</h1>
        <p class="text-muted">تفاصيل الرسالة</p>
    </div>
    <div>
        <a href="<?= App::url('pharmacist/messages') ?>" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            العودة للرسائل
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-envelope me-2"></i>
                        تفاصيل الرسالة
                    </h5>
                    <div>
                        <?php if ($message['read_at'] === null): ?>
                            <span class="badge bg-warning">غير مقروءة</span>
                        <?php else: ?>
                            <span class="badge bg-success">مقروءة</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- معلومات الرسالة -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">المرسل:</label>
                            <p class="mb-0"><?= htmlspecialchars($message['sender_name'] ?? 'غير محدد') ?></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">المستلم:</label>
                            <p class="mb-0"><?= htmlspecialchars($message['recipient_name'] ?? 'غير محدد') ?></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">التاريخ:</label>
                            <p class="mb-0"><?= date('Y-m-d H:i', strtotime($message['created_at'])) ?></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">النوع:</label>
                            <span class="badge bg-secondary"><?= htmlspecialchars($message['type']) ?></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">الأولوية:</label>
                            <?php
                            $priorityClass = 'bg-success';
                            if ($message['priority'] === 'high') $priorityClass = 'bg-danger';
                            elseif ($message['priority'] === 'medium') $priorityClass = 'bg-warning';
                            ?>
                            <span class="badge <?= $priorityClass ?>"><?= htmlspecialchars($message['priority']) ?></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">الحالة:</label>
                            <span class="badge bg-primary"><?= htmlspecialchars($message['status']) ?></span>
                        </div>
                    </div>
                </div>

                <!-- الموضوع -->
                <div class="mb-4">
                    <label class="form-label fw-bold">الموضوع:</label>
                    <div class="alert alert-primary">
                        <h5 class="mb-0"><?= htmlspecialchars($message['subject']) ?></h5>
                    </div>
                </div>

                <!-- محتوى الرسالة -->
                <div class="mb-4">
                    <label class="form-label fw-bold">محتوى الرسالة:</label>
                    <div class="border rounded p-3 bg-light">
                        <p class="mb-0"><?= nl2br(htmlspecialchars($message['content'])) ?></p>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <?php if ($message['read_at']): ?>
                    <div class="mb-3">
                        <label class="form-label fw-bold">تاريخ القراءة:</label>
                        <p class="mb-0"><?= date('Y-m-d H:i', strtotime($message['read_at'])) ?></p>
                    </div>
                <?php endif; ?>

                <!-- أزرار الإجراءات -->
                <div class="d-flex justify-content-between">
                    <div>
                        <a href="<?= App::url('pharmacist/compose-message') ?>" class="btn btn-primary">
                            <i class="bi bi-reply me-2"></i>
                            رد
                        </a>
                        <a href="<?= App::url('pharmacist/compose-message') ?>" class="btn btn-info">
                            <i class="bi bi-share me-2"></i>
                            إعادة توجيه
                        </a>
                    </div>
                    <div>
                        <a href="<?= App::url('pharmacist/delete-message/' . $message['id']) ?>" 
                           class="btn btn-danger"
                           onclick="return confirm('هل أنت متأكد من حذف هذه الرسالة؟')">
                            <i class="bi bi-trash me-2"></i>
                            حذف
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- معلومات سريعة -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    معلومات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>معرف الرسالة:</span>
                    <span class="badge bg-secondary">#<?= $message['id'] ?></span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>نوع الرسالة:</span>
                    <span class="badge bg-primary"><?= htmlspecialchars($message['type']) ?></span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>الأولوية:</span>
                    <?php
                    $priorityClass = 'bg-success';
                    if ($message['priority'] === 'high') $priorityClass = 'bg-danger';
                    elseif ($message['priority'] === 'medium') $priorityClass = 'bg-warning';
                    ?>
                    <span class="badge <?= $priorityClass ?>"><?= htmlspecialchars($message['priority']) ?></span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>الحالة:</span>
                    <span class="badge bg-info"><?= htmlspecialchars($message['status']) ?></span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>تاريخ الإرسال:</span>
                    <small class="text-muted"><?= date('Y-m-d', strtotime($message['created_at'])) ?></small>
                </div>
            </div>
        </div>

        <!-- إحصائيات الرسائل -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    إحصائيات الرسائل
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>إجمالي الرسائل:</span>
                    <span class="badge bg-primary"><?= $stats['total_messages'] ?? 0 ?></span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>غير مقروءة:</span>
                    <span class="badge bg-success"><?= $stats['unread_messages'] ?? 0 ?></span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>مرسلة:</span>
                    <span class="badge bg-info"><?= $stats['sent_messages'] ?? 0 ?></span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>مسودات:</span>
                    <span class="badge bg-warning"><?= $stats['draft_messages'] ?? 0 ?></span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>مهمة:</span>
                    <span class="badge bg-secondary"><?= $stats['important_messages'] ?? 0 ?></span>
                </div>
            </div>
        </div>

        <!-- إجراءات سريعة -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?= App::url('pharmacist/compose-message') ?>" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-plus-circle me-2"></i>
                        رسالة جديدة
                    </a>
                    <a href="<?= App::url('pharmacist/messages') ?>" class="btn btn-outline-secondary btn-sm">
                        <i class="bi bi-envelope me-2"></i>
                        جميع الرسائل
                    </a>
                    <a href="<?= App::url('pharmacist/messages?type=sent') ?>" class="btn btn-outline-info btn-sm">
                        <i class="bi bi-send me-2"></i>
                        الرسائل المرسلة
                    </a>
                    <a href="<?= App::url('pharmacist/messages?type=draft') ?>" class="btn btn-outline-warning btn-sm">
                        <i class="bi bi-file-earmark-text me-2"></i>
                        المسودات
                    </a>
                </div>
            </div>
        </div>

        <!-- نصائح -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightbulb me-2"></i>
                    نصائح
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle me-2"></i>نصائح للرد:</h6>
                    <ul class="mb-0">
                        <li>اقرأ الرسالة بعناية قبل الرد</li>
                        <li>استخدم لغة مهنية ومحترمة</li>
                        <li>أجب على جميع النقاط المذكورة</li>
                        <li>اختبر ردك قبل الإرسال</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div> 