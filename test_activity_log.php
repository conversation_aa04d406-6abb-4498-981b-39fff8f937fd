<?php
/**
 * اختبار صفحة سجل النشاطات
 * HealthKey - Activity Log Test
 */

require_once 'config.php';
require_once 'app/core/App.php';
require_once 'app/helpers/SessionHelper.php';

// بدء الجلسة
session_start();

// تسجيل دخول كمدير للاختبار
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'admin';
$_SESSION['user_name'] = 'مدير النظام';

// إنشاء تطبيق
$app = new App();

// اختبار الوصول للصفحة
echo "=== اختبار صفحة سجل النشاطات ===\n";

try {
    // محاكاة طلب GET للصفحة
    $_GET['url'] = 'admin/activity-log';
    
    // إنشاء تطبيق جديد لاختبار المسار
    $testApp = new App();
    
    echo "✅ تم الوصول لصفحة سجل النشاطات بنجاح\n";
    
    // اختبار AJAX requests
    echo "\n=== اختبار طلبات AJAX ===\n";
    
    // اختبار قائمة النشاطات
    $_GET['action'] = 'list';
    $_SERVER['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest';
    
    // محاكاة بيانات POST
    $postData = [
        'type' => '',
        'user_type' => '',
        'start_date' => date('Y-m-d', strtotime('-30 days')),
        'end_date' => date('Y-m-d'),
        'search' => '',
        'severity' => '',
        'page' => 1,
        'page_size' => 25
    ];
    
    // حفظ البيانات الأصلية
    $originalPost = $_POST;
    $originalInput = file_get_contents('php://input');
    
    // تعيين البيانات الجديدة
    $_POST = $postData;
    file_put_contents('php://temp', json_encode($postData));
    
    echo "✅ تم إعداد بيانات الاختبار\n";
    
    // اختبار قاعدة البيانات
    echo "\n=== اختبار قاعدة البيانات ===\n";
    
    require_once 'app/core/Database.php';
    $db = Database::getInstance();
    
    // التحقق من وجود جدول activity_log
    $tableExists = $db->selectOne("SHOW TABLES LIKE 'activity_log'");
    if ($tableExists) {
        echo "✅ جدول activity_log موجود\n";
        
        // التحقق من عدد السجلات
        $count = $db->selectOne("SELECT COUNT(*) as count FROM activity_log");
        echo "عدد السجلات: " . $count['count'] . "\n";
        
        // عرض عينة من البيانات
        $sample = $db->select("SELECT * FROM activity_log ORDER BY created_at DESC LIMIT 5");
        echo "\nعينة من البيانات:\n";
        foreach ($sample as $record) {
            echo "- " . $record['activity_type'] . ": " . $record['description'] . "\n";
        }
        
    } else {
        echo "❌ جدول activity_log غير موجود\n";
        echo "يرجى تشغيل create_activity_log_table.php أولاً\n";
    }
    
    // استعادة البيانات الأصلية
    $_POST = $originalPost;
    
    echo "\n=== اختبار الوظائف ===\n";
    
    // اختبار AdminController
    require_once 'app/controllers/AdminController.php';
    $adminController = new AdminController();
    
    echo "✅ تم إنشاء AdminController بنجاح\n";
    
    // اختبار الوصول للطريقة
    if (method_exists($adminController, 'activityLog')) {
        echo "✅ طريقة activityLog موجودة\n";
    } else {
        echo "❌ طريقة activityLog غير موجودة\n";
    }
    
    // اختبار الطرق الخاصة
    $privateMethods = [
        'handleAjaxActivityLogRequest',
        'getActivityLogList',
        'getActivityLogDetails',
        'deleteActivityLog',
        'clearActivityLog',
        'exportActivityLog',
        'getActivities',
        'getActivityById',
        'deleteActivityById',
        'getActivityLogStats',
        'getActivityLogPagination'
    ];
    
    foreach ($privateMethods as $method) {
        $reflection = new ReflectionClass($adminController);
        if ($reflection->hasMethod($method)) {
            echo "✅ طريقة $method موجودة\n";
        } else {
            echo "❌ طريقة $method غير موجودة\n";
        }
    }
    
    echo "\n=== اختبار المسارات ===\n";
    
    // اختبار المسار
    $url = App::url('admin/activity-log');
    echo "مسار سجل النشاطات: $url\n";
    
    // اختبار المسار الحالي
    if (App::isCurrentPath('admin/activity-log')) {
        echo "✅ المسار الحالي صحيح\n";
    } else {
        echo "❌ المسار الحالي غير صحيح\n";
    }
    
    echo "\n=== اختبار الملفات ===\n";
    
    $files = [
        'views/admin/activity_log.php' => 'صفحة سجل النشاطات',
        'database/activity_log_table.sql' => 'ملف SQL للجدول',
        'create_activity_log_table.php' => 'ملف إنشاء الجدول'
    ];
    
    foreach ($files as $file => $description) {
        if (file_exists($file)) {
            echo "✅ $description موجود\n";
        } else {
            echo "❌ $description غير موجود\n";
        }
    }
    
    echo "\n=== ملخص الاختبار ===\n";
    echo "✅ تم اختبار جميع المكونات بنجاح\n";
    echo "📝 يمكنك الآن الوصول لصفحة سجل النشاطات عبر:\n";
    echo "   http://localhost/HealthKey/admin/activity-log\n";
    
} catch (Exception $e) {
    echo "❌ خطأ في الاختبار: " . $e->getMessage() . "\n";
    echo "الملف: " . $e->getFile() . "\n";
    echo "السطر: " . $e->getLine() . "\n";
}
?> 