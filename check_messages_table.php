<?php
/**
 * التحقق من وجود جدول الرسائل
 */

require_once 'config.php';

try {
    $db = new PDO(
        'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4',
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n";
    
    // التحقق من وجود جدول الرسائل
    $stmt = $db->query("SHOW TABLES LIKE 'messages'");
    $result = $stmt->fetch();
    
    if ($result) {
        echo "✅ جدول الرسائل موجود\n";
        
        // التحقق من عدد الرسائل
        $stmt = $db->query("SELECT COUNT(*) as count FROM messages");
        $count = $stmt->fetch()['count'];
        echo "📊 عدد الرسائل: $count\n";
        
        // عرض عينة من الرسائل
        $stmt = $db->query("SELECT * FROM messages LIMIT 3");
        $messages = $stmt->fetchAll();
        
        echo "\n📋 عينة من الرسائل:\n";
        foreach ($messages as $message) {
            echo "- {$message['subject']} ({$message['type']}) - {$message['status']}\n";
        }
        
    } else {
        echo "❌ جدول الرسائل غير موجود\n";
        echo "💡 قم بتشغيل create_messages_table.php لإنشاء الجدول\n";
    }
    
} catch (PDOException $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}
?> 