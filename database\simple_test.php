<?php
/**
 * اختبار بسيط لقاعدة البيانات
 */

// تضمين ملف التكوين
require_once __DIR__ . '/../config.php';

echo "🧪 اختبار قاعدة البيانات HealthKey\n";
echo str_repeat("=", 50) . "\n";

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n\n";
    
    // اختبار الجداول
    echo "📋 الجداول الموجودة:\n";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($tables as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM `$table`");
        $count = $stmt->fetch()['count'];
        echo "  ✅ $table: $count سجل\n";
    }
    
    echo "\n";
    
    // اختبار المستخدمين
    echo "👥 المستخدمين حسب النوع:\n";
    $stmt = $pdo->query("SELECT user_type, COUNT(*) as count FROM users GROUP BY user_type");
    $users = $stmt->fetchAll();
    
    foreach ($users as $user) {
        $typeLabels = [
            'admin' => 'مدير',
            'doctor' => 'طبيب', 
            'patient' => 'مريض',
            'pharmacist' => 'صيدلي'
        ];
        $label = $typeLabels[$user['user_type']] ?? $user['user_type'];
        echo "  👤 $label: {$user['count']}\n";
    }
    
    echo "\n";
    
    // اختبار حساب المدير
    $stmt = $pdo->query("SELECT email FROM users WHERE user_type = 'admin' LIMIT 1");
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "✅ حساب المدير موجود: {$admin['email']}\n";
    } else {
        echo "❌ حساب المدير غير موجود\n";
    }
    
    // اختبار الإعدادات
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM system_settings");
    $settingsCount = $stmt->fetch()['count'];
    echo "⚙️ إعدادات النظام: $settingsCount إعداد\n";
    
    // اختبار استعلام بسيط
    $stmt = $pdo->query("SELECT 1 as test");
    $test = $stmt->fetch();
    
    if ($test['test'] == 1) {
        echo "✅ اختبار الاستعلام نجح\n";
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "🎉 جميع الاختبارات نجحت! قاعدة البيانات جاهزة للاستخدام\n";
    echo "\n📋 معلومات الدخول:\n";
    echo "👨‍💼 المدير: <EMAIL>\n";
    echo "👨‍⚕️ الطبيب: <EMAIL>\n";
    echo "👤 المريض: <EMAIL>\n";
    echo "💊 الصيدلي: <EMAIL>\n";
    echo "🔑 كلمة المرور: password\n";
    
} catch (PDOException $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
    exit(1);
}
?>
