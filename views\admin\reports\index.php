<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="fw-bold text-primary mb-1">
            <i class="bi bi-graph-up me-2"></i>
            التقارير والإحصائيات
        </h2>
        <p class="text-muted mb-0">مراقبة وتحليل أداء النظام الشامل</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-outline-primary" onclick="exportReport()">
            <i class="bi bi-download me-2"></i>
            تصدير التقرير
        </button>
        <button class="btn btn-primary" onclick="refreshReports()">
            <i class="bi bi-arrow-clockwise me-2"></i>
            تحديث
        </button>
    </div>
</div>

<!-- فلاتر التاريخ -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label class="form-label fw-semibold">التاريخ من</label>
                <input type="date" class="form-control" name="start_date" 
                       value="<?= htmlspecialchars($startDate) ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label fw-semibold">التاريخ إلى</label>
                <input type="date" class="form-control" name="end_date" 
                       value="<?= htmlspecialchars($endDate) ?>">
            </div>
            <div class="col-md-3">
                <label class="form-label fw-semibold">نوع التقرير</label>
                <select class="form-select" name="type">
                    <option value="overview" <?= $reportType === 'overview' ? 'selected' : '' ?>>نظرة عامة</option>
                    <option value="users" <?= $reportType === 'users' ? 'selected' : '' ?>>المستخدمين</option>
                    <option value="appointments" <?= $reportType === 'appointments' ? 'selected' : '' ?>>المواعيد</option>
                    <option value="prescriptions" <?= $reportType === 'prescriptions' ? 'selected' : '' ?>>الوصفات</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label fw-semibold">&nbsp;</label>
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-funnel-fill me-2"></i>
                        تطبيق الفلاتر
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- الإحصائيات السريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card bg-gradient-primary text-white">
            <div class="stats-icon">
                <i class="bi bi-people"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number"><?= $userStats['total'] ?? 0 ?></div>
                <div class="stats-label">إجمالي المستخدمين</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-gradient-success text-white">
            <div class="stats-icon">
                <i class="bi bi-calendar-check"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number"><?= $appointmentStats['total'] ?? 0 ?></div>
                <div class="stats-label">إجمالي المواعيد</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-gradient-info text-white">
            <div class="stats-icon">
                <i class="bi bi-prescription2"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number"><?= $prescriptionStats['total'] ?? 0 ?></div>
                <div class="stats-label">إجمالي الوصفات</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-gradient-warning text-white">
            <div class="stats-icon">
                <i class="bi bi-calendar-day"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number"><?= $appointmentStats['today'] ?? 0 ?></div>
                <div class="stats-label">مواعيد اليوم</div>
            </div>
        </div>
    </div>
</div>

<!-- الإحصائيات الإضافية -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card bg-gradient-danger text-white">
            <div class="stats-icon">
                <i class="bi bi-speedometer2"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number"><?= $performanceStats['success_rate'] ?? 0 ?>%</div>
                <div class="stats-label">معدل نجاح المواعيد</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-gradient-secondary text-white">
            <div class="stats-icon">
                <i class="bi bi-currency-dollar"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number"><?= number_format($financialStats['total_revenue'] ?? 0) ?></div>
                <div class="stats-label">إجمالي الإيرادات</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-gradient-success text-white">
            <div class="stats-icon">
                <i class="bi bi-star-fill"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number"><?= $qualityStats['satisfaction_rate'] ?? 0 ?>/5</div>
                <div class="stats-label">معدل رضا المرضى</div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-gradient-info text-white">
            <div class="stats-icon">
                <i class="bi bi-activity"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number"><?= $activityStats['total_activity'] ?? 0 ?></div>
                <div class="stats-label">إجمالي النشاط</div>
            </div>
        </div>
    </div>
</div>

<!-- الرسوم البيانية -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-pie-chart me-2"></i>
                    توزيع المستخدمين حسب النوع
                </h5>
            </div>
            <div class="card-body">
                <canvas id="userTypeChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-bar-chart me-2"></i>
                    حالة المواعيد
                </h5>
            </div>
            <div class="card-body">
                <canvas id="appointmentStatusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- الجداول التفصيلية -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-list-ol me-2"></i>
                    أفضل الأطباء (حسب المواعيد)
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الطبيب</th>
                                <th>عدد المواعيد</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($appointmentStats['by_doctor'])): ?>
                                <?php foreach ($appointmentStats['by_doctor'] as $doctor): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($doctor['doctor_name']) ?></td>
                                        <td>
                                            <span class="badge bg-primary"><?= $doctor['count'] ?></span>
                                        </td>
                                        <td>
                                            <?= round(($doctor['count'] / $appointmentStats['total']) * 100, 1) ?>%
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="3" class="text-center text-muted">لا توجد بيانات</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-list-ol me-2"></i>
                    أفضل الأطباء (حسب الوصفات)
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>الطبيب</th>
                                <th>عدد الوصفات</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (!empty($prescriptionStats['by_doctor'])): ?>
                                <?php foreach ($prescriptionStats['by_doctor'] as $doctor): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($doctor['doctor_name']) ?></td>
                                        <td>
                                            <span class="badge bg-success"><?= $doctor['count'] ?></span>
                                        </td>
                                        <td>
                                            <?= round(($doctor['count'] / $prescriptionStats['total']) * 100, 1) ?>%
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="3" class="text-center text-muted">لا توجد بيانات</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات إضافية -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    إحصائيات إضافية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-primary"><?= $userStats['active'] ?? 0 ?></div>
                            <small class="text-muted">المستخدمين النشطين</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-success"><?= $appointmentStats['this_week'] ?? 0 ?></div>
                            <small class="text-muted">مواعيد هذا الأسبوع</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-info"><?= count($userStats['by_type'] ?? []) ?></div>
                            <small class="text-muted">أنواع المستخدمين</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4 text-warning"><?= count($appointmentStats['by_status'] ?? []) ?></div>
                            <small class="text-muted">حالات المواعيد</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الإحصائيات التفصيلية -->
<div class="row mb-4">
    <!-- إحصائيات الأداء -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-speedometer2 me-2"></i>
                    إحصائيات الأداء
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-success"><?= $performanceStats['success_rate'] ?? 0 ?>%</div>
                            <small class="text-muted">معدل النجاح</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-info"><?= $performanceStats['avg_response_time'] ?? 0 ?> دقيقة</div>
                            <small class="text-muted">متوسط وقت الاستجابة</small>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-warning"><?= $performanceStats['attendance_rate'] ?? 0 ?>%</div>
                            <small class="text-muted">معدل الحضور</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-danger"><?= $performanceStats['no_show_count'] ?? 0 ?></div>
                            <small class="text-muted">لم يحضر</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات مالية -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-currency-dollar me-2"></i>
                    الإحصائيات المالية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-success"><?= number_format($financialStats['total_revenue'] ?? 0) ?> ريال</div>
                            <small class="text-muted">إجمالي الإيرادات</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-info"><?= $financialStats['avg_cost_per_appointment'] ?? 0 ?> ريال</div>
                            <small class="text-muted">متوسط التكلفة</small>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-warning"><?= $financialStats['monthly_growth'] ?? 0 ?>%</div>
                            <small class="text-muted">معدل النمو الشهري</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-primary"><?= count($financialStats['revenue_by_doctor'] ?? []) ?></div>
                            <small class="text-muted">أطباء نشطين</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- إحصائيات الجودة -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-star-fill me-2"></i>
                    إحصائيات الجودة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-warning"><?= $qualityStats['satisfaction_rate'] ?? 0 ?>/5</div>
                            <small class="text-muted">معدل رضا المرضى</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-success"><?= $qualityStats['rebooking_rate'] ?? 0 ?>%</div>
                            <small class="text-muted">معدل إعادة الحجز</small>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-danger"><?= $qualityStats['complaints_count'] ?? 0 ?></div>
                            <small class="text-muted">عدد الشكاوى</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-info"><?= $qualityStats['repeat_patients'] ?? 0 ?></div>
                            <small class="text-muted">مرضى متكررين</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات النشاط -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-activity me-2"></i>
                    إحصائيات النشاط
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-primary"><?= $activityStats['total_activity'] ?? 0 ?></div>
                            <small class="text-muted">إجمالي النشاط</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-success"><?= count($activityStats['busiest_days'] ?? []) ?></div>
                            <small class="text-muted">أيام نشطة</small>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row">
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-warning"><?= count($activityStats['peak_hours'] ?? []) ?></div>
                            <small class="text-muted">أوقات ذروة</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <div class="h4 text-info"><?= count($activityStats['doctor_activity'] ?? []) ?></div>
                            <small class="text-muted">أطباء نشطين</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// رسم بياني دائري لتوزيع المستخدمين
const userTypeCtx = document.getElementById('userTypeChart').getContext('2d');
const userTypeChart = new Chart(userTypeCtx, {
    type: 'doughnut',
    data: {
        labels: [
            <?php if (!empty($userStats['by_type'])): ?>
                <?php foreach ($userStats['by_type'] as $type): ?>
                    '<?= $type['user_type'] === 'patient' ? 'المرضى' : ($type['user_type'] === 'doctor' ? 'الأطباء' : ($type['user_type'] === 'pharmacist' ? 'الصيادلة' : 'المديرين')) ?>',
                <?php endforeach; ?>
            <?php endif; ?>
        ],
        datasets: [{
            data: [
                <?php if (!empty($userStats['by_type'])): ?>
                    <?php foreach ($userStats['by_type'] as $type): ?>
                        <?= $type['count'] ?>,
                    <?php endforeach; ?>
                <?php endif; ?>
            ],
            backgroundColor: [
                '#007bff',
                '#28a745',
                '#ffc107',
                '#dc3545'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// رسم بياني شريطي لحالة المواعيد
const appointmentStatusCtx = document.getElementById('appointmentStatusChart').getContext('2d');
const appointmentStatusChart = new Chart(appointmentStatusCtx, {
    type: 'bar',
    data: {
        labels: [
            <?php if (!empty($appointmentStats['by_status'])): ?>
                <?php foreach ($appointmentStats['by_status'] as $status): ?>
                    '<?= $status['status'] === 'scheduled' ? 'مجدول' : ($status['status'] === 'confirmed' ? 'مؤكد' : ($status['status'] === 'completed' ? 'مكتمل' : ($status['status'] === 'cancelled' ? 'ملغي' : 'لم يحضر'))) ?>',
                <?php endforeach; ?>
            <?php endif; ?>
        ],
        datasets: [{
            label: 'عدد المواعيد',
            data: [
                <?php if (!empty($appointmentStats['by_status'])): ?>
                    <?php foreach ($appointmentStats['by_status'] as $status): ?>
                        <?= $status['count'] ?>,
                    <?php endforeach; ?>
                <?php endif; ?>
            ],
            backgroundColor: [
                '#ffc107',
                '#28a745',
                '#007bff',
                '#dc3545',
                '#6c757d'
            ],
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// تصدير التقرير
function exportReport() {
    const currentUrl = new URL(window.location.href);
    const exportUrl = '<?= App::url('admin/reports/export') ?>' + currentUrl.search;
    window.open(exportUrl, '_blank');
}

// تحديث التقارير
function refreshReports() {
    location.reload();
}

// تحديث الرسوم البيانية عند تغيير الفلاتر
document.querySelector('form').addEventListener('submit', function() {
    showLoading();
});

// عرض رسالة التحميل
function showLoading() {
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'position-fixed top-50 start-50 translate-middle';
    loadingDiv.style.zIndex = '9999';
    loadingDiv.innerHTML = `
        <div class="bg-white p-3 rounded shadow">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <div class="mt-2">جاري تحديث التقارير...</div>
        </div>
    `;
    document.body.appendChild(loadingDiv);
}
</script> 