<?php
require_once 'config.php';
require_once 'app/models/Inventory.php';

// محاكاة تسجيل دخول صيدلي
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'pharmacist';
$_SESSION['user'] = [
    'id' => 1,
    'first_name' => 'أحمد',
    'last_name' => 'محمد',
    'email' => '<EMAIL>'
];

// إنشاء نموذج المخزون
$inventoryModel = new Inventory();

// الحصول على البيانات
$inventory = $inventoryModel->getAll(10);
$stats = $inventoryModel->getStats();
$categories = $inventoryModel->getCategories();

echo "<h1>اختبار صفحة المخزون</h1>";

echo "<h2>إحصائيات المخزون:</h2>";
echo "<ul>";
echo "<li>إجمالي الأدوية: " . $stats['total_items'] . "</li>";
echo "<li>منخفضة المخزون: " . $stats['low_stock'] . "</li>";
echo "<li>منتهية الصلاحية: " . $stats['expired'] . "</li>";
echo "<li>قيمة المخزون: " . number_format($stats['total_value'], 2) . " ريال</li>";
echo "</ul>";

echo "<h2>الأدوية في المخزون:</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>الاسم</th><th>الفئة</th><th>الكمية</th><th>السعر</th><th>تاريخ الانتهاء</th></tr>";

foreach ($inventory as $item) {
    $expiryClass = '';
    if (strtotime($item['expiry_date']) <= time()) {
        $expiryClass = 'style="color: red;"';
    } elseif (strtotime($item['expiry_date']) <= strtotime('+30 days')) {
        $expiryClass = 'style="color: orange;"';
    }
    
    echo "<tr>";
    echo "<td>" . htmlspecialchars($item['name']) . "</td>";
    echo "<td>" . htmlspecialchars($item['category']) . "</td>";
    echo "<td>" . $item['quantity'] . "</td>";
    echo "<td>" . number_format($item['unit_price'], 2) . " ريال</td>";
    echo "<td $expiryClass>" . $item['expiry_date'] . "</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h2>الفئات المتوفرة:</h2>";
echo "<ul>";
foreach ($categories as $category) {
    echo "<li>" . htmlspecialchars($category['category']) . "</li>";
}
echo "</ul>";

echo "<h2>✅ انتهى الاختبار بنجاح</h2>";
?> 