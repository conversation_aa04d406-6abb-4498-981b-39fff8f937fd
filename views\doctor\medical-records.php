<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-file-earmark-medical me-2"></i>
            السجلات الطبية
        </h1>
        <p class="text-muted">إدارة ومراجعة السجلات الطبية للمرضى</p>
    </div>
    <div>
        <a href="<?= App::url('doctor/add-medical-record') ?>" class="btn btn-success">
            <i class="bi bi-plus-circle me-2"></i>
            إضافة سجل جديد
        </a>
        <a href="<?= App::url('doctor/dashboard') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['total'] ?? 0 ?></h4>
                        <p class="mb-0">إجمالي السجلات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-file-earmark-medical display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['this_month'] ?? 0 ?></h4>
                        <p class="mb-0">سجلات هذا الشهر</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-month display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['unique_patients'] ?? 0 ?></h4>
                        <p class="mb-0">مرضى فريدون</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['today'] ?? 0 ?></h4>
                        <p class="mb-0">سجلات اليوم</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-day display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?= App::url('doctor/medical-records') ?>" class="row g-3">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="text" 
                           class="form-control" 
                           name="search" 
                           value="<?= htmlspecialchars($search) ?>"
                           placeholder="البحث في السجلات الطبية...">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search me-1"></i>
                        بحث
                    </button>
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" name="patient_id">
                    <option value="">جميع المرضى</option>
                    <?php foreach ($patients as $patient): ?>
                        <option value="<?= $patient['id'] ?>" <?= $selectedPatient == $patient['id'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars(User::getFullName($patient)) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <input type="date" 
                       class="form-control" 
                       name="date" 
                       value="<?= htmlspecialchars($selectedDate) ?>"
                       placeholder="تاريخ الزيارة">
            </div>
            <div class="col-md-2">
                <div class="d-flex gap-2">
                    <a href="<?= App::url('doctor/medical-records') ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        إعادة تعيين
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Medical Records List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-list-ul me-2"></i>
            قائمة السجلات الطبية
            <span class="badge bg-primary ms-2"><?= count($records) ?></span>
        </h5>
        <div>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                <i class="bi bi-printer me-1"></i>
                طباعة
            </button>
            <button type="button" class="btn btn-sm btn-outline-success" onclick="exportRecords()">
                <i class="bi bi-download me-1"></i>
                تصدير
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($records)): ?>
            <!-- Empty State -->
            <div class="text-center py-5">
                <i class="bi bi-file-earmark-medical display-1 text-muted"></i>
                <h4 class="mt-3 text-muted">لا توجد سجلات طبية</h4>
                <p class="text-muted">
                    <?php if (!empty($search) || !empty($selectedPatient) || !empty($selectedDate)): ?>
                        لم يتم العثور على سجلات تطابق المعايير المحددة
                    <?php else: ?>
                        لم يتم إنشاء أي سجلات طبية بعد
                    <?php endif; ?>
                </p>
                <a href="<?= App::url('doctor/add-medical-record') ?>" class="btn btn-success">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة سجل طبي جديد
                </a>
            </div>
        <?php else: ?>
            <!-- Records Table -->
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>المريض</th>
                            <th>تاريخ الزيارة</th>
                            <th>الشكوى الرئيسية</th>
                            <th>التشخيص</th>
                            <th>العلاج</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($records as $record): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                            <i class="bi bi-person text-primary"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?= htmlspecialchars($record['patient_name']) ?></h6>
                                            <small class="text-muted">
                                                معرف المريض: <?= $record['patient_id'] ?>
                                            </small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="badge bg-info mb-1">
                                            <?= date('Y-m-d', strtotime($record['visit_date'])) ?>
                                        </div>
                                        <br>
                                        <small class="text-muted">
                                            <?= date('H:i', strtotime($record['created_at'])) ?>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="<?= htmlspecialchars($record['chief_complaint']) ?>">
                                        <?= htmlspecialchars($record['chief_complaint'] ?? 'غير محدد') ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="<?= htmlspecialchars($record['diagnosis']) ?>">
                                        <?= htmlspecialchars($record['diagnosis'] ?? 'غير محدد') ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="<?= htmlspecialchars($record['treatment_plan']) ?>">
                                        <?= htmlspecialchars($record['treatment_plan'] ?? 'غير محدد') ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= App::url('doctor/view-medical-record/' . $record['id']) ?>" 
                                           class="btn btn-sm btn-outline-primary" 
                                           title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="<?= App::url('doctor/edit-medical-record/' . $record['id']) ?>" 
                                           class="btn btn-sm btn-outline-warning" 
                                           title="تعديل السجل">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteRecord(<?= $record['id'] ?>)" 
                                                title="حذف السجل">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Quick Actions -->
<?php if (!empty($records)): ?>
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="bi bi-file-earmark-medical display-4 mb-3"></i>
                    <h5>إضافة سجل طبي</h5>
                    <p class="mb-3">إضافة سجل طبي جديد لمريض</p>
                    <a href="<?= App::url('doctor/add-medical-record') ?>" class="btn btn-light">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة سجل
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="bi bi-prescription2 display-4 mb-3"></i>
                    <h5>إنشاء وصفة طبية</h5>
                    <p class="mb-3">إنشاء وصفة طبية جديدة</p>
                    <a href="<?= App::url('doctor/create-prescription') ?>" class="btn btn-light">
                        <i class="bi bi-plus-circle me-2"></i>
                        إنشاء وصفة
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <i class="bi bi-graph-up display-4 mb-3"></i>
                    <h5>التقارير</h5>
                    <p class="mb-3">عرض تقارير السجلات الطبية</p>
                    <a href="<?= App::url('doctor/reports') ?>" class="btn btn-light">
                        <i class="bi bi-graph-up me-2"></i>
                        عرض التقارير
                    </a>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- View Medical Record Modal -->
<div class="modal fade" id="viewMedicalRecordModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-file-earmark-medical me-2"></i>
                    تفاصيل السجل الطبي
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="medicalRecordDetails">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-warning" onclick="editRecord()">
                    <i class="bi bi-pencil me-2"></i>
                    تعديل
                </button>
                <button type="button" class="btn btn-success" onclick="printRecord()">
                    <i class="bi bi-printer me-2"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentRecordId = null;

function viewRecord(recordId) {
    currentRecordId = recordId;
    
    // تحميل تفاصيل السجل الطبي
    fetch(`<?= App::url('doctor/view-medical-record/') ?>${recordId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('medicalRecordDetails').innerHTML = data.html;
                new bootstrap.Modal(document.getElementById('viewMedicalRecordModal')).show();
            } else {
                alert('فشل في تحميل تفاصيل السجل الطبي');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحميل التفاصيل');
        });
}

function editRecord() {
    if (currentRecordId) {
        window.location.href = `<?= App::url('doctor/edit-medical-record/') ?>${currentRecordId}`;
    }
}

function deleteRecord(recordId) {
    if (confirm('هل أنت متأكد من حذف هذا السجل الطبي؟')) {
        fetch(`<?= App::url('doctor/delete-medical-record/') ?>${recordId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حذف السجل الطبي بنجاح');
                location.reload();
            } else {
                alert('فشل في حذف السجل الطبي: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف السجل الطبي');
        });
    }
}

function exportRecords() {
    const search = '<?= htmlspecialchars($search) ?>';
    const patientId = '<?= htmlspecialchars($selectedPatient) ?>';
    const date = '<?= htmlspecialchars($selectedDate) ?>';
    
    const url = `<?= App::url('doctor/export-medical-records') ?>?search=${encodeURIComponent(search)}&patient_id=${encodeURIComponent(patientId)}&date=${encodeURIComponent(date)}`;
    
    window.open(url, '_blank');
}

function printRecord() {
    if (currentRecordId) {
        const printWindow = window.open(`<?= App::url('doctor/print-medical-record/') ?>${currentRecordId}`, '_blank');
        printWindow.focus();
    }
}

// تحسين البحث
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[name="search"]');
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            this.form.submit();
        }, 500);
    });
    
    // تحديث الفلاتر تلقائياً
    const patientSelect = document.querySelector('select[name="patient_id"]');
    const dateInput = document.querySelector('input[name="date"]');
    
    [patientSelect, dateInput].forEach(element => {
        if (element) {
            element.addEventListener('change', function() {
                this.form.submit();
            });
        }
    });
});
</script> 