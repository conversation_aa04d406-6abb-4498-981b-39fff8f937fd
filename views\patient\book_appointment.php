<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-calendar-plus me-2"></i>
            حجز موعد جديد
        </h1>
        <p class="text-muted">احجز موعدك الطبي مع الطبيب المناسب</p>
    </div>
    <div>
        <a href="<?= App::url('patient/appointments') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للمواعيد
        </a>
    </div>
</div>

<!-- Booking Form -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-form me-2"></i>
                    معلومات الموعد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= App::url('patient/book-appointment') ?>" id="bookingForm">
                    <!-- Doctor Selection -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">اختر الطبيب <span class="text-danger">*</span></label>
                        <select class="form-select" name="doctor_id" id="doctorSelect" required>
                            <option value="">-- اختر الطبيب --</option>
                            <?php foreach ($doctors as $doctor): ?>
                                <option value="<?= $doctor['id'] ?>" 
                                        data-specialization="<?= htmlspecialchars($doctor['specialization'] ?? '') ?>"
                                        <?= (SessionHelper::getOldInput('doctor_id') == $doctor['id']) ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                    <?php if (!empty($doctor['specialization'])): ?>
                                        - <?= htmlspecialchars($doctor['specialization']) ?>
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (SessionHelper::hasValidationError('doctor_id')): ?>
                            <div class="text-danger mt-1"><?= SessionHelper::getValidationError('doctor_id') ?></div>
                        <?php endif; ?>
                    </div>

                    <!-- Date Selection -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">التاريخ <span class="text-danger">*</span></label>
                        <input type="date" 
                               class="form-control" 
                               name="appointment_date" 
                               id="appointmentDate"
                               min="<?= date('Y-m-d') ?>"
                               value="<?= SessionHelper::getOldInput('appointment_date') ?>"
                               required>
                        <?php if (SessionHelper::hasValidationError('appointment_date')): ?>
                            <div class="text-danger mt-1"><?= SessionHelper::getValidationError('appointment_date') ?></div>
                        <?php endif; ?>
                    </div>

                    <!-- Time Selection -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">الوقت <span class="text-danger">*</span></label>
                        <select class="form-select" name="appointment_time" id="appointmentTime" required>
                            <option value="">-- اختر الوقت --</option>
                        </select>
                        <?php if (SessionHelper::hasValidationError('appointment_time')): ?>
                            <div class="text-danger mt-1"><?= SessionHelper::getValidationError('appointment_time') ?></div>
                        <?php endif; ?>
                        <small class="text-muted">سيتم عرض الأوقات المتاحة بعد اختيار الطبيب والتاريخ</small>
                    </div>

                    <!-- Reason -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">سبب الموعد</label>
                        <textarea class="form-control" 
                                  name="reason" 
                                  rows="4" 
                                  placeholder="اشرح سبب زيارتك للطبيب..."><?= SessionHelper::getOldInput('reason') ?></textarea>
                        <?php if (SessionHelper::hasValidationError('reason')): ?>
                            <div class="text-danger mt-1"><?= SessionHelper::getValidationError('reason') ?></div>
                        <?php endif; ?>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                            <i class="bi bi-arrow-clockwise me-2"></i>
                            إعادة تعيين
                        </button>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="bi bi-calendar-check me-2"></i>
                            حجز الموعد
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Doctor Information -->
        <div class="card mb-4" id="doctorInfo" style="display: none;">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-person-badge me-2"></i>
                    معلومات الطبيب
                </h6>
            </div>
            <div class="card-body">
                <div id="doctorDetails">
                    <!-- Doctor details will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Available Slots -->
        <div class="card mb-4" id="availableSlots" style="display: none;">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-clock me-2"></i>
                    الأوقات المتاحة
                </h6>
            </div>
            <div class="card-body">
                <div id="slotsList">
                    <!-- Available slots will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Booking Tips -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-lightbulb me-2"></i>
                    نصائح للحجز
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        احجز موعدك مبكراً لضمان التوفر
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        تأكد من وصولك قبل الموعد بـ 10 دقائق
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        أحضر جميع الوثائق المطلوبة
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        يمكنك إلغاء الموعد قبل 24 ساعة
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mb-0">جاري التحقق من الأوقات المتاحة...</p>
            </div>
        </div>
    </div>
</div>

<script>
let selectedDoctor = null;
let selectedDate = null;

// Doctor selection change
$('#doctorSelect').change(function() {
    selectedDoctor = $(this).val();
    selectedDate = $('#appointmentDate').val();
    
    if (selectedDoctor && selectedDate) {
        loadAvailableSlots();
        loadDoctorInfo();
    } else {
        $('#availableSlots').hide();
        $('#doctorInfo').hide();
    }
});

// Date selection change
$('#appointmentDate').change(function() {
    selectedDate = $(this).val();
    selectedDoctor = $('#doctorSelect').val();
    
    if (selectedDoctor && selectedDate) {
        loadAvailableSlots();
    } else {
        $('#availableSlots').hide();
    }
});

// Load available time slots
function loadAvailableSlots() {
    if (!selectedDoctor || !selectedDate) return;
    
    $('#loadingModal').modal('show');
    
    $.get('<?= App::url('patient/get-available-slots') ?>', {
        doctor_id: selectedDoctor,
        date: selectedDate
    }, function(response) {
        $('#loadingModal').modal('hide');
        
        const timeSelect = $('#appointmentTime');
        timeSelect.empty();
        timeSelect.append('<option value="">-- اختر الوقت --</option>');
        
        if (response.slots && response.slots.length > 0) {
            response.slots.forEach(function(slot) {
                timeSelect.append(`<option value="${slot}">${slot}</option>`);
            });
            $('#availableSlots').show();
            $('#slotsList').html(`
                <div class="alert alert-success">
                    <i class="bi bi-check-circle me-2"></i>
                    متوفر ${response.slots.length} موعد في هذا اليوم
                </div>
            `);
        } else {
            $('#availableSlots').show();
            $('#slotsList').html(`
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    لا توجد مواعيد متاحة في هذا اليوم
                </div>
            `);
        }
    }, 'json').fail(function() {
        $('#loadingModal').modal('hide');
        showAlert('حدث خطأ في تحميل الأوقات المتاحة', 'error');
    });
}

// Load doctor information
function loadDoctorInfo() {
    if (!selectedDoctor) return;
    
    const selectedOption = $('#doctorSelect option:selected');
    const doctorName = selectedOption.text();
    const specialization = selectedOption.data('specialization');
    
    $('#doctorDetails').html(`
        <div class="text-center mb-3">
            <div class="avatar-lg bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                <i class="bi bi-person-badge display-4 text-primary"></i>
            </div>
            <h6 class="mb-1">${doctorName}</h6>
            ${specialization ? `<small class="text-muted">${specialization}</small>` : ''}
        </div>
        <div class="row text-center">
            <div class="col-6">
                <div class="border-end">
                    <h6 class="mb-1 text-primary">متاح</h6>
                    <small class="text-muted">حالة الطبيب</small>
                </div>
            </div>
            <div class="col-6">
                <h6 class="mb-1 text-success">30 دقيقة</h6>
                <small class="text-muted">مدة الموعد</small>
            </div>
        </div>
    `);
    
    $('#doctorInfo').show();
}

// Form validation
$('#bookingForm').submit(function(e) {
    const doctorId = $('#doctorSelect').val();
    const appointmentDate = $('#appointmentDate').val();
    const appointmentTime = $('#appointmentTime').val();
    
    if (!doctorId) {
        showAlert('يرجى اختيار الطبيب', 'error');
        e.preventDefault();
        return false;
    }
    
    if (!appointmentDate) {
        showAlert('يرجى اختيار التاريخ', 'error');
        e.preventDefault();
        return false;
    }
    
    if (!appointmentTime) {
        showAlert('يرجى اختيار الوقت', 'error');
        e.preventDefault();
        return false;
    }
    
    // Show loading state
    $('#submitBtn').prop('disabled', true).html(`
        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
        جاري الحجز...
    `);
});

// Reset form
function resetForm() {
    $('#bookingForm')[0].reset();
    $('#availableSlots').hide();
    $('#doctorInfo').hide();
    selectedDoctor = null;
    selectedDate = null;
}

// Show alert
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert
    $('.container-fluid').prepend(alertHtml);
    
    // Auto dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}

// Initialize form
$(document).ready(function() {
    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    $('#appointmentDate').attr('min', today);
    
    // If there are validation errors, show the form with old data
    if ($('.text-danger').length > 0) {
        const doctorId = '<?= SessionHelper::getOldInput('doctor_id') ?>';
        const appointmentDate = '<?= SessionHelper::getOldInput('appointment_date') ?>';
        
        if (doctorId && appointmentDate) {
            selectedDoctor = doctorId;
            selectedDate = appointmentDate;
            loadAvailableSlots();
            loadDoctorInfo();
        }
    }
});
</script>

<style>
.avatar-lg {
    width: 80px;
    height: 80px;
}

.form-control:focus,
.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}
</style> 