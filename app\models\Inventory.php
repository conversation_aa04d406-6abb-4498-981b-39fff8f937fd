<?php

/**
 * نموذج إدارة المخزون
 */
class Inventory
{
    private $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * الحصول على جميع الأدوية في المخزون
     */
    public function getAll($limit = null, $offset = 0)
    {
        $sql = "SELECT * FROM inventory ORDER BY name ASC";
        
        if ($limit) {
            $sql .= " LIMIT $limit OFFSET $offset";
        }
        
        return $this->db->select($sql);
    }

    /**
     * الحصول على دواء بواسطة المعرف
     */
    public function findById($id)
    {
        $sql = "SELECT * FROM inventory WHERE id = ?";
        return $this->db->selectOne($sql, [$id]);
    }

    /**
     * البحث في المخزون
     */
    public function search($query, $category = '')
    {
        $sql = "SELECT * FROM inventory WHERE 1=1";
        $params = [];

        if (!empty($query)) {
            $sql .= " AND (name LIKE ? OR generic_name LIKE ? OR description LIKE ?)";
            $searchTerm = "%$query%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        if (!empty($category)) {
            $sql .= " AND category = ?";
            $params[] = $category;
        }

        $sql .= " ORDER BY name ASC";
        
        return $this->db->select($sql, $params);
    }

    /**
     * إضافة دواء جديد للمخزون
     */
    public function create($data)
    {
        $sql = "INSERT INTO inventory (name, generic_name, category, dosage_form, strength, 
                manufacturer, description, quantity, unit_price, reorder_level, expiry_date, 
                batch_number, location, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $params = [
            $data['name'],
            $data['generic_name'],
            $data['category'],
            $data['dosage_form'],
            $data['strength'],
            $data['manufacturer'],
            $data['description'],
            $data['quantity'],
            $data['unit_price'],
            $data['reorder_level'],
            $data['expiry_date'],
            $data['batch_number'],
            $data['location']
        ];

        return $this->db->insert($sql, $params);
    }

    /**
     * تحديث معلومات الدواء
     */
    public function update($id, $data)
    {
        $sql = "UPDATE inventory SET 
                name = ?, generic_name = ?, category = ?, dosage_form = ?, 
                strength = ?, manufacturer = ?, description = ?, quantity = ?, 
                unit_price = ?, reorder_level = ?, expiry_date = ?, 
                batch_number = ?, location = ?, updated_at = NOW() 
                WHERE id = ?";
        
        $params = [
            $data['name'],
            $data['generic_name'],
            $data['category'],
            $data['dosage_form'],
            $data['strength'],
            $data['manufacturer'],
            $data['description'],
            $data['quantity'],
            $data['unit_price'],
            $data['reorder_level'],
            $data['expiry_date'],
            $data['batch_number'],
            $data['location'],
            $id
        ];

        return $this->db->update($sql, $params);
    }

    /**
     * حذف دواء من المخزون
     */
    public function delete($id)
    {
        $sql = "DELETE FROM inventory WHERE id = ?";
        return $this->db->query($sql, [$id]);
    }

    /**
     * تحديث الكمية
     */
    public function updateQuantity($id, $quantity)
    {
        $sql = "UPDATE inventory SET quantity = ?, updated_at = NOW() WHERE id = ?";
        return $this->db->update($sql, [$quantity, $id]);
    }

    /**
     * الحصول على الأدوية منخفضة المخزون
     */
    public function getLowStock($limit = 10)
    {
        $sql = "SELECT * FROM inventory WHERE quantity <= reorder_level ORDER BY quantity ASC";
        
        if ($limit) {
            $sql .= " LIMIT $limit";
        }
        
        return $this->db->select($sql);
    }

    /**
     * الحصول على الأدوية منتهية الصلاحية
     */
    public function getExpired($limit = 10)
    {
        $sql = "SELECT * FROM inventory WHERE expiry_date <= CURDATE() ORDER BY expiry_date ASC";
        
        if ($limit) {
            $sql .= " LIMIT $limit";
        }
        
        return $this->db->select($sql);
    }

    /**
     * الحصول على الأدوية التي ستنتهي صلاحيتها قريباً
     */
    public function getExpiringSoon($days = 30, $limit = 10)
    {
        $sql = "SELECT * FROM inventory 
                WHERE expiry_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY)
                ORDER BY expiry_date ASC";
        
        if ($limit) {
            $sql .= " LIMIT $limit";
        }
        
        return $this->db->select($sql, [$days]);
    }

    /**
     * الحصول على إحصائيات المخزون
     */
    public function getStats()
    {
        $stats = [];

        // إجمالي عدد الأدوية
        $sql = "SELECT COUNT(*) as total FROM inventory";
        $result = $this->db->selectOne($sql);
        $stats['total_items'] = $result['total'];

        // الأدوية منخفضة المخزون
        $sql = "SELECT COUNT(*) as low_stock FROM inventory WHERE quantity <= reorder_level";
        $result = $this->db->selectOne($sql);
        $stats['low_stock'] = $result['low_stock'];

        // الأدوية منتهية الصلاحية
        $sql = "SELECT COUNT(*) as expired FROM inventory WHERE expiry_date <= CURDATE()";
        $result = $this->db->selectOne($sql);
        $stats['expired'] = $result['expired'];

        // الأدوية التي ستنتهي صلاحيتها قريباً
        $sql = "SELECT COUNT(*) as expiring_soon FROM inventory 
                WHERE expiry_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)";
        $result = $this->db->selectOne($sql);
        $stats['expiring_soon'] = $result['expiring_soon'];

        // إجمالي قيمة المخزون
        $sql = "SELECT SUM(quantity * unit_price) as total_value FROM inventory";
        $result = $this->db->selectOne($sql);
        $stats['total_value'] = $result['total_value'] ?? 0;

        return $stats;
    }

    /**
     * الحصول على فئات الأدوية
     */
    public function getCategories()
    {
        $sql = "SELECT DISTINCT category FROM inventory WHERE category IS NOT NULL ORDER BY category";
        return $this->db->select($sql);
    }

    /**
     * الحصول على أشكال الجرعات
     */
    public function getDosageForms()
    {
        $sql = "SELECT DISTINCT dosage_form FROM inventory WHERE dosage_form IS NOT NULL ORDER BY dosage_form";
        return $this->db->select($sql);
    }
} 