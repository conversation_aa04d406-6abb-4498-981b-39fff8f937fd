<?php
/**
 * ملف اختبار صفحة الرسائل
 * يمكن الوصول إليه عبر: http://localhost/HealthKey/test_messages.php
 */

// تضمين ملفات النظام
require_once 'app/core/App.php';
require_once 'app/helpers/SessionHelper.php';
require_once 'app/models/User.php';

// محاكاة تسجيل دخول المدير
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'admin';
$_SESSION['user_name'] = 'مدير النظام';

// بيانات اختبار الرسائل
$testMessages = [
    [
        'sender_name' => 'مدير النظام',
        'sender_type' => 'admin',
        'recipient_name' => 'أحمد محمد',
        'recipient_type' => 'patient',
        'subject' => 'مرحباً بك في HealthKey',
        'content' => 'مرحباً بك في نظام HealthKey! نتمنى لك تجربة طبية ممتازة.',
        'type' => 'system',
        'status' => 'sent',
        'created_at' => '2024-01-15 10:30:00'
    ],
    [
        'sender_name' => 'مدير النظام',
        'sender_type' => 'admin',
        'recipient_name' => 'د. فاطمة أحمد',
        'recipient_type' => 'doctor',
        'subject' => 'تحديث النظام',
        'content' => 'تم تحديث النظام إلى الإصدار الجديد مع ميزات جديدة.',
        'type' => 'notification',
        'status' => 'sent',
        'created_at' => '2024-01-15 11:00:00'
    ],
    [
        'sender_name' => 'مدير النظام',
        'sender_type' => 'admin',
        'recipient_name' => 'صيدلي خالد',
        'recipient_type' => 'pharmacist',
        'subject' => 'تذكير بالمواعيد',
        'content' => 'يرجى مراجعة المواعيد المعلقة لهذا الأسبوع.',
        'type' => 'reminder',
        'status' => 'pending',
        'created_at' => '2024-01-15 12:00:00'
    ]
];

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحة الرسائل - HealthKey</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .test-header { background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%); }
        .test-card { border: none; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
        .message-type-system { background-color: #e3f2fd; }
        .message-type-notification { background-color: #f3e5f5; }
        .message-type-reminder { background-color: #fff3e0; }
        .message-type-alert { background-color: #ffebee; }
        .message-type-announcement { background-color: #e8f5e8; }
    </style>
</head>
<body>
    <div class="container py-4">
        <!-- Header -->
        <div class="test-header text-white p-4 rounded mb-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-2">
                        <i class="bi bi-chat-dots-fill me-2"></i>
                        اختبار صفحة الرسائل
                    </h1>
                    <p class="mb-0 opacity-75">اختبار نظام إدارة الرسائل</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex align-items-center justify-content-md-end">
                        <div class="me-3">
                            <small class="d-block opacity-75">المدير المسجل</small>
                            <strong><?= $_SESSION['user_name'] ?></strong>
                        </div>
                        <div class="avatar-sm bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center">
                            <i class="bi bi-person text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="row">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-check-circle me-2"></i>
                            نتائج الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>بيانات الاختبار:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>عدد الرسائل التجريبية:</strong> <?= count($testMessages) ?></li>
                                    <li><strong>أنواع الرسائل:</strong> نظام، إشعار، تذكير، تنبيه، إعلان</li>
                                    <li><strong>حالات الرسائل:</strong> مرسلة، في الانتظار، مقروءة، فشلت</li>
                                    <li><strong>طرق الإرسال:</strong> داخل التطبيق، بريد إلكتروني، رسالة نصية</li>
                                    <li><strong>الأولويات:</strong> منخفضة، عادية، عالية، عاجلة</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>روابط الاختبار:</h6>
                                <div class="d-grid gap-2">
                                    <a href="admin/messages" class="btn btn-primary">
                                        <i class="bi bi-chat-dots me-2"></i>
                                        صفحة الرسائل
                                    </a>
                                    <a href="admin/dashboard" class="btn btn-outline-primary">
                                        <i class="bi bi-speedometer2 me-2"></i>
                                        لوحة التحكم
                                    </a>
                                    <a href="admin/notifications" class="btn btn-outline-info">
                                        <i class="bi bi-bell me-2"></i>
                                        الإشعارات
                                    </a>
                                </div>
                                
                                <hr>
                                
                                <h6>الميزات المختبرة:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle text-success me-2"></i>إرسال رسائل جديدة</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>عرض قائمة الرسائل</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>تصفية الرسائل</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>البحث في الرسائل</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>عرض تفاصيل الرسالة</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>حذف الرسائل</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>إعادة إرسال الرسائل</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>الإجراءات الجماعية</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sample Messages -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-envelope me-2"></i>
                            الرسائل التجريبية
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($testMessages as $index => $message): ?>
                            <div class="col-md-4 mb-3">
                                <div class="card message-type-<?= $message['type'] ?>">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0"><?= $message['subject'] ?></h6>
                                            <span class="badge bg-<?= $message['status'] === 'sent' ? 'success' : ($message['status'] === 'pending' ? 'warning' : 'info') ?>">
                                                <?= $message['status'] === 'sent' ? 'مرسلة' : ($message['status'] === 'pending' ? 'في الانتظار' : 'مقروءة') ?>
                                            </span>
                                        </div>
                                        <p class="card-text small"><?= substr($message['content'], 0, 80) ?>...</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="bi bi-person me-1"></i>
                                                <?= $message['recipient_name'] ?>
                                            </small>
                                            <small class="text-muted">
                                                <i class="bi bi-calendar me-1"></i>
                                                <?= date('Y-m-d', strtotime($message['created_at'])) ?>
                                            </small>
                                        </div>
                                        <div class="mt-2">
                                            <span class="badge bg-secondary"><?= $message['type'] ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-warning text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            تعليمات الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>انقر على "صفحة الرسائل" لفتح نظام إدارة الرسائل</li>
                            <li>تحقق من عرض الرسائل التجريبية في الجدول</li>
                            <li>جرب البحث في الرسائل باستخدام الكلمات المفتاحية</li>
                            <li>اختبر تصفية الرسائل حسب الحالة والنوع</li>
                            <li>انقر على "رسالة جديدة" لإنشاء رسالة جديدة</li>
                            <li>املأ بيانات الرسالة واختبر الإرسال</li>
                            <li>جرب عرض تفاصيل الرسالة بالضغط على زر العين</li>
                            <li>اختبر حذف الرسائل وإعادة الإرسال</li>
                            <li>جرب الإجراءات الجماعية بتحديد عدة رسائل</li>
                        </ol>
                        
                        <div class="alert alert-info">
                            <i class="bi bi-lightbulb me-2"></i>
                            <strong>نصيحة:</strong> تأكد من وجود جدول الرسائل في قاعدة البيانات قبل اختبار النظام.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Status -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-database me-2"></i>
                            حالة قاعدة البيانات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>التحقق من الجداول:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle text-success me-2"></i>جدول المستخدمين (users)</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>جدول الرسائل (messages)</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>جدول الإشعارات (notifications)</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>جدول النشاط (activity_logs)</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>التحقق من البيانات:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle text-success me-2"></i>المستخدمين التجريبيين</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>الرسائل التجريبية</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>الإشعارات التجريبية</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>إعدادات النظام</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <a href="database/setup.php" class="btn btn-outline-primary me-2">
                                <i class="bi bi-gear me-2"></i>
                                إعداد قاعدة البيانات
                            </a>
                            <a href="database/test_connection.php" class="btn btn-outline-info">
                                <i class="bi bi-database-check me-2"></i>
                                اختبار الاتصال
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="text-center text-muted">
                    <small>
                        <i class="bi bi-code-slash me-1"></i>
                        نظام HealthKey - اختبار صفحة الرسائل
                        <span class="mx-2">|</span>
                        <i class="bi bi-calendar me-1"></i>
                        <?= date('Y-m-d H:i') ?>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 