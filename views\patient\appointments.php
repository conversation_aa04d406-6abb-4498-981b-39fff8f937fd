<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-calendar-check me-2"></i>
            مواعيدي
        </h1>
        <p class="text-muted">إدارة مواعيدك الطبية والجدول الزمني</p>
    </div>
    <div>
        <a href="<?= App::url('patient/book-appointment') ?>" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>
            حجز موعد جديد
        </a>
        <a href="<?= App::url('patient/dashboard') ?>" class="btn btn-outline-secondary ms-2">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <?php
    $totalAppointments = count($appointments);
    $upcomingAppointments = array_filter($appointments, function($a) { 
        return in_array($a['status'], ['scheduled', 'confirmed']) && 
               strtotime($a['appointment_date']) >= strtotime(date('Y-m-d')); 
    });
    $completedAppointments = array_filter($appointments, function($a) { 
        return $a['status'] === 'completed'; 
    });
    $cancelledAppointments = array_filter($appointments, function($a) { 
        return $a['status'] === 'cancelled'; 
    });
    ?>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count($upcomingAppointments) ?></h4>
                        <p class="mb-0">المواعيد القادمة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-day display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count($completedAppointments) ?></h4>
                        <p class="mb-0">مواعيد مكتملة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count(array_filter($appointments, function($a) { return $a['status'] === 'scheduled'; })) ?></h4>
                        <p class="mb-0">مواعيد مجدولة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count(array_filter($appointments, function($a) { return $a['status'] === 'confirmed'; })) ?></h4>
                        <p class="mb-0">مواعيد مؤكدة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check2-all display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?= App::url('patient/appointments') ?>" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">حالة الموعد</label>
                <select class="form-select" name="status">
                    <option value="">جميع المواعيد</option>
                    <option value="scheduled" <?= $currentStatus === 'scheduled' ? 'selected' : '' ?>>مجدول</option>
                    <option value="confirmed" <?= $currentStatus === 'confirmed' ? 'selected' : '' ?>>مؤكد</option>
                    <option value="completed" <?= $currentStatus === 'completed' ? 'selected' : '' ?>>مكتمل</option>
                    <option value="cancelled" <?= $currentStatus === 'cancelled' ? 'selected' : '' ?>>ملغي</option>
                    <option value="no_show" <?= $currentStatus === 'no_show' ? 'selected' : '' ?>>لم يحضر</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">الطبيب</label>
                <select class="form-select" name="doctor_id">
                    <option value="">جميع الأطباء</option>
                    <?php foreach ($doctors as $doctor): ?>
                        <option value="<?= $doctor['id'] ?>" <?= (isset($_GET['doctor_id']) && $_GET['doctor_id'] == $doctor['id']) ? 'selected' : '' ?>>
                            <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                            <?php if (!empty($doctor['specialization'])): ?>
                                - <?= htmlspecialchars($doctor['specialization']) ?>
                            <?php endif; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid gap-2 d-md-flex">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search me-1"></i>
                        تصفية
                    </button>
                    <a href="<?= App::url('patient/appointments') ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        إعادة تعيين
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Appointments List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-list-ul me-2"></i>
            قائمة المواعيد
            <span class="badge bg-primary ms-2"><?= count($appointments) ?></span>
        </h5>
        <div>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                <i class="bi bi-printer me-1"></i>
                طباعة
            </button>
            <button type="button" class="btn btn-sm btn-outline-success" onclick="exportAppointments()">
                <i class="bi bi-download me-1"></i>
                تصدير
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($appointments)): ?>
            <!-- Empty State -->
            <div class="text-center py-5">
                <i class="bi bi-calendar-x display-1 text-muted"></i>
                <h4 class="mt-3 text-muted">لا توجد مواعيد</h4>
                <p class="text-muted">
                    <?php if (!empty($currentStatus)): ?>
                        لم يتم العثور على مواعيد تطابق المعايير المحددة
                    <?php else: ?>
                        لم يتم جدولة أي مواعيد بعد
                    <?php endif; ?>
                </p>
                <a href="<?= App::url('patient/book-appointment') ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    حجز موعد جديد
                </a>
            </div>
        <?php else: ?>
            <!-- Appointments Table -->
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>الطبيب</th>
                            <th>التاريخ والوقت</th>
                            <th>السبب</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($appointments as $appointment): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                            <i class="bi bi-person-badge text-primary"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?= htmlspecialchars($appointment['doctor_name']) ?></h6>
                                            <small class="text-muted">
                                                <?= htmlspecialchars($appointment['doctor_specialization'] ?? 'غير محدد') ?>
                                            </small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="badge bg-info mb-1">
                                            <?= date('Y-m-d', strtotime($appointment['appointment_date'])) ?>
                                        </div>
                                        <br>
                                        <small class="text-muted">
                                            <?= date('H:i', strtotime($appointment['appointment_time'])) ?>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="<?= htmlspecialchars($appointment['reason']) ?>">
                                        <?= htmlspecialchars($appointment['reason'] ?? 'غير محدد') ?>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $statusLabel = Appointment::getStatusLabel($appointment['status']);
                                    $statusColor = Appointment::getStatusColor($appointment['status']);
                                    ?>
                                    <span class="badge bg-<?= $statusColor ?>">
                                        <i class="bi bi-circle-fill me-1"></i>
                                        <?= $statusLabel ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <?php if (in_array($appointment['status'], ['scheduled', 'confirmed'])): ?>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    onclick="cancelAppointment(<?= $appointment['id'] ?>)" 
                                                    title="إلغاء الموعد">
                                                <i class="bi bi-x-circle"></i>
                                            </button>
                                        <?php endif; ?>
                                        
                                        <?php if ($appointment['status'] === 'completed' && empty($appointment['rating'])): ?>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-warning" 
                                                    onclick="rateAppointment(<?= $appointment['id'] ?>)" 
                                                    title="تقييم الموعد">
                                                <i class="bi bi-star"></i>
                                            </button>
                                        <?php endif; ?>
                                        
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-info" 
                                                onclick="viewAppointmentDetails(<?= $appointment['id'] ?>)" 
                                                title="تفاصيل الموعد">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Cancel Appointment Modal -->
<div class="modal fade" id="cancelAppointmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إلغاء الموعد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في إلغاء هذا الموعد؟</p>
                <p class="text-muted small">لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmCancelBtn">تأكيد الإلغاء</button>
            </div>
        </div>
    </div>
</div>

<!-- Rate Appointment Modal -->
<div class="modal fade" id="rateAppointmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تقييم الموعد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="ratingForm">
                    <div class="mb-3">
                        <label class="form-label">التقييم</label>
                        <div class="rating-stars">
                            <i class="bi bi-star star-rating" data-rating="1"></i>
                            <i class="bi bi-star star-rating" data-rating="2"></i>
                            <i class="bi bi-star star-rating" data-rating="3"></i>
                            <i class="bi bi-star star-rating" data-rating="4"></i>
                            <i class="bi bi-star star-rating" data-rating="5"></i>
                        </div>
                        <input type="hidden" id="ratingValue" name="rating" value="">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ملاحظات (اختياري)</label>
                        <textarea class="form-control" name="rating_notes" rows="3" placeholder="أضف ملاحظاتك حول الموعد..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="submitRatingBtn">إرسال التقييم</button>
            </div>
        </div>
    </div>
</div>

<!-- Appointment Details Modal -->
<div class="modal fade" id="appointmentDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الموعد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="appointmentDetailsContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<style>
.rating-stars {
    font-size: 1.5rem;
    color: #ddd;
}

.rating-stars .star-rating {
    cursor: pointer;
    transition: color 0.2s;
}

.rating-stars .star-rating:hover,
.rating-stars .star-rating.active {
    color: #ffc107;
}

.rating-stars .star-rating.filled {
    color: #ffc107;
}
</style>

<script>
let currentAppointmentId = null;

// Cancel Appointment
function cancelAppointment(appointmentId) {
    currentAppointmentId = appointmentId;
    $('#cancelAppointmentModal').modal('show');
}

$('#confirmCancelBtn').click(function() {
    if (!currentAppointmentId) return;
    
    $.post('<?= App::url('patient/cancel-appointment') ?>', {
        appointment_id: currentAppointmentId
    }, function(response) {
        if (response.success) {
            showAlert('تم إلغاء الموعد بنجاح', 'success');
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert(response.message || 'حدث خطأ أثناء إلغاء الموعد', 'error');
        }
    }, 'json').fail(function() {
        showAlert('حدث خطأ في الاتصال', 'error');
    });
    
    $('#cancelAppointmentModal').modal('hide');
});

// Rate Appointment
function rateAppointment(appointmentId) {
    currentAppointmentId = appointmentId;
    $('#ratingValue').val('');
    $('.star-rating').removeClass('filled active');
    $('#rateAppointmentModal').modal('show');
}

$('.star-rating').click(function() {
    const rating = $(this).data('rating');
    $('#ratingValue').val(rating);
    
    $('.star-rating').removeClass('filled active');
    $('.star-rating').each(function(index) {
        if (index < rating) {
            $(this).addClass('filled active');
        }
    });
});

$('#submitRatingBtn').click(function() {
    const rating = $('#ratingValue').val();
    if (!rating) {
        showAlert('يرجى اختيار تقييم', 'error');
        return;
    }
    
    const formData = new FormData($('#ratingForm')[0]);
    formData.append('appointment_id', currentAppointmentId);
    
    $.ajax({
        url: '<?= App::url('patient/rate-appointment') ?>',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                showAlert('تم إرسال التقييم بنجاح', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showAlert(response.message || 'حدث خطأ أثناء إرسال التقييم', 'error');
            }
        },
        error: function() {
            showAlert('حدث خطأ في الاتصال', 'error');
        }
    });
    
    $('#rateAppointmentModal').modal('hide');
});

// View Appointment Details
function viewAppointmentDetails(appointmentId) {
    $.get('<?= App::url('patient/appointment-details') ?>/' + appointmentId, function(response) {
        $('#appointmentDetailsContent').html(response);
        $('#appointmentDetailsModal').modal('show');
    }).fail(function() {
        showAlert('حدث خطأ في تحميل تفاصيل الموعد', 'error');
    });
}

// Export Appointments
function exportAppointments() {
    const params = new URLSearchParams(window.location.search);
    params.append('export', '1');
    
    window.open('<?= App::url('patient/appointments') ?>?' + params.toString(), '_blank');
}

// Show Alert
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert
    $('.container-fluid').prepend(alertHtml);
    
    // Auto dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}
</script> 