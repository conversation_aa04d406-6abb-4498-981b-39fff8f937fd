<?php

/**
 * نموذج المستخدم
 * يتعامل مع جدول المستخدمين وجميع العمليات المتعلقة بهم
 */
class User
{
    private $db;
    private $table = 'users';

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * الحصول على مستخدم بواسطة ID
     */
    public function findById($id)
    {
        $query = "SELECT u.*, d.specialization, d.license_number as doctor_license, d.years_of_experience,
                         p.pharmacy_name, p.license_number as pharmacist_license, p.pharmacy_address
                  FROM {$this->table} u
                  LEFT JOIN doctors d ON u.id = d.user_id
                  LEFT JOIN pharmacists p ON u.id = p.user_id
                  WHERE u.id = :id";
        
        return $this->db->selectOne($query, ['id' => $id]);
    }

    /**
     * الحصول على مستخدم بواسطة البريد الإلكتروني
     */
    public function findByEmail($email)
    {
        $query = "SELECT * FROM {$this->table} WHERE email = :email";
        return $this->db->selectOne($query, ['email' => $email]);
    }

    /**
     * الحصول على مستخدم بواسطة الرقم الوطني
     */
    public function findByNationalId($nationalId)
    {
        $query = "SELECT * FROM {$this->table} WHERE national_id = :national_id";
        return $this->db->selectOne($query, ['national_id' => $nationalId]);
    }

    /**
     * الحصول على مستخدمين بواسطة مجموعة من المعرفات
     */
    public function getByIds($ids)
    {
        if (empty($ids)) {
            return [];
        }

        $placeholders = str_repeat('?,', count($ids) - 1) . '?';
        $query = "SELECT * FROM {$this->table} WHERE id IN ($placeholders) ORDER BY first_name, last_name";
        
        return $this->db->select($query, $ids);
    }

    /**
     * إنشاء مستخدم جديد
     */
    public function create($data)
    {
        // إضافة تصحيح للتأكد من البيانات
        error_log("User::create called with data: " . print_r($data, true));
        
        // إنشاء كلمة مرور عشوائية إذا لم يتم توفيرها
        if (empty($data['password'])) {
            $data['password'] = $this->generateRandomPassword();
        }

        $query = "INSERT INTO {$this->table} (email, password, user_type, first_name, last_name, 
                                            phone, date_of_birth, gender, national_id, address, 
                                            emergency_contact, emergency_phone, is_active, created_at) 
                  VALUES (:email, :password, :user_type, :first_name, :last_name, 
                          :phone, :date_of_birth, :gender, :national_id, :address,
                          :emergency_contact, :emergency_phone, :is_active, NOW())";

        $params = [
            ':email' => $data['email'],
            ':password' => password_hash($data['password'], PASSWORD_DEFAULT),
            ':user_type' => $data['user_type'],
            ':first_name' => $data['first_name'],
            ':last_name' => $data['last_name'],
            ':phone' => $data['phone'] ?? null,
            ':date_of_birth' => $data['date_of_birth'] ?? null,
            ':gender' => $data['gender'] ?? null,
            ':national_id' => $data['national_id'] ?? null,
            ':address' => $data['address'] ?? null,
            ':emergency_contact' => $data['emergency_contact'] ?? null,
            ':emergency_phone' => $data['emergency_phone'] ?? null,
            ':is_active' => $data['is_active'] ?? 1
        ];

        error_log("SQL Query: " . $query);
        error_log("SQL Params: " . print_r($params, true));

        $result = $this->db->insert($query, $params);
        error_log("Insert result: " . $result);
        
        return $result;
    }

    /**
     * تحديث بيانات المستخدم
     */
    public function update($id, $data)
    {
        $fields = [];
        $params = [':id' => $id];

        foreach ($data as $key => $value) {
            if ($key !== 'id' && $key !== 'password') {
                $fields[] = "$key = :$key";
                $params[":$key"] = $value;
            }
        }

        if (empty($fields)) {
            return false;
        }

        $query = "UPDATE {$this->table} SET " . implode(', ', $fields) . ", updated_at = NOW() WHERE id = :id";
        return $this->db->update($query, $params) > 0;
    }

    /**
     * تحديث كلمة المرور
     */
    public function updatePassword($id, $newPassword)
    {
        $query = "UPDATE {$this->table} SET password = :password, updated_at = NOW() WHERE id = :id";
        $params = [
            ':id' => $id,
            ':password' => password_hash($newPassword, PASSWORD_DEFAULT)
        ];

        return $this->db->update($query, $params) > 0;
    }

    /**
     * تفعيل/إلغاء تفعيل المستخدم
     */
    public function toggleActive($id)
    {
        $query = "UPDATE {$this->table} SET is_active = NOT is_active, updated_at = NOW() WHERE id = :id";
        return $this->db->update($query, [':id' => $id]) > 0;
    }

    /**
     * حذف المستخدم
     */
    public function delete($id)
    {
        $query = "DELETE FROM {$this->table} WHERE id = :id";
        return $this->db->delete($query, [':id' => $id]) > 0;
    }

    /**
     * الحصول على جميع المستخدمين مع التصفية
     */
    public function getAll($filters = [])
    {
        $query = "SELECT u.*, d.specialization, p.pharmacy_name 
                  FROM {$this->table} u
                  LEFT JOIN doctors d ON u.id = d.user_id
                  LEFT JOIN pharmacists p ON u.id = p.user_id
                  WHERE 1=1";
        
        $params = [];

        // تطبيق المرشحات
        if (!empty($filters['user_type'])) {
            $query .= " AND u.user_type = :user_type";
            $params[':user_type'] = $filters['user_type'];
        }

        if (!empty($filters['is_active'])) {
            $query .= " AND u.is_active = :is_active";
            $params[':is_active'] = $filters['is_active'];
        }

        if (!empty($filters['search'])) {
            $query .= " AND (u.first_name LIKE :search OR u.last_name LIKE :search OR u.email LIKE :search)";
            $params[':search'] = '%' . $filters['search'] . '%';
        }

        $query .= " ORDER BY u.created_at DESC";

        // تطبيق التصفح
        if (!empty($filters['limit'])) {
            $query .= " LIMIT " . (int)$filters['limit'];
            
            if (!empty($filters['offset'])) {
                $query .= " OFFSET " . (int)$filters['offset'];
            }
        }

        return $this->db->select($query, $params);
    }

    /**
     * عدد المستخدمين مع التصفية
     */
    public function count($filters = [])
    {
        $query = "SELECT COUNT(*) as count FROM {$this->table} WHERE 1=1";
        $params = [];

        if (!empty($filters['user_type'])) {
            $query .= " AND user_type = :user_type";
            $params[':user_type'] = $filters['user_type'];
        }

        if (!empty($filters['is_active'])) {
            $query .= " AND is_active = :is_active";
            $params[':is_active'] = $filters['is_active'];
        }

        if (!empty($filters['search'])) {
            $query .= " AND (first_name LIKE :search OR last_name LIKE :search OR email LIKE :search)";
            $params[':search'] = '%' . $filters['search'] . '%';
        }

        $result = $this->db->selectOne($query, $params);
        return (int)$result['count'];
    }

    /**
     * الحصول على المرضى للطبيب
     */
    public function getPatientsByDoctor($doctorId)
    {
        // الحصول على جميع المرضى النشطين بدلاً من الاعتماد على المواعيد فقط
        $query = "SELECT u.* 
                  FROM {$this->table} u
                  WHERE u.user_type = 'patient' AND u.is_active = 1
                  ORDER BY u.first_name, u.last_name";
        
        return $this->db->select($query);
    }

    /**
     * الحصول على الأطباء النشطين
     */
    public function getActiveDoctors()
    {
        $query = "SELECT u.*, d.specialization, d.years_of_experience 
                  FROM {$this->table} u
                  INNER JOIN doctors d ON u.id = d.user_id
                  WHERE u.user_type = 'doctor' AND u.is_active = 1
                  ORDER BY u.first_name, u.last_name";
        
        return $this->db->select($query);
    }

    /**
     * الحصول على جميع الأطباء
     */
    public function getAllDoctors()
    {
        $query = "SELECT u.*, d.specialization, d.years_of_experience 
                  FROM {$this->table} u
                  LEFT JOIN doctors d ON u.id = d.user_id
                  WHERE u.user_type = 'doctor'
                  ORDER BY u.first_name, u.last_name";
        
        return $this->db->select($query);
    }

    /**
     * الحصول على الأطباء مع التفاصيل الكاملة
     */
    public function getDoctorsWithDetails()
    {
        $query = "SELECT u.*, d.specialization, d.license_number, d.years_of_experience, 
                         d.hospital_affiliation, d.consultation_fee, d.is_available,
                         d.education, d.certifications, d.bio
                  FROM {$this->table} u
                  LEFT JOIN doctors d ON u.id = d.user_id
                  WHERE u.user_type = 'doctor' AND u.is_active = 1
                  ORDER BY d.is_available DESC, u.first_name, u.last_name";
        
        return $this->db->select($query);
    }

    /**
     * الحصول على المرضى النشطين
     */
    public function getActivePatients()
    {
        $query = "SELECT u.*, p.emergency_contact, p.emergency_phone
                  FROM {$this->table} u
                  LEFT JOIN patients p ON u.id = p.user_id
                  WHERE u.user_type = 'patient' AND u.is_active = 1
                  ORDER BY u.first_name, u.last_name";
        
        return $this->db->select($query);
    }

    /**
     * الحصول على الصيادلة النشطين
     */
    public function getActivePharmacists()
    {
        $query = "SELECT u.*, p.pharmacy_name, p.pharmacy_address 
                  FROM {$this->table} u
                  INNER JOIN pharmacists p ON u.id = p.user_id
                  WHERE u.user_type = 'pharmacist' AND u.is_active = 1
                  ORDER BY u.first_name, u.last_name";
        
        return $this->db->select($query);
    }

    /**
     * البحث في المستخدمين
     */
    public function search($term, $userType = null)
    {
        $query = "SELECT u.*, d.specialization, p.pharmacy_name
                  FROM {$this->table} u
                  LEFT JOIN doctors d ON u.id = d.user_id
                  LEFT JOIN pharmacists p ON u.id = p.user_id
                  WHERE u.is_active = 1";

        $params = [];

        // إضافة شرط البحث فقط إذا كان المصطلح غير فارغ
        if (!empty($term)) {
            $query .= " AND (u.first_name LIKE :term OR u.last_name LIKE :term OR u.email LIKE :term OR u.national_id LIKE :term)";
            $params[':term'] = "%$term%";
        }

        if ($userType) {
            $query .= " AND u.user_type = :user_type";
            $params[':user_type'] = $userType;
        }

        $query .= " ORDER BY u.first_name, u.last_name LIMIT 20";

        return $this->db->select($query, $params);
    }

    /**
     * إحصائيات المستخدمين
     */
    public function getStats()
    {
        $stats = [];

        // إجمالي المستخدمين
        $total = $this->db->selectOne("SELECT COUNT(*) as count FROM {$this->table}");
        $stats['total'] = (int)$total['count'];

        // المستخدمين النشطين
        $active = $this->db->selectOne("SELECT COUNT(*) as count FROM {$this->table} WHERE is_active = 1");
        $stats['active'] = (int)$active['count'];

        // المستخدمين غير النشطين
        $inactive = $this->db->selectOne("SELECT COUNT(*) as count FROM {$this->table} WHERE is_active = 0");
        $stats['inactive'] = (int)$inactive['count'];

        // المستخدمين حسب النوع
        $byType = $this->db->select(
            "SELECT user_type, COUNT(*) as count FROM {$this->table} WHERE is_active = 1 GROUP BY user_type"
        );

        foreach ($byType as $type) {
            $stats[$type['user_type']] = (int)$type['count'];
        }

        // المستخدمين الجدد هذا الشهر
        $newThisMonth = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM {$this->table} 
             WHERE MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())"
        );
        $stats['new_this_month'] = (int)$newThisMonth['count'];

        return $stats;
    }

    /**
     * التحقق من صحة البيانات
     */
    public function validate($data, $isUpdate = false)
    {
        $errors = [];

        // التحقق من الحقول المطلوبة
        if (!$isUpdate) {
            $required = ['email', 'user_type', 'first_name', 'last_name'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    $errors[$field] = "حقل $field مطلوب";
                }
            }
        }

        // التحقق من البريد الإلكتروني
        if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'البريد الإلكتروني غير صحيح';
        }

        // التحقق من كلمة المرور (اختياري للمستخدمين الجدد)
        if (!empty($data['password']) && strlen($data['password']) < 8) {
            $errors['password'] = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
        }

        // التحقق من نوع المستخدم
        if (!empty($data['user_type']) && !in_array($data['user_type'], ['patient', 'doctor', 'pharmacist', 'admin'])) {
            $errors['user_type'] = 'نوع المستخدم غير صحيح';
        }

        // التحقق من الأسماء
        if (!empty($data['first_name']) && (strlen($data['first_name']) < 2 || strlen($data['first_name']) > 50)) {
            $errors['first_name'] = 'الاسم الأول يجب أن يكون بين 2-50 حرف';
        }

        if (!empty($data['last_name']) && (strlen($data['last_name']) < 2 || strlen($data['last_name']) > 50)) {
            $errors['last_name'] = 'الاسم الأخير يجب أن يكون بين 2-50 حرف';
        }

        return $errors;
    }

    /**
     * الحصول على اسم المستخدم الكامل
     */
    public static function getFullName($user)
    {
        if (is_array($user)) {
            $firstName = $user['first_name'] ?? '';
            $lastName = $user['last_name'] ?? '';
            $fullName = trim($firstName . ' ' . $lastName);
            return !empty($fullName) ? $fullName : 'مريض غير محدد';
        }
        return 'مريض غير محدد';
    }

    /**
     * الحصول على نوع المستخدم باللغة العربية
     */
    public static function getUserTypeLabel($userType)
    {
        $types = [
            'patient' => 'مريض',
            'doctor' => 'طبيب',
            'pharmacist' => 'صيدلي',
            'admin' => 'مدير'
        ];

        return $types[$userType] ?? $userType;
    }

    /**
     * التحقق من كلمة المرور
     */
    public function verifyPassword($userId, $password)
    {
        $user = $this->findById($userId);
        if ($user) {
            return password_verify($password, $user['password']);
        }
        return false;
    }

    /**
     * الحصول على لون نوع المستخدم
     */
    public static function getTypeColor($userType)
    {
        $colors = [
            'patient' => 'primary',
            'doctor' => 'success',
            'pharmacist' => 'info',
            'admin' => 'warning'
        ];

        return $colors[$userType] ?? 'secondary';
    }

    /**
     * الحصول على نوع المستخدم باللغة العربية
     */
    public static function getTypeLabel($userType)
    {
        $types = [
            'patient' => 'مريض',
            'doctor' => 'طبيب',
            'pharmacist' => 'صيدلي',
            'admin' => 'مدير'
        ];

        return $types[$userType] ?? $userType;
    }

    /**
     * الحصول على بيانات الطبيب
     */
    public function getDoctorData($userId)
    {
        $query = "SELECT specialization, years_of_experience FROM doctors WHERE user_id = :user_id";
        return $this->db->selectOne($query, [':user_id' => $userId]);
    }

    /**
     * الحصول على بيانات الصيدلي
     */
    public function getPharmacistData($userId)
    {
        $query = "SELECT pharmacy_name, pharmacy_address FROM pharmacists WHERE user_id = :user_id";
        return $this->db->selectOne($query, [':user_id' => $userId]);
    }

    /**
     * إنشاء كلمة مرور عشوائية
     */
    private function generateRandomPassword($length = 8)
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        $password = '';
        
        for ($i = 0; $i < $length; $i++) {
            $password .= $chars[rand(0, strlen($chars) - 1)];
        }
        
        return $password;
    }
}
