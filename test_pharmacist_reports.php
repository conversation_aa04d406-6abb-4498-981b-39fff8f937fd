<?php
/**
 * اختبار صفحة التقارير الشاملة للصيدلي
 */

// محاكاة تسجيل دخول الصيدلي
session_start();
$_SESSION['user_id'] = 2;
$_SESSION['user_type'] = 'pharmacist';
$_SESSION['user'] = [
    'id' => 2,
    'first_name' => 'أحمد',
    'last_name' => 'محمد',
    'email' => '<EMAIL>'
];

echo "<h1>اختبار صفحة التقارير الشاملة للصيدلي</h1>";

try {
    // تضمين الملفات المطلوبة
    require_once 'config.php';
    require_once 'app/core/App.php';
    require_once 'app/core/Controller.php';
    require_once 'app/core/Database.php';
    require_once 'app/controllers/PharmacistController.php';
    
    echo "✅ تم تضمين الملفات بنجاح<br>";
    
    // اختبار إنشاء متحكم الصيدلي
    echo "<h2>اختبار إنشاء متحكم الصيدلي</h2>";
    $controller = new PharmacistController();
    echo "✅ تم إنشاء متحكم الصيدلي بنجاح<br>";
    
    // اختبار دالة reports
    echo "<h2>اختبار دالة reports</h2>";
    $controller->reports();
    echo "✅ تم استدعاء دالة reports بنجاح<br>";
    
    // اختبار الدوال المساعدة
    echo "<h2>اختبار الدوال المساعدة</h2>";
    
    // اختبار getRecentActivity
    $recentActivity = $controller->getRecentActivity(2, '2024-01-01', '2024-12-31');
    echo "✅ دالة getRecentActivity: " . count($recentActivity) . " نشاط<br>";
    
    // اختبار getTopMedications
    $topMedications = $controller->getTopMedications(2, '2024-01-01', '2024-12-31');
    echo "✅ دالة getTopMedications: " . count($topMedications) . " دواء<br>";
    
    // اختبار getDailyDispensing
    $dailyDispensing = $controller->getDailyDispensing(2, '2024-01-01', '2024-12-31');
    echo "✅ دالة getDailyDispensing: " . count($dailyDispensing) . " يوم<br>";
    
    // اختبار getMonthlyTrends
    $monthlyTrends = $controller->getMonthlyTrends(2);
    echo "✅ دالة getMonthlyTrends: " . count($monthlyTrends) . " شهر<br>";
    
    // اختبار getPerformanceMetrics
    $performanceMetrics = $controller->getPerformanceMetrics(2, '2024-01-01', '2024-12-31');
    echo "✅ دالة getPerformanceMetrics: " . json_encode($performanceMetrics, JSON_UNESCAPED_UNICODE) . "<br>";
    
    echo "<h3>روابط صفحة التقارير:</h3>";
    echo "<a href='index.php?url=pharmacist/reports' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>فتح صفحة التقارير الشاملة</a><br><br>";
    
    echo "<a href='index.php?url=pharmacist/dispensing-report' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>فتح تقرير الصرف التفصيلي</a>";
    
    echo "<h2>✅ انتهى الاختبار بنجاح</h2>";
    echo "<p>صفحة التقارير الشاملة جاهزة للاستخدام!</p>";
    
    echo "<h3>الميزات المتاحة:</h3>";
    echo "<ul>";
    echo "<li>📊 مقاييس الأداء (إجمالي الوصفات، الأدوية المصروفة، معدل الإنجاز)</li>";
    echo "<li>📈 الرسوم البيانية للصرف اليومي والاتجاهات الشهرية</li>";
    echo "<li>🏆 قائمة الأدوية الأكثر صرفاً</li>";
    echo "<li>⏰ النشاط الأخير مع الجدول الزمني</li>";
    echo "<li>📦 ملخص المخزون وإحصائيات الوصفات</li>";
    echo "<li>🔗 إجراءات سريعة للوصول للصفحات الأخرى</li>";
    echo "<li>📅 مرشحات الفترة الزمنية</li>";
    echo "<li>📥 تصدير التقارير</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في الاختبار</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 