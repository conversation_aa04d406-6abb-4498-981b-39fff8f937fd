<?php
/**
 * اختبار إصلاح مشكلة isPost
 */

// محاكاة تسجيل دخول الصيدلي
session_start();
$_SESSION['user_id'] = 2;
$_SESSION['user_type'] = 'pharmacist';
$_SESSION['user'] = [
    'id' => 2,
    'first_name' => 'أحمد',
    'last_name' => 'محمد',
    'email' => '<EMAIL>'
];

echo "<h1>اختبار إصلاح مشكلة isPost</h1>";

try {
    // تضمين الملفات المطلوبة
    require_once 'config.php';
    require_once 'app/core/App.php';
    require_once 'app/core/Controller.php';
    require_once 'app/core/Database.php';
    require_once 'app/controllers/PharmacistController.php';
    
    echo "✅ تم تضمين الملفات بنجاح<br>";
    
    // اختبار دالة App::isPost
    echo "<h2>اختبار دالة App::isPost</h2>";
    $_SERVER['REQUEST_METHOD'] = 'GET';
    echo "✅ App::isPost() للطلب GET: " . (App::isPost() ? 'نعم' : 'لا') . "<br>";
    
    $_SERVER['REQUEST_METHOD'] = 'POST';
    echo "✅ App::isPost() للطلب POST: " . (App::isPost() ? 'نعم' : 'لا') . "<br>";
    
    // اختبار إنشاء متحكم الصيدلي
    echo "<h2>اختبار إنشاء متحكم الصيدلي</h2>";
    $controller = new PharmacistController();
    echo "✅ تم إنشاء متحكم الصيدلي بنجاح<br>";
    
    // اختبار دالة workingHours مع GET
    echo "<h2>اختبار دالة workingHours مع GET</h2>";
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $controller->workingHours();
    echo "✅ تم استدعاء دالة workingHours مع GET بنجاح<br>";
    
    // اختبار دالة workingHours مع POST
    echo "<h2>اختبار دالة workingHours مع POST</h2>";
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_POST['sunday_working'] = 1;
    $_POST['sunday_start'] = '09:00';
    $_POST['sunday_end'] = '18:00';
    $controller->workingHours();
    echo "✅ تم استدعاء دالة workingHours مع POST بنجاح<br>";
    
    echo "<h3>رابط صفحة ساعات العمل:</h3>";
    echo "<a href='index.php?url=pharmacist/working-hours' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>فتح صفحة ساعات العمل</a>";
    
    echo "<h2>✅ انتهى الاختبار بنجاح</h2>";
    echo "<p>مشكلة isPost تم إصلاحها بنجاح!</p>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في الاختبار</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 