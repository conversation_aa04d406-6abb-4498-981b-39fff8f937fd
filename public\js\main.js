/**
 * ملف JavaScript الرئيسي لنظام HealthKey
 * يحتوي على الوظائف العامة والتفاعلات الأساسية
 */

// تشغيل الكود عند تحميل الصفحة
$(document).ready(function() {
    // تهيئة التطبيق
    HealthKey.init();
});

// كائن التطبيق الرئيسي
const HealthKey = {
    // تهيئة التطبيق
    init: function() {
        this.setupEventListeners();
        this.initializeComponents();
        this.setupFormValidation();
        this.setupAjaxDefaults();
    },

    // إعداد مستمعي الأحداث
    setupEventListeners: function() {
        // إغلاق التنبيهات تلقائياً
        this.autoCloseAlerts();
        
        // تأكيد الحذف
        this.setupDeleteConfirmation();
        
        // تحسين النماذج
        this.setupFormEnhancements();
        
        // إعداد البحث المباشر
        this.setupLiveSearch();
    },

    // تهيئة المكونات
    initializeComponents: function() {
        // تهيئة tooltips
        if (typeof bootstrap !== 'undefined') {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        // تهيئة popovers
        if (typeof bootstrap !== 'undefined') {
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        }
    },

    // إعداد التحقق من النماذج
    setupFormValidation: function() {
        // التحقق من النماذج عند الإرسال
        $('form[data-validate="true"]').on('submit', function(e) {
            if (!HealthKey.validateForm(this)) {
                e.preventDefault();
                return false;
            }
        });

        // التحقق المباشر من الحقول
        $('input[data-validate], textarea[data-validate], select[data-validate]').on('blur', function() {
            HealthKey.validateField(this);
        });
    },

    // إعداد AJAX الافتراضي
    setupAjaxDefaults: function() {
        // إعدادات AJAX العامة
        $.ajaxSetup({
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            error: function(xhr, status, error) {
                HealthKey.showAlert('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'error');
            }
        });
    },

    // إغلاق التنبيهات تلقائياً
    autoCloseAlerts: function() {
        $('.alert[data-auto-close]').each(function() {
            const alert = $(this);
            const delay = alert.data('auto-close') || 5000;
            
            setTimeout(function() {
                alert.fadeOut();
            }, delay);
        });
    },

    // إعداد تأكيد الحذف
    setupDeleteConfirmation: function() {
        $(document).on('click', '[data-confirm-delete]', function(e) {
            e.preventDefault();
            
            const element = $(this);
            const message = element.data('confirm-delete') || 'هل أنت متأكد من الحذف؟';
            
            if (confirm(message)) {
                // إذا كان رابط، انتقل إليه
                if (element.is('a')) {
                    window.location.href = element.attr('href');
                }
                // إذا كان زر في نموذج، أرسل النموذج
                else if (element.is('button') && element.closest('form').length) {
                    element.closest('form').submit();
                }
            }
        });
    },

    // تحسينات النماذج
    setupFormEnhancements: function() {
        // إضافة مؤشر التحميل عند إرسال النماذج
        $('form').on('submit', function() {
            const submitBtn = $(this).find('button[type="submit"]');
            const originalText = submitBtn.text();
            
            submitBtn.prop('disabled', true)
                    .html('<span class="spinner-border spinner-border-sm me-2"></span>جاري المعالجة...');
            
            // إعادة تفعيل الزر بعد 10 ثوان (في حالة عدم إعادة التوجيه)
            setTimeout(function() {
                submitBtn.prop('disabled', false).text(originalText);
            }, 10000);
        });

        // تحسين حقول كلمة المرور
        $('.password-toggle').on('click', function() {
            const input = $(this).siblings('input');
            const icon = $(this).find('i');
            
            if (input.attr('type') === 'password') {
                input.attr('type', 'text');
                icon.removeClass('bi-eye').addClass('bi-eye-slash');
            } else {
                input.attr('type', 'password');
                icon.removeClass('bi-eye-slash').addClass('bi-eye');
            }
        });
    },

    // إعداد البحث المباشر
    setupLiveSearch: function() {
        $('[data-live-search]').on('input', function() {
            const input = $(this);
            const target = input.data('live-search');
            const query = input.val().toLowerCase();
            
            $(target).each(function() {
                const text = $(this).text().toLowerCase();
                if (text.includes(query)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });
    },

    // التحقق من النموذج
    validateForm: function(form) {
        let isValid = true;
        const $form = $(form);
        
        // إزالة رسائل الخطأ السابقة
        $form.find('.is-invalid').removeClass('is-invalid');
        $form.find('.invalid-feedback').remove();
        
        // التحقق من الحقول المطلوبة
        $form.find('[required]').each(function() {
            if (!this.value.trim()) {
                HealthKey.showFieldError(this, 'هذا الحقل مطلوب');
                isValid = false;
            }
        });
        
        // التحقق من البريد الإلكتروني
        $form.find('input[type="email"]').each(function() {
            if (this.value && !HealthKey.isValidEmail(this.value)) {
                HealthKey.showFieldError(this, 'البريد الإلكتروني غير صحيح');
                isValid = false;
            }
        });
        
        // التحقق من تطابق كلمات المرور
        const password = $form.find('input[name="password"]').val();
        const confirmPassword = $form.find('input[name="confirm_password"]').val();
        
        if (password && confirmPassword && password !== confirmPassword) {
            HealthKey.showFieldError($form.find('input[name="confirm_password"]')[0], 'كلمات المرور غير متطابقة');
            isValid = false;
        }
        
        return isValid;
    },

    // التحقق من حقل واحد
    validateField: function(field) {
        const $field = $(field);
        const value = field.value.trim();
        
        // إزالة رسائل الخطأ السابقة
        $field.removeClass('is-invalid');
        $field.siblings('.invalid-feedback').remove();
        
        // التحقق من الحقول المطلوبة
        if (field.hasAttribute('required') && !value) {
            HealthKey.showFieldError(field, 'هذا الحقل مطلوب');
            return false;
        }
        
        // التحقق من البريد الإلكتروني
        if (field.type === 'email' && value && !HealthKey.isValidEmail(value)) {
            HealthKey.showFieldError(field, 'البريد الإلكتروني غير صحيح');
            return false;
        }
        
        return true;
    },

    // عرض خطأ في الحقل
    showFieldError: function(field, message) {
        const $field = $(field);
        $field.addClass('is-invalid');
        $field.after('<div class="invalid-feedback">' + message + '</div>');
    },

    // التحقق من صحة البريد الإلكتروني
    isValidEmail: function(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    // عرض تنبيه
    showAlert: function(message, type = 'info', autoClose = true) {
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';
        
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // إضافة التنبيه في أعلى المحتوى
        $('.main-content').prepend(alertHtml);
        
        // إغلاق تلقائي
        if (autoClose) {
            setTimeout(function() {
                $('.alert').first().fadeOut();
            }, 5000);
        }
    },

    // تحميل محتوى عبر AJAX
    loadContent: function(url, container, showLoading = true) {
        const $container = $(container);
        
        if (showLoading) {
            $container.html('<div class="text-center p-4"><div class="spinner-border" role="status"></div></div>');
        }
        
        $.get(url)
            .done(function(data) {
                $container.html(data);
            })
            .fail(function() {
                $container.html('<div class="alert alert-danger">فشل في تحميل المحتوى</div>');
            });
    },

    // تنسيق التاريخ
    formatDate: function(date, format = 'YYYY-MM-DD') {
        if (!date) return '';
        
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        
        return format.replace('YYYY', year)
                    .replace('MM', month)
                    .replace('DD', day);
    },

    // تنسيق الوقت
    formatTime: function(time) {
        if (!time) return '';
        
        const [hours, minutes] = time.split(':');
        const hour12 = hours % 12 || 12;
        const ampm = hours >= 12 ? 'م' : 'ص';
        
        return `${hour12}:${minutes} ${ampm}`;
    },

    // تنسيق الأرقام
    formatNumber: function(number, decimals = 0) {
        return Number(number).toLocaleString('ar-SA', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    }
};

// وظائف مساعدة عامة
window.showAlert = HealthKey.showAlert;
window.loadContent = HealthKey.loadContent;

// تحسينات خاصة للوحة التحكم
const DashboardEnhancements = {
    // تهيئة لوحة التحكم
    init: function() {
        if (document.querySelector('.admin-content')) {
            this.initStatsCards();
            this.initCharts();
            this.initRealTimeUpdates();
            this.initAnimations();
        }
    },

    // تهيئة بطاقات الإحصائيات
    initStatsCards: function() {
        const statsCards = document.querySelectorAll('.stats-card');

        statsCards.forEach((card, index) => {
            // تأثير الظهور التدريجي
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 150);

            // تأثيرات التفاعل
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
                this.style.boxShadow = '0 25px 50px rgba(0,0,0,0.15)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.boxShadow = '0 10px 30px rgba(0,0,0,0.1)';
            });

            // تأثير النقر
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                }, 150);
            });
        });
    },

    // تهيئة الرسوم البيانية
    initCharts: function() {
        // تحسين مظهر الرسوم البيانية
        const chartContainers = document.querySelectorAll('.chart-container');
        chartContainers.forEach(container => {
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';

            setTimeout(() => {
                container.style.transition = 'all 0.8s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 500);
        });
    },

    // تحديثات فورية
    initRealTimeUpdates: function() {
        // تحديث الوقت
        this.updateTime();
        setInterval(() => this.updateTime(), 1000);

        // تحديث الإحصائيات كل 30 ثانية
        setInterval(() => this.updateStats(), 30000);
    },

    // تحديث الوقت
    updateTime: function() {
        const timeElements = document.querySelectorAll('.current-time');
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        timeElements.forEach(element => {
            element.textContent = timeString;
        });
    },

    // تحديث الإحصائيات
    updateStats: function() {
        const statsNumbers = document.querySelectorAll('.stats-number');

        // محاكاة تحديث البيانات
        statsNumbers.forEach(number => {
            const currentValue = parseInt(number.textContent) || 0;
            const change = Math.floor(Math.random() * 3) - 1; // -1, 0, أو 1
            const newValue = Math.max(0, currentValue + change);

            if (newValue !== currentValue) {
                this.animateNumber(number, currentValue, newValue, 1000);
            }
        });
    },

    // تحريك الأرقام
    animateNumber: function(element, start, end, duration) {
        const startTime = performance.now();
        const difference = end - start;

        function updateNumber(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // استخدام easing function للحركة السلسة
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = Math.floor(start + (difference * easeOutQuart));

            element.textContent = current;

            // تأثير بصري أثناء التحديث
            element.style.transform = `scale(${1 + (Math.sin(progress * Math.PI) * 0.1)})`;
            element.style.color = progress < 1 ? '#667eea' : '';

            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            } else {
                element.style.transform = 'scale(1)';
                element.style.color = '';
            }
        }

        requestAnimationFrame(updateNumber);
    },

    // تهيئة الحركات
    initAnimations: function() {
        // تأثير الظهور للجداول
        const tables = document.querySelectorAll('.table-responsive');
        tables.forEach((table, index) => {
            table.style.opacity = '0';
            table.style.transform = 'translateX(-20px)';

            setTimeout(() => {
                table.style.transition = 'all 0.6s ease';
                table.style.opacity = '1';
                table.style.transform = 'translateX(0)';
            }, 800 + (index * 200));
        });

        // تأثير hover للصفوف
        const tableRows = document.querySelectorAll('.table tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = 'rgba(102, 126, 234, 0.05)';
                this.style.transform = 'scale(1.01)';
                this.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
            });

            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
                this.style.transform = 'scale(1)';
                this.style.boxShadow = '';
            });
        });
    }
};

// تشغيل تحسينات لوحة التحكم عند تحميل الصفحة
$(document).ready(function() {
    DashboardEnhancements.init();
});

// إضافة دالة تحديث لوحة التحكم للنافذة العامة
window.refreshDashboard = function() {
    const refreshBtn = document.querySelector('button[onclick="refreshDashboard()"]');
    if (refreshBtn) {
        const icon = refreshBtn.querySelector('i');

        // تأثير دوران
        icon.style.animation = 'spin 1s linear infinite';
        refreshBtn.disabled = true;

        // محاكاة التحديث
        setTimeout(() => {
            DashboardEnhancements.updateStats();
            icon.style.animation = '';
            refreshBtn.disabled = false;

            // إظهار رسالة نجاح
            HealthKey.showAlert('تم تحديث البيانات بنجاح', 'success', 2000);
        }, 1500);
    }
};

// إضافة أنماط CSS للحركات
const dashboardStyles = `
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .stats-card {
        cursor: pointer;
    }

    .table tbody tr {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .chart-container {
        transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }
`;

// إضافة الأنماط للصفحة
if (!document.getElementById('dashboard-styles')) {
    const styleSheet = document.createElement('style');
    styleSheet.id = 'dashboard-styles';
    styleSheet.textContent = dashboardStyles;
    document.head.appendChild(styleSheet);
}
