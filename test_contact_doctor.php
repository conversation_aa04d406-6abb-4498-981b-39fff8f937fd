<?php
/**
 * اختبار صفحة التواصل مع الطبيب
 * Test Contact Doctor Page
 */

// تضمين الملفات المطلوبة
require_once 'app/core/App.php';
require_once 'app/core/Database.php';
require_once 'app/helpers/SessionHelper.php';
require_once 'app/models/User.php';
require_once 'app/models/Message.php';
require_once 'app/models/Notification.php';

// تهيئة التطبيق
App::init();

// محاكاة تسجيل دخول المريض
SessionHelper::set('user_id', 3); // معرف المريض التجريبي
SessionHelper::set('user_type', 'patient');
SessionHelper::set('user', [
    'id' => 3,
    'first_name' => 'فاطمة',
    'last_name' => 'علي',
    'email' => '<EMAIL>',
    'user_type' => 'patient'
]);

// اختبار الحصول على قائمة الأطباء
echo "<h2>اختبار الحصول على قائمة الأطباء</h2>";
$userModel = new User();
$doctors = $userModel->getDoctorsWithDetails();

if ($doctors) {
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>قائمة الأطباء المتاحين:</h3>";
    echo "<ul>";
    foreach ($doctors as $doctor) {
        echo "<li>";
        echo "<strong>الاسم:</strong> د. " . htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) . "<br>";
        if (!empty($doctor['specialization'])) {
            echo "<strong>التخصص:</strong> " . htmlspecialchars($doctor['specialization']) . "<br>";
        }
        if (!empty($doctor['hospital_affiliation'])) {
            echo "<strong>المستشفى:</strong> " . htmlspecialchars($doctor['hospital_affiliation']) . "<br>";
        }
        echo "<strong>الحالة:</strong> " . ($doctor['is_available'] ? 'متاح' : 'غير متاح') . "<br>";
        echo "</li>";
    }
    echo "</ul>";
    echo "</div>";
} else {
    echo "<p style='color: red;'>لا يوجد أطباء متاحون</p>";
}

// اختبار إرسال رسالة
echo "<h2>اختبار إرسال رسالة للطبيب</h2>";
if ($_POST) {
    $messageModel = new Message();
    $notificationModel = new Notification();
    
    $patientId = 3; // معرف المريض التجريبي
    $doctorId = $_POST['doctor_id'] ?? 2; // معرف الطبيب التجريبي
    $subject = $_POST['subject'] ?? 'رسالة تجريبية';
    $content = $_POST['content'] ?? 'محتوى الرسالة التجريبية';
    $messageType = $_POST['message_type'] ?? 'consultation';
    $priority = $_POST['priority'] ?? 'normal';
    
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h4>بيانات الرسالة:</h4>";
    echo "<p><strong>من:</strong> المريض (ID: $patientId)</p>";
    echo "<p><strong>إلى:</strong> الطبيب (ID: $doctorId)</p>";
    echo "<p><strong>الموضوع:</strong> $subject</p>";
    echo "<p><strong>النوع:</strong> $messageType</p>";
    echo "<p><strong>الأولوية:</strong> $priority</p>";
    echo "<p><strong>المحتوى:</strong> $content</p>";
    echo "</div>";
    
    // محاولة إرسال الرسالة
    $result = $messageModel->create($patientId, $doctorId, $subject, $content, $messageType, $priority);
    
    if ($result) {
        echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h4>✅ تم إرسال الرسالة بنجاح!</h4>";
        echo "<p>تم حفظ الرسالة في قاعدة البيانات</p>";
        echo "</div>";
        
        // إرسال إشعار للطبيب
        $notificationResult = $notificationModel->create(
            $doctorId,
            'رسالة جديدة من مريض',
            'لقد تلقيت رسالة جديدة من مريض. يرجى مراجعة صندوق الرسائل.',
            'message',
            'normal'
        );
        
        if ($notificationResult) {
            echo "<div style='background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
            echo "<h4>📢 تم إرسال إشعار للطبيب</h4>";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h4>❌ فشل في إرسال الرسالة</h4>";
        echo "</div>";
    }
}

// نموذج اختبار إرسال رسالة
echo "<h2>نموذج اختبار إرسال رسالة</h2>";
echo "<form method='POST' style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<div style='margin-bottom: 15px;'>";
echo "<label for='doctor_id'><strong>اختر الطبيب:</strong></label><br>";
echo "<select name='doctor_id' id='doctor_id' style='width: 100%; padding: 8px; border-radius: 4px; border: 1px solid #ddd;'>";
if ($doctors) {
    foreach ($doctors as $doctor) {
        $selected = ($doctor['id'] == 2) ? 'selected' : '';
        echo "<option value='{$doctor['id']}' $selected>";
        echo "د. " . htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']);
        if (!empty($doctor['specialization'])) {
            echo " - " . htmlspecialchars($doctor['specialization']);
        }
        echo "</option>";
    }
}
echo "</select>";
echo "</div>";

echo "<div style='margin-bottom: 15px;'>";
echo "<label for='subject'><strong>الموضوع:</strong></label><br>";
echo "<input type='text' name='subject' id='subject' value='رسالة تجريبية من المريض' style='width: 100%; padding: 8px; border-radius: 4px; border: 1px solid #ddd;'>";
echo "</div>";

echo "<div style='margin-bottom: 15px;'>";
echo "<label for='message_type'><strong>نوع الرسالة:</strong></label><br>";
echo "<select name='message_type' id='message_type' style='width: 100%; padding: 8px; border-radius: 4px; border: 1px solid #ddd;'>";
echo "<option value='consultation'>استشارة طبية</option>";
echo "<option value='appointment'>طلب موعد</option>";
echo "<option value='prescription'>استفسار عن وصفة</option>";
echo "<option value='follow_up'>متابعة حالة</option>";
echo "<option value='emergency'>حالة طارئة</option>";
echo "<option value='general'>استفسار عام</option>";
echo "</select>";
echo "</div>";

echo "<div style='margin-bottom: 15px;'>";
echo "<label for='priority'><strong>الأولوية:</strong></label><br>";
echo "<select name='priority' id='priority' style='width: 100%; padding: 8px; border-radius: 4px; border: 1px solid #ddd;'>";
echo "<option value='normal'>عادية</option>";
echo "<option value='high'>عالية</option>";
echo "<option value='urgent'>عاجلة</option>";
echo "</select>";
echo "</div>";

echo "<div style='margin-bottom: 15px;'>";
echo "<label for='content'><strong>محتوى الرسالة:</strong></label><br>";
echo "<textarea name='content' id='content' rows='6' style='width: 100%; padding: 8px; border-radius: 4px; border: 1px solid #ddd;'>السلام عليكم ورحمة الله وبركاته،

أحتاج إلى استشارة طبية بخصوص حالتي الصحية.

الأعراض التي أعاني منها:
- [اكتب الأعراض هنا]

مدة ظهور الأعراض:
- [اكتب المدة هنا]

الأدوية التي أتناولها حالياً:
- [اكتب الأدوية هنا]

أشكركم مقدماً على مساعدتكم.</textarea>";
echo "</div>";

echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>إرسال الرسالة</button>";
echo "</form>";

// اختبار عرض الرسائل المرسلة
echo "<h2>اختبار عرض الرسائل المرسلة</h2>";
$messageModel = new Message();
$sentMessages = $messageModel->getBySender(3, 5); // آخر 5 رسائل من المريض

if ($sentMessages) {
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>آخر الرسائل المرسلة:</h3>";
    echo "<div style='max-height: 300px; overflow-y: auto;'>";
    foreach ($sentMessages as $message) {
        echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; background: white;'>";
        echo "<h5>" . htmlspecialchars($message['subject']) . "</h5>";
        echo "<p><strong>إلى:</strong> الطبيب (ID: {$message['recipient_id']})</p>";
        echo "<p><strong>النوع:</strong> {$message['type']}</p>";
        echo "<p><strong>الأولوية:</strong> {$message['priority']}</p>";
        echo "<p><strong>التاريخ:</strong> {$message['created_at']}</p>";
        echo "<p><strong>المحتوى:</strong> " . htmlspecialchars(substr($message['content'], 0, 100)) . "...</p>";
        echo "</div>";
    }
    echo "</div>";
    echo "</div>";
} else {
    echo "<p style='color: #6c757d;'>لا توجد رسائل مرسلة</p>";
}

echo "<hr>";
echo "<p><a href='index.php' style='color: #007bff; text-decoration: none;'>← العودة للصفحة الرئيسية</a></p>";
?> 