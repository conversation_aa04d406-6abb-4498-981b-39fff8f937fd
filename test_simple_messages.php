<?php
/**
 * اختبار بسيط لنظام الرسائل
 */

// تضمين الملفات المطلوبة
require_once 'config.php';
require_once 'app/models/Message.php';
require_once 'app/models/User.php';

echo "<h1>اختبار بسيط لنظام الرسائل</h1>";

try {
    // إنشاء نموذج الرسائل
    $messageModel = new Message();
    $userModel = new User();
    
    echo "<h2>1. اختبار الحصول على الرسائل الواردة</h2>";
    $inboxMessages = $messageModel->getInbox(2, false, 5);
    echo "عدد الرسائل الواردة: " . count($inboxMessages) . "<br>";
    
    echo "<h2>2. اختبار الحصول على الرسائل الصادرة</h2>";
    $sentMessages = $messageModel->getSent(2, 5);
    echo "عدد الرسائل الصادرة: " . count($sentMessages) . "<br>";
    
    echo "<h2>3. اختبار إحصائيات الرسائل</h2>";
    $stats = $messageModel->getStats(2);
    echo "الإحصائيات: " . json_encode($stats, JSON_UNESCAPED_UNICODE) . "<br>";
    
    echo "<h2>✅ الاختبار تم بنجاح!</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في الاختبار</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
}
?> 