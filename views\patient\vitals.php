<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-heart-pulse me-2"></i>
            العلامات الحيوية
        </h1>
        <p class="text-muted">عرض وتتبع العلامات الحيوية الخاصة بك</p>
    </div>
    <div>
        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
            <i class="bi bi-printer me-2"></i>
            طباعة
        </button>
        <a href="<?= App::url('patient/dashboard') ?>" class="btn btn-outline-primary ms-2">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $totalRecords ?></h4>
                        <p class="mb-0">إجمالي القياسات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clipboard2-pulse display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $latestVitals ? date('Y/m/d', strtotime($latestVitals['visit_date'])) : 'لا يوجد' ?></h4>
                        <p class="mb-0">آخر قياس</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-check display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $latestVitals ? count($latestVitals['formatted_vitals']) : 0 ?></h4>
                        <p class="mb-0">العلامات المقاسة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-activity display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $latestVitals ? $latestVitals['doctor_name'] : 'لا يوجد' ?></h4>
                        <p class="mb-0">آخر طبيب</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-person-badge display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if ($latestVitals): ?>
<!-- Latest Vitals Card -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-heart-pulse me-2"></i>
            أحدث العلامات الحيوية
            <small class="text-muted">- <?= date('Y/m/d H:i', strtotime($latestVitals['visit_date'])) ?></small>
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <?php foreach ($latestVitals['formatted_vitals'] as $vitalName => $vitalValue): ?>
            <div class="col-md-4 col-sm-6 mb-3">
                <div class="d-flex align-items-center p-3 border rounded">
                    <div class="me-3">
                        <?php
                        $icon = 'bi-activity';
                        if (strpos($vitalName, 'الحرارة') !== false) $icon = 'bi-thermometer-half';
                        elseif (strpos($vitalName, 'ضغط الدم') !== false) $icon = 'bi-heart-pulse';
                        elseif (strpos($vitalName, 'ضربات القلب') !== false) $icon = 'bi-heart';
                        elseif (strpos($vitalName, 'التنفس') !== false) $icon = 'bi-wind';
                        elseif (strpos($vitalName, 'الوزن') !== false) $icon = 'bi-weight';
                        elseif (strpos($vitalName, 'الطول') !== false) $icon = 'bi-arrows-expand';
                        ?>
                        <i class="bi <?= $icon ?> fs-2 text-primary"></i>
                    </div>
                    <div>
                        <h6 class="mb-1"><?= $vitalName ?></h6>
                        <p class="mb-0 text-muted"><?= $vitalValue ?></p>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Vitals Statistics -->
<?php if (!empty($vitalsStats)): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    إحصائيات العلامات الحيوية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php
                    $vitalTypes = [
                        'temperature' => ['name' => 'درجة الحرارة', 'unit' => '°C', 'icon' => 'bi-thermometer-half'],
                        'blood_pressure_systolic' => ['name' => 'ضغط الدم الانقباضي', 'unit' => 'mmHg', 'icon' => 'bi-heart-pulse'],
                        'blood_pressure_diastolic' => ['name' => 'ضغط الدم الانبساطي', 'unit' => 'mmHg', 'icon' => 'bi-heart-pulse'],
                        'heart_rate' => ['name' => 'معدل ضربات القلب', 'unit' => 'bpm', 'icon' => 'bi-heart'],
                        'respiratory_rate' => ['name' => 'معدل التنفس', 'unit' => '/min', 'icon' => 'bi-wind'],
                        'weight' => ['name' => 'الوزن', 'unit' => 'kg', 'icon' => 'bi-weight'],
                        'height' => ['name' => 'الطول', 'unit' => 'cm', 'icon' => 'bi-arrows-expand']
                    ];
                    ?>
                    
                    <?php foreach ($vitalTypes as $vitalKey => $vitalInfo): ?>
                        <?php if ($vitalsStats[$vitalKey]['avg'] !== null): ?>
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="card border">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <i class="bi <?= $vitalInfo['icon'] ?> fs-3 text-primary me-3"></i>
                                        <div>
                                            <h6 class="mb-1"><?= $vitalInfo['name'] ?></h6>
                                            <p class="mb-0 text-muted"><?= $vitalInfo['unit'] ?></p>
                                        </div>
                                    </div>
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="border-end">
                                                <h6 class="mb-1 text-success"><?= $vitalsStats[$vitalKey]['min'] ?></h6>
                                                <small class="text-muted">الأدنى</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="border-end">
                                                <h6 class="mb-1 text-primary"><?= $vitalsStats[$vitalKey]['avg'] ?></h6>
                                                <small class="text-muted">المتوسط</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div>
                                                <h6 class="mb-1 text-danger"><?= $vitalsStats[$vitalKey]['max'] ?></h6>
                                                <small class="text-muted">الأعلى</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Vitals History -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-clock-history me-2"></i>
            سجل العلامات الحيوية
        </h5>
        <div>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportVitalsHistory()">
                <i class="bi bi-download me-1"></i>
                تصدير
            </button>
        </div>
    </div>
    <div class="card-body">
        <?php if (!empty($vitalsRecords)): ?>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>الطبيب</th>
                        <th>العلامات الحيوية</th>
                        <th>التشخيص</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($vitalsRecords as $record): ?>
                    <tr>
                        <td>
                            <div class="d-flex flex-column">
                                <span class="fw-bold"><?= date('Y/m/d', strtotime($record['visit_date'])) ?></span>
                                <small class="text-muted"><?= date('H:i', strtotime($record['visit_date'])) ?></small>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="bi bi-person-badge me-2 text-primary"></i>
                                <span><?= htmlspecialchars($record['doctor_name']) ?></span>
                            </div>
                        </td>
                        <td>
                            <div class="d-flex flex-wrap gap-1">
                                <?php foreach ($record['formatted_vitals'] as $vitalName => $vitalValue): ?>
                                <span class="badge bg-light text-dark border">
                                    <?= $vitalName ?>: <?= $vitalValue ?>
                                </span>
                                <?php endforeach; ?>
                            </div>
                        </td>
                        <td>
                            <?php if (!empty($record['diagnosis'])): ?>
                                <span class="text-truncate d-inline-block" style="max-width: 200px;" title="<?= htmlspecialchars($record['diagnosis']) ?>">
                                    <?= htmlspecialchars($record['diagnosis']) ?>
                                </span>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="<?= App::url('patient/medical-record-details/' . $record['id']) ?>" 
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <button type="button" class="btn btn-outline-secondary" 
                                        onclick="printVitalsRecord(<?= $record['id'] ?>)" title="طباعة">
                                    <i class="bi bi-printer"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
        <div class="text-center py-5">
            <i class="bi bi-heart-pulse display-1 text-muted"></i>
            <h5 class="mt-3">لا توجد علامات حيوية مسجلة</h5>
            <p class="text-muted">لم يتم تسجيل أي علامات حيوية بعد. سيتم عرضها هنا بعد زيارتك للطبيب.</p>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
function exportVitalsHistory() {
    // يمكن إضافة وظيفة تصدير البيانات هنا
    alert('سيتم إضافة وظيفة التصدير قريباً');
}

function printVitalsRecord(recordId) {
    // يمكن إضافة وظيفة طباعة السجل المحدد هنا
    window.open('<?= App::url("patient/medical-record-details/") ?>' + recordId + '?print=1', '_blank');
}
</script> 