<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-plus-circle me-2"></i>
            رسالة جديدة
        </h1>
        <p class="text-muted">إنشاء وإرسال رسالة جديدة</p>
    </div>
    <div>
        <a href="<?= App::url('patient/messages') ?>" class="btn btn-outline-primary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للرسائل
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Compose Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-pencil me-2"></i>
                    إنشاء الرسالة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= App::url('patient/compose-message') ?>">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="recipient_id" class="form-label">إلى <span class="text-danger">*</span></label>
                            <select class="form-select" id="recipient_id" name="recipient_id" required>
                                <option value="">اختر المستلم</option>
                                <?php foreach ($doctors as $doctor): ?>
                                    <option value="<?= $doctor['id'] ?>">
                                        د. <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                        <?php if (!empty($doctor['specialization'])): ?>
                                            - <?= htmlspecialchars($doctor['specialization']) ?>
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="type" class="form-label">نوع الرسالة</label>
                            <select class="form-select" id="type" name="type">
                                <option value="personal">شخصي</option>
                                <option value="medical">طبي</option>
                                <option value="appointment">موعد</option>
                                <option value="prescription">وصفة طبية</option>
                                <option value="general">عام</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="priority" class="form-label">الأولوية</label>
                            <select class="form-select" id="priority" name="priority">
                                <option value="normal">عادية</option>
                                <option value="high">عالية</option>
                                <option value="urgent">عاجلة</option>
                                <option value="low">منخفضة</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="subject" class="form-label">الموضوع <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="subject" name="subject" 
                                   placeholder="أدخل موضوع الرسالة" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="content" class="form-label">محتوى الرسالة <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="content" name="content" rows="10" 
                                  placeholder="أدخل محتوى الرسالة هنا..." required></textarea>
                        <div class="form-text">
                            يمكنك كتابة رسالة مفصلة تحتوي على جميع المعلومات المطلوبة
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-secondary" id="saveDraft">
                            <i class="bi bi-save me-2"></i>
                            حفظ كمسودة
                        </button>
                        <div>
                            <button type="button" class="btn btn-outline-secondary me-2" id="preview">
                                <i class="bi bi-eye me-2"></i>
                                معاينة
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-send me-2"></i>
                                إرسال الرسالة
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Message Guidelines -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    إرشادات الرسائل
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        اكتب موضوع واضح ومختصر
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        اذكر تفاصيل الحالة الطبية بوضوح
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        اذكر الأعراض والتاريخ الطبي
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        اذكر الأدوية الحالية إن وجدت
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        اذكر مواعيدك السابقة إن وجدت
                    </li>
                </ul>
            </div>
        </div>

        <!-- Message Templates -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-file-text me-2"></i>
                    قوالب الرسائل
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">اختر قالب</label>
                    <select class="form-select" id="template">
                        <option value="">اختر قالب...</option>
                        <option value="appointment">استفسار عن موعد</option>
                        <option value="prescription">استفسار عن وصفة طبية</option>
                        <option value="symptoms">تقرير أعراض</option>
                        <option value="medication">استفسار عن دواء</option>
                        <option value="general">استفسار عام</option>
                    </select>
                </div>
                <button type="button" class="btn btn-outline-primary btn-sm" id="loadTemplate">
                    <i class="bi bi-arrow-down me-2"></i>
                    تحميل القالب
                </button>
            </div>
        </div>

        <!-- Character Counter -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-bar-chart me-2"></i>
                    إحصائيات الرسالة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h6 class="mb-0" id="charCount">0</h6>
                        <small class="text-muted">حرف</small>
                    </div>
                    <div class="col-6">
                        <h6 class="mb-0" id="wordCount">0</h6>
                        <small class="text-muted">كلمة</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-eye me-2"></i>
                    معاينة الرسالة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<style>
.message-template {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
    border-left: 4px solid #007bff;
    margin-bottom: 1rem;
}

.template-item {
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s;
}

.template-item:hover {
    background-color: #e9ecef;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const contentTextarea = document.getElementById('content');
    const subjectInput = document.getElementById('subject');
    const charCount = document.getElementById('charCount');
    const wordCount = document.getElementById('wordCount');
    const templateSelect = document.getElementById('template');
    const loadTemplateBtn = document.getElementById('loadTemplate');
    const previewBtn = document.getElementById('preview');
    const saveDraftBtn = document.getElementById('saveDraft');

    // تحديث عداد الأحرف والكلمات
    function updateCounters() {
        const content = contentTextarea.value;
        const chars = content.length;
        const words = content.trim() === '' ? 0 : content.trim().split(/\s+/).length;
        
        charCount.textContent = chars;
        wordCount.textContent = words;
    }

    contentTextarea.addEventListener('input', updateCounters);
    subjectInput.addEventListener('input', updateCounters);

    // قوالب الرسائل
    const templates = {
        appointment: {
            subject: 'استفسار عن موعد طبي',
            content: `السلام عليكم ورحمة الله وبركاته،

أود الاستفسار عن المواعيد المتاحة لديكم.

التفاصيل:
- نوع الاستشارة: 
- التاريخ المفضل: 
- الوقت المفضل: 

أشكركم على حسن تعاونكم.`
        },
        prescription: {
            subject: 'استفسار عن وصفة طبية',
            content: `السلام عليكم ورحمة الله وبركاته،

أود الاستفسار عن الوصفة الطبية المرفقة.

التفاصيل:
- رقم الوصفة: 
- تاريخ الوصفة: 
- الاستفسار: 

أشكركم على حسن تعاونكم.`
        },
        symptoms: {
            subject: 'تقرير أعراض جديدة',
            content: `السلام عليكم ورحمة الله وبركاته،

أود إبلاغكم بأعراض جديدة ظهرت علي.

الأعراض:
- 

التاريخ:
- بداية الأعراض: 
- شدة الأعراض: 

الأدوية الحالية:
- 

أشكركم على حسن تعاونكم.`
        },
        medication: {
            subject: 'استفسار عن دواء',
            content: `السلام عليكم ورحمة الله وبركاته،

أود الاستفسار عن الدواء الموصوف.

التفاصيل:
- اسم الدواء: 
- الجرعة: 
- الاستفسار: 

أشكركم على حسن تعاونكم.`
        },
        general: {
            subject: 'استفسار عام',
            content: `السلام عليكم ورحمة الله وبركاته،

أود الاستفسار عن:

الاستفسار:
- 

التفاصيل الإضافية:
- 

أشكركم على حسن تعاونكم.`
        }
    };

    // تحميل القالب
    loadTemplateBtn.addEventListener('click', function() {
        const selectedTemplate = templateSelect.value;
        if (selectedTemplate && templates[selectedTemplate]) {
            const template = templates[selectedTemplate];
            subjectInput.value = template.subject;
            contentTextarea.value = template.content;
            updateCounters();
        }
    });

    // معاينة الرسالة
    previewBtn.addEventListener('click', function() {
        const subject = subjectInput.value;
        const content = contentTextarea.value;
        const recipient = document.getElementById('recipient_id');
        const selectedOption = recipient.options[recipient.selectedIndex];
        
        if (!subject || !content) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        const previewContent = `
            <div class="message-preview">
                <div class="mb-3">
                    <strong>إلى:</strong> ${selectedOption.text}
                </div>
                <div class="mb-3">
                    <strong>الموضوع:</strong> ${subject}
                </div>
                <div class="mb-3">
                    <strong>المحتوى:</strong>
                    <div class="message-content mt-2">
                        ${content.replace(/\n/g, '<br>')}
                    </div>
                </div>
            </div>
        `;

        document.getElementById('previewContent').innerHTML = previewContent;
        new bootstrap.Modal(document.getElementById('previewModal')).show();
    });

    // حفظ كمسودة
    saveDraftBtn.addEventListener('click', function() {
        const formData = {
            recipient_id: document.getElementById('recipient_id').value,
            subject: subjectInput.value,
            content: contentTextarea.value,
            type: document.getElementById('type').value,
            priority: document.getElementById('priority').value
        };

        // حفظ في localStorage
        localStorage.setItem('messageDraft', JSON.stringify(formData));
        showAlert('success', 'تم حفظ الرسالة كمسودة');
    });

    // تحميل المسودة المحفوظة
    const savedDraft = localStorage.getItem('messageDraft');
    if (savedDraft) {
        const draft = JSON.parse(savedDraft);
        document.getElementById('recipient_id').value = draft.recipient_id || '';
        subjectInput.value = draft.subject || '';
        contentTextarea.value = draft.content || '';
        document.getElementById('type').value = draft.type || 'personal';
        document.getElementById('priority').value = draft.priority || 'normal';
        updateCounters();
    }

    // تنظيف المسودة بعد الإرسال
    const form = document.querySelector('form');
    form.addEventListener('submit', function() {
        localStorage.removeItem('messageDraft');
    });
});

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه تلقائياً بعد 3 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}
</script> 