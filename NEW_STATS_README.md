# الإحصائيات الجديدة في صفحة التقارير

## نظرة عامة
تم إضافة 4 مجموعات جديدة من الإحصائيات إلى صفحة `admin/reports` لتوفير تحليل أكثر شمولية لأداء النظام.

## الإحصائيات الجديدة

### 1. إحصائيات الأداء (Performance Stats)
- **معدل نجاح المواعيد**: نسبة المواعيد المكتملة من إجمالي المواعيد
- **متوسط وقت الاستجابة**: الوقت المتوسط للرد على المواعيد
- **معدل الحضور**: نسبة المرضى الذين حضروا مواعيدهم
- **عدد من لم يحضروا**: إجمالي عدد المواعيد التي لم يحضر لها المرضى

### 2. الإحصائيات المالية (Financial Stats)
- **إجمالي الإيرادات**: إجمالي الإيرادات من المواعيد المكتملة
- **متوسط التكلفة**: متوسط تكلفة كل موعد
- **معدل النمو الشهري**: نسبة نمو الإيرادات شهرياً
- **أطباء نشطين**: عدد الأطباء الذين لديهم إيرادات

### 3. إحصائيات الجودة (Quality Stats)
- **معدل رضا المرضى**: متوسط تقييم المرضى للخدمة
- **معدل إعادة الحجز**: نسبة المرضى الذين حجزوا مواعيد متكررة
- **عدد الشكاوى**: عدد المواعيد الملغاة (كمؤشر للشكاوى)
- **مرضى متكررين**: عدد المرضى الذين حجزوا أكثر من موعد

### 4. إحصائيات النشاط (Activity Stats)
- **إجمالي النشاط**: إجمالي عدد المواعيد في الفترة المحددة
- **أيام نشطة**: عدد الأيام التي كان فيها نشاط
- **أوقات ذروة**: عدد أوقات الذروة المكتشفة
- **أطباء نشطين**: عدد الأطباء الذين لديهم مواعيد

## الملفات المعدلة

### 1. `app/controllers/AdminController.php`
تم إضافة الدوال التالية:
- `getPerformanceStats($startDate, $endDate)`
- `getFinancialStats($startDate, $endDate)`
- `getQualityStats($startDate, $endDate)`
- `getActivityStats($startDate, $endDate)`
- `calculateGrowthRate($data)`

### 2. `views/admin/reports/index.php`
تم إضافة:
- 4 بطاقات إحصائيات جديدة في القسم العلوي
- 4 أقسام تفصيلية للإحصائيات الجديدة
- عرض مرئي للإحصائيات مع ألوان مختلفة

### 3. `app/core/Database.php`
تم إصلاح مشكلة في معالجة المعاملات في الاستعلامات.

## الاستعلامات المستخدمة

### إحصائيات الأداء
```sql
-- معدل نجاح المواعيد
SELECT COUNT(*) as total FROM appointments WHERE appointment_date BETWEEN :start_date AND :end_date
SELECT COUNT(*) as completed FROM appointments WHERE appointment_date BETWEEN :start_date AND :end_date AND status = 'completed'

-- متوسط وقت الاستجابة
SELECT AVG(TIMESTAMPDIFF(MINUTE, created_at, updated_at)) as avg_time FROM appointments WHERE appointment_date BETWEEN :start_date AND :end_date AND status IN ('confirmed', 'completed')

-- معدل الحضور
SELECT COUNT(CASE WHEN status = 'completed' THEN 1 END) as attended, COUNT(CASE WHEN status = 'no_show' THEN 1 END) as no_show, COUNT(*) as total FROM appointments WHERE appointment_date BETWEEN :start_date AND :end_date
```

### الإحصائيات المالية
```sql
-- إجمالي الإيرادات
SELECT COUNT(*) * 100 as revenue FROM appointments WHERE appointment_date BETWEEN :start_date AND :end_date AND status = 'completed'

-- إيرادات الأطباء
SELECT CONCAT(u.first_name, ' ', u.last_name) as doctor_name, COUNT(*) * 100 as revenue FROM appointments a JOIN users u ON a.doctor_id = u.id WHERE a.appointment_date BETWEEN :start_date AND :end_date AND a.status = 'completed' GROUP BY a.doctor_id ORDER BY revenue DESC LIMIT 5
```

### إحصائيات الجودة
```sql
-- تقييمات الأطباء
SELECT CONCAT(u.first_name, ' ', u.last_name) as doctor_name, AVG(4.5) as avg_rating, COUNT(*) as total_appointments FROM appointments a JOIN users u ON a.doctor_id = u.id WHERE a.appointment_date BETWEEN :start_date AND :end_date AND a.status = 'completed' GROUP BY a.doctor_id HAVING total_appointments >= 5 ORDER BY avg_rating DESC LIMIT 10
```

### إحصائيات النشاط
```sql
-- أكثر الأيام نشاطاً
SELECT DAYNAME(appointment_date) as day_name, COUNT(*) as appointment_count FROM appointments WHERE appointment_date BETWEEN :start_date AND :end_date GROUP BY DAYNAME(appointment_date) ORDER BY appointment_count DESC

-- أوقات الذروة
SELECT HOUR(appointment_time) as hour, COUNT(*) as appointment_count FROM appointments WHERE appointment_date BETWEEN :start_date AND :end_date GROUP BY HOUR(appointment_time) ORDER BY appointment_count DESC LIMIT 5
```

## الميزات الجديدة

### 1. عرض مرئي محسن
- بطاقات إحصائيات ملونة
- أيقونات Bootstrap لكل نوع إحصائيات
- تخطيط متجاوب للشاشات المختلفة

### 2. تحليل متقدم
- تحليل الأداء والكفاءة
- تحليل مالي مبسط
- تحليل جودة الخدمة
- تحليل أنماط النشاط

### 3. مرونة في الفلترة
- فلترة حسب التاريخ
- فلترة حسب نوع التقرير
- إمكانية تصدير البيانات

## الاستخدام

### الوصول للصفحة
```
http://localhost/HealthKey/admin/reports
```

### معاملات URL
- `start_date`: تاريخ البداية (YYYY-MM-DD)
- `end_date`: تاريخ النهاية (YYYY-MM-DD)
- `type`: نوع التقرير (overview, users, appointments, prescriptions)

### مثال
```
http://localhost/HealthKey/admin/reports?start_date=2025-01-01&end_date=2025-12-31&type=overview
```

## الاختبار

تم إنشاء ملف `test_new_stats.php` لاختبار الإحصائيات الجديدة:

```bash
php test_new_stats.php
```

## ملاحظات تقنية

### 1. محاكاة البيانات
بعض الإحصائيات تستخدم قيم محاكاة (مثل تقييمات المرضى) لأن النظام لا يحتوي على جداول تقييم فعلية.

### 2. الأداء
- تم تحسين الاستعلامات لتجنب الاستعلامات الفرعية المعقدة
- استخدام الفهارس المناسبة على الأعمدة المهمة
- تجنب الاستعلامات المتداخلة العميقة

### 3. الأمان
- استخدام المعاملات المُعدة مسبقاً (Prepared Statements)
- تنظيف المدخلات
- التحقق من صلاحيات المستخدم

## التطوير المستقبلي

### 1. إضافة جداول فعلية
- جدول تقييمات المرضى
- جدول المدفوعات
- جدول الشكاوى

### 2. تحسينات إضافية
- رسوم بيانية تفاعلية أكثر
- تصدير إلى Excel
- إشعارات تلقائية للإحصائيات المهمة

### 3. تحليلات متقدمة
- تحليل الاتجاهات
- التنبؤ بالطلب
- تحليل المنافسة

## الدعم

لأي استفسارات أو مشاكل، يرجى التواصل مع فريق التطوير. 