<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-person-plus me-2"></i>
            اختيار مريض لإنشاء وصفة طبية
        </h1>
        <p class="text-muted">اختر المريض الذي تريد إنشاء وصفة طبية له</p>
    </div>
    <div>
        <a href="<?= App::url('doctor/prescriptions') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للوصفات الطبية
        </a>
        <a href="<?= App::url('doctor/dashboard') ?>" class="btn btn-outline-primary">
            <i class="bi bi-house me-2"></i>
            لوحة التحكم
        </a>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-6">
                <label for="searchPatient" class="form-label">البحث في المرضى</label>
                <input type="text" class="form-control" id="searchPatient" 
                       placeholder="ابحث بالاسم أو البريد الإلكتروني أو رقم الهاتف">
            </div>
            <div class="col-md-3">
                <label for="filterStatus" class="form-label">حالة المريض</label>
                <select class="form-select" id="filterStatus">
                    <option value="">جميع المرضى</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="button" class="btn btn-primary" onclick="searchPatients()">
                        <i class="bi bi-search me-2"></i>
                        بحث
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count($patients) ?></h4>
                        <p class="mb-0">إجمالي المرضى</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count(array_filter($patients, function($p) { return $p['status'] === 'active'; })) ?></h4>
                        <p class="mb-0">مرضى نشطون</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count(array_filter($patients, function($p) { return !empty($p['last_appointment']); })) ?></h4>
                        <p class="mb-0">مرضى مع مواعيد حديثة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-check display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count(array_filter($patients, function($p) { return !empty($p['medical_records']); })) ?></h4>
                        <p class="mb-0">مرضى مع سجلات طبية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-file-earmark-medical display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Patients List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-people me-2"></i>
            قائمة المرضى
        </h5>
        <div>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportPatients()">
                <i class="bi bi-download me-1"></i>
                تصدير
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($patients)): ?>
            <div class="text-center py-5">
                <i class="bi bi-people display-1 text-muted"></i>
                <h4 class="mt-3 text-muted">لا توجد مرضى</h4>
                <p class="text-muted">لم يتم العثور على مرضى في قائمة المرضى</p>
                <a href="<?= App::url('doctor/patients') ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة مريض جديد
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>المريض</th>
                            <th>معلومات الاتصال</th>
                            <th>آخر موعد</th>
                            <th>السجلات الطبية</th>
                            <th>الوصفات السابقة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($patients as $patient): ?>
                            <tr class="patient-row" data-patient-id="<?= $patient['id'] ?>">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-lg bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                            <?= strtoupper(substr($patient['first_name'] ?? '', 0, 1)) ?>
                                        </div>
                                        <div>
                                            <h6 class="mb-1"><?= htmlspecialchars(User::getFullName($patient)) ?></h6>
                                            <small class="text-muted">
                                                <?= htmlspecialchars($patient['gender'] ?? 'غير محدد') ?> • 
                                                <?= htmlspecialchars($patient['age'] ?? 'غير محدد') ?> سنة
                                            </small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong><?= htmlspecialchars($patient['email'] ?? 'غير محدد') ?></strong>
                                        <br>
                                        <small class="text-muted"><?= htmlspecialchars($patient['phone'] ?? 'غير محدد') ?></small>
                                    </div>
                                </td>
                                <td>
                                    <?php if (!empty($patient['last_appointment'])): ?>
                                        <div>
                                            <strong><?= date('Y-m-d', strtotime($patient['last_appointment'])) ?></strong>
                                            <br>
                                            <small class="text-muted"><?= date('H:i', strtotime($patient['last_appointment'])) ?></small>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">لا توجد مواعيد</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php 
                                    $medicalRecordsCount = $patient['medical_records_count'] ?? 0;
                                    if ($medicalRecordsCount > 0): 
                                    ?>
                                        <span class="badge bg-info"><?= $medicalRecordsCount ?> سجل</span>
                                    <?php else: ?>
                                        <span class="text-muted">لا توجد سجلات</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php 
                                    $prescriptionsCount = $patient['prescriptions_count'] ?? 0;
                                    if ($prescriptionsCount > 0): 
                                    ?>
                                        <span class="badge bg-success"><?= $prescriptionsCount ?> وصفة</span>
                                    <?php else: ?>
                                        <span class="text-muted">لا توجد وصفات</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $status = $patient['status'] ?? 'active';
                                    $statusColors = [
                                        'active' => 'success',
                                        'inactive' => 'secondary'
                                    ];
                                    $statusLabels = [
                                        'active' => 'نشط',
                                        'inactive' => 'غير نشط'
                                    ];
                                    ?>
                                    <span class="badge bg-<?= $statusColors[$status] ?>">
                                        <?= $statusLabels[$status] ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-primary" 
                                                onclick="selectPatient(<?= $patient['id'] ?>, '<?= htmlspecialchars(User::getFullName($patient)) ?>')"
                                                title="اختيار هذا المريض">
                                            <i class="bi bi-check-circle me-1"></i>
                                            اختيار
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="viewPatient(<?= $patient['id'] ?>)"
                                                title="عرض تفاصيل المريض">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-success" 
                                                onclick="viewMedicalRecords(<?= $patient['id'] ?>)"
                                                title="عرض السجلات الطبية">
                                            <i class="bi bi-file-earmark-medical"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="bi bi-plus-circle display-4 mb-3"></i>
                <h5>إضافة مريض جديد</h5>
                <p class="mb-3">إضافة مريض جديد إلى النظام</p>
                <a href="<?= App::url('doctor/add-patient') ?>" class="btn btn-light">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة مريض
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="bi bi-calendar-plus display-4 mb-3"></i>
                <h5>حجز موعد جديد</h5>
                <p class="mb-3">حجز موعد جديد لمريض</p>
                <a href="<?= App::url('doctor/schedule-appointment') ?>" class="btn btn-light">
                    <i class="bi bi-calendar-plus me-2"></i>
                    حجز موعد
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="bi bi-file-earmark-medical display-4 mb-3"></i>
                <h5>إضافة سجل طبي</h5>
                <p class="mb-3">إضافة سجل طبي جديد</p>
                <a href="<?= App::url('doctor/add-medical-record') ?>" class="btn btn-light">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة سجل
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Patient Details Modal -->
<div class="modal fade" id="patientDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person me-2"></i>
                    تفاصيل المريض
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="patientDetails">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="selectCurrentPatient()">
                    <i class="bi bi-check-circle me-2"></i>
                    اختيار هذا المريض
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentPatientId = null;
let currentPatientName = null;

function selectPatient(patientId, patientName) {
    currentPatientId = patientId;
    currentPatientName = patientName;
    
    if (confirm(`هل تريد اختيار المريض "${patientName}" لإنشاء وصفة طبية؟`)) {
        window.location.href = `<?= App::url('doctor/create-prescription') ?>?patient_id=${patientId}`;
    }
}

function viewPatient(patientId) {
    currentPatientId = patientId;
    
    // تحميل تفاصيل المريض
    fetch(`<?= App::url('doctor/view-patient/') ?>${patientId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('patientDetails').innerHTML = data.html;
                new bootstrap.Modal(document.getElementById('patientDetailsModal')).show();
            } else {
                alert('فشل في تحميل تفاصيل المريض');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحميل التفاصيل');
        });
}

function viewMedicalRecords(patientId) {
    window.location.href = `<?= App::url('doctor/medical-records') ?>?patient_id=${patientId}`;
}

function selectCurrentPatient() {
    if (currentPatientId) {
        window.location.href = `<?= App::url('doctor/create-prescription') ?>?patient_id=${currentPatientId}`;
    }
}

function searchPatients() {
    const searchTerm = document.getElementById('searchPatient').value;
    const statusFilter = document.getElementById('filterStatus').value;
    
    const rows = document.querySelectorAll('.patient-row');
    rows.forEach(row => {
        const patientName = row.querySelector('h6').textContent.toLowerCase();
        const patientEmail = row.querySelector('td:nth-child(2) strong').textContent.toLowerCase();
        const patientStatus = row.querySelector('.badge').textContent;
        
        let showRow = true;
        
        // تطبيق فلتر البحث
        if (searchTerm && !patientName.includes(searchTerm.toLowerCase()) && !patientEmail.includes(searchTerm.toLowerCase())) {
            showRow = false;
        }
        
        // تطبيق فلتر الحالة
        if (statusFilter && patientStatus !== statusFilter) {
            showRow = false;
        }
        
        row.style.display = showRow ? '' : 'none';
    });
}

function exportPatients() {
    const searchTerm = document.getElementById('searchPatient').value;
    const statusFilter = document.getElementById('filterStatus').value;
    
    const url = `<?= App::url('doctor/export-patients') ?>?search=${encodeURIComponent(searchTerm)}&status=${encodeURIComponent(statusFilter)}`;
    
    window.open(url, '_blank');
}

// تحسين التفاعل
document.addEventListener('DOMContentLoaded', function() {
    // البحث المباشر عند الكتابة
    const searchInput = document.getElementById('searchPatient');
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(searchPatients, 300);
    });
    
    // فلتر الحالة
    document.getElementById('filterStatus').addEventListener('change', searchPatients);
    
    // إضافة تأثيرات بصرية للصفوف
    const tableRows = document.querySelectorAll('.patient-row');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
        
        // النقر على الصف لاختيار المريض
        row.addEventListener('click', function(e) {
            if (!e.target.closest('.btn-group')) {
                const patientId = this.getAttribute('data-patient-id');
                const patientName = this.querySelector('h6').textContent;
                selectPatient(patientId, patientName);
            }
        });
    });
    
    // تفعيل tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>

<style>
.avatar-lg {
    width: 48px;
    height: 48px;
    font-size: 18px;
}

.patient-row {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.patient-row:hover {
    background-color: #f8f9fa;
}

.table th {
    border-top: none;
    font-weight: 600;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.btn-group .btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

@media print {
    .btn, .card-header, .modal {
        display: none !important;
    }
    
    .table {
        font-size: 0.875rem;
    }
}
</style> 