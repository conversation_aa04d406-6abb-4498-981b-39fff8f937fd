<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تفعيل/إلغاء تفعيل المستخدمين - HealthKey</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">اختبار تفعيل/إلغاء تفعيل المستخدمين</h4>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="userId" class="form-label">معرف المستخدم:</label>
                            <input type="number" class="form-control" id="userId" value="1" min="1">
                        </div>
                        
                        <div class="d-flex gap-2 mb-3">
                            <button class="btn btn-success" onclick="testToggleStatus()">
                                <i class="bi bi-play-circle me-2"></i>اختبار تغيير الحالة
                            </button>
                            <button class="btn btn-info" onclick="testGetUser()">
                                <i class="bi bi-eye me-2"></i>عرض بيانات المستخدم
                            </button>
                        </div>
                        
                        <div id="result" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    // دوال مساعدة للواجهة
    function showLoading() {
        if (!document.getElementById('loadingOverlay')) {
            const overlay = document.createElement('div');
            overlay.id = 'loadingOverlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            `;
            overlay.innerHTML = `
                <div class="text-center text-white">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <div class="mt-2">جاري التحميل...</div>
                </div>
            `;
            document.body.appendChild(overlay);
        } else {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }
    }

    function hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
    }

    function showAlert(message, type = 'info') {
        const existingAlerts = document.querySelectorAll('.alert-floating');
        existingAlerts.forEach(alert => alert.remove());
        
        const alert = document.createElement('div');
        alert.className = `alert alert-${type === 'error' ? 'danger' : type} alert-floating`;
        alert.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        alert.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <strong>${type === 'success' ? 'نجح' : type === 'error' ? 'خطأ' : type === 'warning' ? 'تحذير' : 'معلومات'}</strong>
                    <br>
                    ${message}
                </div>
                <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            if (alert.parentElement) {
                alert.remove();
            }
        }, 5000);
    }

    function testToggleStatus() {
        const userId = document.getElementById('userId').value;
        if (!userId) {
            showAlert('يرجى إدخال معرف المستخدم', 'warning');
            return;
        }

        showLoading();
        fetch('admin/toggle-user-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'user_id=' + userId
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <div class="alert alert-${data.success ? 'success' : 'danger'}">
                    <h6>نتيجة الطلب:</h6>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
            
            if (data.success) {
                showAlert(data.message, 'success');
            } else {
                showAlert(data.message, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showAlert('حدث خطأ في الاتصال: ' + error.message, 'error');
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <h6>خطأ في الاتصال:</h6>
                    <pre>${error.message}</pre>
                </div>
            `;
        });
    }

    function testGetUser() {
        const userId = document.getElementById('userId').value;
        if (!userId) {
            showAlert('يرجى إدخال معرف المستخدم', 'warning');
            return;
        }

        showLoading();
        fetch(`admin/users?user_id=${userId}`)
        .then(response => response.text())
        .then(html => {
            hideLoading();
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `
                <div class="alert alert-info">
                    <h6>استجابة الصفحة:</h6>
                    <div style="max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; border-radius: 5px;">
                        <pre>${html.substring(0, 1000)}...</pre>
                    </div>
                </div>
            `;
        })
        .catch(error => {
            hideLoading();
            showAlert('حدث خطأ في الاتصال: ' + error.message, 'error');
        });
    }
    </script>
</body>
</html> 