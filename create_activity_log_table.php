<?php
/**
 * إنشاء جدول سجل النشاطات
 * HealthKey - Activity Log Table Creator
 */

require_once 'config.php';
require_once 'app/core/Database.php';

try {
    $db = Database::getInstance();
    
    echo "=== إنشاء جدول سجل النشاطات ===\n";
    
    // قراءة ملف SQL
    $sqlFile = 'database/activity_log_table.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("ملف SQL غير موجود: $sqlFile");
    }
    
    echo "✓ تم العثور على ملف SQL: $sqlFile\n";
    
    $sql = file_get_contents($sqlFile);
    echo "✓ تم قراءة محتوى الملف (" . strlen($sql) . " حرف)\n";
    
    // تقسيم الأوامر SQL
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    echo "✓ تم تقسيم الأوامر SQL (" . count($statements) . " أمر)\n";
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (empty($statement)) continue;
        
        try {
            // استخدام الطريقة المناسبة حسب نوع الأمر
            if (stripos($statement, 'CREATE') !== false || stripos($statement, 'ALTER') !== false) {
                // استخدام الاتصال المباشر للأوامر DDL
                $pdo = $db->getConnection();
                $pdo->exec($statement);
            } elseif (stripos($statement, 'INSERT') !== false) {
                // استخدام طريقة insert للبيانات
                $db->insert($statement);
            } else {
                // استخدام طريقة select للاستعلامات الأخرى
                $db->select($statement);
            }
            $successCount++;
            echo "✓ تم تنفيذ الأمر بنجاح\n";
        } catch (Exception $e) {
            $errorCount++;
            echo "✗ خطأ في تنفيذ الأمر: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n=== ملخص النتائج ===\n";
    echo "الأوامر الناجحة: $successCount\n";
    echo "الأوامر الفاشلة: $errorCount\n";
    
    if ($errorCount === 0) {
        echo "\n✅ تم إنشاء جدول سجل النشاطات بنجاح!\n";
        
        // التحقق من وجود البيانات
        $result = $db->selectOne("SELECT COUNT(*) as count FROM activity_log");
        echo "عدد السجلات في الجدول: " . $result['count'] . "\n";
        
    } else {
        echo "\n❌ حدثت أخطاء أثناء إنشاء الجدول\n";
    }
    
} catch (Exception $e) {
    echo "خطأ عام: " . $e->getMessage() . "\n";
    echo "الملف: " . $e->getFile() . "\n";
    echo "السطر: " . $e->getLine() . "\n";
}
?> 