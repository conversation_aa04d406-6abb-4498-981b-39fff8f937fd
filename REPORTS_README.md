# صفحة التقارير والإحصائيات - HealthKey

## نظرة عامة

صفحة التقارير والإحصائيات هي أداة شاملة للمدير لمراقبة وتحليل أداء النظام. توفر هذه الصفحة إحصائيات مفصلة ورسوم بيانية تفاعلية لجميع جوانب النظام.

## المميزات

### 📊 إحصائيات سريعة
- إجمالي المستخدمين
- إجمالي المواعيد
- إجمالي الوصفات
- مواعيد اليوم

### 📈 رسوم بيانية تفاعلية
- رسم بياني دائري لتوزيع المستخدمين حسب النوع
- رسم بياني شريطي لحالة المواعيد
- رسوم بيانية ديناميكية باستخدام Chart.js

### 📋 جداول تفصيلية
- أفضل الأطباء حسب المواعيد
- أفضل الأطباء حسب الوصفات
- إحصائيات إضافية مفصلة

### 📅 فلاتر متقدمة
- اختيار فترة زمنية محددة
- تصفية حسب نوع التقرير
- تحديث فوري للبيانات

### 📤 تصدير التقارير
- تصدير بصيغة CSV
- دعم كامل للغة العربية
- تقارير مفصلة حسب النوع

## أنواع التقارير

### 1. نظرة عامة (Overview)
تقرير شامل يحتوي على:
- إحصائيات عامة للنظام
- ملخص سريع للأداء
- مؤشرات الأداء الرئيسية

### 2. تقرير المستخدمين (Users)
تقرير مفصل عن المستخدمين:
- إحصائيات التسجيل
- توزيع المستخدمين حسب النوع
- المستخدمين النشطين وغير النشطين

### 3. تقرير المواعيد (Appointments)
تقرير شامل للمواعيد:
- إحصائيات حسب الحالة
- أفضل الأطباء حسب المواعيد
- تحليل الأداء الزمني

### 4. تقرير الوصفات (Prescriptions)
تقرير مفصل للوصفات:
- إحصائيات حسب الطبيب
- تحليل الأدوية الموصوفة
- حالة الوصفات

## كيفية الاستخدام

### الوصول للصفحة
```
URL: /admin/reports
```

### تطبيق الفلاتر
1. اختر التاريخ من وإلى
2. اختر نوع التقرير المطلوب
3. اضغط "تطبيق الفلاتر"

### تصدير التقرير
1. اضبط الفلاتر المطلوبة
2. اضغط "تصدير التقرير"
3. سيتم تحميل ملف CSV

## الملفات المطلوبة

### المتحكم
- `app/controllers/AdminController.php`
  - `reports()` - عرض صفحة التقارير
  - `exportReport()` - تصدير التقارير

### النماذج
- `app/models/User.php` - إحصائيات المستخدمين
- `app/models/Appointment.php` - إحصائيات المواعيد
- `app/models/Prescription.php` - إحصائيات الوصفات

### العرض
- `views/admin/reports/index.php` - صفحة التقارير الرئيسية

### CSS
- `public/css/admin.css` - تنسيقات خاصة بالمدير

## الدوال المساعدة

### AdminController
```php
// إحصائيات تسجيل المستخدمين
private function getUserRegistrationStats($startDate, $endDate)

// إحصائيات المواعيد حسب الحالة
private function getAppointmentsByStatus($startDate, $endDate)

// إحصائيات الوصفات حسب الطبيب
private function getPrescriptionsByDoctor($startDate, $endDate)

// تصدير التقارير
private function exportUserReport($startDate, $endDate)
private function exportAppointmentReport($startDate, $endDate)
private function exportPrescriptionReport($startDate, $endDate)
private function exportOverviewReport($startDate, $endDate)
```

## التقنيات المستخدمة

### Frontend
- **Bootstrap 5** - إطار العمل الأساسي
- **Chart.js** - الرسوم البيانية التفاعلية
- **Bootstrap Icons** - الأيقونات
- **JavaScript** - التفاعل والوظائف

### Backend
- **PHP** - منطق الخادم
- **MySQL** - قاعدة البيانات
- **PDO** - الاتصال بقاعدة البيانات

## الأمان

### التحقق من الصلاحيات
- التحقق من تسجيل الدخول
- التحقق من نوع المستخدم (admin)
- حماية من الوصول غير المصرح

### معالجة البيانات
- تنظيف المدخلات
- حماية من SQL Injection
- ترميز المخرجات

## الأداء

### تحسينات قاعدة البيانات
- استعلامات محسنة
- فهارس مناسبة
- تجميع البيانات

### تحسينات الواجهة
- تحميل تدريجي للبيانات
- رسوم بيانية محسنة
- استجابة سريعة

## الاختبار

### ملف الاختبار
- `test_reports.php` - اختبار شامل للوظائف

### اختبار الوظائف
- اختبار الوصول للصفحة
- اختبار تطبيق الفلاتر
- اختبار تصدير التقارير
- اختبار الإحصائيات

## الصيانة

### التحديثات الدورية
- تحديث الإحصائيات
- تنظيف البيانات القديمة
- تحسين الأداء

### المراقبة
- مراقبة استخدام الذاكرة
- مراقبة استجابة قاعدة البيانات
- مراقبة أخطاء النظام

## الدعم

### في حالة المشاكل
1. تحقق من اتصال قاعدة البيانات
2. تحقق من صلاحيات المستخدم
3. راجع سجلات الأخطاء
4. اختبر الوظائف الأساسية

### التحسينات المستقبلية
- إضافة المزيد من الرسوم البيانية
- دعم تصدير بصيغ إضافية
- إضافة تقارير مخصصة
- تحسين الأداء

---

**تم تطوير هذه الصفحة بواسطة فريق HealthKey**
**تاريخ التطوير: 2024** 