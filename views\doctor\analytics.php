<?php
/**
 * صفحة التحليلات للطبيب
 */
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-chart-line text-primary"></i>
                التحليلات والإحصائيات
            </h1>
            <p class="text-muted">عرض التحليلات والإحصائيات التفصيلية</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary" onclick="refreshAnalytics()">
                <i class="fas fa-sync-alt"></i>
                تحديث البيانات
            </button>
            <button class="btn btn-primary" onclick="exportAnalytics()">
                <i class="fas fa-download"></i>
                تصدير التحليلات
            </button>
        </div>
    </div>

    <!-- فلتر التاريخ -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="period" class="form-label">الفترة الزمنية</label>
                    <select class="form-select" id="period" name="period" onchange="updateDateRange()">
                        <option value="7" <?= ($period ?? '30') == '7' ? 'selected' : '' ?>>آخر 7 أيام</option>
                        <option value="30" <?= ($period ?? '30') == '30' ? 'selected' : '' ?>>آخر 30 يوم</option>
                        <option value="90" <?= ($period ?? '30') == '90' ? 'selected' : '' ?>>آخر 90 يوم</option>
                        <option value="365" <?= ($period ?? '30') == '365' ? 'selected' : '' ?>>آخر سنة</option>
                        <option value="custom" <?= ($period ?? '30') == 'custom' ? 'selected' : '' ?>>فترة مخصصة</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="start_date" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" 
                           value="<?= htmlspecialchars($startDate) ?>">
                </div>
                <div class="col-md-3">
                    <label for="end_date" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" 
                           value="<?= htmlspecialchars($endDate) ?>">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search"></i>
                        تطبيق
                    </button>
                    <a href="<?= App::url('doctor/analytics') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- الإحصائيات السريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي المواعيد
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['appointments']['total'] ?? 0) ?>
                            </div>
                            <div class="text-xs text-muted">
                                <?= $stats['appointments']['growth'] ?? 0 ?>% من الشهر السابق
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                الوصفات الطبية
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['prescriptions']['total'] ?? 0) ?>
                            </div>
                            <div class="text-xs text-muted">
                                <?= $stats['prescriptions']['growth'] ?? 0 ?>% من الشهر السابق
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-prescription fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                المرضى الجدد
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['new_patients']['total'] ?? 0) ?>
                            </div>
                            <div class="text-xs text-muted">
                                <?= $stats['new_patients']['growth'] ?? 0 ?>% من الشهر السابق
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-plus fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-right-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                معدل الإشغال
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <?= number_format($stats['occupancy_rate']['percentage'] ?? 0) ?>%
                            </div>
                            <div class="text-xs text-muted">
                                <?= $stats['occupancy_rate']['change'] ?? 0 ?>% من الشهر السابق
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row mb-4">
        <!-- رسم بياني للمواعيد -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-area"></i>
                        تطور المواعيد عبر الزمن
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="appointmentsChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- رسم بياني دائري لأنواع المواعيد -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie"></i>
                        توزيع حالات المواعيد
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="appointmentTypesChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- المزيد من التحليلات -->
    <div class="row mb-4">
        <!-- رسم بياني للوصفات الطبية -->
        <div class="col-xl-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar"></i>
                        الوصفات الطبية الشهرية
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="prescriptionsChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- رسم بياني للمرضى -->
        <div class="col-xl-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-line"></i>
                        نمو قاعدة المرضى
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="patientsChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- تحليلات مفصلة -->
    <div class="row mb-4">
        <!-- أفضل الأوقات -->
        <div class="col-xl-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-clock"></i>
                        أفضل أوقات المواعيد
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($peakHours)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>الوقت</th>
                                        <th>عدد المواعيد</th>
                                        <th>النسبة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($peakHours as $hour): ?>
                                        <tr>
                                            <td><?= $hour['time_slot'] ?></td>
                                            <td><?= $hour['count'] ?></td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar" style="width: <?= $hour['percentage'] ?>%">
                                                        <?= number_format($hour['percentage'], 1) ?>%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">لا توجد بيانات متاحة</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- أكثر الأمراض شيوعاً -->
        <div class="col-xl-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-stethoscope"></i>
                        أكثر الأمراض شيوعاً
                    </h6>
                </div>
                <div class="card-body">
                    <?php if (!empty($commonDiagnoses)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>التشخيص</th>
                                        <th>عدد الحالات</th>
                                        <th>النسبة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($commonDiagnoses as $diagnosis): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($diagnosis['diagnosis']) ?></td>
                                            <td><?= $diagnosis['count'] ?></td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-success" style="width: <?= $diagnosis['percentage'] ?>%">
                                                        <?= number_format($diagnosis['percentage'], 1) ?>%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-muted text-center">لا توجد بيانات متاحة</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- أداء الطبيب -->
        <div class="col-xl-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user-md"></i>
                        مؤشرات الأداء
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>معدل رضا المرضى</span>
                            <span><?= number_format($performance['satisfaction_rate'] ?? 0, 1) ?>%</span>
                        </div>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-success" style="width: <?= $performance['satisfaction_rate'] ?? 0 ?>%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>متوسط وقت الموعد</span>
                            <span><?= $performance['avg_appointment_time'] ?? 0 ?> دقيقة</span>
                        </div>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-info" style="width: <?= min(100, ($performance['avg_appointment_time'] ?? 0) / 60 * 100) ?>%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>معدل الحضور</span>
                            <span><?= number_format($performance['attendance_rate'] ?? 0, 1) ?>%</span>
                        </div>
                        <div class="progress" style="height: 10px;">
                            <div class="progress-bar bg-warning" style="width: <?= $performance['attendance_rate'] ?? 0 ?>%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// تهيئة الرسوم البيانية
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

function initializeCharts() {
    // رسم بياني للمواعيد
    const appointmentsCtx = document.getElementById('appointmentsChart').getContext('2d');
    new Chart(appointmentsCtx, {
        type: 'line',
        data: {
            labels: <?= json_encode($chartData['appointments']['labels'] ?? []) ?>,
            datasets: [{
                label: 'المواعيد',
                data: <?= json_encode($chartData['appointments']['data'] ?? []) ?>,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // رسم بياني دائري لأنواع المواعيد
    const typesCtx = document.getElementById('appointmentTypesChart').getContext('2d');
    new Chart(typesCtx, {
        type: 'doughnut',
        data: {
            labels: <?= json_encode($chartData['appointmentTypes']['labels'] ?? []) ?>,
            datasets: [{
                data: <?= json_encode($chartData['appointmentTypes']['data'] ?? []) ?>,
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });

    // رسم بياني للوصفات الطبية
    const prescriptionsCtx = document.getElementById('prescriptionsChart').getContext('2d');
    new Chart(prescriptionsCtx, {
        type: 'bar',
        data: {
            labels: <?= json_encode($chartData['prescriptions']['labels'] ?? []) ?>,
            datasets: [{
                label: 'الوصفات الطبية',
                data: <?= json_encode($chartData['prescriptions']['data'] ?? []) ?>,
                backgroundColor: 'rgba(54, 162, 235, 0.8)'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // رسم بياني للمرضى
    const patientsCtx = document.getElementById('patientsChart').getContext('2d');
    new Chart(patientsCtx, {
        type: 'line',
        data: {
            labels: <?= json_encode($chartData['patients']['labels'] ?? []) ?>,
            datasets: [{
                label: 'المرضى الجدد',
                data: <?= json_encode($chartData['patients']['data'] ?? []) ?>,
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function updateDateRange() {
    const period = document.getElementById('period').value;
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');
    
    if (period !== 'custom') {
        const end = new Date();
        const start = new Date();
        start.setDate(start.getDate() - parseInt(period));
        
        endDate.value = end.toISOString().split('T')[0];
        startDate.value = start.toISOString().split('T')[0];
    }
}

function refreshAnalytics() {
    location.reload();
}

function exportAnalytics() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    
    const exportUrl = '<?= App::url('doctor/analytics/export') ?>' + 
                     '?start_date=' + startDate + 
                     '&end_date=' + endDate;
    
    window.open(exportUrl, '_blank');
}
</script> 