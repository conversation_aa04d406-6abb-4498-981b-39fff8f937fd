<?php
/**
 * اختبار صفحة التحليلات الخاصة بالطبيب
 */

require_once 'config.php';

echo "<h1>اختبار صفحة التحليلات الخاصة بالطبيب</h1>";

try {
    // اختبار الوصول إلى صفحة التحليلات
    $url = 'doctor/analytics';
    
    echo "<h2>1. اختبار المسار</h2>";
    echo "<p>المسار: <code>$url</code></p>";
    
    // محاكاة تسجيل دخول طبيب
    $_SESSION['user_id'] = 2; // افتراض أن المستخدم رقم 2 هو طبيب
    $_SESSION['user_type'] = 'doctor';
    $_SESSION['user_name'] = 'د. أحمد محمد';
    
    echo "<p>✅ تم تسجيل دخول الطبيب</p>";
    
    // اختبار الوصول إلى الصفحة
    echo "<h2>2. اختبار الوصول إلى الصفحة</h2>";
    echo "<p><a href='public/index.php?url=$url' target='_blank'>فتح صفحة التحليلات</a></p>";
    
    // اختبار معاملات التاريخ
    echo "<h2>3. اختبار معاملات التاريخ</h2>";
    $testUrls = [
        'doctor/analytics?period=7',
        'doctor/analytics?period=30',
        'doctor/analytics?period=90',
        'doctor/analytics?period=365',
        'doctor/analytics?start_date=2024-01-01&end_date=2024-01-31&period=custom'
    ];
    
    foreach ($testUrls as $testUrl) {
        echo "<p><a href='public/index.php?url=$testUrl' target='_blank'>$testUrl</a></p>";
    }
    
    echo "<h2>✅ انتهى الاختبار</h2>";
    echo "<p>إذا كانت الصفحة تفتح بدون أخطاء، فهذا يعني أن المشكلة قد تم حلها.</p>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 