<?php
/**
 * الصفحة الرئيسية لنظام HealthKey
 * Main Entry Point for HealthKey System
 */

// تضمين ملف التكوين
require_once 'config.php';

// بدء الجلسة
SessionHelper::start();

// التحقق من حالة قاعدة البيانات
try {
    $db = Database::getInstance();
    $dbStatus = true;
    $dbMessage = "قاعدة البيانات متصلة";
} catch (Exception $e) {
    $dbStatus = false;
    $dbMessage = "خطأ في الاتصال: " . $e->getMessage();
}

// إعداد البيانات للصفحة
$data = [
    'title' => 'نظام HealthKey - إدارة الرعاية الصحية',
    'dbStatus' => $dbStatus,
    'dbMessage' => $dbMessage,
    'isLoggedIn' => SessionHelper::isLoggedIn(),
    'currentUser' => SessionHelper::getCurrentUser()
];

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $data['title'] ?></title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        .hero-section {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 100px 0;
        }
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="<?= UrlHelper::url('') ?>">
                <i class="bi bi-heart-pulse me-2"></i>
                <strong>HealthKey</strong>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">المميزات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">حول النظام</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if ($data['isLoggedIn']): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i>
                                <?= htmlspecialchars($data['currentUser']['first_name'] ?? 'المستخدم') ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?= UrlHelper::url($data['currentUser']['user_type'] . '/dashboard') ?>">
                                    <i class="bi bi-speedometer2 me-2"></i>لوحة التحكم
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?= UrlHelper::url('auth/logout') ?>">
                                    <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= UrlHelper::url('auth/login') ?>">
                                <i class="bi bi-box-arrow-in-right me-1"></i>
                                تسجيل الدخول
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= UrlHelper::url('auth/register') ?>">
                                <i class="bi bi-person-plus me-1"></i>
                                إنشاء حساب
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-4">
                <i class="bi bi-heart-pulse me-3"></i>
                مرحباً بك في HealthKey
            </h1>
            <p class="lead mb-5">
                نظام إدارة الرعاية الصحية الإلكترونية الشامل
                <br>
                إدارة المواعيد، الوصفات الطبية، والسجلات الصحية بكل سهولة
            </p>
            
            <!-- System Status -->
            <div class="alert alert-<?= $data['dbStatus'] ? 'success' : 'danger' ?> d-inline-block">
                <span class="status-indicator <?= $data['dbStatus'] ? 'status-online' : 'status-offline' ?>"></span>
                <?= $data['dbMessage'] ?>
            </div>
            
            <?php if (!$data['isLoggedIn']): ?>
                <div class="mt-4">
                    <a href="<?= UrlHelper::url('auth/login') ?>" class="btn btn-light btn-lg me-3">
                        <i class="bi bi-box-arrow-in-right me-2"></i>
                        تسجيل الدخول
                    </a>
                    <a href="<?= UrlHelper::url('auth/register') ?>" class="btn btn-outline-light btn-lg">
                        <i class="bi bi-person-plus me-2"></i>
                        إنشاء حساب جديد
                    </a>
                </div>
            <?php else: ?>
                <div class="mt-4">
                    <a href="<?= UrlHelper::url($data['currentUser']['user_type'] . '/dashboard') ?>" class="btn btn-light btn-lg">
                        <i class="bi bi-speedometer2 me-2"></i>
                        الذهاب إلى لوحة التحكم
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-5">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="display-5 fw-bold">مميزات النظام</h2>
                    <p class="lead text-muted">حلول شاملة لإدارة الرعاية الصحية</p>
                </div>
            </div>
            
            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <div class="card feature-card h-100 text-center p-4">
                        <div class="card-body">
                            <i class="bi bi-calendar-check display-4 text-primary mb-3"></i>
                            <h5 class="card-title">إدارة المواعيد</h5>
                            <p class="card-text">حجز وإدارة المواعيد الطبية بسهولة مع نظام تذكير ذكي</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="card feature-card h-100 text-center p-4">
                        <div class="card-body">
                            <i class="bi bi-prescription2 display-4 text-success mb-3"></i>
                            <h5 class="card-title">الوصفات الطبية</h5>
                            <p class="card-text">إنشاء وإدارة الوصفات الطبية الإلكترونية مع نظام صرف آمن</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="card feature-card h-100 text-center p-4">
                        <div class="card-body">
                            <i class="bi bi-clipboard2-pulse display-4 text-info mb-3"></i>
                            <h5 class="card-title">السجلات الطبية</h5>
                            <p class="card-text">حفظ وإدارة السجلات الطبية الشاملة للمرضى</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="card feature-card h-100 text-center p-4">
                        <div class="card-body">
                            <i class="bi bi-bell display-4 text-warning mb-3"></i>
                            <h5 class="card-title">نظام الإشعارات</h5>
                            <p class="card-text">تنبيهات فورية للمواعيد والوصفات والتحديثات المهمة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- User Types Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row text-center mb-5">
                <div class="col-12">
                    <h2 class="display-5 fw-bold">أنواع المستخدمين</h2>
                    <p class="lead text-muted">النظام يخدم جميع أطراف الرعاية الصحية</p>
                </div>
            </div>
            
            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <div class="card text-center p-4">
                        <div class="card-body">
                            <i class="bi bi-person display-4 text-primary mb-3"></i>
                            <h5 class="card-title">المرضى</h5>
                            <p class="card-text">حجز المواعيد، متابعة الوصفات، والوصول للسجلات الطبية</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="card text-center p-4">
                        <div class="card-body">
                            <i class="bi bi-person-badge display-4 text-success mb-3"></i>
                            <h5 class="card-title">الأطباء</h5>
                            <p class="card-text">إدارة المواعيد، كتابة الوصفات، وتسجيل السجلات الطبية</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="card text-center p-4">
                        <div class="card-body">
                            <i class="bi bi-capsule display-4 text-info mb-3"></i>
                            <h5 class="card-title">الصيادلة</h5>
                            <p class="card-text">صرف الأدوية، التحقق من الوصفات، وإدارة المخزون</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6">
                    <div class="card text-center p-4">
                        <div class="card-body">
                            <i class="bi bi-gear display-4 text-warning mb-3"></i>
                            <h5 class="card-title">المديرين</h5>
                            <p class="card-text">إدارة النظام، المستخدمين، والتقارير الشاملة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h2 class="display-5 fw-bold mb-4">حول نظام HealthKey</h2>
                    <p class="lead">
                        نظام HealthKey هو حل شامل لإدارة الرعاية الصحية الإلكترونية، 
                        مصمم لتسهيل التواصل بين المرضى والأطباء والصيادلة.
                    </p>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>واجهة سهلة الاستخدام</li>
                        <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>أمان عالي للبيانات</li>
                        <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>دعم اللغة العربية</li>
                        <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>تقارير شاملة</li>
                        <li class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>نظام إشعارات ذكي</li>
                    </ul>
                </div>
                <div class="col-lg-6">
                    <div class="text-center">
                        <i class="bi bi-heart-pulse display-1 text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="bi bi-heart-pulse me-2"></i>HealthKey</h5>
                    <p class="mb-0">نظام إدارة الرعاية الصحية الإلكترونية</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <small>
                            حالة النظام: 
                            <span class="badge bg-<?= $data['dbStatus'] ? 'success' : 'danger' ?>">
                                <?= $data['dbStatus'] ? 'متصل' : 'غير متصل' ?>
                            </span>
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
