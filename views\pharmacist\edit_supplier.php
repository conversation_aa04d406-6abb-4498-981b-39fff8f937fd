<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">تعديل معلومات المورد</h1>
        <p class="text-muted">تعديل معلومات المورد: <?= htmlspecialchars($supplier['name']) ?></p>
    </div>
    <div>
        <a href="<?= App::url('pharmacist/suppliers') ?>" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            العودة للموردين
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Form Card -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-pencil me-2"></i>
                    معلومات المورد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= App::url('pharmacist/edit-supplier/' . $supplier['id']) ?>" id="editSupplierForm">
                    <div class="row">
                        <!-- اسم المورد -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">
                                اسم المورد <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?= htmlspecialchars($supplier['name']) ?>" 
                                   placeholder="أدخل اسم المورد" required>
                            <div class="invalid-feedback">
                                يرجى إدخال اسم المورد
                            </div>
                        </div>

                        <!-- الشخص المسؤول -->
                        <div class="col-md-6 mb-3">
                            <label for="contact_person" class="form-label">
                                الشخص المسؤول
                            </label>
                            <input type="text" class="form-control" id="contact_person" name="contact_person" 
                                   value="<?= htmlspecialchars($supplier['contact_person'] ?? '') ?>" 
                                   placeholder="أدخل اسم الشخص المسؤول">
                        </div>

                        <!-- البريد الإلكتروني -->
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                البريد الإلكتروني
                            </label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?= htmlspecialchars($supplier['email'] ?? '') ?>" 
                                   placeholder="أدخل البريد الإلكتروني">
                            <div class="invalid-feedback">
                                يرجى إدخال بريد إلكتروني صحيح
                            </div>
                        </div>

                        <!-- رقم الهاتف -->
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">
                                رقم الهاتف
                            </label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="<?= htmlspecialchars($supplier['phone'] ?? '') ?>" 
                                   placeholder="أدخل رقم الهاتف">
                        </div>

                        <!-- العنوان -->
                        <div class="col-12 mb-3">
                            <label for="address" class="form-label">
                                العنوان
                            </label>
                            <textarea class="form-control" id="address" name="address" rows="3" 
                                      placeholder="أدخل العنوان الكامل"><?= htmlspecialchars($supplier['address'] ?? '') ?></textarea>
                        </div>

                        <!-- الموقع الإلكتروني -->
                        <div class="col-md-6 mb-3">
                            <label for="website" class="form-label">
                                الموقع الإلكتروني
                            </label>
                            <input type="url" class="form-control" id="website" name="website" 
                                   value="<?= htmlspecialchars($supplier['website'] ?? '') ?>" 
                                   placeholder="https://example.com">
                            <div class="invalid-feedback">
                                يرجى إدخال رابط صحيح
                            </div>
                        </div>

                        <!-- الحالة -->
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">
                                الحالة
                            </label>
                            <select class="form-select" id="status" name="status">
                                <option value="active" <?= ($supplier['status'] == 'active') ? 'selected' : '' ?>>نشط</option>
                                <option value="inactive" <?= ($supplier['status'] == 'inactive') ? 'selected' : '' ?>>غير نشط</option>
                            </select>
                        </div>

                        <!-- ملاحظات -->
                        <div class="col-12 mb-3">
                            <label for="notes" class="form-label">
                                ملاحظات
                            </label>
                            <textarea class="form-control" id="notes" name="notes" rows="4" 
                                      placeholder="أدخل أي ملاحظات إضافية"><?= htmlspecialchars($supplier['notes'] ?? '') ?></textarea>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-secondary" onclick="history.back()">
                            <i class="bi bi-x-circle me-2"></i>
                            إلغاء
                        </button>
                        <div>
                            <button type="reset" class="btn btn-outline-secondary me-2">
                                <i class="bi bi-arrow-clockwise me-2"></i>
                                إعادة تعيين
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>
                                حفظ التغييرات
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Supplier Info -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    معلومات المورد
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>معرف المورد:</strong>
                    <span class="badge bg-secondary">#<?= $supplier['id'] ?></span>
                </div>
                <div class="mb-3">
                    <strong>تاريخ الإضافة:</strong>
                    <br>
                    <small class="text-muted">
                        <?= date('Y-m-d H:i', strtotime($supplier['created_at'])) ?>
                    </small>
                </div>
                <div class="mb-3">
                    <strong>آخر تحديث:</strong>
                    <br>
                    <small class="text-muted">
                        <?= date('Y-m-d H:i', strtotime($supplier['updated_at'])) ?>
                    </small>
                </div>
                <div class="mb-3">
                    <strong>الحالة:</strong>
                    <?php if ($supplier['status'] == 'active'): ?>
                        <span class="badge bg-success">
                            <i class="bi bi-check-circle me-1"></i>
                            نشط
                        </span>
                    <?php else: ?>
                        <span class="badge bg-secondary">
                            <i class="bi bi-pause-circle me-1"></i>
                            غير نشط
                        </span>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-danger" 
                            onclick="deleteSupplier(<?= $supplier['id'] ?>, '<?= htmlspecialchars($supplier['name']) ?>')">
                        <i class="bi bi-trash me-2"></i>
                        حذف المورد
                    </button>
                    <a href="<?= App::url('pharmacist/suppliers') ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-2"></i>
                        العودة للموردين
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form Validation
document.getElementById('editSupplierForm').addEventListener('submit', function(e) {
    let isValid = true;
    
    // التحقق من اسم المورد
    const name = document.getElementById('name');
    if (!name.value.trim()) {
        name.classList.add('is-invalid');
        isValid = false;
    } else {
        name.classList.remove('is-invalid');
    }
    
    // التحقق من البريد الإلكتروني إذا تم إدخاله
    const email = document.getElementById('email');
    if (email.value && !isValidEmail(email.value)) {
        email.classList.add('is-invalid');
        isValid = false;
    } else {
        email.classList.remove('is-invalid');
    }
    
    // التحقق من الموقع الإلكتروني إذا تم إدخاله
    const website = document.getElementById('website');
    if (website.value && !isValidUrl(website.value)) {
        website.classList.add('is-invalid');
        isValid = false;
    } else {
        website.classList.remove('is-invalid');
    }
    
    if (!isValid) {
        e.preventDefault();
    }
});

// دالة التحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// دالة التحقق من صحة الرابط
function isValidUrl(url) {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

// دالة حذف المورد
function deleteSupplier(id, name) {
    if (confirm('هل أنت متأكد من حذف المورد "' + name + '"؟')) {
        window.location.href = '<?= App::url('pharmacist/delete-supplier/') ?>' + id;
    }
}

// تفعيل tooltips
var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
});
</script> 