<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-chat-dots me-2"></i>
            الرسائل
        </h1>
        <p class="text-muted">إدارة رسائلك والتواصل مع الأطباء</p>
    </div>
    <div>
        <a href="<?= App::url('patient/compose-message') ?>" class="btn btn-primary me-2">
            <i class="bi bi-plus-circle me-2"></i>
            رسالة جديدة
        </a>
        <a href="<?= App::url('patient/dashboard') ?>" class="btn btn-outline-primary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['inbox'] ?? 0 ?></h4>
                        <p class="mb-0">الرسائل الواردة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-inbox fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $unreadCount ?></h4>
                        <p class="mb-0">غير مقروءة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-envelope fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['sent'] ?? 0 ?></h4>
                        <p class="mb-0">الرسائل الصادرة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-send fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $totalMessages ?></h4>
                        <p class="mb-0">إجمالي الرسائل</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-chat fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Actions -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="search" 
                               placeholder="البحث في الرسائل..." 
                               value="<?= htmlspecialchars($search) ?>">
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="type">
                            <option value="all" <?= $selectedType === 'all' ? 'selected' : '' ?>>جميع الرسائل</option>
                            <option value="inbox" <?= $selectedType === 'inbox' ? 'selected' : '' ?>>الواردة</option>
                            <option value="sent" <?= $selectedType === 'sent' ? 'selected' : '' ?>>الصادرة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </form>
            </div>
            <div class="col-md-4 text-end">
                <a href="<?= App::url('patient/messages') ?>" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Messages List -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-list-ul me-2"></i>
            الرسائل (<?= $totalMessages ?>)
        </h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($messages)): ?>
            <div class="text-center py-5">
                <i class="bi bi-chat-slash fs-1 text-muted"></i>
                <h5 class="mt-3 text-muted">لا توجد رسائل</h5>
                <p class="text-muted">ستظهر هنا الرسائل عند وصولها</p>
                <a href="<?= App::url('patient/compose-message') ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    إنشاء رسالة جديدة
                </a>
            </div>
        <?php else: ?>
            <div class="list-group list-group-flush">
                <?php foreach ($messages as $message): ?>
                    <div class="list-group-item list-group-item-action message-item <?= $message['status'] === 'sent' && $message['recipient_id'] == $this->currentUser['id'] ? 'unread' : '' ?>" 
                         data-id="<?= $message['id'] ?>">
                        <div class="d-flex w-100 justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="badge bg-<?= Message::getTypeColor($message['type']) ?> me-2">
                                        <i class="bi <?= Message::getTypeIcon($message['type']) ?>"></i>
                                        <?= Message::getTypeLabel($message['type']) ?>
                                    </span>
                                    <?php if ($message['status'] === 'sent' && $message['recipient_id'] == $this->currentUser['id']): ?>
                                        <span class="badge bg-warning">جديد</span>
                                    <?php endif; ?>
                                    <span class="badge bg-<?= Message::getPriorityColor($message['priority']) ?> ms-2">
                                        <?= ucfirst($message['priority']) ?>
                                    </span>
                                </div>
                                <h6 class="mb-1 <?= $message['status'] === 'sent' && $message['recipient_id'] == $this->currentUser['id'] ? 'fw-bold' : '' ?>">
                                    <?= htmlspecialchars($message['subject']) ?>
                                </h6>
                                <p class="mb-1 text-muted">
                                    <?php if ($message['sender_id'] == $this->currentUser['id']): ?>
                                        <strong>إلى:</strong> <?= htmlspecialchars($message['recipient_name']) ?>
                                    <?php else: ?>
                                        <strong>من:</strong> <?= htmlspecialchars($message['sender_name']) ?>
                                    <?php endif; ?>
                                </p>
                                <p class="mb-1 text-muted small">
                                    <?= htmlspecialchars(substr($message['content'], 0, 100)) ?><?= strlen($message['content']) > 100 ? '...' : '' ?>
                                </p>
                                <small class="text-muted">
                                    <i class="bi bi-clock me-1"></i>
                                    <?= Message::formatTime($message['created_at']) ?>
                                </small>
                            </div>
                            <div class="ms-3">
                                <div class="btn-group btn-group-sm">
                                    <a href="<?= App::url('patient/view-message', ['id' => $message['id']]) ?>" 
                                       class="btn btn-outline-primary btn-sm" title="عرض">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <?php if ($message['status'] === 'sent' && $message['recipient_id'] == $this->currentUser['id']): ?>
                                        <button type="button" class="btn btn-outline-success btn-sm mark-read" 
                                                data-id="<?= $message['id'] ?>" title="تسجيل كمقروء">
                                            <i class="bi bi-check"></i>
                                        </button>
                                    <?php endif; ?>
                                    <button type="button" class="btn btn-outline-danger btn-sm delete-message" 
                                            data-id="<?= $message['id'] ?>" title="حذف">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="card-footer">
                    <nav aria-label="صفحات الرسائل">
                        <ul class="pagination justify-content-center mb-0">
                            <?php if ($currentPage > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?= App::url('patient/messages', ['page' => $currentPage - 1, 'type' => $selectedType, 'search' => $search]) ?>">
                                        <i class="bi bi-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++): ?>
                                <li class="page-item <?= $i === $currentPage ? 'active' : '' ?>">
                                    <a class="page-link" href="<?= App::url('patient/messages', ['page' => $i, 'type' => $selectedType, 'search' => $search]) ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($currentPage < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?= App::url('patient/messages', ['page' => $currentPage + 1, 'type' => $selectedType, 'search' => $search]) ?>">
                                        <i class="bi bi-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<style>
.message-item.unread {
    background-color: #f8f9fa;
    border-left: 4px solid #ffc107;
}

.message-item:hover {
    background-color: #f8f9fa;
}

.message-item .btn-group {
    opacity: 0;
    transition: opacity 0.2s;
}

.message-item:hover .btn-group {
    opacity: 1;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تسجيل الرسالة كمقروءة
    document.querySelectorAll('.mark-read').forEach(button => {
        button.addEventListener('click', function() {
            const messageId = this.dataset.id;
            markMessageAsRead(messageId);
        });
    });

    // حذف الرسالة
    document.querySelectorAll('.delete-message').forEach(button => {
        button.addEventListener('click', function() {
            const messageId = this.dataset.id;
            if (confirm('هل أنت متأكد من حذف هذه الرسالة؟')) {
                deleteMessage(messageId);
            }
        });
    });
});

function markMessageAsRead(messageId) {
    fetch('<?= App::url('patient/mark-message-read') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'message_id=' + messageId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إزالة علامة "جديد" وتحديث المظهر
            const messageItem = document.querySelector(`[data-id="${messageId}"]`);
            if (messageItem) {
                messageItem.classList.remove('unread');
                const newBadge = messageItem.querySelector('.badge.bg-warning');
                if (newBadge) newBadge.remove();
                const title = messageItem.querySelector('h6');
                if (title) title.classList.remove('fw-bold');
                
                // إزالة زر "تسجيل كمقروء"
                const markReadBtn = messageItem.querySelector('.mark-read');
                if (markReadBtn) markReadBtn.remove();
            }
            
            // تحديث العداد
            updateMessageCount();
            showAlert('success', data.message);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ أثناء تسجيل الرسالة كمقروءة');
    });
}

function deleteMessage(messageId) {
    fetch('<?= App::url('patient/delete-message') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'message_id=' + messageId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إزالة العنصر من الصفحة
            const messageItem = document.querySelector(`[data-id="${messageId}"]`);
            if (messageItem) {
                messageItem.remove();
            }
            
            // تحديث العداد
            updateMessageCount();
            showAlert('success', data.message);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ أثناء حذف الرسالة');
    });
}

function updateMessageCount() {
    // تحديث عداد الرسائل غير المقروءة في الشريط الجانبي
    const unreadCount = document.querySelectorAll('.message-item.unread').length;
    const sidebarBadge = document.querySelector('.nav-link[href*="messages"] .nav-badge');
    if (sidebarBadge) {
        sidebarBadge.textContent = unreadCount;
        if (unreadCount === 0) {
            sidebarBadge.style.display = 'none';
        }
    }
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه تلقائياً بعد 3 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}
</script> 