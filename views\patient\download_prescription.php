<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الوصفة الطبية - <?= htmlspecialchars($patient['name']) ?></title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fff;
            padding: 20px;
        }
        
        .prescription-header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .clinic-name {
            font-size: 28px;
            font-weight: 700;
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .prescription-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .prescription-subtitle {
            font-size: 16px;
            color: #666;
        }
        
        .prescription-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .info-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        
        .info-section h3 {
            font-size: 18px;
            font-weight: 600;
            color: #007bff;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #555;
        }
        
        .info-value {
            color: #333;
        }
        
        .medications-section {
            margin-bottom: 30px;
        }
        
        .medications-section h3 {
            font-size: 20px;
            font-weight: 600;
            color: #007bff;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .medications-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .medications-table th {
            background-color: #007bff;
            color: white;
            padding: 12px;
            text-align: center;
            font-weight: 600;
        }
        
        .medications-table td {
            padding: 12px;
            border: 1px solid #dee2e6;
            text-align: center;
        }
        
        .medications-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .medication-name {
            font-weight: 600;
            color: #007bff;
        }
        
        .generic-name {
            font-size: 12px;
            color: #666;
            font-style: italic;
        }
        
        .instructions-section {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .instructions-section h4 {
            color: #856404;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .instructions-content {
            color: #856404;
            line-height: 1.8;
        }
        
        .diagnosis-section {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .diagnosis-section h4 {
            color: #0c5460;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .diagnosis-content {
            color: #0c5460;
            line-height: 1.8;
        }
        
        .notes-section {
            background-color: #e2e3e5;
            border: 1px solid #d6d8db;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .notes-section h4 {
            color: #383d41;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .notes-content {
            color: #383d41;
            line-height: 1.8;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #dee2e6;
            text-align: center;
        }
        
        .doctor-signature {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
        }
        
        .signature-box {
            width: 200px;
            height: 80px;
            border: 2px solid #333;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: #666;
        }
        
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 14px;
        }
        
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-expired {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status-dispensed {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .status-cancelled {
            background-color: #e2e3e5;
            color: #383d41;
        }
        
        .expiry-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .expiry-warning h5 {
            color: #856404;
            margin-bottom: 5px;
        }
        
        .expiry-warning p {
            color: #856404;
            margin: 0;
        }
        
        @media print {
            body {
                padding: 0;
            }
            
            .prescription-header {
                border-bottom: 3px solid #007bff;
            }
            
            .info-section {
                background-color: #f8f9fa !important;
                border: 1px solid #dee2e6 !important;
            }
            
            .medications-table th {
                background-color: #007bff !important;
                color: white !important;
            }
        }
    </style>
</head>
<body>
    <!-- Prescription Header -->
    <div class="prescription-header">
        <div class="clinic-name">HealthKey</div>
        <div class="prescription-title">الوصفة الطبية</div>
        <div class="prescription-subtitle">نظام إدارة الرعاية الصحية</div>
    </div>
    
    <!-- Patient and Doctor Information -->
    <div class="prescription-info">
        <div class="info-section">
            <h3>معلومات المريض</h3>
            <div class="info-row">
                <span class="info-label">الاسم:</span>
                <span class="info-value"><?= htmlspecialchars($patient['name']) ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">رقم الهوية:</span>
                <span class="info-value"><?= htmlspecialchars($patient['id_number'] ?? 'غير محدد') ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">تاريخ الميلاد:</span>
                <span class="info-value"><?= $patient['birth_date'] ? date('Y-m-d', strtotime($patient['birth_date'])) : 'غير محدد' ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">رقم الهاتف:</span>
                <span class="info-value"><?= htmlspecialchars($patient['phone'] ?? 'غير محدد') ?></span>
            </div>
        </div>
        
        <div class="info-section">
            <h3>معلومات الوصفة</h3>
            <div class="info-row">
                <span class="info-label">رقم الوصفة:</span>
                <span class="info-value">#<?= $prescription['id'] ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">تاريخ الوصفة:</span>
                                                <span class="info-value"><?= $prescription['issue_date'] ? date('Y-m-d', strtotime($prescription['issue_date'])) : 'غير محدد' ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">تاريخ الانتهاء:</span>
                <span class="info-value">
                    <?php if ($prescription['expiry_date']): ?>
                        <?= date('Y-m-d', strtotime($prescription['expiry_date'])) ?>
                    <?php else: ?>
                        غير محدد
                    <?php endif; ?>
                </span>
            </div>
            <div class="info-row">
                <span class="info-label">الطبيب:</span>
                <span class="info-value"><?= htmlspecialchars($prescription['doctor_name'] ?? 'غير محدد') ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">الحالة:</span>
                <span class="info-value">
                    <span class="status-badge status-<?= $prescription['status'] ?>">
                        <?= Prescription::getStatusLabel($prescription['status']) ?>
                    </span>
                </span>
            </div>
        </div>
    </div>
    
    <!-- Expiry Warning -->
    <?php if ($prescription['expiry_date'] && strtotime($prescription['expiry_date']) < time()): ?>
        <div class="expiry-warning">
            <h5>⚠️ تنبيه</h5>
            <p>هذه الوصفة منتهية الصلاحية ولا يمكن استخدامها</p>
        </div>
    <?php endif; ?>
    
    <!-- Diagnosis Section -->
    <?php if (!empty($prescription['diagnosis'])): ?>
        <div class="diagnosis-section">
            <h4>التشخيص</h4>
            <div class="diagnosis-content">
                <?= nl2br(htmlspecialchars($prescription['diagnosis'])) ?>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Medications Section -->
    <?php if (!empty($medications)): ?>
        <div class="medications-section">
            <h3>الأدوية الموصوفة</h3>
            <table class="medications-table">
                <thead>
                    <tr>
                        <th>الدواء</th>
                        <th>الكمية</th>
                        <th>الجرعة</th>
                        <th>المدة</th>
                        <th>التعليمات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($medications as $medication): ?>
                        <tr>
                            <td>
                                <div class="medication-name"><?= htmlspecialchars($medication['medication_name']) ?></div>
                                <?php if (!empty($medication['generic_name'])): ?>
                                    <div class="generic-name"><?= htmlspecialchars($medication['generic_name']) ?></div>
                                <?php endif; ?>
                            </td>
                            <td><?= htmlspecialchars($medication['quantity']) ?> <?= htmlspecialchars($medication['unit']) ?></td>
                            <td><?= htmlspecialchars($medication['dosage']) ?> <?= htmlspecialchars($medication['dosage_unit'] ?? '') ?></td>
                            <td><?= htmlspecialchars($medication['duration']) ?> <?= htmlspecialchars($medication['duration_unit'] ?? '') ?></td>
                            <td>
                                <?php if (!empty($medication['instructions'])): ?>
                                    <?= nl2br(htmlspecialchars($medication['instructions'])) ?>
                                <?php else: ?>
                                    <span style="color: #999;">لا توجد تعليمات خاصة</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
    
    <!-- Special Instructions -->
    <?php if (!empty($prescription['special_instructions'])): ?>
        <div class="instructions-section">
            <h4>تعليمات خاصة</h4>
            <div class="instructions-content">
                <?= nl2br(htmlspecialchars($prescription['special_instructions'])) ?>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Notes -->
    <?php if (!empty($prescription['notes'])): ?>
        <div class="notes-section">
            <h4>ملاحظات</h4>
            <div class="notes-content">
                <?= nl2br(htmlspecialchars($prescription['notes'])) ?>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Footer -->
    <div class="footer">
        <div class="doctor-signature">
            <div>
                <div class="signature-box">توقيع المريض</div>
            </div>
            <div>
                <div class="signature-box">توقيع الطبيب</div>
            </div>
        </div>
        
        <div style="margin-top: 30px; text-align: center; color: #666; font-size: 14px;">
            <p>تم إنشاء هذه الوصفة في: <?= $prescription['created_at'] ? date('Y-m-d H:i', strtotime($prescription['created_at'])) : 'غير محدد' ?></p>
            <p>HealthKey - نظام إدارة الرعاية الصحية</p>
        </div>
    </div>
</body>
</html> 