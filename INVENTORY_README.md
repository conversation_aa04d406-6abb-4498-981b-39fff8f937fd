# ميزة إدارة المخزون - HealthKey

## نظرة عامة

تم إنشاء ميزة إدارة المخزون للصيادلة في نظام HealthKey. هذه الميزة تسمح للصيادلة بإدارة أدوية الصيدلية بشكل شامل وفعال.

## المميزات الرئيسية

### 1. عرض المخزون
- عرض جميع الأدوية في المخزون
- إحصائيات شاملة (إجمالي الأدوية، منخفضة المخزون، منتهية الصلاحية)
- قيمة إجمالية للمخزون

### 2. البحث والفلترة
- البحث في اسم الدواء أو الاسم العلمي
- فلترة حسب فئة الدواء
- عرض نتائج البحث بشكل منظم

### 3. إدارة الأدوية
- إضافة دواء جديد للمخزون
- تعديل معلومات الدواء
- حذف دواء من المخزون
- تحديث كمية الدواء

### 4. التنبيهات
- تنبيهات للأدوية منخفضة المخزون
- تنبيهات للأدوية منتهية الصلاحية
- تنبيهات للأدوية التي ستنتهي صلاحيتها قريباً

## الملفات المضافة

### النماذج (Models)
- `app/models/Inventory.php` - نموذج إدارة المخزون

### المتحكمات (Controllers)
- تم إضافة دوال إدارة المخزون في `app/controllers/PharmacistController.php`:
  - `inventory()` - عرض صفحة المخزون
  - `addInventory()` - إضافة دواء جديد
  - `editInventory($id)` - تعديل معلومات الدواء
  - `deleteInventory($id)` - حذف دواء
  - `updateQuantity()` - تحديث الكمية

### الصفحات (Views)
- `views/pharmacist/inventory.php` - صفحة إدارة المخزون الرئيسية
- `views/pharmacist/add_inventory.php` - صفحة إضافة دواء جديد
- `views/pharmacist/edit_inventory.php` - صفحة تعديل معلومات الدواء

### قاعدة البيانات
- جدول `inventory` يحتوي على:
  - معلومات الدواء الأساسية (الاسم، الاسم العلمي، الفئة)
  - معلومات الجرعة (الشكل، القوة)
  - معلومات المخزون (الكمية، السعر، مستوى إعادة الطلب)
  - معلومات الصلاحية (تاريخ الانتهاء، رقم الدفعة)
  - معلومات إضافية (الشركة المصنعة، الموقع، الوصف)

## المسارات (Routes)

تم إضافة المسارات التالية في `app/core/App.php`:
- `pharmacist/inventory` - صفحة إدارة المخزون
- `pharmacist/add-inventory` - إضافة دواء جديد
- `pharmacist/edit-inventory` - تعديل معلومات الدواء
- `pharmacist/delete-inventory` - حذف دواء
- `pharmacist/update-quantity` - تحديث الكمية

## كيفية الاستخدام

### 1. الوصول لصفحة المخزون
```
http://localhost/HealthKey/pharmacist/inventory
```

### 2. إضافة دواء جديد
```
http://localhost/HealthKey/pharmacist/add-inventory
```

### 3. تعديل دواء موجود
```
http://localhost/HealthKey/pharmacist/edit-inventory/{id}
```

## البيانات التجريبية

تم إضافة 5 أدوية تجريبية للمخزون:
1. باراسيتامول (مسكنات)
2. أموكسيسيلين (مضادات حيوية)
3. أوميبرازول (مضادات الحموضة)
4. فيتامين سي (فيتامينات)
5. إيبوبروفين (مسكنات)

## الأمان

- التحقق من تسجيل الدخول
- التحقق من نوع المستخدم (صيدلي فقط)
- حماية من SQL Injection
- التحقق من صحة البيانات المدخلة

## التطوير المستقبلي

- إضافة تقارير المخزون
- إضافة نظام تنبيهات تلقائي
- إضافة نظام طلب تلقائي للأدوية منخفضة المخزون
- إضافة نظام تتبع حركة المخزون
- إضافة نظام إدارة الموردين

## الاختبار

يمكن اختبار الميزة باستخدام:
```bash
php test_inventory_model.php
php test_inventory_page.php
```

## ملاحظات تقنية

- تم استخدام PDO للاتصال بقاعدة البيانات
- تم استخدام Bootstrap 5 للواجهة
- تم استخدام Bootstrap Icons للأيقونات
- تم تطبيق التصميم المتجاوب (Responsive Design)
- تم دعم اللغة العربية والاتجاه RTL 