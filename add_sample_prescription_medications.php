<?php
/**
 * إضافة بيانات تجريبية لجدول prescription_medications
 */

require_once 'config.php';
require_once 'app/core/Database.php';

echo "<h1>إضافة بيانات تجريبية لجدول prescription_medications</h1>";

try {
    $db = Database::getInstance();
    
    // إضافة بيانات تجريبية
    $sampleData = [
        [
            'prescription_id' => 1,
            'medication_id' => 1, // باراسيتامول
            'quantity' => 2,
            'dosage_instructions' => 'قرص واحد كل 6 ساعات عند الحاجة',
            'pharmacist_id' => 2,
            'dispensed_at' => '2024-01-15 10:30:00'
        ],
        [
            'prescription_id' => 1,
            'medication_id' => 2, // أموكسيسيلين
            'quantity' => 1,
            'dosage_instructions' => 'كبسولة واحدة كل 8 ساعات',
            'pharmacist_id' => 2,
            'dispensed_at' => '2024-01-15 10:30:00'
        ],
        [
            'prescription_id' => 2,
            'medication_id' => 3, // أوميبرازول
            'quantity' => 1,
            'dosage_instructions' => 'كبسولة واحدة قبل الإفطار',
            'pharmacist_id' => 2,
            'dispensed_at' => '2024-01-16 14:20:00'
        ],
        [
            'prescription_id' => 3,
            'medication_id' => 4, // فيتامين د
            'quantity' => 1,
            'dosage_instructions' => 'كبسولة واحدة يومياً',
            'pharmacist_id' => 2,
            'dispensed_at' => '2024-01-17 09:15:00'
        ],
        [
            'prescription_id' => 4,
            'medication_id' => 5, // إيبوبروفين
            'quantity' => 2,
            'dosage_instructions' => 'قرص واحد كل 8 ساعات عند الحاجة',
            'pharmacist_id' => 2,
            'dispensed_at' => '2024-01-18 16:45:00'
        ]
    ];
    
    $insertedCount = 0;
    foreach ($sampleData as $data) {
        try {
            $insertSql = "INSERT INTO prescription_medications (prescription_id, medication_id, quantity, dosage_instructions, pharmacist_id, dispensed_at) 
                          VALUES (?, ?, ?, ?, ?, ?)";
            
            $db->insert($insertSql, [
                $data['prescription_id'],
                $data['medication_id'],
                $data['quantity'],
                $data['dosage_instructions'],
                $data['pharmacist_id'],
                $data['dispensed_at']
            ]);
            $insertedCount++;
        } catch (Exception $e) {
            echo "⚠️ خطأ في إدراج السجل: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "✅ تم إدراج $insertedCount سجل بنجاح<br>";
    
    // التحقق من عدد السجلات
    $count = $db->selectOne("SELECT COUNT(*) as count FROM prescription_medications")['count'];
    echo "✅ إجمالي عدد السجلات في الجدول: $count<br>";
    
    // عرض البيانات المضافة
    echo "<h3>البيانات المضافة:</h3>";
    $medications = $db->select("
        SELECT pm.*, p.prescription_code, m.name as medication_name, u.first_name, u.last_name
        FROM prescription_medications pm
        LEFT JOIN prescriptions p ON pm.prescription_id = p.id
        LEFT JOIN medications m ON pm.medication_id = m.id
        LEFT JOIN users u ON pm.pharmacist_id = u.id
        ORDER BY pm.id DESC
        LIMIT 10
    ");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Prescription</th><th>Medication</th><th>Quantity</th><th>Pharmacist</th><th>Dispensed At</th></tr>";
    foreach ($medications as $med) {
        echo "<tr>";
        echo "<td>" . $med['id'] . "</td>";
        echo "<td>" . htmlspecialchars($med['prescription_code'] ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($med['medication_name'] ?? 'N/A') . "</td>";
        echo "<td>" . $med['quantity'] . "</td>";
        echo "<td>" . htmlspecialchars(($med['first_name'] ?? '') . ' ' . ($med['last_name'] ?? '')) . "</td>";
        echo "<td>" . $med['dispensed_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>✅ تم إضافة البيانات التجريبية بنجاح!</h2>";
    echo "<p>يمكنك الآن الوصول لصفحة التقارير:</p>";
    echo "<a href='index.php?url=pharmacist/reports' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>فتح صفحة التقارير</a>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في إضافة البيانات</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 