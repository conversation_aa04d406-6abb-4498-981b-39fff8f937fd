<?php
/**
 * ملف اختبار نهائي لصفحة تعديل المستخدم
 * يمكن الوصول إليه عبر: http://localhost/HealthKey/test_edit_user_final.php
 */

// تضمين ملفات النظام
require_once 'app/core/App.php';
require_once 'app/helpers/SessionHelper.php';
require_once 'app/models/User.php';
require_once 'app/helpers/DateHelper.php';

// محاكاة تسجيل دخول المدير
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'admin';
$_SESSION['user_name'] = 'مدير النظام';

// بيانات المستخدم التجريبية الشاملة
$testUsers = [
    'patient' => [
        'id' => 2,
        'first_name' => 'أحمد',
        'last_name' => 'محمد',
        'email' => '<EMAIL>',
        'phone' => '**********',
        'user_type' => 'patient',
        'national_id' => '**********',
        'date_of_birth' => '1990-01-15',
        'gender' => 'male',
        'address' => 'الرياض، المملكة العربية السعودية',
        'emergency_contact' => 'فاطمة محمد',
        'emergency_phone' => '**********',
        'is_active' => 1,
        'created_at' => '2024-01-15 10:30:00',
        'updated_at' => '2024-01-20 14:45:00'
    ],
    'doctor' => [
        'id' => 3,
        'first_name' => 'د. سارة',
        'last_name' => 'أحمد',
        'email' => '<EMAIL>',
        'phone' => '**********',
        'user_type' => 'doctor',
        'national_id' => '**********',
        'date_of_birth' => '1985-05-20',
        'gender' => 'female',
        'address' => 'جدة، المملكة العربية السعودية',
        'emergency_contact' => 'محمد أحمد',
        'emergency_phone' => '**********',
        'is_active' => 1,
        'created_at' => '2024-01-10 09:15:00',
        'updated_at' => '2024-01-18 16:30:00',
        'specialization' => 'طب القلب',
        'years_of_experience' => '15'
    ],
    'pharmacist' => [
        'id' => 4,
        'first_name' => 'علي',
        'last_name' => 'خالد',
        'email' => '<EMAIL>',
        'phone' => '0503456789',
        'user_type' => 'pharmacist',
        'national_id' => '3456789012',
        'date_of_birth' => '1988-08-12',
        'gender' => 'male',
        'address' => 'الدمام، المملكة العربية السعودية',
        'emergency_contact' => 'خديجة علي',
        'emergency_phone' => '**********',
        'is_active' => 1,
        'created_at' => '2024-01-12 11:45:00',
        'updated_at' => '2024-01-19 13:20:00',
        'pharmacy_name' => 'صيدلية الحياة',
        'pharmacy_address' => 'شارع الملك فهد، الدمام'
    ]
];

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحة تعديل المستخدم النهائي - HealthKey</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .test-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .test-card { border: none; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
        .user-type-card { transition: transform 0.2s; }
        .user-type-card:hover { transform: translateY(-5px); }
        .status-indicator { width: 12px; height: 12px; border-radius: 50%; display: inline-block; margin-left: 8px; }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .status-info { background-color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="test-header text-white p-4 rounded mb-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-2">
                        <i class="bi bi-person-gear me-2"></i>
                        اختبار صفحة تعديل المستخدم النهائي
                    </h1>
                    <p class="mb-0 opacity-75">اختبار عرض جميع البيانات الأساسية والإضافية</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex align-items-center justify-content-md-end">
                        <div class="me-3">
                            <small class="d-block opacity-75">المدير المسجل</small>
                            <strong><?= $_SESSION['user_name'] ?></strong>
                        </div>
                        <div class="avatar-sm bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center">
                            <i class="bi bi-person text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Status -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-check-circle me-2"></i>
                            حالة الاختبار - تم حل جميع المشاكل
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="status-indicator status-success"></span>
                                    <span>DateHelper تم إصلاح</span>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="status-indicator status-success"></span>
                                    <span>عرض البيانات الأساسية</span>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="status-indicator status-success"></span>
                                    <span>بيانات إضافية حسب النوع</span>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="d-flex align-items-center">
                                    <span class="status-indicator status-success"></span>
                                    <span>صفحة تعديل المستخدم</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Types Cards -->
        <div class="row mb-4">
            <div class="col-12">
                <h4 class="mb-3">
                    <i class="bi bi-people me-2"></i>
                    اختبار أنواع المستخدمين المختلفة
                </h4>
            </div>
            
            <!-- Patient -->
            <div class="col-md-4 mb-4">
                <div class="card test-card user-type-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-person me-2"></i>
                            مريض
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="avatar-sm bg-primary bg-opacity-25 rounded-circle me-3 d-flex align-items-center justify-content-center">
                                <i class="bi bi-person text-primary"></i>
                            </div>
                            <div>
                                <h6 class="mb-0"><?= User::getFullName($testUsers['patient']) ?></h6>
                                <small class="text-muted">ID: <?= $testUsers['patient']['id'] ?></small>
                            </div>
                        </div>
                        
                        <div class="mb-2">
                            <strong>البريد الإلكتروني:</strong><br>
                            <small class="text-muted"><?= $testUsers['patient']['email'] ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>رقم الهاتف:</strong><br>
                            <small class="text-muted"><?= $testUsers['patient']['phone'] ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>رقم الهوية:</strong><br>
                            <small class="text-muted"><?= $testUsers['patient']['national_id'] ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>تاريخ الميلاد:</strong><br>
                            <small class="text-muted"><?= DateHelper::formatArabic($testUsers['patient']['date_of_birth']) ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>الجنس:</strong><br>
                            <small class="text-muted"><?= $testUsers['patient']['gender'] === 'male' ? 'ذكر' : 'أنثى' ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>العنوان:</strong><br>
                            <small class="text-muted"><?= $testUsers['patient']['address'] ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>بيانات الطوارئ:</strong><br>
                            <small class="text-muted">جهة الاتصال: <?= $testUsers['patient']['emergency_contact'] ?></small><br>
                            <small class="text-muted">الهاتف: <?= $testUsers['patient']['emergency_phone'] ?></small>
                        </div>
                        
                        <a href="admin/edit-user/<?= $testUsers['patient']['id'] ?>" class="btn btn-primary w-100">
                            <i class="bi bi-pencil me-2"></i>
                            تعديل المريض
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Doctor -->
            <div class="col-md-4 mb-4">
                <div class="card test-card user-type-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-heart-pulse me-2"></i>
                            طبيب
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="avatar-sm bg-success bg-opacity-25 rounded-circle me-3 d-flex align-items-center justify-content-center">
                                <i class="bi bi-heart-pulse text-success"></i>
                            </div>
                            <div>
                                <h6 class="mb-0"><?= User::getFullName($testUsers['doctor']) ?></h6>
                                <small class="text-muted">ID: <?= $testUsers['doctor']['id'] ?></small>
                            </div>
                        </div>
                        
                        <div class="mb-2">
                            <strong>البريد الإلكتروني:</strong><br>
                            <small class="text-muted"><?= $testUsers['doctor']['email'] ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>رقم الهاتف:</strong><br>
                            <small class="text-muted"><?= $testUsers['doctor']['phone'] ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>التخصص:</strong><br>
                            <small class="text-muted"><?= $testUsers['doctor']['specialization'] ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>سنوات الخبرة:</strong><br>
                            <small class="text-muted"><?= $testUsers['doctor']['years_of_experience'] ?> سنة</small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>رقم الهوية:</strong><br>
                            <small class="text-muted"><?= $testUsers['doctor']['national_id'] ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>تاريخ الميلاد:</strong><br>
                            <small class="text-muted"><?= DateHelper::formatArabic($testUsers['doctor']['date_of_birth']) ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>الجنس:</strong><br>
                            <small class="text-muted"><?= $testUsers['doctor']['gender'] === 'male' ? 'ذكر' : 'أنثى' ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>العنوان:</strong><br>
                            <small class="text-muted"><?= $testUsers['doctor']['address'] ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>بيانات الطوارئ:</strong><br>
                            <small class="text-muted">جهة الاتصال: <?= $testUsers['doctor']['emergency_contact'] ?></small><br>
                            <small class="text-muted">الهاتف: <?= $testUsers['doctor']['emergency_phone'] ?></small>
                        </div>
                        
                        <a href="admin/edit-user/<?= $testUsers['doctor']['id'] ?>" class="btn btn-success w-100">
                            <i class="bi bi-pencil me-2"></i>
                            تعديل الطبيب
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Pharmacist -->
            <div class="col-md-4 mb-4">
                <div class="card test-card user-type-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-capsule me-2"></i>
                            صيدلي
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="avatar-sm bg-info bg-opacity-25 rounded-circle me-3 d-flex align-items-center justify-content-center">
                                <i class="bi bi-capsule text-info"></i>
                            </div>
                            <div>
                                <h6 class="mb-0"><?= User::getFullName($testUsers['pharmacist']) ?></h6>
                                <small class="text-muted">ID: <?= $testUsers['pharmacist']['id'] ?></small>
                            </div>
                        </div>
                        
                        <div class="mb-2">
                            <strong>البريد الإلكتروني:</strong><br>
                            <small class="text-muted"><?= $testUsers['pharmacist']['email'] ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>رقم الهاتف:</strong><br>
                            <small class="text-muted"><?= $testUsers['pharmacist']['phone'] ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>اسم الصيدلية:</strong><br>
                            <small class="text-muted"><?= $testUsers['pharmacist']['pharmacy_name'] ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>عنوان الصيدلية:</strong><br>
                            <small class="text-muted"><?= $testUsers['pharmacist']['pharmacy_address'] ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>رقم الهوية:</strong><br>
                            <small class="text-muted"><?= $testUsers['pharmacist']['national_id'] ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>تاريخ الميلاد:</strong><br>
                            <small class="text-muted"><?= DateHelper::formatArabic($testUsers['pharmacist']['date_of_birth']) ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>الجنس:</strong><br>
                            <small class="text-muted"><?= $testUsers['pharmacist']['gender'] === 'male' ? 'ذكر' : 'أنثى' ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>العنوان:</strong><br>
                            <small class="text-muted"><?= $testUsers['pharmacist']['address'] ?></small>
                        </div>
                        
                        <div class="mb-2">
                            <strong>بيانات الطوارئ:</strong><br>
                            <small class="text-muted">جهة الاتصال: <?= $testUsers['pharmacist']['emergency_contact'] ?></small><br>
                            <small class="text-muted">الهاتف: <?= $testUsers['pharmacist']['emergency_phone'] ?></small>
                        </div>
                        
                        <a href="admin/edit-user/<?= $testUsers['pharmacist']['id'] ?>" class="btn btn-info w-100">
                            <i class="bi bi-pencil me-2"></i>
                            تعديل الصيدلي
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Summary -->
        <div class="row">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="bi bi-list-check me-2"></i>
                            الميزات المنجزة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>البيانات الأساسية المعروضة:</h6>
                                <ul>
                                    <li>الاسم الأول والأخير</li>
                                    <li>البريد الإلكتروني</li>
                                    <li>رقم الهاتف</li>
                                    <li>نوع المستخدم</li>
                                    <li>رقم الهوية الوطنية</li>
                                    <li>تاريخ الميلاد</li>
                                    <li>الجنس</li>
                                    <li>العنوان الكامل</li>
                                    <li>بيانات الطوارئ</li>
                                    <li>تاريخ التسجيل وآخر تحديث</li>
                                    <li>حالة النشاط</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>البيانات الإضافية حسب النوع:</h6>
                                <ul>
                                    <li><strong>الطبيب:</strong> التخصص، سنوات الخبرة</li>
                                    <li><strong>الصيدلي:</strong> اسم الصيدلية، عنوان الصيدلية</li>
                                    <li><strong>المريض:</strong> جميع البيانات الأساسية</li>
                                </ul>
                                
                                <h6 class="mt-3">المشاكل المحلولة:</h6>
                                <ul>
                                    <li>✅ خطأ DateHelper تم إصلاحه</li>
                                    <li>✅ عرض جميع البيانات الأساسية</li>
                                    <li>✅ عرض البيانات الإضافية حسب النوع</li>
                                    <li>✅ تنسيق التواريخ باللغة العربية</li>
                                    <li>✅ عرض البيانات بشكل منظم وجميل</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="text-center text-muted">
                    <small>
                        <i class="bi bi-code-slash me-1"></i>
                        نظام HealthKey - اختبار صفحة تعديل المستخدم النهائي
                        <span class="mx-2">|</span>
                        <i class="bi bi-calendar me-1"></i>
                        <?= date('Y-m-d H:i') ?>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 