<?php

/**
 * متحكم الصفحات العامة
 * يعالج الصفحات العامة والثابتة مثل الصفحة الرئيسية وصفحة "عن المشروع"
 */
class PagesController extends Controller
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * الصفحة الرئيسية
     */
    public function index()
    {
        // إذا كان المستخدم مسجل دخول، إعادة توجيه لصفحته
        if ($this->isLoggedIn()) {
            $this->redirectToDashboard();
            return;
        }

        $data = [
            'title' => 'الصفحة الرئيسية',
            'stats' => $this->getSystemStats()
        ];

        $this->view('pages/index', $data);
    }

    /**
     * صفحة عن المشروع
     */
    public function about()
    {
        $data = [
            'title' => 'عن النظام',
            'features' => $this->getSystemFeatures(),
            'team' => $this->getTeamMembers()
        ];

        $this->view('pages/about', $data);
    }

    /**
     * الحصول على إحصائيات النظام
     */
    private function getSystemStats()
    {
        try {
            $stats = [];

            // عدد المستخدمين
            $userStats = $this->db->select(
                "SELECT user_type, COUNT(*) as count 
                 FROM users 
                 WHERE is_active = 1 
                 GROUP BY user_type"
            );

            $stats['users'] = [
                'total' => 0,
                'patients' => 0,
                'doctors' => 0,
                'pharmacists' => 0,
                'admins' => 0
            ];

            foreach ($userStats as $stat) {
                $stats['users'][$stat['user_type'] . 's'] = (int)$stat['count'];
                $stats['users']['total'] += (int)$stat['count'];
            }

            // عدد الوصفات
            $prescriptionCount = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM prescriptions"
            );
            $stats['prescriptions'] = (int)($prescriptionCount['count'] ?? 0);

            // عدد المواعيد
            $appointmentCount = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM appointments"
            );
            $stats['appointments'] = (int)($appointmentCount['count'] ?? 0);

            // عدد السجلات الطبية
            $recordCount = $this->db->selectOne(
                "SELECT COUNT(*) as count FROM medical_records"
            );
            $stats['medical_records'] = (int)($recordCount['count'] ?? 0);

            return $stats;

        } catch (Exception $e) {
            // في حالة عدم وجود قاعدة البيانات أو الجداول
            return [
                'users' => [
                    'total' => 0,
                    'patients' => 0,
                    'doctors' => 0,
                    'pharmacists' => 0,
                    'admins' => 0
                ],
                'prescriptions' => 0,
                'appointments' => 0,
                'medical_records' => 0
            ];
        }
    }

    /**
     * الحصول على مميزات النظام
     */
    private function getSystemFeatures()
    {
        return [
            [
                'icon' => 'bi-person-heart',
                'title' => 'إدارة المرضى',
                'description' => 'نظام شامل لإدارة بيانات المرضى وسجلاتهم الطبية بشكل آمن ومنظم'
            ],
            [
                'icon' => 'bi-clipboard2-pulse',
                'title' => 'السجلات الطبية',
                'description' => 'حفظ وإدارة السجلات الطبية الإلكترونية مع إمكانية الوصول السريع والآمن'
            ],
            [
                'icon' => 'bi-prescription2',
                'title' => 'الوصفات الطبية',
                'description' => 'إنشاء وإدارة الوصفات الطبية الإلكترونية مع التحقق من التفاعلات الدوائية'
            ],
            [
                'icon' => 'bi-calendar-check',
                'title' => 'إدارة المواعيد',
                'description' => 'نظام متقدم لجدولة وإدارة المواعيد بين الأطباء والمرضى'
            ],
            [
                'icon' => 'bi-shield-check',
                'title' => 'الأمان والخصوصية',
                'description' => 'حماية عالية للبيانات الطبية مع التشفير والتحكم في الصلاحيات'
            ],
            [
                'icon' => 'bi-graph-up',
                'title' => 'التقارير والإحصائيات',
                'description' => 'تقارير شاملة وإحصائيات مفصلة لمساعدة في اتخاذ القرارات الطبية'
            ]
        ];
    }

    /**
     * الحصول على أعضاء الفريق
     */
    private function getTeamMembers()
    {
        return [
            [
                'name' => 'فريق التطوير',
                'role' => 'مطوري النظام',
                'description' => 'فريق متخصص في تطوير الأنظمة الطبية الإلكترونية',
                'image' => 'team-dev.jpg'
            ],
            [
                'name' => 'الاستشاريون الطبيون',
                'role' => 'المستشارون',
                'description' => 'أطباء متخصصون في مراجعة وتطوير النظام',
                'image' => 'team-medical.jpg'
            ],
            [
                'name' => 'فريق الأمان',
                'role' => 'أمان المعلومات',
                'description' => 'متخصصون في حماية البيانات الطبية الحساسة',
                'image' => 'team-security.jpg'
            ]
        ];
    }

    /**
     * إعادة التوجيه لصفحة المستخدم المناسبة
     */
    private function redirectToDashboard()
    {
        if (!$this->currentUser) {
            return;
        }

        $userType = $this->currentUser['user_type'];
        $this->redirect($userType . '/dashboard');
    }

    /**
     * صفحة الخطأ 403 (ممنوع)
     */
    public function forbidden()
    {
        http_response_code(403);
        
        $data = [
            'title' => 'الوصول مرفوض',
            'error_code' => '403',
            'error_title' => 'الوصول مرفوض',
            'error_message' => 'ليس لديك صلاحية للوصول إلى هذه الصفحة.'
        ];

        $this->view('errors/403', $data, null);
    }

    /**
     * صفحة الخطأ 500 (خطأ في الخادم)
     */
    public function serverError()
    {
        http_response_code(500);
        
        $data = [
            'title' => 'خطأ في الخادم',
            'error_code' => '500',
            'error_title' => 'خطأ في الخادم',
            'error_message' => 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى لاحقاً.'
        ];

        $this->view('errors/500', $data, null);
    }

    /**
     * صفحة قيد الصيانة
     */
    public function maintenance()
    {
        http_response_code(503);
        
        $data = [
            'title' => 'قيد الصيانة',
            'maintenance_message' => 'النظام قيد الصيانة حالياً. سنعود قريباً.',
            'estimated_time' => 'الوقت المتوقع للانتهاء: ساعة واحدة'
        ];

        $this->view('pages/maintenance', $data, null);
    }

    /**
     * البحث العام في النظام
     */
    public function search()
    {
        $query = App::get('q', '');
        $results = [];

        if (!empty($query) && $this->isLoggedIn()) {
            $results = $this->performSearch($query);
        }

        $data = [
            'title' => 'البحث',
            'query' => $query,
            'results' => $results
        ];

        $this->view('pages/search', $data);
    }

    /**
     * تنفيذ البحث
     */
    private function performSearch($query)
    {
        $results = [];
        $userType = $this->currentUser['user_type'];

        try {
            // البحث حسب نوع المستخدم
            switch ($userType) {
                case 'doctor':
                    $results = $this->searchForDoctor($query);
                    break;
                case 'pharmacist':
                    $results = $this->searchForPharmacist($query);
                    break;
                case 'patient':
                    $results = $this->searchForPatient($query);
                    break;
                case 'admin':
                    $results = $this->searchForAdmin($query);
                    break;
            }
        } catch (Exception $e) {
            // تسجيل الخطأ
            error_log("خطأ في البحث: " . $e->getMessage());
        }

        return $results;
    }

    /**
     * البحث للطبيب
     */
    private function searchForDoctor($query)
    {
        return $this->db->select(
            "SELECT CONCAT(first_name, ' ', last_name) as name, 'patient' as type, id
             FROM users 
             WHERE user_type = 'patient' 
             AND (first_name LIKE :query OR last_name LIKE :query OR email LIKE :query)
             AND is_active = 1
             LIMIT 10",
            ['query' => "%$query%"]
        );
    }

    /**
     * البحث للصيدلي
     */
    private function searchForPharmacist($query)
    {
        return $this->db->select(
            "SELECT prescription_code as name, 'prescription' as type, id
             FROM prescriptions 
             WHERE prescription_code LIKE :query
             AND status = 'active'
             LIMIT 10",
            ['query' => "%$query%"]
        );
    }

    /**
     * البحث للمريض
     */
    private function searchForPatient($query)
    {
        return $this->db->select(
            "SELECT CONCAT('وصفة طبية - ', prescription_code) as name, 'prescription' as type, id
             FROM prescriptions 
             WHERE patient_id = :patient_id
             AND prescription_code LIKE :query
             LIMIT 10",
            [
                'patient_id' => $this->currentUser['id'],
                'query' => "%$query%"
            ]
        );
    }

    /**
     * البحث للمدير
     */
    private function searchForAdmin($query)
    {
        return $this->db->select(
            "SELECT CONCAT(first_name, ' ', last_name, ' (', user_type, ')') as name, 'user' as type, id
             FROM users 
             WHERE (first_name LIKE :query OR last_name LIKE :query OR email LIKE :query)
             AND is_active = 1
             LIMIT 10",
            ['query' => "%$query%"]
        );
    }
}
