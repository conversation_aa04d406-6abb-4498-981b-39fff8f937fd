<?php
// اختبار صفحات الصيدلي
echo "<h1>اختبار صفحات الصيدلي</h1>";

// تعريف الثوابت المطلوبة
define('DB_HOST', 'localhost');
define('DB_NAME', 'healthkey');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');
define('APP_DEBUG', true);
define('APP_URL', 'http://localhost/HealthKey');

// محاكاة جلسة المستخدم
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'pharmacist';
$_SESSION['user'] = [
    'id' => 1,
    'first_name' => 'أحمد',
    'last_name' => 'محمد',
    'email' => '<EMAIL>',
    'user_type' => 'pharmacist'
];

try {
    require_once 'app/core/Database.php';
    require_once 'app/core/Controller.php';
    require_once 'app/core/App.php';
    require_once 'app/controllers/PharmacistController.php';
    
    echo "<h2>✅ تم تحميل الملفات بنجاح</h2>";
    
    // اختبار إنشاء متحكم الصيدلي
    $controller = new PharmacistController();
    echo "<h2>✅ تم إنشاء متحكم الصيدلي بنجاح</h2>";
    
    // اختبار دالة getInventoryStats
    $stats = $controller->getInventoryStats();
    echo "<h3>إحصائيات المخزون:</h3>";
    echo "<pre>" . print_r($stats, true) . "</pre>";
    
    // اختبار دالة getInventoryItems
    $items = $controller->getInventoryItems();
    echo "<h3>عناصر المخزون:</h3>";
    echo "<p>عدد العناصر: " . count($items) . "</p>";
    
    // اختبار دالة getInventoryCategories
    $categories = $controller->getInventoryCategories();
    echo "<h3>فئات المخزون:</h3>";
    echo "<pre>" . print_r($categories, true) . "</pre>";
    
    // اختبار دالة getSuppliers
    $suppliers = $controller->getSuppliers();
    echo "<h3>الموردين:</h3>";
    echo "<p>عدد الموردين: " . count($suppliers) . "</p>";
    
    echo "<h2>✅ جميع الاختبارات نجحت!</h2>";
    
    // اختبار عرض الصفحات
    echo "<h3>اختبار عرض الصفحات:</h3>";
    echo "<ul>";
    echo "<li><a href='http://localhost:8000/index.php?url=pharmacist/inventory' target='_blank'>صفحة إدارة المخزون</a></li>";
    echo "<li><a href='http://localhost:8000/index.php?url=pharmacist/add-inventory' target='_blank'>صفحة إضافة دواء جديد</a></li>";
    echo "<li><a href='http://localhost:8000/index.php?url=pharmacist/suppliers' target='_blank'>صفحة إدارة الموردين</a></li>";
    echo "<li><a href='http://localhost:8000/index.php?url=pharmacist/add-supplier' target='_blank'>صفحة إضافة مورد جديد</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في الاختبار:</h2>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "<p>الملف: " . $e->getFile() . "</p>";
    echo "<p>السطر: " . $e->getLine() . "</p>";
}
?> 