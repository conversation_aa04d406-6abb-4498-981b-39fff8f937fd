<?php
// اختبار صفحات إدارة المخزون
echo "<h1>اختبار صفحات إدارة المخزون</h1>";

// تعريف ثوابت قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'healthkey');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');
define('APP_DEBUG', true);
define('APP_URL', 'http://localhost/HealthKey');

// اختبار إنشاء متحكم الصيدلي
try {
    require_once 'app/core/Database.php';
    require_once 'app/core/Controller.php';
    require_once 'app/core/App.php';
    require_once 'app/controllers/PharmacistController.php';
    
    echo "<h2>✅ تم تحميل الملفات بنجاح</h2>";
    
    // اختبار إنشاء متحكم الصيدلي
    $controller = new PharmacistController();
    echo "<h2>✅ تم إنشاء متحكم الصيدلي بنجاح</h2>";
    
    // اختبار دالة getInventoryStats
    $stats = $controller->getInventoryStats();
    echo "<h3>إحصائيات المخزون:</h3>";
    echo "<pre>" . print_r($stats, true) . "</pre>";
    
    // اختبار دالة getInventoryItems
    $items = $controller->getInventoryItems();
    echo "<h3>عناصر المخزون:</h3>";
    echo "<p>عدد العناصر: " . count($items) . "</p>";
    
    // اختبار دالة getInventoryCategories
    $categories = $controller->getInventoryCategories();
    echo "<h3>فئات المخزون:</h3>";
    echo "<pre>" . print_r($categories, true) . "</pre>";
    
    // اختبار دالة getSuppliers
    $suppliers = $controller->getSuppliers();
    echo "<h3>الموردين:</h3>";
    echo "<p>عدد الموردين: " . count($suppliers) . "</p>";
    
    echo "<h2>✅ جميع الاختبارات نجحت!</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في الاختبار:</h2>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "<p>الملف: " . $e->getFile() . "</p>";
    echo "<p>السطر: " . $e->getLine() . "</p>";
}
?> 