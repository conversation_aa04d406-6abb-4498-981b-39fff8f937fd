<?php

/**
 * نموذج إدارة الأدوية
 */
class Medication
{
    private $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * الحصول على جميع الأدوية
     */
    public function getAll($limit = null, $offset = 0)
    {
        $sql = "SELECT * FROM medications ORDER BY name ASC";
        
        if ($limit) {
            $sql .= " LIMIT $limit OFFSET $offset";
        }
        
        return $this->db->select($sql);
    }

    /**
     * الحصول على دواء بواسطة المعرف
     */
    public function findById($id)
    {
        $sql = "SELECT * FROM medications WHERE id = ?";
        return $this->db->selectOne($sql, [$id]);
    }

    /**
     * البحث في الأدوية
     */
    public function search($query, $category = '', $status = '')
    {
        $sql = "SELECT * FROM medications WHERE 1=1";
        $params = [];

        if (!empty($query)) {
            $sql .= " AND (name LIKE ? OR generic_name LIKE ? OR description LIKE ?)";
            $searchTerm = "%$query%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        if (!empty($category)) {
            $sql .= " AND category = ?";
            $params[] = $category;
        }

        if (!empty($status)) {
            $sql .= " AND status = ?";
            $params[] = $status;
        }

        $sql .= " ORDER BY name ASC";
        
        return $this->db->select($sql, $params);
    }

    /**
     * إضافة دواء جديد
     */
    public function create($data)
    {
        $sql = "INSERT INTO medications (name, generic_name, category, dosage_form, 
                strength, description, side_effects, contraindications, 
                storage_conditions, prescription_required, status, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $params = [
            $data['name'],
            $data['generic_name'],
            $data['category'],
            $data['dosage_form'],
            $data['strength'],
            $data['description'],
            $data['side_effects'] ?? '',
            $data['contraindications'] ?? '',
            $data['storage_conditions'] ?? '',
            $data['prescription_required'] ?? 0,
            $data['status'] ?? 'active'
        ];

        return $this->db->insert($sql, $params);
    }

    /**
     * تحديث معلومات الدواء
     */
    public function update($id, $data)
    {
        $sql = "UPDATE medications SET 
                name = ?, generic_name = ?, category = ?, dosage_form = ?, 
                strength = ?, description = ?, side_effects = ?, contraindications = ?, 
                storage_conditions = ?, prescription_required = ?, status = ?, 
                updated_at = NOW() 
                WHERE id = ?";
        
        $params = [
            $data['name'],
            $data['generic_name'],
            $data['category'],
            $data['dosage_form'],
            $data['strength'],
            $data['description'],
            $data['side_effects'] ?? '',
            $data['contraindications'] ?? '',
            $data['storage_conditions'] ?? '',
            $data['prescription_required'] ?? 0,
            $data['status'] ?? 'active',
            $id
        ];

        return $this->db->update($sql, $params);
    }

    /**
     * حذف دواء
     */
    public function delete($id)
    {
        $sql = "DELETE FROM medications WHERE id = ?";
        return $this->db->delete($sql, [$id]);
    }

    /**
     * الحصول على الأدوية النشطة
     */
    public function getActive($limit = 10)
    {
        $sql = "SELECT * FROM medications WHERE status = 'active' ORDER BY name ASC";
        
        if ($limit) {
            $sql .= " LIMIT $limit";
        }
        
        return $this->db->select($sql);
    }

    /**
     * الحصول على إحصائيات الأدوية
     */
    public function getStats()
    {
        $stats = [];

        // إجمالي عدد الأدوية
        $sql = "SELECT COUNT(*) as total FROM medications";
        $result = $this->db->selectOne($sql);
        $stats['total_medications'] = $result['total'];

        // الأدوية النشطة
        $sql = "SELECT COUNT(*) as active FROM medications WHERE status = 'active'";
        $result = $this->db->selectOne($sql);
        $stats['active_medications'] = $result['active'];

        // الأدوية غير النشطة
        $sql = "SELECT COUNT(*) as inactive FROM medications WHERE status = 'inactive'";
        $result = $this->db->selectOne($sql);
        $stats['inactive_medications'] = $result['inactive'];

        // الأدوية التي تحتاج وصفة طبية
        $sql = "SELECT COUNT(*) as prescription_required FROM medications WHERE prescription_required = 1";
        $result = $this->db->selectOne($sql);
        $stats['prescription_required'] = $result['prescription_required'];

        // الأدوية التي لا تحتاج وصفة طبية
        $sql = "SELECT COUNT(*) as otc FROM medications WHERE prescription_required = 0";
        $result = $this->db->selectOne($sql);
        $stats['otc_medications'] = $result['otc'];

        // الأدوية الجديدة هذا الشهر
        $sql = "SELECT COUNT(*) as new_this_month FROM medications 
                WHERE MONTH(created_at) = MONTH(CURDATE()) 
                AND YEAR(created_at) = YEAR(CURDATE())";
        $result = $this->db->selectOne($sql);
        $stats['new_this_month'] = $result['new_this_month'];

        return $stats;
    }

    /**
     * الحصول على فئات الأدوية
     */
    public function getCategories()
    {
        $sql = "SELECT DISTINCT category FROM medications WHERE category IS NOT NULL ORDER BY category";
        return $this->db->select($sql);
    }

    /**
     * الحصول على أشكال الجرعات
     */
    public function getDosageForms()
    {
        $sql = "SELECT DISTINCT dosage_form FROM medications WHERE dosage_form IS NOT NULL ORDER BY dosage_form";
        return $this->db->select($sql);
    }

    /**
     * الحصول على الأدوية حسب الفئة
     */
    public function getByCategory($category, $limit = 10)
    {
        $sql = "SELECT * FROM medications WHERE category = ? AND status = 'active' ORDER BY name ASC";
        
        if ($limit) {
            $sql .= " LIMIT $limit";
        }
        
        return $this->db->select($sql, [$category]);
    }

    /**
     * الحصول على الأدوية التي تحتاج وصفة طبية
     */
    public function getPrescriptionRequired($limit = 10)
    {
        $sql = "SELECT * FROM medications WHERE prescription_required = 1 AND status = 'active' ORDER BY name ASC";
        
        if ($limit) {
            $sql .= " LIMIT $limit";
        }
        
        return $this->db->select($sql);
    }

    /**
     * الحصول على الأدوية التي لا تحتاج وصفة طبية
     */
    public function getOverTheCounter($limit = 10)
    {
        $sql = "SELECT * FROM medications WHERE prescription_required = 0 AND status = 'active' ORDER BY name ASC";
        
        if ($limit) {
            $sql .= " LIMIT $limit";
        }
        
        return $this->db->select($sql);
    }

    /**
     * البحث في الأدوية حسب القوة
     */
    public function searchByStrength($strength)
    {
        $sql = "SELECT * FROM medications WHERE strength LIKE ? AND status = 'active' ORDER BY name ASC";
        return $this->db->select($sql, ["%$strength%"]);
    }

    /**
     * الحصول على الأدوية الشائعة
     */
    public function getPopularMedications($limit = 10)
    {
        // مؤقتاً نعيد الأدوية النشطة فقط حتى يتم إنشاء جدول الوصفات الطبية
        $sql = "SELECT m.*, 0 as prescription_count 
                FROM medications m 
                WHERE m.status = 'active' 
                ORDER BY m.name ASC 
                LIMIT $limit";
        
        return $this->db->select($sql);
    }

    /**
     * التحقق من تفاعلات الأدوية
     */
    public function checkDrugInteractions($medicationId, $otherMedications)
    {
        // مؤقتاً نعيد مصفوفة فارغة حتى يتم إنشاء جدول تفاعلات الأدوية
        // في التطبيق الحقيقي، ستكون أكثر تعقيداً
        return [];
    }
} 