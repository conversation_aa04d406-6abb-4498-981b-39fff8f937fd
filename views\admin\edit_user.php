<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-person-gear me-2"></i>
            تعديل المستخدم
        </h1>
        <p class="text-muted">تعديل بيانات المستخدم: <?= User::getFullName($user) ?></p>
    </div>
    <div>
        <a href="<?= App::url('admin/users') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right"></i>
            العودة لقائمة المستخدمين
        </a>
    </div>
</div>

<!-- User Info Card -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    معلومات المستخدم
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="avatar-lg bg-light rounded-circle me-3 d-flex align-items-center justify-content-center">
                        <i class="bi bi-person text-primary fs-1"></i>
                    </div>
                    <div>
                        <h6 class="mb-0"><?= User::getFullName($user) ?></h6>
                        <small class="text-muted">ID: <?= $user['id'] ?></small>
                        <br>
                        <span class="badge bg-<?= User::getTypeColor($user['user_type']) ?>">
                            <?= User::getTypeLabel($user['user_type']) ?>
                        </span>
                    </div>
                </div>
                
                <div class="mb-2">
                    <strong>البريد الإلكتروني:</strong>
                    <br>
                    <small class="text-muted"><?= htmlspecialchars($user['email']) ?></small>
                </div>
                
                <div class="mb-2">
                    <strong>تاريخ التسجيل:</strong>
                    <br>
                    <small class="text-muted"><?= DateHelper::formatArabic($user['created_at']) ?></small>
                </div>
                
                <div class="mb-2">
                    <strong>آخر تحديث:</strong>
                    <br>
                    <small class="text-muted"><?= DateHelper::formatArabic($user['updated_at'] ?? $user['created_at']) ?></small>
                </div>
                
                <div class="mb-2">
                    <strong>الحالة:</strong>
                    <br>
                    <span class="badge bg-<?= $user['is_active'] ? 'success' : 'secondary' ?>">
                        <?= $user['is_active'] ? 'نشط' : 'غير نشط' ?>
                    </span>
                </div>
                
                <?php if (!empty($user['phone'])): ?>
                <div class="mb-2">
                    <strong>رقم الهاتف:</strong>
                    <br>
                    <small class="text-muted"><?= htmlspecialchars($user['phone']) ?></small>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($user['national_id'])): ?>
                <div class="mb-2">
                    <strong>رقم الهوية الوطنية:</strong>
                    <br>
                    <small class="text-muted"><?= htmlspecialchars($user['national_id']) ?></small>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($user['date_of_birth'])): ?>
                <div class="mb-2">
                    <strong>تاريخ الميلاد:</strong>
                    <br>
                    <small class="text-muted"><?= DateHelper::formatArabic($user['date_of_birth']) ?></small>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($user['gender'])): ?>
                <div class="mb-2">
                    <strong>الجنس:</strong>
                    <br>
                    <small class="text-muted"><?= $user['gender'] === 'male' ? 'ذكر' : 'أنثى' ?></small>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($user['address'])): ?>
                <div class="mb-2">
                    <strong>العنوان:</strong>
                    <br>
                    <small class="text-muted"><?= htmlspecialchars($user['address']) ?></small>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($user['emergency_contact']) || !empty($user['emergency_phone'])): ?>
                <div class="mb-2">
                    <strong>بيانات الطوارئ:</strong>
                    <br>
                    <?php if (!empty($user['emergency_contact'])): ?>
                        <small class="text-muted">جهة الاتصال: <?= htmlspecialchars($user['emergency_contact']) ?></small><br>
                    <?php endif; ?>
                    <?php if (!empty($user['emergency_phone'])): ?>
                        <small class="text-muted">الهاتف: <?= htmlspecialchars($user['emergency_phone']) ?></small>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                
                <!-- معلومات إضافية حسب نوع المستخدم -->
                <?php if ($user['user_type'] === 'doctor'): ?>
                <div class="mb-2">
                    <strong>معلومات الطبيب:</strong>
                    <br>
                    <small class="text-muted">التخصص: <?= htmlspecialchars($user['specialization'] ?? 'غير محدد') ?></small><br>
                    <small class="text-muted">سنوات الخبرة: <?= htmlspecialchars($user['years_of_experience'] ?? 'غير محدد') ?></small>
                </div>
                <?php endif; ?>
                
                <?php if ($user['user_type'] === 'pharmacist'): ?>
                <div class="mb-2">
                    <strong>معلومات الصيدلي:</strong>
                    <br>
                    <small class="text-muted">اسم الصيدلية: <?= htmlspecialchars($user['pharmacy_name'] ?? 'غير محدد') ?></small><br>
                    <small class="text-muted">عنوان الصيدلية: <?= htmlspecialchars($user['pharmacy_address'] ?? 'غير محدد') ?></small>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-pencil-square me-2"></i>
                    تعديل البيانات
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= App::url('admin/edit-user/' . $user['id']) ?>" id="editUserForm">
                    <div class="row">
                        <!-- البيانات الأساسية -->
                        <div class="col-md-6">
                            <h6 class="mb-3 text-primary">
                                <i class="bi bi-person me-2"></i>
                                البيانات الأساسية
                            </h6>
                            
                            <div class="mb-3">
                                <label for="first_name" class="form-label">الاسم الأول <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="first_name" name="first_name" value="<?= htmlspecialchars($user['first_name']) ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="last_name" class="form-label">الاسم الأخير <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="last_name" name="last_name" value="<?= htmlspecialchars($user['last_name']) ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" value="<?= htmlspecialchars($user['email']) ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" value="<?= htmlspecialchars($user['phone'] ?? '') ?>">
                            </div>
                        </div>
                        
                        <!-- البيانات الإضافية -->
                        <div class="col-md-6">
                            <h6 class="mb-3 text-primary">
                                <i class="bi bi-card-text me-2"></i>
                                البيانات الإضافية
                            </h6>
                            
                            <div class="mb-3">
                                <label for="user_type" class="form-label">نوع المستخدم <span class="text-danger">*</span></label>
                                <select class="form-select" id="user_type" name="user_type" required>
                                    <option value="">اختر نوع المستخدم</option>
                                    <option value="patient" <?= ($user['user_type'] === 'patient') ? 'selected' : '' ?>>مريض</option>
                                    <option value="doctor" <?= ($user['user_type'] === 'doctor') ? 'selected' : '' ?>>طبيب</option>
                                    <option value="pharmacist" <?= ($user['user_type'] === 'pharmacist') ? 'selected' : '' ?>>صيدلي</option>
                                    <option value="admin" <?= ($user['user_type'] === 'admin') ? 'selected' : '' ?>>مدير</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="national_id" class="form-label">رقم الهوية الوطنية</label>
                                <input type="text" class="form-control" id="national_id" name="national_id" value="<?= htmlspecialchars($user['national_id'] ?? '') ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="date_of_birth" class="form-label">تاريخ الميلاد</label>
                                <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" value="<?= $user['date_of_birth'] ?? '' ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="gender" class="form-label">الجنس</label>
                                <select class="form-select" id="gender" name="gender">
                                    <option value="">اختر الجنس</option>
                                    <option value="male" <?= ($user['gender'] === 'male') ? 'selected' : '' ?>>ذكر</option>
                                    <option value="female" <?= ($user['gender'] === 'female') ? 'selected' : '' ?>>أنثى</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- العنوان -->
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3"><?= htmlspecialchars($user['address'] ?? '') ?></textarea>
                    </div>
                    
                    <!-- بيانات الطوارئ -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="emergency_contact" class="form-label">جهة الاتصال في الطوارئ</label>
                                <input type="text" class="form-control" id="emergency_contact" name="emergency_contact" value="<?= htmlspecialchars($user['emergency_contact'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="emergency_phone" class="form-label">هاتف الطوارئ</label>
                                <input type="tel" class="form-control" id="emergency_phone" name="emergency_phone" value="<?= htmlspecialchars($user['emergency_phone'] ?? '') ?>">
                            </div>
                        </div>
                    </div>
                    
                    <!-- حالة النشاط -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                   <?= ($user['is_active'] == 1) ? 'checked' : '' ?>>
                            <label class="form-check-label" for="is_active">
                                المستخدم نشط
                            </label>
                        </div>
                    </div>
                    
                    <!-- أزرار الإجراءات -->
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-2"></i>
                                حفظ التغييرات
                            </button>
                            <a href="<?= App::url('admin/users') ?>" class="btn btn-outline-secondary ms-2">
                                <i class="bi bi-x-circle me-2"></i>
                                إلغاء
                            </a>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-warning" onclick="resetPassword()">
                                <i class="bi bi-key me-2"></i>
                                إعادة تعيين كلمة المرور
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Additional Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gear me-2"></i>
                    إجراءات إضافية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-info w-100" onclick="viewUserActivity()">
                            <i class="bi bi-activity me-2"></i>
                            عرض النشاط
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-success w-100" onclick="sendNotification()">
                            <i class="bi bi-bell me-2"></i>
                            إرسال إشعار
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-warning w-100" onclick="exportUserData()">
                            <i class="bi bi-download me-2"></i>
                            تصدير البيانات
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-danger w-100" onclick="deleteUser()">
                            <i class="bi bi-trash me-2"></i>
                            حذف المستخدم
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-key me-2"></i>
                    إعادة تعيين كلمة المرور
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل تريد إعادة تعيين كلمة المرور للمستخدم <strong><?= User::getFullName($user) ?></strong>؟</p>
                <p class="text-muted">سيتم إرسال كلمة مرور جديدة إلى البريد الإلكتروني الخاص بالمستخدم.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="confirmResetPassword()">
                    <i class="bi bi-check-circle me-2"></i>
                    تأكيد إعادة التعيين
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// التحقق من صحة النموذج
document.getElementById('editUserForm').addEventListener('submit', function(e) {
    const requiredFields = this.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (!isValid) {
        e.preventDefault();
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
    }
});

// إعادة تعيين كلمة المرور
function resetPassword() {
    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    modal.show();
}

function confirmResetPassword() {
    showLoading();
    
    fetch('<?= App::url('admin/reset-user-password') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'user_id=<?= $user['id'] ?>'
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal')).hide();
        } else {
            showAlert(data.message, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showAlert('حدث خطأ في الاتصال', 'error');
    });
}

// عرض نشاط المستخدم
function viewUserActivity() {
    window.open('<?= App::url('admin/user-activity/' . $user['id']) ?>', '_blank');
}

// إرسال إشعار
function sendNotification() {
    const message = prompt('أدخل رسالة الإشعار:');
    if (message) {
        showLoading();
        
        fetch('<?= App::url('admin/send-user-notification') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: <?= $user['id'] ?>,
                message: message
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showAlert('تم إرسال الإشعار بنجاح', 'success');
            } else {
                showAlert(data.message, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showAlert('حدث خطأ في الاتصال', 'error');
        });
    }
}

// تصدير بيانات المستخدم
function exportUserData() {
    window.open('<?= App::url('admin/export-user-data/' . $user['id']) ?>', '_blank');
}

// حذف المستخدم
function deleteUser() {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        showLoading();
        
        fetch('<?= App::url('admin/delete-user') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'user_id=<?= $user['id'] ?>'
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showAlert(data.message, 'success');
                setTimeout(() => {
                    window.location.href = '<?= App::url('admin/users') ?>';
                }, 1000);
            } else {
                showAlert(data.message, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showAlert('حدث خطأ في الاتصال', 'error');
        });
    }
}

// تحديث نوع المستخدم
document.getElementById('user_type').addEventListener('change', function() {
    const userType = this.value;
    const userTypeLabel = this.options[this.selectedIndex].text;
    
    // تحديث لون البادج في البطاقة
    const badge = document.querySelector('.avatar-lg + div .badge');
    if (badge) {
        badge.className = `badge bg-${getTypeColor(userType)}`;
        badge.textContent = userTypeLabel;
    }
});

function getTypeColor(userType) {
    const colors = {
        'patient': 'primary',
        'doctor': 'success',
        'pharmacist': 'info',
        'admin': 'warning'
    };
    return colors[userType] || 'secondary';
}
</script> 