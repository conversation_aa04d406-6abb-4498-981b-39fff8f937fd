<?php

/**
 * متحكم المريض
 * يعالج جميع العمليات المتعلقة بالمرضى
 */
class PatientController extends Controller
{
    private $userModel;
    private $appointmentModel;
    private $prescriptionModel;
    private $medicalRecordModel;
    private $notificationModel;

    public function __construct()
    {
        parent::__construct();
        
        // التحقق من تسجيل الدخول ونوع المستخدم
        $this->requireAuth();
        $this->requireUserType('patient');
        
        // تحميل النماذج
        $this->userModel = new User();
        $this->appointmentModel = new Appointment();
        $this->prescriptionModel = new Prescription();
        $this->medicalRecordModel = new MedicalRecord();
        $this->notificationModel = new Notification();
    }

    /**
     * لوحة تحكم المريض
     */
    public function dashboard()
    {
        $patientId = $this->currentUser['id'];
        
        // الحصول على البيانات للوحة التحكم
        $data = [
            'title' => 'لوحة تحكم المريض',
            'patient' => $this->currentUser,
            'upcomingAppointments' => $this->appointmentModel->getUpcoming($patientId, 'patient', 5),
            'recentPrescriptions' => $this->prescriptionModel->getByPatient($patientId, 5),
            'notifications' => $this->notificationModel->getByUser($patientId, false, 5),
            'unreadNotifications' => $this->notificationModel->getUnreadCount($patientId),
            'medicalSummary' => $this->medicalRecordModel->getPatientSummary($patientId)
        ];

        $this->view('patient/dashboard', $data);
    }

    /**
     * الملف الشخصي
     */
    public function profile()
    {
        $patientId = $this->currentUser['id'];
        
        if (App::isPost()) {
            $this->updateProfile();
            return;
        }

        $data = [
            'title' => 'الملف الشخصي',
            'patient' => $this->userModel->findById($patientId),
            'allergies' => $this->medicalRecordModel->getAllergies($patientId)
        ];

        $this->view('patient/profile', $data);
    }

    /**
     * تحديث الملف الشخصي
     */
    private function updateProfile()
    {
        $patientId = $this->currentUser['id'];
        $postData = App::post();
        
        // التحقق من صحة البيانات
        $errors = $this->validate($postData, [
            'first_name' => [
                'required' => true,
                'min_length' => 2,
                'max_length' => 50
            ],
            'last_name' => [
                'required' => true,
                'min_length' => 2,
                'max_length' => 50
            ],
            'phone' => [
                'required' => true,
                'phone' => true
            ],
            'email' => [
                'required' => true,
                'email' => true
            ]
        ]);

        // التحقق من تفرد البريد الإلكتروني
        if (empty($errors['email'])) {
            $existingUser = $this->userModel->findByEmail($postData['email']);
            if ($existingUser && $existingUser['id'] != $patientId) {
                $errors['email'] = 'البريد الإلكتروني مستخدم بالفعل';
            }
        }

        if (!empty($errors)) {
            SessionHelper::setValidationErrors($errors);
            SessionHelper::setOldInput($postData);
            $this->redirect('patient/profile');
            return;
        }

        // تحديث البيانات
        $updateData = [
            'first_name' => $postData['first_name'],
            'last_name' => $postData['last_name'],
            'phone' => $postData['phone'],
            'email' => $postData['email'],
            'date_of_birth' => $postData['date_of_birth'] ?? null,
            'gender' => $postData['gender'] ?? null,
            'address' => $postData['address'] ?? null
        ];

        if ($this->userModel->update($patientId, $updateData)) {
            // تحديث بيانات الجلسة
            $_SESSION['user_name'] = $postData['first_name'] . ' ' . $postData['last_name'];
            
            $this->setFlashMessage('تم تحديث الملف الشخصي بنجاح', 'success');
        } else {
            $this->setFlashMessage('حدث خطأ أثناء تحديث الملف الشخصي', 'error');
        }

        $this->redirect('patient/profile');
    }

    /**
     * المواعيد
     */
    public function appointments()
    {
        $patientId = $this->currentUser['id'];
        $status = App::get('status');
        
        $data = [
            'title' => 'مواعيدي',
            'appointments' => $this->appointmentModel->getByPatient($patientId, $status),
            'currentStatus' => $status,
            'doctors' => $this->userModel->getActiveDoctors()
        ];

        $this->view('patient/appointments', $data);
    }

    /**
     * حجز موعد جديد
     */
    public function bookAppointment()
    {
        if (App::isPost()) {
            $this->processBookAppointment();
            return;
        }

        $data = [
            'title' => 'حجز موعد جديد',
            'doctors' => $this->userModel->getActiveDoctors()
        ];

        $this->view('patient/book_appointment', $data);
    }

    /**
     * معالجة حجز الموعد
     */
    private function processBookAppointment()
    {
        $patientId = $this->currentUser['id'];
        $postData = App::post();
        
        // التحقق من صحة البيانات
        $errors = $this->appointmentModel->validate(array_merge($postData, ['patient_id' => $patientId]));

        if (!empty($errors)) {
            SessionHelper::setValidationErrors($errors);
            SessionHelper::setOldInput($postData);
            $this->redirect('patient/book-appointment');
            return;
        }

        // إنشاء الموعد
        $appointmentData = [
            'patient_id' => $patientId,
            'doctor_id' => $postData['doctor_id'],
            'appointment_date' => $postData['appointment_date'],
            'appointment_time' => $postData['appointment_time'],
            'reason' => $postData['reason'] ?? null,
            'status' => 'scheduled'
        ];

        $appointmentId = $this->appointmentModel->create($appointmentData);

        if ($appointmentId) {
            // إرسال إشعار
            $this->notificationModel->notifyNewAppointment(
                $patientId,
                $postData['doctor_id'],
                $postData['appointment_date'],
                $postData['appointment_time']
            );

            $this->setFlashMessage('تم حجز الموعد بنجاح', 'success');
            $this->redirect('patient/appointments');
        } else {
            $this->setFlashMessage('فشل في حجز الموعد. يرجى المحاولة مرة أخرى', 'error');
            $this->redirect('patient/book-appointment');
        }
    }

    /**
     * إلغاء موعد
     */
    public function cancelAppointment()
    {
        $appointmentId = App::post('appointment_id');
        $reason = App::post('reason', 'إلغاء من قبل المريض');
        
        if (!$appointmentId) {
            $this->json(['success' => false, 'message' => 'معرف الموعد مطلوب'], 400);
            return;
        }

        // التحقق من ملكية الموعد
        $appointment = $this->appointmentModel->findById($appointmentId);
        if (!$appointment || $appointment['patient_id'] != $this->currentUser['id']) {
            $this->json(['success' => false, 'message' => 'الموعد غير موجود'], 404);
            return;
        }

        if ($this->appointmentModel->cancel($appointmentId, $reason)) {
            // إرسال إشعار للطبيب
            $this->notificationModel->notifyAppointmentCancellation(
                $appointment['patient_id'],
                $appointment['doctor_id'],
                $appointment['appointment_date'],
                $appointment['appointment_time']
            );

            $this->json(['success' => true, 'message' => 'تم إلغاء الموعد بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في إلغاء الموعد'], 500);
        }
    }

    /**
     * الوصفات الطبية
     */
    public function prescriptions()
    {
        $patientId = $this->currentUser['id'];
        
        $data = [
            'title' => 'وصفاتي الطبية',
            'prescriptions' => $this->prescriptionModel->getByPatient($patientId)
        ];

        $this->view('patient/prescriptions', $data);
    }

    /**
     * عرض تفاصيل الوصفة
     */
    public function viewPrescription($prescriptionId)
    {
        // التحقق من ملكية الوصفة
        $prescription = $this->prescriptionModel->findById($prescriptionId);
        if (!$prescription || $prescription['patient_id'] != $this->currentUser['id']) {
            $this->setFlashMessage('الوصفة غير موجودة', 'error');
            $this->redirect('patient/prescriptions');
            return;
        }

        $data = [
            'title' => 'تفاصيل الوصفة',
            'prescription' => $prescription,
            'medications' => $this->prescriptionModel->getMedications($prescriptionId)
        ];

        $this->view('patient/prescription_details', $data);
    }

    /**
     * تحميل الوصفة الطبية
     */
    public function downloadPrescription($prescriptionId)
    {
        // التحقق من ملكية الوصفة
        $prescription = $this->prescriptionModel->findById($prescriptionId);
        if (!$prescription || $prescription['patient_id'] != $this->currentUser['id']) {
            $this->setFlashMessage('الوصفة غير موجودة', 'error');
            $this->redirect('patient/prescriptions');
            return;
        }

        $data = [
            'prescription' => $prescription,
            'medications' => $this->prescriptionModel->getMedications($prescriptionId),
            'patient' => $this->currentUser
        ];

        // إعداد headers للتحميل
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="prescription_' . $prescriptionId . '_' . date('Y-m-d') . '.html"');

        $this->view('patient/download_prescription', $data);
    }

    /**
     * تصدير الوصفات الطبية
     */
    public function exportPrescriptions()
    {
        $patientId = $this->currentUser['id'];
        
        $data = [
            'prescriptions' => $this->prescriptionModel->getByPatient($patientId),
            'patient' => $this->currentUser
        ];

        // إعداد headers للتصدير
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="prescriptions_' . date('Y-m-d') . '.html"');

        $this->view('patient/export_prescriptions', $data);
    }

    /**
     * السجل الطبي
     */
    public function medicalRecord()
    {
        $patientId = $this->currentUser['id'];
        
        $data = [
            'title' => 'سجلي الطبي',
            'medicalRecords' => $this->medicalRecordModel->getByPatient($patientId),
            'allergies' => $this->medicalRecordModel->getAllergies($patientId),
            'labTests' => $this->medicalRecordModel->getLabTests($patientId),
            'summary' => $this->medicalRecordModel->getPatientSummary($patientId)
        ];

        $this->view('patient/medical_record', $data);
    }

    /**
     * السجلات الطبية (صفحة متقدمة)
     */
    public function medicalRecords()
    {
        $patientId = $this->currentUser['id'];
        
        $data = [
            'title' => 'السجلات الطبية',
            'medicalRecords' => $this->medicalRecordModel->getByPatient($patientId),
            'allergies' => $this->medicalRecordModel->getAllergies($patientId),
            'labTests' => $this->medicalRecordModel->getLabTests($patientId),
            'summary' => $this->medicalRecordModel->getPatientSummary($patientId)
        ];

        $this->view('patient/medical_records', $data);
    }

    /**
     * العلامات الحيوية
     */
    public function vitals()
    {
        $patientId = $this->currentUser['id'];
        
        // الحصول على السجلات الطبية التي تحتوي على العلامات الحيوية
        $medicalRecords = $this->medicalRecordModel->getByPatient($patientId);
        
        // تصفية السجلات التي تحتوي على العلامات الحيوية
        $vitalsRecords = [];
        foreach ($medicalRecords as $record) {
            if (!empty($record['vital_signs'])) {
                $vitals = json_decode($record['vital_signs'], true);
                if (is_array($vitals) && !empty($vitals)) {
                    $record['formatted_vitals'] = MedicalRecord::formatVitalSigns($vitals);
                    $vitalsRecords[] = $record;
                }
            }
        }
        
        // ترتيب السجلات حسب التاريخ (الأحدث أولاً)
        usort($vitalsRecords, function($a, $b) {
            return strtotime($b['visit_date']) - strtotime($a['visit_date']);
        });
        
        // الحصول على أحدث العلامات الحيوية
        $latestVitals = null;
        if (!empty($vitalsRecords)) {
            $latestVitals = $vitalsRecords[0];
        }
        
        // الحصول على إحصائيات العلامات الحيوية
        $vitalsStats = $this->getVitalsStats($vitalsRecords);
        
        $data = [
            'title' => 'العلامات الحيوية',
            'vitalsRecords' => $vitalsRecords,
            'latestVitals' => $latestVitals,
            'vitalsStats' => $vitalsStats,
            'totalRecords' => count($vitalsRecords)
        ];

        $this->view('patient/vitals', $data);
    }

    /**
     * الحصول على إحصائيات العلامات الحيوية
     */
    private function getVitalsStats($vitalsRecords)
    {
        $stats = [
            'temperature' => ['min' => null, 'max' => null, 'avg' => null],
            'blood_pressure_systolic' => ['min' => null, 'max' => null, 'avg' => null],
            'blood_pressure_diastolic' => ['min' => null, 'max' => null, 'avg' => null],
            'heart_rate' => ['min' => null, 'max' => null, 'avg' => null],
            'respiratory_rate' => ['min' => null, 'max' => null, 'avg' => null],
            'weight' => ['min' => null, 'max' => null, 'avg' => null],
            'height' => ['min' => null, 'max' => null, 'avg' => null]
        ];
        
        foreach ($stats as $vitalType => &$vitalStats) {
            $values = [];
            foreach ($vitalsRecords as $record) {
                $vitals = json_decode($record['vital_signs'], true);
                if (isset($vitals[$vitalType]) && is_numeric($vitals[$vitalType])) {
                    $values[] = (float)$vitals[$vitalType];
                }
            }
            
            if (!empty($values)) {
                $vitalStats['min'] = min($values);
                $vitalStats['max'] = max($values);
                $vitalStats['avg'] = round(array_sum($values) / count($values), 1);
            }
        }
        
        return $stats;
    }

    /**
     * صفحة الحساسيات
     */
    public function allergies()
    {
        $patientId = $this->currentUser['id'];
        
        // معالجة إضافة حساسية جديدة
        if (App::isPost()) {
            $this->processAddAllergy();
            return;
        }
        
        // الحصول على الحساسيات
        $allergies = $this->medicalRecordModel->getAllergies($patientId);
        
        // حساب الإحصائيات
        $allergiesStats = $this->getAllergiesStats($allergies);
        
        $data = [
            'title' => 'الحساسيات',
            'allergies' => $allergies,
            'allergiesStats' => $allergiesStats,
            'totalAllergies' => count($allergies)
        ];

        $this->view('patient/allergies', $data);
    }

    /**
     * معالجة إضافة حساسية جديدة
     */
    private function processAddAllergy()
    {
        $patientId = $this->currentUser['id'];
        $allergen = App::post('allergen');
        $reaction = App::post('reaction');
        $severity = App::post('severity', 'mild');
        $notes = App::post('notes');

        // التحقق من صحة البيانات
        if (empty($allergen)) {
            $this->json(['success' => false, 'message' => 'اسم المادة المسببة للحساسية مطلوب'], 400);
            return;
        }

        // إضافة الحساسية
        $allergyId = $this->medicalRecordModel->addAllergy($patientId, $allergen, $reaction, $severity, $notes);

        if ($allergyId) {
            $this->json(['success' => true, 'message' => 'تم إضافة الحساسية بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في إضافة الحساسية'], 500);
        }
    }

    /**
     * حذف حساسية
     */
    public function deleteAllergy()
    {
        if (!App::isPost()) {
            $this->json(['success' => false, 'message' => 'طريقة غير مسموحة'], 405);
            return;
        }

        $allergyId = App::post('allergy_id');
        
        if (empty($allergyId)) {
            $this->json(['success' => false, 'message' => 'معرف الحساسية مطلوب'], 400);
            return;
        }

        $success = $this->medicalRecordModel->deleteAllergy($allergyId);

        if ($success) {
            $this->json(['success' => true, 'message' => 'تم حذف الحساسية بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في حذف الحساسية'], 500);
        }
    }

    /**
     * الحصول على إحصائيات الحساسيات
     */
    private function getAllergiesStats($allergies)
    {
        $stats = [
            'total' => count($allergies),
            'by_severity' => [
                'mild' => 0,
                'moderate' => 0,
                'severe' => 0
            ],
            'common_allergens' => []
        ];

        $allergenCounts = [];
        
        foreach ($allergies as $allergy) {
            // حساب حسب الشدة
            $severity = $allergy['severity'] ?? 'mild';
            $stats['by_severity'][$severity]++;
            
            // حساب المواد المسببة للحساسية الشائعة
            $allergen = strtolower(trim($allergy['allergen']));
            if (!isset($allergenCounts[$allergen])) {
                $allergenCounts[$allergen] = 0;
            }
            $allergenCounts[$allergen]++;
        }

        // ترتيب المواد المسببة للحساسية حسب التكرار
        arsort($allergenCounts);
        $stats['common_allergens'] = array_slice($allergenCounts, 0, 5, true);

        return $stats;
    }

    /**
     * إضافة حساسية
     */
    public function addAllergy()
    {
        if (!App::isPost()) {
            $this->json(['success' => false, 'message' => 'طريقة غير مسموحة'], 405);
            return;
        }

        $patientId = $this->currentUser['id'];
        $allergen = App::post('allergen');
        $reaction = App::post('reaction');
        $severity = App::post('severity', 'mild');
        $notes = App::post('notes');

        if (empty($allergen)) {
            $this->json(['success' => false, 'message' => 'اسم المادة المسببة للحساسية مطلوب'], 400);
            return;
        }

        $allergyId = $this->medicalRecordModel->addAllergy($patientId, $allergen, $reaction, $severity, $notes);

        if ($allergyId) {
            $this->json(['success' => true, 'message' => 'تم إضافة الحساسية بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في إضافة الحساسية'], 500);
        }
    }

    /**
     * الإشعارات
     */
    public function notifications()
    {
        $patientId = $this->currentUser['id'];
        $page = (int)App::get('page', 1);
        $type = App::get('type', '');
        $status = App::get('status', '');
        $search = App::get('search', '');
        $limit = 20;
        $offset = ($page - 1) * $limit;
        
        // تحميل نموذج الإشعارات
        $notificationModel = new Notification();
        
        // الحصول على الإشعارات مع الفلاتر
        $notifications = $notificationModel->getByUser($patientId, false, $limit);
        
        // تطبيق الفلاتر
        if (!empty($type)) {
            $notifications = array_filter($notifications, function($notification) use ($type) {
                return $notification['type'] === $type;
            });
        }
        
        if (!empty($status)) {
            $notifications = array_filter($notifications, function($notification) use ($status) {
                if ($status === 'unread') {
                    return !$notification['is_read'];
                } else {
                    return $notification['is_read'];
                }
            });
        }
        
        if (!empty($search)) {
            $notifications = array_filter($notifications, function($notification) use ($search) {
                return stripos($notification['title'], $search) !== false ||
                       stripos($notification['message'], $search) !== false;
            });
        }
        
        // الحصول على الإحصائيات
        $stats = $notificationModel->getStats($patientId);
        $unreadCount = $notificationModel->getUnreadCount($patientId);
        
        // حساب إجمالي الصفحات
        $totalNotifications = count($notifications);
        $totalPages = ceil($totalNotifications / $limit);
        
        $data = [
            'title' => 'الإشعارات',
            'notifications' => array_slice($notifications, $offset, $limit),
            'stats' => $stats,
            'unreadCount' => $unreadCount,
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'totalNotifications' => $totalNotifications,
            'selectedType' => $type,
            'selectedStatus' => $status,
            'search' => $search
        ];

        $this->view('patient/notifications', $data);
    }

    /**
     * تسجيل الإشعار كمقروء
     */
    public function markNotificationRead()
    {
        if (!App::isPost()) {
            $this->json(['success' => false, 'message' => 'طريقة غير مسموحة'], 405);
            return;
        }

        $notificationId = App::post('notification_id');
        $patientId = $this->currentUser['id'];
        
        if (!$notificationId) {
            $this->json(['success' => false, 'message' => 'معرف الإشعار مطلوب'], 400);
            return;
        }

        $notificationModel = new Notification();
        
        // التحقق من ملكية الإشعار
        $notification = $notificationModel->findById($notificationId);
        if (!$notification || $notification['user_id'] != $patientId) {
            $this->json(['success' => false, 'message' => 'الإشعار غير موجود'], 404);
            return;
        }

        if ($notificationModel->markAsRead($notificationId)) {
            $this->json(['success' => true, 'message' => 'تم تسجيل الإشعار كمقروء']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في تسجيل الإشعار كمقروء'], 500);
        }
    }

    /**
     * تسجيل جميع الإشعارات كمقروءة
     */
    public function markAllNotificationsRead()
    {
        if (!App::isPost()) {
            $this->json(['success' => false, 'message' => 'طريقة غير مسموحة'], 405);
            return;
        }

        $patientId = $this->currentUser['id'];
        $notificationModel = new Notification();
        
        if ($notificationModel->markAllAsRead($patientId)) {
            $this->json(['success' => true, 'message' => 'تم تسجيل جميع الإشعارات كمقروءة']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في تسجيل الإشعارات كمقروءة'], 500);
        }
    }

    /**
     * حذف الإشعار
     */
    public function deleteNotification()
    {
        if (!App::isPost()) {
            $this->json(['success' => false, 'message' => 'طريقة غير مسموحة'], 405);
            return;
        }

        $notificationId = App::post('notification_id');
        $patientId = $this->currentUser['id'];
        
        if (!$notificationId) {
            $this->json(['success' => false, 'message' => 'معرف الإشعار مطلوب'], 400);
            return;
        }

        $notificationModel = new Notification();
        
        // التحقق من ملكية الإشعار
        $notification = $notificationModel->findById($notificationId);
        if (!$notification || $notification['user_id'] != $patientId) {
            $this->json(['success' => false, 'message' => 'الإشعار غير موجود'], 404);
            return;
        }

        if ($notificationModel->delete($notificationId)) {
            $this->json(['success' => true, 'message' => 'تم حذف الإشعار بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في حذف الإشعار'], 500);
        }
    }

    /**
     * البحث في الأطباء
     */
    public function searchDoctors()
    {
        $query = App::get('q', '');
        $specialization = App::get('specialization', '');
        
        if (empty($query) && empty($specialization)) {
            $this->json(['doctors' => []]);
            return;
        }

        $doctors = $this->userModel->search($query, 'doctor');
        
        // تصفية حسب التخصص إذا تم تحديده
        if (!empty($specialization)) {
            $doctors = array_filter($doctors, function($doctor) use ($specialization) {
                return stripos($doctor['specialization'], $specialization) !== false;
            });
        }

        $this->json(['doctors' => array_values($doctors)]);
    }

    /**
     * الحصول على الأوقات المتاحة للطبيب
     */
    public function getAvailableSlots()
    {
        $doctorId = App::get('doctor_id');
        $date = App::get('date');
        
        if (!$doctorId || !$date) {
            $this->json(['slots' => []], 400);
            return;
        }

        $availableSlots = $this->appointmentModel->getAvailableTimeSlots($doctorId, $date);
        
        $this->json(['slots' => $availableSlots]);
    }

    /**
     * تقييم موعد
     */
    public function rateAppointment()
    {
        if (!App::isPost()) {
            $this->json(['success' => false, 'message' => 'طريقة غير مسموحة'], 405);
            return;
        }

        $appointmentId = App::post('appointment_id');
        $rating = App::post('rating');
        $notes = App::post('rating_notes', '');

        if (!$appointmentId || !$rating) {
            $this->json(['success' => false, 'message' => 'معرف الموعد والتقييم مطلوبان'], 400);
            return;
        }

        // التحقق من ملكية الموعد
        $appointment = $this->appointmentModel->findById($appointmentId);
        if (!$appointment || $appointment['patient_id'] != $this->currentUser['id']) {
            $this->json(['success' => false, 'message' => 'الموعد غير موجود'], 404);
            return;
        }

        // التحقق من أن الموعد مكتمل
        if ($appointment['status'] !== 'completed') {
            $this->json(['success' => false, 'message' => 'يمكن تقييم المواعيد المكتملة فقط'], 400);
            return;
        }

        // التحقق من أن التقييم لم يتم من قبل
        if (!empty($appointment['rating'])) {
            $this->json(['success' => false, 'message' => 'تم تقييم هذا الموعد من قبل'], 400);
            return;
        }

        // التحقق من صحة التقييم
        if (!is_numeric($rating) || $rating < 1 || $rating > 5) {
            $this->json(['success' => false, 'message' => 'التقييم يجب أن يكون بين 1 و 5'], 400);
            return;
        }

        $updateData = [
            'rating' => $rating,
            'notes' => $appointment['notes'] . "\n\nتقييم المريض: " . $notes
        ];

        if ($this->appointmentModel->update($appointmentId, $updateData)) {
            // إرسال إشعار للطبيب
            $this->notificationModel->notifyAppointmentRating(
                $appointment['patient_id'],
                $appointment['doctor_id'],
                $rating
            );

            $this->json(['success' => true, 'message' => 'تم إرسال التقييم بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في إرسال التقييم'], 500);
        }
    }

    /**
     * عرض تفاصيل الموعد
     */
    public function appointmentDetails($appointmentId)
    {
        // التحقق من ملكية الموعد
        $appointment = $this->appointmentModel->findById($appointmentId);
        if (!$appointment || $appointment['patient_id'] != $this->currentUser['id']) {
            $this->setFlashMessage('الموعد غير موجود', 'error');
            $this->redirect('patient/appointments');
            return;
        }

        $data = [
            'appointment' => $appointment
        ];

        $this->view('patient/appointment_details', $data);
    }

    /**
     * عرض تفاصيل السجل الطبي
     */
    public function medicalRecordDetails($recordId)
    {
        // التحقق من ملكية السجل الطبي
        $record = $this->medicalRecordModel->findById($recordId);
        if (!$record || $record['patient_id'] != $this->currentUser['id']) {
            $this->setFlashMessage('السجل الطبي غير موجود', 'error');
            $this->redirect('patient/medical-record');
            return;
        }

        $data = [
            'record' => $record
        ];

        $this->view('patient/medical_record_details', $data);
    }

    /**
     * عرض نتائج فحص المختبر
     */
    public function labTestResults($testId)
    {
        // التحقق من ملكية فحص المختبر
        $test = $this->medicalRecordModel->getLabTestById($testId);
        if (!$test || $test['patient_id'] != $this->currentUser['id']) {
            $this->setFlashMessage('فحص المختبر غير موجود', 'error');
            $this->redirect('patient/medical-record');
            return;
        }

        $data = [
            'test' => $test
        ];

        $this->view('patient/lab_test_results', $data);
    }

    /**
     * تصدير السجلات الطبية
     */
    public function exportMedicalRecords()
    {
        $patientId = $this->currentUser['id'];
        
        $data = [
            'medicalRecords' => $this->medicalRecordModel->getByPatient($patientId),
            'allergies' => $this->medicalRecordModel->getAllergies($patientId),
            'labTests' => $this->medicalRecordModel->getLabTests($patientId),
            'summary' => $this->medicalRecordModel->getPatientSummary($patientId),
            'patient' => $this->currentUser
        ];

        // إعداد headers للتصدير
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="medical_records_' . date('Y-m-d') . '.html"');

        $this->view('patient/export_medical_records', $data);
    }

    /**
     * تحميل نتائج فحص المختبر
     */
    public function downloadLabResults($testId)
    {
        // التحقق من ملكية فحص المختبر
        $test = $this->medicalRecordModel->getLabTestById($testId);
        if (!$test || $test['patient_id'] != $this->currentUser['id']) {
            $this->setFlashMessage('فحص المختبر غير موجود', 'error');
            $this->redirect('patient/medical-record');
            return;
        }

        $data = [
            'test' => $test,
            'patient' => $this->currentUser
        ];

        // إعداد headers للتحميل
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="lab_results_' . $testId . '_' . date('Y-m-d') . '.html"');

        $this->view('patient/download_lab_results', $data);
    }

    /**
     * تغيير كلمة المرور
     */
    public function changePassword()
    {
        if (!App::isPost()) {
            $this->json(['success' => false, 'message' => 'طريقة غير مسموحة'], 405);
            return;
        }

        $patientId = $this->currentUser['id'];
        $currentPassword = App::post('current_password');
        $newPassword = App::post('new_password');
        $confirmPassword = App::post('confirm_password');

        // التحقق من كلمة المرور الحالية
        if (!$this->userModel->verifyPassword($patientId, $currentPassword)) {
            $this->json(['success' => false, 'message' => 'كلمة المرور الحالية غير صحيحة'], 400);
            return;
        }

        // التحقق من تطابق كلمة المرور الجديدة
        if ($newPassword !== $confirmPassword) {
            $this->json(['success' => false, 'message' => 'كلمات المرور الجديدة غير متطابقة'], 400);
            return;
        }

        // التحقق من قوة كلمة المرور
        $passwordErrors = ValidationHelper::validatePasswordStrength($newPassword);
        if (!empty($passwordErrors)) {
            $this->json(['success' => false, 'message' => implode(', ', $passwordErrors)], 400);
            return;
        }

        if ($this->userModel->updatePassword($patientId, $newPassword)) {
            $this->json(['success' => true, 'message' => 'تم تغيير كلمة المرور بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في تغيير كلمة المرور'], 500);
        }
    }

    /**
     * الأدوية
     */
    public function medications()
    {
        $patientId = $this->currentUser['id'];
        $search = App::get('search', '');
        $category = App::get('category', '');
        $prescriptionRequired = App::get('prescription_required', '');
        
        // تحميل نموذج الأدوية
        $medicationModel = new Medication();
        
        // الحصول على الأدوية مع الفلاتر
        $medications = $medicationModel->search($search, $category, $prescriptionRequired);
        
        // الحصول على الفئات وأشكال الجرعات للفلاتر
        $categories = $medicationModel->getCategories();
        $dosageForms = $medicationModel->getDosageForms();
        
        // الحصول على الأدوية الشائعة
        $popularMedications = $medicationModel->getPopularMedications(6);
        
        // الحصول على الأدوية التي لا تحتاج وصفة طبية
        $otcMedications = $medicationModel->getOverTheCounter(8);
        
        $data = [
            'title' => 'الأدوية',
            'medications' => $medications,
            'categories' => $categories,
            'dosageForms' => $dosageForms,
            'popularMedications' => $popularMedications,
            'otcMedications' => $otcMedications,
            'search' => $search,
            'selectedCategory' => $category,
            'selectedPrescriptionRequired' => $prescriptionRequired,
            'stats' => $medicationModel->getStats()
        ];

        $this->view('patient/medications', $data);
    }

    /**
     * عرض تفاصيل الدواء
     */
    public function viewMedication($medicationId)
    {
        $medicationModel = new Medication();
        $medication = $medicationModel->findById($medicationId);
        
        if (!$medication) {
            $this->setFlashMessage('الدواء غير موجود', 'error');
            $this->redirect('patient/medications');
            return;
        }

        // الحصول على أدوية مشابهة
        $similarMedications = $medicationModel->getByCategory($medication['category'], 4);
        $similarMedications = array_filter($similarMedications, function($med) use ($medicationId) {
            return $med['id'] != $medicationId;
        });

        $data = [
            'title' => 'تفاصيل الدواء - ' . $medication['name'],
            'medication' => $medication,
            'similarMedications' => array_slice($similarMedications, 0, 3)
        ];

        $this->view('patient/medication_details', $data);
    }

    /**
     * الرسائل
     */
    public function messages()
    {
        $patientId = $this->currentUser['id'];
        $page = (int)App::get('page', 1);
        $type = App::get('type', 'all'); // all, inbox, sent
        $search = App::get('search', '');
        $limit = 20;
        $offset = ($page - 1) * $limit;
        
        // تحميل نموذج الرسائل
        $messageModel = new Message();
        
        // الحصول على الرسائل حسب النوع
        $messages = $messageModel->getByUser($patientId, $type, $limit, $offset);
        
        // تطبيق البحث إذا كان موجوداً
        if (!empty($search)) {
            $messages = $messageModel->search($search, $patientId, $type);
        }
        
        // الحصول على الإحصائيات
        $stats = $messageModel->getStats($patientId);
        $unreadCount = $messageModel->getUnreadCount($patientId);
        
        // حساب إجمالي الصفحات
        $totalMessages = count($messages);
        $totalPages = ceil($totalMessages / $limit);
        
        $data = [
            'title' => 'الرسائل',
            'messages' => array_slice($messages, 0, $limit),
            'stats' => $stats,
            'unreadCount' => $unreadCount,
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'totalMessages' => $totalMessages,
            'selectedType' => $type,
            'search' => $search
        ];

        $this->view('patient/messages', $data);
    }

    /**
     * عرض رسالة واحدة
     */
    public function viewMessage()
    {
        $messageId = App::get('id');
        $patientId = $this->currentUser['id'];
        
        if (!$messageId) {
            $this->redirect('patient/messages');
            return;
        }

        $messageModel = new Message();
        $message = $messageModel->findById($messageId);
        
        if (!$message || ($message['sender_id'] != $patientId && $message['recipient_id'] != $patientId)) {
            $this->redirect('patient/messages');
            return;
        }

        // تسجيل الرسالة كمقروءة إذا كان المريض هو المستقبل
        if ($message['recipient_id'] == $patientId && $message['status'] == 'sent') {
            $messageModel->markAsRead($messageId);
        }

        $data = [
            'title' => 'عرض الرسالة',
            'message' => $message
        ];

        $this->view('patient/view_message', $data);
    }

    /**
     * إنشاء رسالة جديدة
     */
    public function composeMessage()
    {
        if (App::isPost()) {
            $patientId = $this->currentUser['id'];
            $recipientId = App::post('recipient_id');
            $subject = App::post('subject');
            $content = App::post('content');
            $type = App::post('type', 'personal');
            $priority = App::post('priority', 'normal');
            
            if (!$recipientId || !$subject || !$content) {
                SessionHelper::setFlash('error', 'جميع الحقول مطلوبة');
                $this->redirect('patient/compose-message');
                return;
            }

            $messageModel = new Message();
            $result = $messageModel->create($patientId, $recipientId, $subject, $content, $type, $priority);
            
            if ($result) {
                SessionHelper::setFlash('success', 'تم إرسال الرسالة بنجاح');
                $this->redirect('patient/messages');
            } else {
                SessionHelper::setFlash('error', 'فشل في إرسال الرسالة');
                $this->redirect('patient/compose-message');
            }
            return;
        }

        // الحصول على قائمة الأطباء للاختيار
        $userModel = new User();
        $doctors = $userModel->getByType('doctor');
        
        $data = [
            'title' => 'إنشاء رسالة جديدة',
            'doctors' => $doctors
        ];

        $this->view('patient/compose_message', $data);
    }

    /**
     * التواصل مع الطبيب
     */
    public function contactDoctor()
    {
        if (App::isPost()) {
            $patientId = $this->currentUser['id'];
            $doctorId = App::post('doctor_id');
            $subject = App::post('subject');
            $content = App::post('content');
            $messageType = App::post('message_type', 'consultation');
            $priority = App::post('priority', 'normal');
            
            if (!$doctorId || !$subject || !$content) {
                SessionHelper::setFlash('error', 'جميع الحقول مطلوبة');
                $this->redirect('patient/contact-doctor');
                return;
            }

            // التحقق من وجود الطبيب
            $userModel = new User();
            $doctor = $userModel->findById($doctorId);
            if (!$doctor || $doctor['user_type'] !== 'doctor') {
                SessionHelper::setFlash('error', 'الطبيب غير موجود');
                $this->redirect('patient/contact-doctor');
                return;
            }

            $messageModel = new Message();
            $result = $messageModel->create($patientId, $doctorId, $subject, $content, $messageType, $priority);
            
            if ($result) {
                // إرسال إشعار للطبيب
                $notificationModel = new Notification();
                $notificationModel->create(
                    $doctorId,
                    'رسالة جديدة من مريض',
                    'لقد تلقيت رسالة جديدة من مريض. يرجى مراجعة صندوق الرسائل.',
                    'message',
                    'normal'
                );
                
                SessionHelper::setFlash('success', 'تم إرسال الرسالة للطبيب بنجاح');
                $this->redirect('patient/messages');
            } else {
                SessionHelper::setFlash('error', 'فشل في إرسال الرسالة');
                $this->redirect('patient/contact-doctor');
            }
            return;
        }

        // الحصول على قائمة الأطباء مع معلومات إضافية
        $userModel = new User();
        $doctors = $userModel->getDoctorsWithDetails();
        
        $data = [
            'title' => 'التواصل مع الطبيب',
            'doctors' => $doctors
        ];

        $this->view('patient/contact-doctor', $data);
    }

    /**
     * تسجيل الرسالة كمقروءة
     */
    public function markMessageRead()
    {
        if (!App::isPost()) {
            $this->json(['success' => false, 'message' => 'طريقة غير مسموحة'], 405);
            return;
        }

        $messageId = App::post('message_id');
        $patientId = $this->currentUser['id'];
        
        if (!$messageId) {
            $this->json(['success' => false, 'message' => 'معرف الرسالة مطلوب'], 400);
            return;
        }

        $messageModel = new Message();
        
        // التحقق من ملكية الرسالة
        $message = $messageModel->findById($messageId);
        if (!$message || $message['recipient_id'] != $patientId) {
            $this->json(['success' => false, 'message' => 'الرسالة غير موجودة'], 404);
            return;
        }

        if ($messageModel->markAsRead($messageId)) {
            $this->json(['success' => true, 'message' => 'تم تسجيل الرسالة كمقروءة']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في تسجيل الرسالة كمقروءة'], 500);
        }
    }

    /**
     * حذف الرسالة
     */
    public function deleteMessage()
    {
        if (!App::isPost()) {
            $this->json(['success' => false, 'message' => 'طريقة غير مسموحة'], 405);
            return;
        }

        $messageId = App::post('message_id');
        $patientId = $this->currentUser['id'];
        
        if (!$messageId) {
            $this->json(['success' => false, 'message' => 'معرف الرسالة مطلوب'], 400);
            return;
        }

        $messageModel = new Message();
        
        // التحقق من ملكية الرسالة
        $message = $messageModel->findById($messageId);
        if (!$message || ($message['sender_id'] != $patientId && $message['recipient_id'] != $patientId)) {
            $this->json(['success' => false, 'message' => 'الرسالة غير موجودة'], 404);
            return;
        }

        if ($messageModel->delete($messageId)) {
            $this->json(['success' => true, 'message' => 'تم حذف الرسالة بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في حذف الرسالة'], 500);
        }
    }

    /**
     * جهات الاتصال في الطوارئ
     */
    public function emergencyContacts()
    {
        $patientId = $this->currentUser['id'];
        
        if (App::isPost()) {
            $this->updateEmergencyContacts();
            return;
        }

        $data = [
            'title' => 'جهات الاتصال في الطوارئ',
            'patient' => $this->userModel->findById($patientId)
        ];

        $this->view('patient/emergency-contacts', $data);
    }

    /**
     * تحديث جهات الاتصال في الطوارئ
     */
    private function updateEmergencyContacts()
    {
        $patientId = $this->currentUser['id'];
        $postData = App::post();
        
        // التحقق من صحة البيانات
        $errors = $this->validate($postData, [
            'emergency_contact' => [
                'required' => true,
                'min_length' => 2,
                'max_length' => 100
            ],
            'emergency_phone' => [
                'required' => true,
                'phone' => true
            ]
        ]);

        if (!empty($errors)) {
            SessionHelper::setValidationErrors($errors);
            SessionHelper::setOldInput($postData);
            $this->redirect('patient/emergency-contacts');
            return;
        }

        // تحديث البيانات
        $updateData = [
            'emergency_contact' => $postData['emergency_contact'],
            'emergency_phone' => $postData['emergency_phone']
        ];

        if ($this->userModel->update($patientId, $updateData)) {
            $this->setFlashMessage('تم تحديث جهات الاتصال في الطوارئ بنجاح', 'success');
        } else {
            $this->setFlashMessage('حدث خطأ أثناء تحديث جهات الاتصال في الطوارئ', 'error');
        }

        $this->redirect('patient/emergency-contacts');
    }
}
