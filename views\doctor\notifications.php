<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-bell me-2"></i>
            الإشعارات
        </h1>
        <p class="text-muted">إدارة جميع الإشعارات والتنبيهات</p>
    </div>
    <div class="d-flex gap-2">
        <?php if ($unreadCount > 0): ?>
            <button type="button" class="btn btn-success" onclick="markAllAsRead()">
                <i class="bi bi-check-all me-2"></i>
                تحديد الكل كمقروء
            </button>
        <?php endif; ?>
        <button type="button" class="btn btn-outline-danger" onclick="deleteAllRead()">
            <i class="bi bi-trash me-2"></i>
            حذف المقروءة
        </button>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['total'] ?? 0 ?></h4>
                        <p class="mb-0">إجمالي الإشعارات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-bell display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $unreadCount ?></h4>
                        <p class="mb-0">غير مقروءة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-exclamation-circle display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['appointment'] ?? 0 ?></h4>
                        <p class="mb-0">إشعارات المواعيد</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-check display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['prescription'] ?? 0 ?></h4>
                        <p class="mb-0">إشعارات الوصفات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-prescription2 display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="input-group">
            <span class="input-group-text">
                <i class="bi bi-search"></i>
            </span>
            <input type="text" class="form-control" id="searchNotifications" 
                   placeholder="البحث في الإشعارات..." onkeyup="searchNotifications(this.value)">
        </div>
    </div>
    <div class="col-md-4">
        <select class="form-select" id="filterType" onchange="filterNotifications()">
            <option value="">جميع الأنواع</option>
            <option value="appointment">المواعيد</option>
            <option value="prescription">الوصفات الطبية</option>
            <option value="general">عام</option>
            <option value="system">النظام</option>
        </select>
    </div>
</div>

<!-- Notifications List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-list-ul me-2"></i>
            قائمة الإشعارات
        </h5>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="toggleReadFilter()">
                <i class="bi bi-eye me-1"></i>
                <span id="readFilterText">عرض المقروءة</span>
            </button>
            <button type="button" class="btn btn-sm btn-outline-primary" onclick="refreshNotifications()">
                <i class="bi bi-arrow-clockwise"></i>
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <div id="notificationsList">
            <?php if (empty($notifications)): ?>
                <div class="text-center py-5">
                    <i class="bi bi-bell-slash display-1 text-muted"></i>
                    <h5 class="text-muted mt-3">لا توجد إشعارات</h5>
                    <p class="text-muted">ستظهر هنا جميع الإشعارات والتنبيهات الجديدة</p>
                </div>
            <?php else: ?>
                <div class="list-group list-group-flush">
                    <?php foreach ($notifications as $notification): ?>
                        <div class="list-group-item list-group-item-action notification-item <?= !$notification['is_read'] ? 'unread' : '' ?>" 
                             data-id="<?= $notification['id'] ?>" data-type="<?= $notification['type'] ?>">
                            <div class="d-flex align-items-start">
                                <div class="flex-shrink-0 me-3">
                                    <div class="notification-icon bg-<?= Notification::getTypeColor($notification['type']) ?> bg-opacity-10 rounded-circle p-2">
                                        <i class="bi <?= Notification::getTypeIcon($notification['type']) ?> text-<?= Notification::getTypeColor($notification['type']) ?>"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1 <?= !$notification['is_read'] ? 'fw-bold' : '' ?>">
                                                <?= htmlspecialchars($notification['title']) ?>
                                                <?php if (!$notification['is_read']): ?>
                                                    <span class="badge bg-danger ms-2">جديد</span>
                                                <?php endif; ?>
                                            </h6>
                                            <p class="mb-1 text-muted"><?= htmlspecialchars($notification['message']) ?></p>
                                            <small class="text-muted">
                                                <i class="bi bi-clock me-1"></i>
                                                <?= Notification::formatTime($notification['created_at']) ?>
                                                <span class="mx-2">•</span>
                                                <span class="badge bg-<?= Notification::getTypeColor($notification['type']) ?> bg-opacity-25 text-<?= Notification::getTypeColor($notification['type']) ?>">
                                                    <?= Notification::getTypeLabel($notification['type']) ?>
                                                </span>
                                            </small>
                                        </div>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                                <i class="bi bi-three-dots-vertical"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <?php if (!$notification['is_read']): ?>
                                                    <li>
                                                        <a class="dropdown-item" href="#" onclick="markAsRead(<?= $notification['id'] ?>)">
                                                            <i class="bi bi-check me-2"></i>
                                                            تحديد كمقروء
                                                        </a>
                                                    </li>
                                                <?php endif; ?>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="#" onclick="deleteNotification(<?= $notification['id'] ?>)">
                                                        <i class="bi bi-trash me-2"></i>
                                                        حذف
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Load More Button -->
<?php if (count($notifications) >= 20): ?>
    <div class="text-center mt-4">
        <button type="button" class="btn btn-outline-primary" onclick="loadMoreNotifications()">
            <i class="bi bi-arrow-down me-2"></i>
            تحميل المزيد
        </button>
    </div>
<?php endif; ?>

<!-- Empty State -->
<div id="emptyState" class="text-center py-5" style="display: none;">
    <i class="bi bi-search display-1 text-muted"></i>
    <h5 class="text-muted mt-3">لا توجد نتائج</h5>
    <p class="text-muted">جرب تغيير معايير البحث</p>
    <button type="button" class="btn btn-primary" onclick="clearFilters()">
        <i class="bi bi-arrow-left me-2"></i>
        مسح الفلاتر
    </button>
</div>

<style>
.notification-item {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.notification-item:hover {
    background-color: #f8f9fa;
    border-left-color: #0d6efd;
}

.notification-item.unread {
    background-color: #f0f8ff;
    border-left-color: #0d6efd;
}

.notification-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-item.unread .notification-icon {
    background-color: #0d6efd !important;
    color: white !important;
}

.notification-item.unread .notification-icon i {
    color: white !important;
}

.list-group-item:last-child {
    border-bottom: none;
}

#notificationsList {
    max-height: 600px;
    overflow-y: auto;
}

@media (max-width: 768px) {
    .notification-item .dropdown {
        position: static;
    }
    
    .notification-item .dropdown-menu {
        position: absolute;
        right: 0;
        left: auto;
    }
}
</style>

<script>
let currentPage = 1;
let isLoading = false;
let showReadOnly = false;
let searchTerm = '';
let filterType = '';

// تحديث الإشعارات
function refreshNotifications() {
    location.reload();
}

// البحث في الإشعارات
function searchNotifications(term) {
    searchTerm = term;
    filterNotifications();
}

// فلترة الإشعارات
function filterNotifications() {
    filterType = document.getElementById('filterType').value;
    const items = document.querySelectorAll('.notification-item');
    let visibleCount = 0;
    
    items.forEach(item => {
        const type = item.dataset.type;
        const text = item.textContent.toLowerCase();
        const isRead = !item.classList.contains('unread');
        
        let show = true;
        
        // فلتر النوع
        if (filterType && type !== filterType) {
            show = false;
        }
        
        // فلتر المقروءة
        if (showReadOnly && !isRead) {
            show = false;
        }
        
        // فلتر البحث
        if (searchTerm && !text.includes(searchTerm.toLowerCase())) {
            show = false;
        }
        
        if (show) {
            item.style.display = 'block';
            visibleCount++;
        } else {
            item.style.display = 'none';
        }
    });
    
    // إظهار رسالة فارغة إذا لم توجد نتائج
    const emptyState = document.getElementById('emptyState');
    const notificationsList = document.getElementById('notificationsList');
    
    if (visibleCount === 0) {
        emptyState.style.display = 'block';
        notificationsList.style.display = 'none';
    } else {
        emptyState.style.display = 'none';
        notificationsList.style.display = 'block';
    }
}

// تبديل فلتر المقروءة
function toggleReadFilter() {
    showReadOnly = !showReadOnly;
    const button = document.getElementById('readFilterText');
    button.textContent = showReadOnly ? 'عرض غير المقروءة' : 'عرض المقروءة';
    filterNotifications();
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchNotifications').value = '';
    document.getElementById('filterType').value = '';
    searchTerm = '';
    filterType = '';
    showReadOnly = false;
    document.getElementById('readFilterText').textContent = 'عرض المقروءة';
    filterNotifications();
}

// تحديد إشعار كمقروء
function markAsRead(id) {
    fetch('<?= App::url('doctor/mark-notification-read') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notification_id: id })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const item = document.querySelector(`[data-id="${id}"]`);
            item.classList.remove('unread');
            item.querySelector('.badge.bg-danger')?.remove();
            item.querySelector('h6').classList.remove('fw-bold');
            
            // تحديث العداد
            updateNotificationCount();
        } else {
            showAlert('خطأ في تحديث الإشعار', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال', 'danger');
    });
}

// تحديد جميع الإشعارات كمقروءة
function markAllAsRead() {
    if (!confirm('هل تريد تحديد جميع الإشعارات كمقروءة؟')) {
        return;
    }
    
    fetch('<?= App::url('doctor/mark-all-notifications-read') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.querySelectorAll('.notification-item.unread').forEach(item => {
                item.classList.remove('unread');
                item.querySelector('.badge.bg-danger')?.remove();
                item.querySelector('h6').classList.remove('fw-bold');
            });
            
            updateNotificationCount();
            showAlert('تم تحديد جميع الإشعارات كمقروءة', 'success');
        } else {
            showAlert('خطأ في تحديث الإشعارات', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال', 'danger');
    });
}

// حذف إشعار
function deleteNotification(id) {
    if (!confirm('هل تريد حذف هذا الإشعار؟')) {
        return;
    }
    
    fetch('<?= App::url('doctor/delete-notification') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notification_id: id })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const item = document.querySelector(`[data-id="${id}"]`);
            item.style.opacity = '0';
            setTimeout(() => {
                item.remove();
                updateNotificationCount();
            }, 300);
        } else {
            showAlert('خطأ في حذف الإشعار', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال', 'danger');
    });
}

// حذف جميع الإشعارات المقروءة
function deleteAllRead() {
    if (!confirm('هل تريد حذف جميع الإشعارات المقروءة؟')) {
        return;
    }
    
    fetch('<?= App::url('doctor/delete-read-notifications') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.querySelectorAll('.notification-item:not(.unread)').forEach(item => {
                item.style.opacity = '0';
                setTimeout(() => {
                    item.remove();
                }, 300);
            });
            
            updateNotificationCount();
            showAlert('تم حذف جميع الإشعارات المقروءة', 'success');
        } else {
            showAlert('خطأ في حذف الإشعارات', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال', 'danger');
    });
}

// تحميل المزيد من الإشعارات
function loadMoreNotifications() {
    if (isLoading) return;
    
    isLoading = true;
    currentPage++;
    
    fetch(`<?= App::url('doctor/load-more-notifications') ?>?page=${currentPage}`)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.notifications.length > 0) {
            const list = document.querySelector('.list-group');
            data.notifications.forEach(notification => {
                const item = createNotificationItem(notification);
                list.appendChild(item);
            });
        }
        isLoading = false;
    })
    .catch(error => {
        console.error('Error:', error);
        isLoading = false;
    });
}

// إنشاء عنصر إشعار
function createNotificationItem(notification) {
    const item = document.createElement('div');
    item.className = `list-group-item list-group-item-action notification-item ${!notification.is_read ? 'unread' : ''}`;
    item.dataset.id = notification.id;
    item.dataset.type = notification.type;
    
    item.innerHTML = `
        <div class="d-flex align-items-start">
            <div class="flex-shrink-0 me-3">
                <div class="notification-icon bg-${getTypeColor(notification.type)} bg-opacity-10 rounded-circle p-2">
                    <i class="bi ${getTypeIcon(notification.type)} text-${getTypeColor(notification.type)}"></i>
                </div>
            </div>
            <div class="flex-grow-1">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1 ${!notification.is_read ? 'fw-bold' : ''}">
                            ${notification.title}
                            ${!notification.is_read ? '<span class="badge bg-danger ms-2">جديد</span>' : ''}
                        </h6>
                        <p class="mb-1 text-muted">${notification.message}</p>
                        <small class="text-muted">
                            <i class="bi bi-clock me-1"></i>
                            ${formatTime(notification.created_at)}
                            <span class="mx-2">•</span>
                            <span class="badge bg-${getTypeColor(notification.type)} bg-opacity-25 text-${getTypeColor(notification.type)}">
                                ${getTypeLabel(notification.type)}
                            </span>
                        </small>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-three-dots-vertical"></i>
                        </button>
                        <ul class="dropdown-menu">
                            ${!notification.is_read ? `
                                <li>
                                    <a class="dropdown-item" href="#" onclick="markAsRead(${notification.id})">
                                        <i class="bi bi-check me-2"></i>
                                        تحديد كمقروء
                                    </a>
                                </li>
                            ` : ''}
                            <li>
                                <a class="dropdown-item text-danger" href="#" onclick="deleteNotification(${notification.id})">
                                    <i class="bi bi-trash me-2"></i>
                                    حذف
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    return item;
}

// تحديث عداد الإشعارات
function updateNotificationCount() {
    const unreadCount = document.querySelectorAll('.notification-item.unread').length;
    const badge = document.querySelector('.badge.bg-danger');
    
    if (unreadCount === 0) {
        badge?.remove();
    } else if (badge) {
        badge.textContent = unreadCount;
    } else {
        // إضافة البادج إذا لم يكن موجوداً
        const button = document.querySelector('a[href*="notifications"]');
        if (button) {
            const newBadge = document.createElement('span');
            newBadge.className = 'position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger';
            newBadge.textContent = unreadCount;
            button.appendChild(newBadge);
        }
    }
}

// دوال مساعدة
function getTypeColor(type) {
    const colors = {
        'appointment': 'primary',
        'prescription': 'success',
        'general': 'info',
        'system': 'warning'
    };
    return colors[type] || 'secondary';
}

function getTypeIcon(type) {
    const icons = {
        'appointment': 'bi-calendar-check',
        'prescription': 'bi-prescription2',
        'general': 'bi-info-circle',
        'system': 'bi-gear'
    };
    return icons[type] || 'bi-bell';
}

function getTypeLabel(type) {
    const labels = {
        'appointment': 'موعد',
        'prescription': 'وصفة طبية',
        'general': 'عام',
        'system': 'نظام'
    };
    return labels[type] || type;
}

function formatTime(createdAt) {
    const time = new Date(createdAt);
    const now = new Date();
    const diff = now - time;
    
    if (diff < 60000) {
        return 'الآن';
    } else if (diff < 3600000) {
        const minutes = Math.floor(diff / 60000);
        return `منذ ${minutes} دقيقة`;
    } else if (diff < 86400000) {
        const hours = Math.floor(diff / 3600000);
        return `منذ ${hours} ساعة`;
    } else if (diff < 604800000) {
        const days = Math.floor(diff / 86400000);
        return `منذ ${days} يوم`;
    } else {
        return time.toLocaleDateString('ar-SA');
    }
}

// إظهار تنبيه
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحديث عداد الإشعارات
    updateNotificationCount();
    
    // إضافة مستمع للبحث
    const searchInput = document.getElementById('searchNotifications');
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            searchNotifications(this.value);
        }, 300);
    });
});
</script> 