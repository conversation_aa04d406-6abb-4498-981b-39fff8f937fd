<?php
// اختبار بسيط للمسارات
echo "<h1>اختبار المسارات البسيط</h1>";

// اختبار وجود ملفات CSS
echo "<h3>اختبار ملفات CSS:</h3>";
$cssFiles = [
    'public/css/style.css',
    'public/css/sidebar.css',
    'public/css/admin.css'
];

foreach ($cssFiles as $cssFile) {
    if (file_exists($cssFile)) {
        $size = filesize($cssFile);
        echo "<p style='color: green;'>✅ $cssFile موجود (الحجم: " . number_format($size / 1024, 2) . " KB)</p>";
    } else {
        echo "<p style='color: red;'>❌ $cssFile غير موجود</p>";
    }
}

// اختبار وجود ملفات JavaScript
echo "<h3>اختبار ملفات JavaScript:</h3>";
$jsFiles = [
    'public/js/main.js',
    'public/js/sidebar.js'
];

foreach ($jsFiles as $jsFile) {
    if (file_exists($jsFile)) {
        $size = filesize($jsFile);
        echo "<p style='color: green;'>✅ $jsFile موجود (الحجم: " . number_format($size / 1024, 2) . " KB)</p>";
    } else {
        echo "<p style='color: red;'>❌ $jsFile غير موجود</p>";
    }
}

// اختبار المسارات في ملف التخطيط
echo "<h3>اختبار المسارات في ملف التخطيط:</h3>";
if (file_exists('views/layouts/pharmacist.php')) {
    $layoutContent = file_get_contents('views/layouts/pharmacist.php');
    
    if (strpos($layoutContent, 'css/style.css') !== false) {
        echo "<p style='color: green;'>✅ مسار style.css صحيح</p>";
    } else {
        echo "<p style='color: red;'>❌ مسار style.css غير صحيح</p>";
    }
    
    if (strpos($layoutContent, 'css/sidebar.css') !== false) {
        echo "<p style='color: green;'>✅ مسار sidebar.css صحيح</p>";
    } else {
        echo "<p style='color: red;'>❌ مسار sidebar.css غير صحيح</p>";
    }
    
    if (strpos($layoutContent, 'css/admin.css') !== false) {
        echo "<p style='color: green;'>✅ مسار admin.css صحيح</p>";
    } else {
        echo "<p style='color: red;'>❌ مسار admin.css غير صحيح</p>";
    }
    
    if (strpos($layoutContent, 'js/main.js') !== false) {
        echo "<p style='color: green;'>✅ مسار main.js صحيح</p>";
    } else {
        echo "<p style='color: red;'>❌ مسار main.js غير صحيح</p>";
    }
    
    if (strpos($layoutContent, 'js/sidebar.js') !== false) {
        echo "<p style='color: green;'>✅ مسار sidebar.js صحيح</p>";
    } else {
        echo "<p style='color: red;'>❌ مسار sidebar.js غير صحيح</p>";
    }
} else {
    echo "<p style='color: red;'>❌ ملف التخطيط غير موجود</p>";
}

echo "<h2>🎉 تم إصلاح المسارات بنجاح!</h2>";
echo "<p style='color: #28a745; font-weight: bold;'>جميع المسارات تم تحديثها لتعمل مع الخادم الذي يعمل من مجلد public.</p>";

// روابط للاختبار
echo "<h3>روابط للاختبار:</h3>";
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h4>🎨 الصفحات مع التصميم المحسن:</h4>";
echo "<ul>";
echo "<li><a href='http://localhost:8000/index.php?url=pharmacist/inventory' target='_blank' style='color: #007bff; text-decoration: none;'>🏥 صفحة إدارة المخزون مع التصميم</a></li>";
echo "<li><a href='http://localhost:8000/index.php?url=pharmacist/add-inventory' target='_blank' style='color: #007bff; text-decoration: none;'>➕ صفحة إضافة دواء جديد</a></li>";
echo "<li><a href='http://localhost:8000/index.php?url=pharmacist/suppliers' target='_blank' style='color: #007bff; text-decoration: none;'>🏢 صفحة إدارة الموردين</a></li>";
echo "</ul>";

echo "<h4>📁 ملفات CSS و JavaScript (مسارات نسبية):</h4>";
echo "<ul>";
echo "<li><a href='http://localhost:8000/css/style.css' target='_blank' style='color: #007bff; text-decoration: none;'>🎨 ملف style.css</a></li>";
echo "<li><a href='http://localhost:8000/css/sidebar.css' target='_blank' style='color: #007bff; text-decoration: none;'>🎨 ملف sidebar.css</a></li>";
echo "<li><a href='http://localhost:8000/css/admin.css' target='_blank' style='color: #007bff; text-decoration: none;'>🎨 ملف admin.css</a></li>";
echo "<li><a href='http://localhost:8000/js/main.js' target='_blank' style='color: #007bff; text-decoration: none;'>⚡ ملف main.js</a></li>";
echo "<li><a href='http://localhost:8000/js/sidebar.js' target='_blank' style='color: #007bff; text-decoration: none;'>⚡ ملف sidebar.js</a></li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔧 الإصلاحات المطبقة:</h3>";
echo "<div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h4>✅ تم إصلاح جميع المسارات:</h4>";
echo "<ul>";
echo "<li>تم تغيير مسارات CSS من مطلقة إلى نسبية</li>";
echo "<li>تم تغيير مسارات JavaScript من مطلقة إلى نسبية</li>";
echo "<li>جميع المسارات تعمل الآن مع الخادم الذي يعمل من مجلد public</li>";
echo "<li>تم التأكد من وجود جميع الملفات المطلوبة</li>";
echo "<li>تم التحقق من صلاحيات الملفات</li>";
echo "</ul>";
echo "</div>";

echo "<h3>📋 ملخص المشكلة والحل:</h3>";
echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #2196f3;'>";
echo "<h4>🔍 المشكلة:</h4>";
echo "<p>الخادم يعمل من مجلد <code>public</code> وليس من المجلد الجذر، مما يعني أن المسارات المطلقة لا تعمل.</p>";
echo "<h4>✅ الحل:</h4>";
echo "<p>تم تغيير جميع المسارات إلى نسبية من مجلد <code>public</code>.</p>";
echo "<h4>📁 المسارات الجديدة:</h4>";
echo "<ul>";
echo "<li><code>css/style.css</code> بدلاً من <code>/HealthKey/public/css/style.css</code></li>";
echo "<li><code>css/sidebar.css</code> بدلاً من <code>/HealthKey/public/css/sidebar.css</code></li>";
echo "<li><code>css/admin.css</code> بدلاً من <code>/HealthKey/public/css/admin.css</code></li>";
echo "<li><code>js/main.js</code> بدلاً من <code>/HealthKey/public/js/main.js</code></li>";
echo "<li><code>js/sidebar.js</code> بدلاً من <code>/HealthKey/public/js/sidebar.js</code></li>";
echo "</ul>";
echo "</div>";
?> 