<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نتائج فحص المختبر - <?= htmlspecialchars($test['test_name']) ?></title>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #fff;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #28a745;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #28a745;
            margin: 0;
            font-size: 2rem;
        }
        
        .header p {
            color: #666;
            margin: 5px 0;
        }
        
        .patient-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .patient-info h3 {
            color: #28a745;
            margin-bottom: 15px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        
        .info-value {
            color: #333;
        }
        
        .test-info {
            background-color: #d4edda;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border: 1px solid #c3e6cb;
        }
        
        .test-info h3 {
            color: #155724;
            margin-bottom: 15px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section-title {
            color: #28a745;
            border-bottom: 2px solid #28a745;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .results-table th,
        .results-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: center;
        }
        
        .results-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        
        .normal {
            background-color: #d4edda;
        }
        
        .high {
            background-color: #f8d7da;
        }
        
        .low {
            background-color: #fff3cd;
        }
        
        .content-item {
            margin-bottom: 15px;
        }
        
        .content-label {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        
        .content-value {
            color: #333;
            line-height: 1.5;
        }
        
        .status-badge {
            background-color: #28a745;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: inline-block;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #666;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }
            
            .header {
                border-bottom-color: #000;
            }
            
            .section-title {
                border-bottom-color: #000;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>نتائج فحص المختبر</h1>
        <p>نظام HealthKey لإدارة الرعاية الصحية</p>
        <p>تاريخ التصدير: <?= date('Y-m-d H:i') ?></p>
    </div>
    
    <!-- Patient Information -->
    <div class="patient-info">
        <h3>معلومات المريض</h3>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">الاسم:</span>
                <span class="info-value"><?= htmlspecialchars($patient['first_name'] . ' ' . $patient['last_name']) ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">رقم الهوية:</span>
                <span class="info-value"><?= htmlspecialchars($patient['national_id'] ?? 'غير محدد') ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">تاريخ الميلاد:</span>
                <span class="info-value"><?= $patient['date_of_birth'] ? date('Y-m-d', strtotime($patient['date_of_birth'])) : 'غير محدد' ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">الجنس:</span>
                <span class="info-value"><?= $patient['gender'] === 'male' ? 'ذكر' : ($patient['gender'] === 'female' ? 'أنثى' : 'غير محدد') ?></span>
            </div>
        </div>
    </div>
    
    <!-- Test Information -->
    <div class="test-info">
        <h3>معلومات الفحص</h3>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">اسم الفحص:</span>
                <span class="info-value"><?= htmlspecialchars($test['test_name']) ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">تاريخ الفحص:</span>
                <span class="info-value"><?= date('Y-m-d', strtotime($test['test_date'])) ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">الطبيب:</span>
                <span class="info-value"><?= htmlspecialchars($test['doctor_name']) ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">الحالة:</span>
                <span class="info-value">
                    <span class="status-badge"><?= MedicalRecord::getLabTestStatusLabel($test['status']) ?></span>
                </span>
            </div>
        </div>
        
        <?php if (!empty($test['test_description'])): ?>
            <div class="content-item">
                <div class="content-label">وصف الفحص:</div>
                <div class="content-value"><?= htmlspecialchars($test['test_description']) ?></div>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($test['instructions'])): ?>
            <div class="content-item">
                <div class="content-label">تعليمات الفحص:</div>
                <div class="content-value"><?= htmlspecialchars($test['instructions']) ?></div>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Test Results -->
    <?php if ($test['status'] === 'completed' && !empty($test['results'])): ?>
        <div class="section">
            <h2 class="section-title">نتائج الفحص</h2>
            <table class="results-table">
                <thead>
                    <tr>
                        <th>المعامل</th>
                        <th>النتيجة</th>
                        <th>النطاق الطبيعي</th>
                        <th>الوحدة</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($test['results'] as $result): ?>
                        <tr class="<?= $result['status'] ?? 'normal' ?>">
                            <td><?= htmlspecialchars($result['parameter']) ?></td>
                            <td><strong><?= htmlspecialchars($result['value']) ?></strong></td>
                            <td><?= htmlspecialchars($result['normal_range']) ?></td>
                            <td><?= htmlspecialchars($result['unit']) ?></td>
                            <td>
                                <?php
                                $status = $result['status'] ?? 'normal';
                                $statusLabel = $status === 'normal' ? 'طبيعي' : ($status === 'high' ? 'مرتفع' : 'منخفض');
                                ?>
                                <?= $statusLabel ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <?php if (!empty($test['interpretation'])): ?>
                <div class="content-item" style="margin-top: 20px;">
                    <div class="content-label">تفسير النتائج:</div>
                    <div class="content-value"><?= htmlspecialchars($test['interpretation']) ?></div>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($test['recommendations'])): ?>
                <div class="content-item">
                    <div class="content-label">التوصيات:</div>
                    <div class="content-value"><?= htmlspecialchars($test['recommendations']) ?></div>
                </div>
            <?php endif; ?>
        </div>
    <?php else: ?>
        <div class="section">
            <h2 class="section-title">حالة الفحص</h2>
            <div class="content-item">
                <div class="content-value">
                    <?php if ($test['status'] === 'pending'): ?>
                        <p>الفحص في انتظار الإجراء</p>
                    <?php elseif ($test['status'] === 'in_progress'): ?>
                        <p>الفحص قيد الإجراء في المختبر</p>
                    <?php elseif ($test['status'] === 'cancelled'): ?>
                        <p>تم إلغاء الفحص</p>
                    <?php else: ?>
                        <p>لا توجد نتائج متاحة حالياً</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
    
    <!-- Test Notes -->
    <?php if (!empty($test['notes'])): ?>
        <div class="section">
            <h2 class="section-title">ملاحظات</h2>
            <div class="content-value"><?= htmlspecialchars($test['notes']) ?></div>
        </div>
    <?php endif; ?>
    
    <!-- Footer -->
    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام HealthKey</p>
        <p>جميع الحقوق محفوظة &copy; <?= date('Y') ?></p>
    </div>
</body>
</html> 