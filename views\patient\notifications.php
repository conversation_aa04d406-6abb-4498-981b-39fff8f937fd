<!-- <PERSON> Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-bell me-2"></i>
            الإشعارات
        </h1>
        <p class="text-muted">إدارة إشعاراتك والتنبيهات المهمة</p>
    </div>
    <div>
        <a href="<?= App::url('patient/dashboard') ?>" class="btn btn-outline-primary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['total'] ?? 0 ?></h4>
                        <p class="mb-0">إجمالي الإشعارات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-bell fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $unreadCount ?></h4>
                        <p class="mb-0">غير مقروءة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-envelope fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['appointment'] ?? 0 ?></h4>
                        <p class="mb-0">مواعيد</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-check fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['prescription'] ?? 0 ?></h4>
                        <p class="mb-0">وصفات طبية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-prescription2 fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Actions -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="search" 
                               placeholder="البحث في الإشعارات..." 
                               value="<?= htmlspecialchars($search) ?>">
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="type">
                            <option value="">جميع الأنواع</option>
                            <option value="appointment" <?= $selectedType === 'appointment' ? 'selected' : '' ?>>مواعيد</option>
                            <option value="prescription" <?= $selectedType === 'prescription' ? 'selected' : '' ?>>وصفات طبية</option>
                            <option value="general" <?= $selectedType === 'general' ? 'selected' : '' ?>>عام</option>
                            <option value="system" <?= $selectedType === 'system' ? 'selected' : '' ?>>نظام</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="unread" <?= $selectedStatus === 'unread' ? 'selected' : '' ?>>غير مقروءة</option>
                            <option value="read" <?= $selectedStatus === 'read' ? 'selected' : '' ?>>مقروءة</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </form>
            </div>
            <div class="col-md-4 text-end">
                <button type="button" class="btn btn-success me-2" id="markAllRead">
                    <i class="bi bi-check-all me-2"></i>
                    تسجيل الكل كمقروء
                </button>
                <a href="<?= App::url('patient/notifications') ?>" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-clockwise"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Notifications List -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-list-ul me-2"></i>
            الإشعارات (<?= $totalNotifications ?>)
        </h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($notifications)): ?>
            <div class="text-center py-5">
                <i class="bi bi-bell-slash fs-1 text-muted"></i>
                <h5 class="mt-3 text-muted">لا توجد إشعارات</h5>
                <p class="text-muted">ستظهر هنا الإشعارات الجديدة عند وصولها</p>
            </div>
        <?php else: ?>
            <div class="list-group list-group-flush">
                <?php foreach ($notifications as $notification): ?>
                    <div class="list-group-item list-group-item-action notification-item <?= !$notification['is_read'] ? 'unread' : '' ?>" 
                         data-id="<?= $notification['id'] ?>">
                        <div class="d-flex w-100 justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="badge bg-<?= Notification::getTypeColor($notification['type']) ?> me-2">
                                        <i class="bi <?= Notification::getTypeIcon($notification['type']) ?>"></i>
                                        <?= Notification::getTypeLabel($notification['type']) ?>
                                    </span>
                                    <?php if (!$notification['is_read']): ?>
                                        <span class="badge bg-warning">جديد</span>
                                    <?php endif; ?>
                                </div>
                                <h6 class="mb-1 <?= !$notification['is_read'] ? 'fw-bold' : '' ?>">
                                    <?= htmlspecialchars($notification['title']) ?>
                                </h6>
                                <p class="mb-1 text-muted">
                                    <?= htmlspecialchars($notification['message']) ?>
                                </p>
                                <small class="text-muted">
                                    <i class="bi bi-clock me-1"></i>
                                    <?= Notification::formatTime($notification['created_at']) ?>
                                </small>
                            </div>
                            <div class="ms-3">
                                <div class="btn-group btn-group-sm">
                                    <?php if (!$notification['is_read']): ?>
                                        <button type="button" class="btn btn-outline-success btn-sm mark-read" 
                                                data-id="<?= $notification['id'] ?>" title="تسجيل كمقروء">
                                            <i class="bi bi-check"></i>
                                        </button>
                                    <?php endif; ?>
                                    <button type="button" class="btn btn-outline-danger btn-sm delete-notification" 
                                            data-id="<?= $notification['id'] ?>" title="حذف">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="card-footer">
                    <nav aria-label="صفحات الإشعارات">
                        <ul class="pagination justify-content-center mb-0">
                            <?php if ($currentPage > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?= App::url('patient/notifications', ['page' => $currentPage - 1, 'type' => $selectedType, 'status' => $selectedStatus, 'search' => $search]) ?>">
                                        <i class="bi bi-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++): ?>
                                <li class="page-item <?= $i === $currentPage ? 'active' : '' ?>">
                                    <a class="page-link" href="<?= App::url('patient/notifications', ['page' => $i, 'type' => $selectedType, 'status' => $selectedStatus, 'search' => $search]) ?>">
                                        <?= $i ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($currentPage < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?= App::url('patient/notifications', ['page' => $currentPage + 1, 'type' => $selectedType, 'status' => $selectedStatus, 'search' => $search]) ?>">
                                        <i class="bi bi-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</div>

<style>
.notification-item.unread {
    background-color: #f8f9fa;
    border-left: 4px solid #ffc107;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item .btn-group {
    opacity: 0;
    transition: opacity 0.2s;
}

.notification-item:hover .btn-group {
    opacity: 1;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تسجيل الإشعار كمقروء
    document.querySelectorAll('.mark-read').forEach(button => {
        button.addEventListener('click', function() {
            const notificationId = this.dataset.id;
            markNotificationAsRead(notificationId);
        });
    });

    // حذف الإشعار
    document.querySelectorAll('.delete-notification').forEach(button => {
        button.addEventListener('click', function() {
            const notificationId = this.dataset.id;
            if (confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
                deleteNotification(notificationId);
            }
        });
    });

    // تسجيل جميع الإشعارات كمقروءة
    document.getElementById('markAllRead').addEventListener('click', function() {
        if (confirm('هل تريد تسجيل جميع الإشعارات كمقروءة؟')) {
            markAllNotificationsAsRead();
        }
    });
});

function markNotificationAsRead(notificationId) {
    fetch('<?= App::url('patient/mark-notification-read') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'notification_id=' + notificationId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إزالة علامة "جديد" وتحديث المظهر
            const notificationItem = document.querySelector(`[data-id="${notificationId}"]`);
            if (notificationItem) {
                notificationItem.classList.remove('unread');
                const newBadge = notificationItem.querySelector('.badge.bg-warning');
                if (newBadge) newBadge.remove();
                const title = notificationItem.querySelector('h6');
                if (title) title.classList.remove('fw-bold');
                
                // إزالة زر "تسجيل كمقروء"
                const markReadBtn = notificationItem.querySelector('.mark-read');
                if (markReadBtn) markReadBtn.remove();
            }
            
            // تحديث العداد
            updateNotificationCount();
            showAlert('success', data.message);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ أثناء تسجيل الإشعار كمقروء');
    });
}

function deleteNotification(notificationId) {
    fetch('<?= App::url('patient/delete-notification') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'notification_id=' + notificationId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إزالة العنصر من الصفحة
            const notificationItem = document.querySelector(`[data-id="${notificationId}"]`);
            if (notificationItem) {
                notificationItem.remove();
            }
            
            // تحديث العداد
            updateNotificationCount();
            showAlert('success', data.message);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ أثناء حذف الإشعار');
    });
}

function markAllNotificationsAsRead() {
    fetch('<?= App::url('patient/mark-all-notifications-read') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث جميع الإشعارات في الصفحة
            document.querySelectorAll('.notification-item.unread').forEach(item => {
                item.classList.remove('unread');
                const newBadge = item.querySelector('.badge.bg-warning');
                if (newBadge) newBadge.remove();
                const title = item.querySelector('h6');
                if (title) title.classList.remove('fw-bold');
                
                // إزالة أزرار "تسجيل كمقروء"
                const markReadBtn = item.querySelector('.mark-read');
                if (markReadBtn) markReadBtn.remove();
            });
            
            // تحديث العداد
            updateNotificationCount();
            showAlert('success', data.message);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ أثناء تسجيل الإشعارات كمقروءة');
    });
}

function updateNotificationCount() {
    // تحديث عداد الإشعارات غير المقروءة في الشريط الجانبي
    const unreadCount = document.querySelectorAll('.notification-item.unread').length;
    const sidebarBadge = document.querySelector('.nav-link[href*="notifications"] .nav-badge');
    if (sidebarBadge) {
        sidebarBadge.textContent = unreadCount;
        if (unreadCount === 0) {
            sidebarBadge.style.display = 'none';
        }
    }
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه تلقائياً بعد 3 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}
</script> 