<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-chat-dots me-2"></i>
            عرض الرسالة
        </h1>
        <p class="text-muted">تفاصيل الرسالة والمعلومات المرتبطة</p>
    </div>
    <div>
        <a href="<?= App::url('patient/messages') ?>" class="btn btn-outline-primary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للرسائل
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Message Details -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-envelope me-2"></i>
                        تفاصيل الرسالة
                    </h5>
                    <div>
                        <span class="badge bg-<?= Message::getTypeColor($message['type']) ?> me-2">
                            <i class="bi <?= Message::getTypeIcon($message['type']) ?>"></i>
                            <?= Message::getTypeLabel($message['type']) ?>
                        </span>
                        <span class="badge bg-<?= Message::getPriorityColor($message['priority']) ?>">
                            <?= ucfirst($message['priority']) ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>الموضوع:</strong>
                        <p class="mb-0"><?= htmlspecialchars($message['subject']) ?></p>
                    </div>
                    <div class="col-md-6">
                        <strong>التاريخ:</strong>
                        <p class="mb-0"><?= date('Y-m-d H:i', strtotime($message['created_at'])) ?></p>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>من:</strong>
                        <p class="mb-0">
                            <?= htmlspecialchars($message['sender_name']) ?>
                            <span class="badge bg-secondary ms-2"><?= ucfirst($message['sender_type']) ?></span>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <strong>إلى:</strong>
                        <p class="mb-0">
                            <?= htmlspecialchars($message['recipient_name']) ?>
                            <span class="badge bg-secondary ms-2"><?= ucfirst($message['recipient_type']) ?></span>
                        </p>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>الحالة:</strong>
                        <p class="mb-0">
                            <?php
                            $statusLabels = [
                                'pending' => 'في الانتظار',
                                'sent' => 'تم الإرسال',
                                'read' => 'مقروءة',
                                'deleted' => 'محذوفة'
                            ];
                            $statusColors = [
                                'pending' => 'warning',
                                'sent' => 'info',
                                'read' => 'success',
                                'deleted' => 'danger'
                            ];
                            ?>
                            <span class="badge bg-<?= $statusColors[$message['status']] ?? 'secondary' ?>">
                                <?= $statusLabels[$message['status']] ?? $message['status'] ?>
                            </span>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <strong>وقت الإرسال:</strong>
                        <p class="mb-0">
                            <?= $message['sent_time'] ? date('Y-m-d H:i', strtotime($message['sent_time'])) : 'لم يتم الإرسال بعد' ?>
                        </p>
                    </div>
                </div>
                
                <?php if ($message['read_time']): ?>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>وقت القراءة:</strong>
                        <p class="mb-0"><?= date('Y-m-d H:i', strtotime($message['read_time'])) ?></p>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Message Content -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-file-text me-2"></i>
                    محتوى الرسالة
                </h5>
            </div>
            <div class="card-body">
                <div class="message-content">
                    <?= nl2br(htmlspecialchars($message['content'])) ?>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Message Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gear me-2"></i>
                    الإجراءات
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if ($message['status'] === 'sent' && $message['recipient_id'] == $this->currentUser['id']): ?>
                        <button type="button" class="btn btn-success" id="markRead">
                            <i class="bi bi-check-circle me-2"></i>
                            تسجيل كمقروء
                        </button>
                    <?php endif; ?>
                    
                    <a href="<?= App::url('patient/compose-message') ?>" class="btn btn-primary">
                        <i class="bi bi-reply me-2"></i>
                        رد
                    </a>
                    
                    <button type="button" class="btn btn-danger" id="deleteMessage">
                        <i class="bi bi-trash me-2"></i>
                        حذف
                    </button>
                </div>
            </div>
        </div>

        <!-- Message Info -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    معلومات الرسالة
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <strong>معرف الرسالة:</strong>
                        <span class="text-muted">#<?= $message['id'] ?></span>
                    </li>
                    <li class="mb-2">
                        <strong>النوع:</strong>
                        <span class="text-muted"><?= Message::getTypeLabel($message['type']) ?></span>
                    </li>
                    <li class="mb-2">
                        <strong>الأولوية:</strong>
                        <span class="text-muted"><?= ucfirst($message['priority']) ?></span>
                    </li>
                    <li class="mb-2">
                        <strong>تاريخ الإنشاء:</strong>
                        <span class="text-muted"><?= Message::formatTime($message['created_at']) ?></span>
                    </li>
                    <?php if ($message['sent_time']): ?>
                    <li class="mb-2">
                        <strong>تاريخ الإرسال:</strong>
                        <span class="text-muted"><?= Message::formatTime($message['sent_time']) ?></span>
                    </li>
                    <?php endif; ?>
                    <?php if ($message['read_time']): ?>
                    <li class="mb-2">
                        <strong>تاريخ القراءة:</strong>
                        <span class="text-muted"><?= Message::formatTime($message['read_time']) ?></span>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
.message-content {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
    border-left: 4px solid #007bff;
    white-space: pre-wrap;
    line-height: 1.6;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تسجيل الرسالة كمقروءة
    const markReadBtn = document.getElementById('markRead');
    if (markReadBtn) {
        markReadBtn.addEventListener('click', function() {
            markMessageAsRead(<?= $message['id'] ?>);
        });
    }

    // حذف الرسالة
    const deleteBtn = document.getElementById('deleteMessage');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', function() {
            if (confirm('هل أنت متأكد من حذف هذه الرسالة؟')) {
                deleteMessage(<?= $message['id'] ?>);
            }
        });
    }
});

function markMessageAsRead(messageId) {
    fetch('<?= App::url('patient/mark-message-read') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'message_id=' + messageId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            // إخفاء زر تسجيل كمقروء
            const markReadBtn = document.getElementById('markRead');
            if (markReadBtn) {
                markReadBtn.style.display = 'none';
            }
            // تحديث حالة الرسالة في الواجهة
            updateMessageStatus('read');
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ أثناء تسجيل الرسالة كمقروءة');
    });
}

function deleteMessage(messageId) {
    fetch('<?= App::url('patient/delete-message') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'message_id=' + messageId
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            // إعادة التوجيه إلى صفحة الرسائل بعد ثانيتين
            setTimeout(() => {
                window.location.href = '<?= App::url('patient/messages') ?>';
            }, 2000);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ أثناء حذف الرسالة');
    });
}

function updateMessageStatus(status) {
    // تحديث عرض حالة الرسالة في الواجهة
    const statusElement = document.querySelector('.badge.bg-info');
    if (statusElement) {
        statusElement.textContent = status === 'read' ? 'مقروءة' : 'تم الإرسال';
        statusElement.className = 'badge bg-success';
    }
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه تلقائياً بعد 3 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}
</script> 