<?php
/**
 * سكريبت إصلاح قاعدة البيانات
 * يقوم بإصلاح المشاكل الموجودة في هيكل قاعدة البيانات
 */

require_once '../config/database.php';

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    );

    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n";

    // التحقق من وجود الجداول المطلوبة
    $tables = [
        'users',
        'doctors', 
        'pharmacists',
        'appointments',
        'prescriptions',
        'medical_records',
        'notifications',
        'activity_logs',
        'system_settings'
    ];

    echo "\n🔍 التحقق من وجود الجداول:\n";
    foreach ($tables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        
        if ($stmt->rowCount() > 0) {
            echo "✅ الجدول $table موجود\n";
        } else {
            echo "❌ الجدول $table غير موجود\n";
        }
    }

    // التحقق من هيكل جدول activity_logs
    echo "\n🔍 التحقق من هيكل جدول activity_logs:\n";
    $stmt = $pdo->query("DESCRIBE activity_logs");
    $columns = $stmt->fetchAll();
    
    $hasDescription = false;
    $hasDetails = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'description') {
            $hasDescription = true;
            echo "✅ العمود description موجود\n";
        }
        if ($column['Field'] === 'details') {
            $hasDetails = true;
            echo "⚠️ العمود details موجود (قديم)\n";
        }
    }

    // إصلاح جدول activity_logs إذا لزم الأمر
    if ($hasDetails && !$hasDescription) {
        echo "\n🔧 إصلاح جدول activity_logs: تغيير اسم العمود من details إلى description\n";
        $pdo->exec("ALTER TABLE activity_logs CHANGE details description TEXT");
        echo "✅ تم إصلاح جدول activity_logs\n";
    } elseif (!$hasDescription && !$hasDetails) {
        echo "\n🔧 إضافة العمود description إلى جدول activity_logs\n";
        $pdo->exec("ALTER TABLE activity_logs ADD COLUMN description TEXT AFTER action");
        echo "✅ تم إضافة العمود description\n";
    }

    // التحقق من البيانات الأساسية
    echo "\n🔍 التحقق من البيانات الأساسية:\n";
    
    // التحقق من وجود المدير
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE user_type = 'admin'");
    $stmt->execute();
    $adminCount = $stmt->fetch()['count'];
    
    if ($adminCount > 0) {
        echo "✅ يوجد $adminCount مدير في النظام\n";
    } else {
        echo "⚠️ لا يوجد مدير في النظام\n";
        
        // إضافة مدير افتراضي
        echo "🔧 إضافة مدير افتراضي...\n";
        $hashedPassword = password_hash('password', PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("
            INSERT INTO users (first_name, last_name, email, password, user_type, is_active, email_verified_at) 
            VALUES ('مدير', 'النظام', '<EMAIL>', ?, 'admin', 1, NOW())
        ");
        $stmt->execute([$hashedPassword]);
        echo "✅ تم إضافة المدير الافتراضي\n";
    }

    // التحقق من إعدادات النظام
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM system_settings");
    $stmt->execute();
    $settingsCount = $stmt->fetch()['count'];
    
    if ($settingsCount > 0) {
        echo "✅ يوجد $settingsCount إعداد في النظام\n";
    } else {
        echo "⚠️ لا توجد إعدادات في النظام\n";
        
        // إضافة إعدادات افتراضية
        echo "🔧 إضافة إعدادات افتراضية...\n";
        $defaultSettings = [
            ['app_name', 'HealthKey', 'string', 'اسم التطبيق', 1],
            ['app_version', '1.0.0', 'string', 'إصدار التطبيق', 1],
            ['timezone', 'Asia/Riyadh', 'string', 'المنطقة الزمنية', 0],
            ['date_format', 'Y-m-d', 'string', 'تنسيق التاريخ', 0],
            ['time_format', 'H:i', 'string', 'تنسيق الوقت', 0]
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO system_settings (`key`, `value`, `type`, `description`, `is_public`) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        foreach ($defaultSettings as $setting) {
            $stmt->execute($setting);
        }
        echo "✅ تم إضافة الإعدادات الافتراضية\n";
    }

    // اختبار العمليات الأساسية
    echo "\n🧪 اختبار العمليات الأساسية:\n";
    
    // اختبار INSERT
    try {
        $testKey = 'test_' . time();
        $stmt = $pdo->prepare("
            INSERT INTO system_settings (`key`, `value`, `type`, `description`, `is_public`) 
            VALUES (?, 'test_value', 'string', 'اختبار', 0)
        ");
        $stmt->execute([$testKey]);
        echo "✅ اختبار INSERT نجح\n";
        
        // حذف البيانات التجريبية
        $stmt = $pdo->prepare("DELETE FROM system_settings WHERE `key` = ?");
        $stmt->execute([$testKey]);
        echo "✅ اختبار DELETE نجح\n";
        
    } catch (Exception $e) {
        echo "❌ اختبار العمليات فشل: " . $e->getMessage() . "\n";
    }

    echo "\n🎉 تم إنجاز جميع الفحوصات والإصلاحات بنجاح!\n";
    echo "\n📋 ملخص:\n";
    echo "- تم التحقق من وجود جميع الجداول\n";
    echo "- تم إصلاح هيكل جدول activity_logs\n";
    echo "- تم التحقق من البيانات الأساسية\n";
    echo "- تم اختبار العمليات الأساسية\n";
    echo "\n✅ قاعدة البيانات جاهزة للاستخدام!\n";

} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    exit(1);
}
?>
