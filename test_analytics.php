<?php
// محاكاة جلسة المدير
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'admin';
$_SESSION['user_name'] = 'مدير النظام';

// تضمين الملفات المطلوبة
require_once 'app/core/App.php';
require_once 'app/core/Database.php';
require_once 'app/controllers/AdminController.php';

// إنشاء مثيل من AdminController
$controller = new AdminController();

// اختبار دالة analytics
echo "<h2>اختبار صفحة التحليلات</h2>";

try {
    // اختبار الحصول على البيانات التحليلية
    $startDate = date('Y-m-d', strtotime('-30 days'));
    $endDate = date('Y-m-d');
    
    echo "<h3>بيانات الاختبار:</h3>";
    echo "<ul>";
    echo "<li>تاريخ البداية: $startDate</li>";
    echo "<li>تاريخ النهاية: $endDate</li>";
    echo "</ul>";
    
    // اختبار الإحصائيات
    $reflection = new ReflectionClass($controller);
    $method = $reflection->getMethod('getAnalyticsStats');
    $method->setAccessible(true);
    
    $stats = $method->invoke($controller, $startDate, $endDate);
    
    echo "<h3>الإحصائيات:</h3>";
    echo "<pre>" . print_r($stats, true) . "</pre>";
    
    // اختبار بيانات النشاط
    $method = $reflection->getMethod('getActivityData');
    $method->setAccessible(true);
    
    $activityData = $method->invoke($controller, $startDate, $endDate);
    
    echo "<h3>بيانات النشاط:</h3>";
    echo "<pre>" . print_r($activityData, true) . "</pre>";
    
    // اختبار بيانات المستخدمين
    $method = $reflection->getMethod('getUsersData');
    $method->setAccessible(true);
    
    $usersData = $method->invoke($controller, $startDate, $endDate);
    
    echo "<h3>بيانات المستخدمين:</h3>";
    echo "<pre>" . print_r($usersData, true) . "</pre>";
    
    // اختبار أفضل الأطباء
    $method = $reflection->getMethod('getTopDoctors');
    $method->setAccessible(true);
    
    $topDoctors = $method->invoke($controller, $startDate, $endDate);
    
    echo "<h3>أفضل الأطباء:</h3>";
    echo "<pre>" . print_r($topDoctors, true) . "</pre>";
    
    // اختبار الأمراض الشائعة
    $method = $reflection->getMethod('getCommonDiseases');
    $method->setAccessible(true);
    
    $commonDiseases = $method->invoke($controller, $startDate, $endDate);
    
    echo "<h3>الأمراض الشائعة:</h3>";
    echo "<pre>" . print_r($commonDiseases, true) . "</pre>";
    
    echo "<h3>✅ تم اختبار جميع الدوال بنجاح!</h3>";
    
} catch (Exception $e) {
    echo "<h3>❌ خطأ في الاختبار:</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "<p>الملف: " . $e->getFile() . "</p>";
    echo "<p>السطر: " . $e->getLine() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحة التحليلات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4>اختبار صفحة التحليلات</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5>تعليمات الاختبار:</h5>
                            <ol>
                                <li>تأكد من أن قاعدة البيانات متصلة</li>
                                <li>تأكد من وجود بيانات في الجداول</li>
                                <li>راجع النتائج المعروضة أعلاه</li>
                                <li>إذا كانت النتائج صحيحة، يمكنك الوصول إلى صفحة التحليلات</li>
                            </ol>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                            <a href="<?= App::url('admin/analytics') ?>" class="btn btn-primary">
                                <i class="bi bi-graph-up"></i> الانتقال إلى صفحة التحليلات
                            </a>
                            <a href="<?= App::url('admin/dashboard') ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> العودة إلى لوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 