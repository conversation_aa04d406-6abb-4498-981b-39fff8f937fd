<?php
/**
 * اختبار وحل مشكلة عمود rating المفقود
 */

require_once 'config.php';

echo "<h1>حل مشكلة عمود rating المفقود</h1>";

try {
    $db = Database::getInstance();
    
    echo "<h2>1. فحص بنية جدول المواعيد</h2>";
    
    // فحص الأعمدة الموجودة في جدول المواعيد
    $columnsQuery = "DESCRIBE appointments";
    $columns = $db->select($columnsQuery);
    
    echo "<h3>الأعمدة الموجودة في جدول المواعيد:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>NULL</th><th>المفتاح</th><th>الافتراضي</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // فحص وجود عمود rating
    $hasRating = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'rating') {
            $hasRating = true;
            break;
        }
    }
    
    if (!$hasRating) {
        echo "<h2>2. إضافة عمود rating</h2>";
        
        // إضافة عمود rating
        $addRatingQuery = "ALTER TABLE appointments ADD COLUMN rating DECIMAL(3,1) NULL AFTER notes";
        
        try {
            $db->update($addRatingQuery);
            echo "✅ تم إضافة عمود rating بنجاح<br>";
        } catch (Exception $e) {
            echo "❌ فشل في إضافة عمود rating: " . $e->getMessage() . "<br>";
        }
        
        // إضافة عمود duration أيضاً
        echo "<h2>3. إضافة عمود duration</h2>";
        
        $addDurationQuery = "ALTER TABLE appointments ADD COLUMN duration INT NULL AFTER rating";
        
        try {
            $db->update($addDurationQuery);
            echo "✅ تم إضافة عمود duration بنجاح<br>";
        } catch (Exception $e) {
            echo "❌ فشل في إضافة عمود duration: " . $e->getMessage() . "<br>";
        }
        
    } else {
        echo "<h2>2. عمود rating موجود بالفعل</h2>";
        echo "✅ عمود rating موجود في الجدول<br>";
    }
    
    echo "<h2>4. اختبار الدوال المحدثة</h2>";
    
    // اختبار دالة getSatisfactionRate
    echo "<h3>اختبار دالة getSatisfactionRate:</h3>";
    
    require_once 'app/models/Appointment.php';
    $appointmentModel = new Appointment();
    
    try {
        $satisfactionRate = $appointmentModel->getSatisfactionRate(2, '2024-01-01', '2024-12-31');
        echo "✅ معدل رضا المرضى: " . $satisfactionRate . "%<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في getSatisfactionRate: " . $e->getMessage() . "<br>";
    }
    
    // اختبار دالة getAverageAppointmentTime
    echo "<h3>اختبار دالة getAverageAppointmentTime:</h3>";
    
    try {
        $avgTime = $appointmentModel->getAverageAppointmentTime(2, '2024-01-01', '2024-12-31');
        echo "✅ متوسط وقت الموعد: " . $avgTime . " دقيقة<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في getAverageAppointmentTime: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>5. اختبار صفحة التحليلات</h2>";
    
    // محاكاة تسجيل دخول طبيب
    $_SESSION['user_id'] = 2;
    $_SESSION['user_type'] = 'doctor';
    $_SESSION['user_name'] = 'د. أحمد محمد';
    
    echo "<p><a href='public/index.php?url=doctor/analytics' target='_blank'>فتح صفحة التحليلات</a></p>";
    
    echo "<h2>✅ انتهى الاختبار</h2>";
    echo "<p>إذا تم إضافة الأعمدة بنجاح وفتحت صفحة التحليلات بدون أخطاء، فهذا يعني أن المشكلة قد تم حلها.</p>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ عام</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 