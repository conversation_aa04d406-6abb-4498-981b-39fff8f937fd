<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-people me-2"></i>
            المرضى
        </h1>
        <p class="text-muted">إدارة قائمة المرضى وملفاتهم الطبية</p>
    </div>
    <div>
        <a href="<?= App::url('doctor/dashboard') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?= App::url('doctor/patients') ?>" class="row g-3">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="text" 
                           class="form-control" 
                           name="search" 
                           value="<?= htmlspecialchars($search) ?>"
                           placeholder="البحث بالاسم أو البريد الإلكتروني أو رقم الهاتف...">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search me-1"></i>
                        بحث
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex gap-2">
                    <a href="<?= App::url('doctor/patients') ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        إعادة تعيين
                    </a>
                    <?php if (!empty($search)): ?>
                        <span class="badge bg-info align-self-center">
                            نتائج البحث: <?= count($patients) ?> مريض
                        </span>
                    <?php endif; ?>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Patients List -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-list-ul me-2"></i>
            قائمة المرضى
            <span class="badge bg-primary ms-2"><?= count($patients) ?></span>
        </h5>
        <div>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                <i class="bi bi-printer me-1"></i>
                طباعة
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <?php if (empty($patients)): ?>
            <!-- Empty State -->
            <div class="text-center py-5">
                <i class="bi bi-people display-1 text-muted"></i>
                <h4 class="mt-3 text-muted">لا توجد مرضى</h4>
                <p class="text-muted">
                    <?php if (!empty($search)): ?>
                        لم يتم العثور على مرضى يطابقون البحث: "<?= htmlspecialchars($search) ?>"
                    <?php else: ?>
                        لم يتم تسجيل أي مرضى بعد
                    <?php endif; ?>
                </p>
                <?php if (!empty($search)): ?>
                    <a href="<?= App::url('doctor/patients') ?>" class="btn btn-primary">
                        <i class="bi bi-arrow-clockwise me-2"></i>
                        عرض جميع المرضى
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <!-- Patients Table -->
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>المريض</th>
                            <th>معلومات الاتصال</th>
                            <th>المعلومات الشخصية</th>
                            <th>آخر موعد</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($patients as $patient): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                            <i class="bi bi-person text-primary"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?= htmlspecialchars(User::getFullName($patient)) ?></h6>
                                            <small class="text-muted">
                                                رقم الهوية: <?= htmlspecialchars($patient['national_id'] ?? 'غير محدد') ?>
                                            </small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="mb-1">
                                            <i class="bi bi-envelope text-muted me-1"></i>
                                            <a href="mailto:<?= htmlspecialchars($patient['email']) ?>" class="text-decoration-none">
                                                <?= htmlspecialchars($patient['email']) ?>
                                            </a>
                                        </div>
                                        <div>
                                            <i class="bi bi-telephone text-muted me-1"></i>
                                            <a href="tel:<?= htmlspecialchars($patient['phone']) ?>" class="text-decoration-none">
                                                <?= htmlspecialchars($patient['phone']) ?>
                                            </a>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <div class="mb-1">
                                            <i class="bi bi-calendar text-muted me-1"></i>
                                            <?= $patient['date_of_birth'] ? date('Y-m-d', strtotime($patient['date_of_birth'])) : 'غير محدد' ?>
                                        </div>
                                        <div>
                                            <i class="bi bi-gender-ambiguous text-muted me-1"></i>
                                            <?= $patient['gender'] === 'male' ? 'ذكر' : ($patient['gender'] === 'female' ? 'أنثى' : 'غير محدد') ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    // الحصول على آخر موعد للمريض
                                    $lastAppointment = null;
                                    if (isset($patient['last_appointment'])) {
                                        $lastAppointment = $patient['last_appointment'];
                                    }
                                    ?>
                                    <?php if ($lastAppointment): ?>
                                        <div>
                                            <div class="badge bg-info mb-1">
                                                <?= date('Y-m-d', strtotime($lastAppointment)) ?>
                                            </div>
                                            <br>
                                            <small class="text-muted">
                                                <?= date('H:i', strtotime($lastAppointment)) ?>
                                            </small>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">لا توجد مواعيد</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($patient['is_active']): ?>
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle me-1"></i>
                                            نشط
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">
                                            <i class="bi bi-x-circle me-1"></i>
                                            غير نشط
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= App::url('doctor/view-patient/' . $patient['id']) ?>" 
                                           class="btn btn-sm btn-outline-primary" 
                                           title="عرض الملف الطبي">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="<?= App::url('doctor/add-medical-record?patient_id=' . $patient['id']) ?>" 
                                           class="btn btn-sm btn-outline-success" 
                                           title="إضافة سجل طبي">
                                            <i class="bi bi-plus-circle"></i>
                                        </a>
                                        <a href="<?= App::url('doctor/create-prescription?patient_id=' . $patient['id']) ?>" 
                                           class="btn btn-sm btn-outline-info" 
                                           title="إنشاء وصفة طبية">
                                            <i class="bi bi-prescription2"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Quick Actions -->
<?php if (!empty($patients)): ?>
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <i class="bi bi-file-earmark-medical display-4 mb-3"></i>
                    <h5>إضافة سجل طبي</h5>
                    <p class="mb-3">إضافة سجل طبي جديد لأي مريض</p>
                    <a href="<?= App::url('doctor/add-medical-record') ?>" class="btn btn-light">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة سجل
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="bi bi-prescription2 display-4 mb-3"></i>
                    <h5>إنشاء وصفة طبية</h5>
                    <p class="mb-3">إنشاء وصفة طبية جديدة</p>
                    <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#createPrescriptionModal">
                        <i class="bi bi-plus-circle me-2"></i>
                        إنشاء وصفة
                    </button>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <i class="bi bi-calendar-plus display-4 mb-3"></i>
                    <h5>حجز موعد</h5>
                    <p class="mb-3">حجز موعد جديد لمريض</p>
                    <a href="<?= App::url('doctor/appointments') ?>" class="btn btn-light">
                        <i class="bi bi-calendar-plus me-2"></i>
                        حجز موعد
                    </a>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Add Medical Record Modal -->
<div class="modal fade" id="addMedicalRecordModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-file-earmark-medical me-2"></i>
                    إضافة سجل طبي جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addMedicalRecordForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اختر المريض</label>
                                <select class="form-select" name="patient_id" required>
                                    <option value="">اختر المريض...</option>
                                    <?php foreach ($patients as $patient): ?>
                                        <option value="<?= $patient['id'] ?>">
                                            <?= htmlspecialchars(User::getFullName($patient)) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع السجل</label>
                                <select class="form-select" name="record_type" required>
                                    <option value="">اختر النوع...</option>
                                    <option value="consultation">استشارة</option>
                                    <option value="examination">فحص طبي</option>
                                    <option value="lab_test">فحص مخبري</option>
                                    <option value="treatment">علاج</option>
                                    <option value="follow_up">متابعة</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">التشخيص</label>
                        <textarea class="form-control" name="diagnosis" rows="3" placeholder="أدخل التشخيص..."></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">العلاج الموصوف</label>
                        <textarea class="form-control" name="treatment" rows="3" placeholder="أدخل العلاج الموصوف..."></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" name="notes" rows="2" placeholder="أدخل أي ملاحظات إضافية..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="submitMedicalRecord()">
                    <i class="bi bi-save me-2"></i>
                    حفظ السجل
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Create Prescription Modal -->
<div class="modal fade" id="createPrescriptionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-prescription2 me-2"></i>
                    إنشاء وصفة طبية جديدة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createPrescriptionForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اختر المريض</label>
                                <select class="form-select" name="patient_id" required>
                                    <option value="">اختر المريض...</option>
                                    <?php foreach ($patients as $patient): ?>
                                        <option value="<?= $patient['id'] ?>">
                                            <?= htmlspecialchars(User::getFullName($patient)) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ الوصفة</label>
                                <input type="date" class="form-control" name="prescription_date" value="<?= date('Y-m-d') ?>" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">التشخيص</label>
                        <textarea class="form-control" name="diagnosis" rows="2" placeholder="أدخل التشخيص..."></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الأدوية الموصوفة</label>
                        <textarea class="form-control" name="medications" rows="4" placeholder="أدخل الأدوية الموصوفة مع الجرعات..."></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">تعليمات خاصة</label>
                        <textarea class="form-control" name="instructions" rows="2" placeholder="أدخل أي تعليمات خاصة..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="submitPrescription()">
                    <i class="bi bi-save me-2"></i>
                    إنشاء الوصفة
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function submitMedicalRecord() {
    const form = document.getElementById('addMedicalRecordForm');
    const formData = new FormData(form);
    
    fetch('<?= App::url('doctor/add-medical-record') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إضافة السجل الطبي بنجاح');
            location.reload();
        } else {
            alert('فشل في إضافة السجل الطبي: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء إضافة السجل الطبي');
    });
}

function submitPrescription() {
    const form = document.getElementById('createPrescriptionForm');
    const formData = new FormData(form);
    
    fetch('<?= App::url('doctor/create-prescription') ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إنشاء الوصفة الطبية بنجاح');
            location.reload();
        } else {
            alert('فشل في إنشاء الوصفة الطبية: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء إنشاء الوصفة الطبية');
    });
}

// تحسين البحث
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[name="search"]');
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            this.form.submit();
        }, 500);
    });
});
</script> 