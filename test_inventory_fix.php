<?php
require_once 'config.php';
require_once 'app/models/Inventory.php';
require_once 'app/controllers/PharmacistController.php';

echo "<h1>اختبار إصلاح مشكلة المخزون</h1>";

try {
    // اختبار إنشاء نموذج المخزون
    echo "<h2>1. اختبار إنشاء نموذج المخزون</h2>";
    $inventoryModel = new Inventory();
    echo "✅ تم إنشاء نموذج المخزون بنجاح<br>";
    
    // اختبار دالة getAll
    echo "<h2>2. اختبار دالة getAll</h2>";
    $items = $inventoryModel->getAll(5);
    echo "✅ عدد الأدوية: " . count($items) . "<br>";
    
    // اختبار دالة getStats
    echo "<h2>3. اختبار دالة getStats</h2>";
    $stats = $inventoryModel->getStats();
    echo "✅ الإحصائيات: " . json_encode($stats, JSON_UNESCAPED_UNICODE) . "<br>";
    
    // اختبار إنشاء متحكم الصيدلي
    echo "<h2>4. اختبار إنشاء متحكم الصيدلي</h2>";
    
    // محاكاة تسجيل دخول صيدلي
    session_start();
    $_SESSION['user_id'] = 1;
    $_SESSION['user_type'] = 'pharmacist';
    $_SESSION['user'] = [
        'id' => 1,
        'first_name' => 'أحمد',
        'last_name' => 'محمد',
        'email' => '<EMAIL>'
    ];
    
    // اختبار إنشاء متحكم الصيدلي
    $controller = new PharmacistController();
    echo "✅ تم إنشاء متحكم الصيدلي بنجاح<br>";
    
    // اختبار دالة getInventoryStats
    echo "<h2>5. اختبار دالة getInventoryStats</h2>";
    $inventoryStats = $controller->getInventoryStats();
    echo "✅ إحصائيات المخزون: " . json_encode($inventoryStats, JSON_UNESCAPED_UNICODE) . "<br>";
    
    echo "<h2>✅ انتهى الاختبار بنجاح</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 