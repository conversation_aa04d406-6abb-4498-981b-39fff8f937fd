<?php
/**
 * ملف اختبار إضافة سجل طبي جديد
 * يمكن الوصول إليه عبر: http://localhost/HealthKey/test_add_medical_record.php
 */

// تضمين ملفات النظام
require_once 'app/core/App.php';
require_once 'app/helpers/SessionHelper.php';
require_once 'app/models/User.php';
require_once 'app/models/MedicalRecord.php';

// محاكاة تسجيل دخول الطبيب
session_start();
$_SESSION['user_id'] = 2; // افتراض أن الطبيب له معرف 2
$_SESSION['user_type'] = 'doctor';
$_SESSION['user_name'] = 'د. أحمد محمد';

// تحميل النماذج
$userModel = new User();
$medicalRecordModel = new MedicalRecord();

// الحصول على بيانات الطبيب
$doctor = $userModel->findById(2);
$patients = $userModel->getPatientsByDoctor(2);

// بيانات اختبار السجل الطبي
$testMedicalRecord = [
    'patient_id' => 1, // افتراض أن المريض له معرف 1
    'visit_date' => date('Y-m-d'),
    'visit_type' => 'consultation',
    'chief_complaint' => 'ألم في الصدر وضيق في التنفس',
    'diagnosis' => 'احتشاء عضلة القلب الحاد',
    'treatment_plan' => 'إعطاء الأسبرين 300 ملغ، النيتروجليسرين، والأكسجين. إحالة فورية للقسطرة القلبية.',
    'vital_signs' => [
        'temperature' => 37.2,
        'blood_pressure_systolic' => 140,
        'blood_pressure_diastolic' => 90,
        'heart_rate' => 95,
        'respiratory_rate' => 18,
        'weight' => 75,
        'height' => 170
    ],
    'notes' => 'المريض يعاني من ألم في الصدر منذ ساعتين. التاريخ العائلي إيجابي لأمراض القلب.'
];

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إضافة سجل طبي جديد - HealthKey</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .test-header { background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%); }
        .test-card { border: none; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
        .avatar-lg { width: 80px; height: 80px; }
    </style>
</head>
<body>
    <div class="container py-4">
        <!-- Header -->
        <div class="test-header text-white p-4 rounded mb-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-2">
                        <i class="bi bi-file-earmark-medical me-2"></i>
                        اختبار إضافة سجل طبي جديد
                    </h1>
                    <p class="mb-0 opacity-75">اختبار نظام إضافة السجلات الطبية</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex align-items-center justify-content-md-end">
                        <div class="me-3">
                            <small class="d-block opacity-75">الطبيب المسجل</small>
                            <strong><?= $_SESSION['user_name'] ?></strong>
                        </div>
                        <div class="avatar-lg bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center">
                            <i class="bi bi-person-badge text-white fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="row">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-check-circle me-2"></i>
                            نتائج الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>معلومات الطبيب:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>الاسم:</strong> <?= $doctor ? User::getFullName($doctor) : 'غير محدد' ?></li>
                                    <li><strong>البريد الإلكتروني:</strong> <?= $doctor['email'] ?? 'غير محدد' ?></li>
                                    <li><strong>التخصص:</strong> <?= $doctor['specialization'] ?? 'غير محدد' ?></li>
                                    <li><strong>عدد المرضى:</strong> <?= count($patients) ?></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>روابط الاختبار:</h6>
                                <div class="d-grid gap-2">
                                    <a href="doctor/add-medical-record?patient_id=1" class="btn btn-success">
                                        <i class="bi bi-file-earmark-medical me-2"></i>
                                        إضافة سجل طبي جديد
                                    </a>
                                    <a href="doctor/medical-records" class="btn btn-primary">
                                        <i class="bi bi-list me-2"></i>
                                        السجلات الطبية
                                    </a>
                                    <a href="doctor/patients" class="btn btn-outline-primary">
                                        <i class="bi bi-people me-2"></i>
                                        قائمة المرضى
                                    </a>
                                </div>
                                
                                <hr>
                                
                                <h6>الميزات المختبرة:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle text-success me-2"></i>إضافة سجل طبي جديد</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>إدخال العلامات الحيوية</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>التشخيص وخطة العلاج</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>حساب مؤشر كتلة الجسم</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>التحقق من صحة البيانات</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>حفظ كمسودة</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sample Medical Record -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-file-earmark-text me-2"></i>
                            نموذج سجل طبي للاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>معلومات الزيارة:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>تاريخ الزيارة:</strong> <?= $testMedicalRecord['visit_date'] ?></li>
                                    <li><strong>نوع الزيارة:</strong> <?= $testMedicalRecord['visit_type'] ?></li>
                                    <li><strong>الشكوى الرئيسية:</strong> <?= $testMedicalRecord['chief_complaint'] ?></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>العلامات الحيوية:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>درجة الحرارة:</strong> <?= $testMedicalRecord['vital_signs']['temperature'] ?> °C</li>
                                    <li><strong>ضغط الدم:</strong> <?= $testMedicalRecord['vital_signs']['blood_pressure_systolic'] ?>/<?= $testMedicalRecord['vital_signs']['blood_pressure_diastolic'] ?> mmHg</li>
                                    <li><strong>معدل ضربات القلب:</strong> <?= $testMedicalRecord['vital_signs']['heart_rate'] ?> bpm</li>
                                    <li><strong>الوزن:</strong> <?= $testMedicalRecord['vital_signs']['weight'] ?> kg</li>
                                    <li><strong>الطول:</strong> <?= $testMedicalRecord['vital_signs']['height'] ?> cm</li>
                                </ul>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>التشخيص:</h6>
                                <p class="text-muted"><?= $testMedicalRecord['diagnosis'] ?></p>
                            </div>
                            <div class="col-md-6">
                                <h6>خطة العلاج:</h6>
                                <p class="text-muted"><?= $testMedicalRecord['treatment_plan'] ?></p>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-12">
                                <h6>ملاحظات إضافية:</h6>
                                <p class="text-muted"><?= $testMedicalRecord['notes'] ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-warning text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            تعليمات الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>انقر على "إضافة سجل طبي جديد" لفتح النموذج</li>
                            <li>تحقق من عرض معلومات المريض بشكل صحيح</li>
                            <li>املأ بيانات الزيارة (التاريخ، النوع)</li>
                            <li>أدخل الشكوى الرئيسية للمريض</li>
                            <li>أدخل العلامات الحيوية (اختياري)</li>
                            <li>أدخل التشخيص وخطة العلاج</li>
                            <li>أضف أي ملاحظات إضافية</li>
                            <li>اختبر حساب مؤشر كتلة الجسم التلقائي</li>
                            <li>اختبر وظيفة "حفظ كمسودة"</li>
                            <li>انقر على "حفظ السجل الطبي"</li>
                            <li>تحقق من أن السجل تم حفظه بنجاح</li>
                        </ol>
                        
                        <div class="alert alert-info">
                            <i class="bi bi-lightbulb me-2"></i>
                            <strong>نصيحة:</strong> تأكد من وجود مرضى في قاعدة البيانات للطبيب المسجل لاختبار جميع الميزات.
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>ملاحظة:</strong> سيتم التحقق من صحة البيانات قبل الحفظ، مثل نطاقات العلامات الحيوية وتاريخ الزيارة.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-prescription2 display-4 mb-3"></i>
                        <h5>إنشاء وصفة طبية</h5>
                        <p class="mb-3">إنشاء وصفة طبية للمريض</p>
                        <a href="doctor/create-prescription?patient_id=1" class="btn btn-light">
                            <i class="bi bi-plus-circle me-2"></i>
                            إنشاء وصفة
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-clipboard2-pulse display-4 mb-3"></i>
                        <h5>فحص مخبري</h5>
                        <p class="mb-3">طلب فحص مخبري للمريض</p>
                        <button type="button" class="btn btn-light" onclick="addLabTest()">
                            <i class="bi bi-plus-circle me-2"></i>
                            طلب فحص
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-calendar-plus display-4 mb-3"></i>
                        <h5>حجز موعد</h5>
                        <p class="mb-3">حجز موعد متابعة للمريض</p>
                        <a href="doctor/appointments?patient_id=1" class="btn btn-light">
                            <i class="bi bi-calendar-plus me-2"></i>
                            حجز موعد
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="text-center text-muted">
                    <small>
                        <i class="bi bi-code-slash me-1"></i>
                        نظام HealthKey - اختبار إضافة سجل طبي جديد
                        <span class="mx-2">|</span>
                        <i class="bi bi-calendar me-1"></i>
                        <?= date('Y-m-d H:i') ?>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    function addLabTest() {
        const testName = prompt('أدخل اسم الفحص المخبري:');
        if (testName) {
            alert(`تم طلب فحص: ${testName}\nسيتم إضافة الفحص إلى قائمة الفحوصات المخبرية للمريض.`);
        }
    }
    </script>
</body>
</html> 