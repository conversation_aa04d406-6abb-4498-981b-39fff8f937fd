<?php

/**
 * نموذج الرسائل
 * يتعامل مع جدول الرسائل وجميع العمليات المتعلقة بها
 */
class Message
{
    private $db;
    private $table = 'messages';

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * الحصول على رسالة بواسطة ID
     */
    public function findById($id)
    {
        $query = "SELECT m.*, 
                         CONCAT(sender.first_name, ' ', sender.last_name) as sender_name,
                         CONCAT(recipient.first_name, ' ', recipient.last_name) as recipient_name,
                         sender.user_type as sender_type,
                         recipient.user_type as recipient_type
                  FROM {$this->table} m
                  LEFT JOIN users sender ON m.sender_id = sender.id
                  LEFT JOIN users recipient ON m.recipient_id = recipient.id
                  WHERE m.id = :id";
        
        return $this->db->selectOne($query, [':id' => $id]);
    }

    /**
     * إنشاء رسالة جديدة
     */
    public function create($senderId, $recipientId, $subject, $content, $type = 'system', $priority = 'normal')
    {
        // الحصول على نوع المرسل والمستقبل
        $sender = $this->db->selectOne("SELECT user_type FROM users WHERE id = :sender_id", [':sender_id' => $senderId]);
        $recipient = $this->db->selectOne("SELECT user_type FROM users WHERE id = :recipient_id", [':recipient_id' => $recipientId]);

        if (!$sender || !$recipient) {
            return false;
        }

        $query = "INSERT INTO {$this->table} (sender_id, sender_type, recipient_id, recipient_type, subject, content, type, priority, status, created_at) 
                  VALUES (:sender_id, :sender_type, :recipient_id, :recipient_type, :subject, :content, :type, :priority, 'pending', NOW())";

        $params = [
            ':sender_id' => $senderId,
            ':sender_type' => $sender['user_type'],
            ':recipient_id' => $recipientId,
            ':recipient_type' => $recipient['user_type'],
            ':subject' => $subject,
            ':content' => $content,
            ':type' => $type,
            ':priority' => $priority
        ];

        return $this->db->insert($query, $params);
    }

    /**
     * إرسال رسالة
     */
    public function send($messageId)
    {
        $query = "UPDATE {$this->table} SET status = 'sent', sent_time = NOW() WHERE id = :id";
        return $this->db->update($query, [':id' => $messageId]) > 0;
    }

    /**
     * الحصول على رسائل المستخدم (الواردة والصادرة)
     */
    public function getByUser($userId, $type = 'all', $limit = null, $offset = 0)
    {
        $whereClause = "";
        $params = [':user_id' => $userId];

        switch ($type) {
            case 'inbox':
                $whereClause = "WHERE m.recipient_id = :user_id";
                break;
            case 'sent':
                $whereClause = "WHERE m.sender_id = :user_id";
                break;
            default:
                $whereClause = "WHERE m.sender_id = :user_id OR m.recipient_id = :user_id2";
                $params[':user_id2'] = $userId;
                break;
        }

        $query = "SELECT m.*, 
                         CONCAT(sender.first_name, ' ', sender.last_name) as sender_name,
                         CONCAT(recipient.first_name, ' ', recipient.last_name) as recipient_name,
                         sender.user_type as sender_type,
                         recipient.user_type as recipient_type
                  FROM {$this->table} m
                  LEFT JOIN users sender ON m.sender_id = sender.id
                  LEFT JOIN users recipient ON m.recipient_id = recipient.id
                  $whereClause
                  ORDER BY m.created_at DESC";

        if ($limit) {
            $query .= " LIMIT " . (int)$limit;
            if ($offset) {
                $query .= " OFFSET " . (int)$offset;
            }
        }

        return $this->db->select($query, $params);
    }

    /**
     * الحصول على الرسائل الواردة
     */
    public function getInbox($userId, $unreadOnly = false, $limit = null)
    {
        $query = "SELECT m.*, 
                         CONCAT(sender.first_name, ' ', sender.last_name) as sender_name,
                         sender.user_type as sender_type
                  FROM {$this->table} m
                  LEFT JOIN users sender ON m.sender_id = sender.id
                  WHERE m.recipient_id = :user_id";
        
        $params = [':user_id' => $userId];

        if ($unreadOnly) {
            $query .= " AND m.status = 'sent'";
        }

        $query .= " ORDER BY m.created_at DESC";

        if ($limit) {
            $query .= " LIMIT " . (int)$limit;
        }

        return $this->db->select($query, $params);
    }

    /**
     * الحصول على الرسائل الصادرة
     */
    public function getSent($userId, $limit = null)
    {
        $query = "SELECT m.*, 
                         CONCAT(recipient.first_name, ' ', recipient.last_name) as recipient_name,
                         recipient.user_type as recipient_type
                  FROM {$this->table} m
                  LEFT JOIN users recipient ON m.recipient_id = recipient.id
                  WHERE m.sender_id = :user_id
                  ORDER BY m.created_at DESC";

        $params = [':user_id' => $userId];

        if ($limit) {
            $query .= " LIMIT " . (int)$limit;
        }

        return $this->db->select($query, $params);
    }

    /**
     * تسجيل الرسالة كمقروءة
     */
    public function markAsRead($id)
    {
        $query = "UPDATE {$this->table} SET status = 'read', read_time = NOW() WHERE id = :id";
        return $this->db->update($query, [':id' => $id]) > 0;
    }

    /**
     * حذف الرسالة
     */
    public function delete($id)
    {
        $query = "DELETE FROM {$this->table} WHERE id = :id";
        return $this->db->delete($query, [':id' => $id]) > 0;
    }

    /**
     * عدد الرسائل غير المقروءة للمستخدم
     */
    public function getUnreadCount($userId)
    {
        $query = "SELECT COUNT(*) as count FROM {$this->table} WHERE recipient_id = :user_id AND status = 'sent'";
        $result = $this->db->selectOne($query, [':user_id' => $userId]);
        return (int)$result['count'];
    }

    /**
     * البحث في الرسائل
     */
    public function search($term, $userId, $type = 'all')
    {
        $whereClause = "";
        $params = [':user_id' => $userId, ':term' => "%$term%"];

        switch ($type) {
            case 'inbox':
                $whereClause = "WHERE m.recipient_id = :user_id AND (m.subject LIKE :term OR m.content LIKE :term)";
                break;
            case 'sent':
                $whereClause = "WHERE m.sender_id = :user_id AND (m.subject LIKE :term OR m.content LIKE :term)";
                break;
            default:
                $whereClause = "WHERE (m.sender_id = :user_id OR m.recipient_id = :user_id2) AND (m.subject LIKE :term OR m.content LIKE :term)";
                $params[':user_id2'] = $userId;
                break;
        }

        $query = "SELECT m.*, 
                         CONCAT(sender.first_name, ' ', sender.last_name) as sender_name,
                         CONCAT(recipient.first_name, ' ', recipient.last_name) as recipient_name,
                         sender.user_type as sender_type,
                         recipient.user_type as recipient_type
                  FROM {$this->table} m
                  LEFT JOIN users sender ON m.sender_id = sender.id
                  LEFT JOIN users recipient ON m.recipient_id = recipient.id
                  $whereClause
                  ORDER BY m.created_at DESC
                  LIMIT 20";

        return $this->db->select($query, $params);
    }

    /**
     * الحصول على إحصائيات الرسائل
     */
    public function getStats($userId)
    {
        $stats = [];

        // إجمالي الرسائل الواردة
        $inbox = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM {$this->table} WHERE recipient_id = :user_id",
            [':user_id' => $userId]
        );
        $stats['inbox'] = (int)$inbox['count'];

        // الرسائل غير المقروءة
        $unread = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM {$this->table} WHERE recipient_id = :user_id AND status = 'sent'",
            [':user_id' => $userId]
        );
        $stats['unread'] = (int)$unread['count'];

        // الرسائل الصادرة
        $sent = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM {$this->table} WHERE sender_id = :user_id",
            [':user_id' => $userId]
        );
        $stats['sent'] = (int)$sent['count'];

        // الرسائل حسب النوع
        $byType = $this->db->select(
            "SELECT type, COUNT(*) as count FROM {$this->table} 
             WHERE sender_id = :user_id OR recipient_id = :user_id2 
             GROUP BY type",
            [':user_id' => $userId, ':user_id2' => $userId]
        );

        foreach ($byType as $type) {
            $stats[$type['type']] = (int)$type['count'];
        }

        return $stats;
    }

    /**
     * الحصول على نوع الرسالة باللغة العربية
     */
    public static function getTypeLabel($type)
    {
        $types = [
            'system' => 'نظام',
            'notification' => 'إشعار',
            'reminder' => 'تذكير',
            'alert' => 'تنبيه',
            'announcement' => 'إعلان',
            'personal' => 'شخصي'
        ];

        return $types[$type] ?? $type;
    }

    /**
     * الحصول على أيقونة نوع الرسالة
     */
    public static function getTypeIcon($type)
    {
        $icons = [
            'system' => 'bi-gear',
            'notification' => 'bi-bell',
            'reminder' => 'bi-clock',
            'alert' => 'bi-exclamation-triangle',
            'announcement' => 'bi-megaphone',
            'personal' => 'bi-person'
        ];

        return $icons[$type] ?? 'bi-chat';
    }

    /**
     * الحصول على لون نوع الرسالة
     */
    public static function getTypeColor($type)
    {
        $colors = [
            'system' => 'secondary',
            'notification' => 'info',
            'reminder' => 'warning',
            'alert' => 'danger',
            'announcement' => 'success',
            'personal' => 'primary'
        ];

        return $colors[$type] ?? 'secondary';
    }

    /**
     * الحصول على لون الأولوية
     */
    public static function getPriorityColor($priority)
    {
        $colors = [
            'low' => 'success',
            'normal' => 'info',
            'high' => 'warning',
            'urgent' => 'danger'
        ];

        return $colors[$priority] ?? 'info';
    }

    /**
     * تنسيق وقت الرسالة
     */
    public static function formatTime($createdAt)
    {
        $time = strtotime($createdAt);
        $now = time();
        $diff = $now - $time;

        if ($diff < 60) {
            return 'الآن';
        } elseif ($diff < 3600) {
            $minutes = floor($diff / 60);
            return "منذ $minutes دقيقة";
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return "منذ $hours ساعة";
        } elseif ($diff < 604800) {
            $days = floor($diff / 86400);
            return "منذ $days يوم";
        } else {
            return date('Y-m-d', $time);
        }
    }

    /**
     * إرسال رسالة نظام
     */
    public function sendSystemMessage($recipientId, $subject, $content, $type = 'system')
    {
        return $this->create(1, $recipientId, $subject, $content, $type, 'normal');
    }

    /**
     * إرسال رسائل متعددة
     */
    public function sendBulk($recipientIds, $subject, $content, $type = 'system')
    {
        $results = [];
        foreach ($recipientIds as $recipientId) {
            $results[] = $this->create(1, $recipientId, $subject, $content, $type);
        }
        return $results;
    }

    /**
     * إرسال رسالة لجميع الأطباء
     */
    public function sendToAllDoctors($subject, $content, $type = 'announcement')
    {
        $doctors = $this->db->select(
            "SELECT id FROM users WHERE user_type = 'doctor' AND is_active = 1"
        );

        $doctorIds = array_column($doctors, 'id');
        return $this->sendBulk($doctorIds, $subject, $content, $type);
    }

    /**
     * إرسال رسالة لجميع المرضى
     */
    public function sendToAllPatients($subject, $content, $type = 'announcement')
    {
        $patients = $this->db->select(
            "SELECT id FROM users WHERE user_type = 'patient' AND is_active = 1"
        );

        $patientIds = array_column($patients, 'id');
        return $this->sendBulk($patientIds, $subject, $content, $type);
    }

    /**
     * تنظيف الرسائل القديمة
     */
    public function cleanup($days = 90)
    {
        $query = "DELETE FROM {$this->table} 
                  WHERE created_at < DATE_SUB(NOW(), INTERVAL :days DAY) 
                  AND status IN ('read', 'deleted')";
        
        return $this->db->delete($query, [':days' => $days]);
    }
} 