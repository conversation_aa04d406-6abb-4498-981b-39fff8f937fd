<?php
// اختبار نهائي لصفحة المخزون
require_once 'config.php';

try {
    $db = Database::getInstance();
    
    echo "<h1>اختبار نهائي لصفحة المخزون</h1>";
    
    // محاكاة جلسة المستخدم
    session_start();
    $_SESSION['user_id'] = 1;
    $_SESSION['user_type'] = 'pharmacist';
    $_SESSION['user'] = [
        'id' => 1,
        'first_name' => 'أحمد',
        'last_name' => 'محمد',
        'email' => '<EMAIL>',
        'user_type' => 'pharmacist'
    ];
    
    // اختبار الحصول على البيانات
    echo "<h3>اختبار الحصول على البيانات:</h3>";
    
    // الإحصائيات
    $stats = [
        'total_items' => 0,
        'low_stock' => 0,
        'expired' => 0,
        'total_value' => 0
    ];

    $totalItems = $db->selectOne("SELECT COUNT(*) as count FROM inventory");
    $stats['total_items'] = $totalItems['count'] ?? 0;

    $lowStock = $db->selectOne("SELECT COUNT(*) as count FROM inventory WHERE quantity <= reorder_level");
    $stats['low_stock'] = $lowStock['count'] ?? 0;

    $expired = $db->selectOne("SELECT COUNT(*) as count FROM inventory WHERE expiry_date < CURDATE()");
    $stats['expired'] = $expired['count'] ?? 0;

    $totalValue = $db->selectOne("SELECT SUM(quantity * unit_price) as total FROM inventory");
    $stats['total_value'] = $totalValue['total'] ?? 0;

    echo "<p>✅ تم الحصول على الإحصائيات بنجاح</p>";
    echo "<pre>" . print_r($stats, true) . "</pre>";
    
    // قائمة المخزون
    $inventory = $db->select("SELECT * FROM inventory ORDER BY name ASC LIMIT 5");
    echo "<p>✅ تم الحصول على قائمة المخزون بنجاح</p>";
    echo "<p>عدد العناصر: " . count($inventory) . "</p>";
    
    // الفئات
    $categories = $db->select("SELECT DISTINCT category FROM inventory WHERE category IS NOT NULL AND category != '' ORDER BY category");
    echo "<p>✅ تم الحصول على الفئات بنجاح</p>";
    echo "<p>عدد الفئات: " . count($categories) . "</p>";
    
    // الأدوية منخفضة المخزون
    $lowStockItems = $db->select("SELECT * FROM inventory WHERE quantity <= reorder_level ORDER BY name ASC");
    echo "<p>✅ تم الحصول على الأدوية منخفضة المخزون بنجاح</p>";
    echo "<p>عدد الأدوية منخفضة المخزون: " . count($lowStockItems) . "</p>";
    
    // الأدوية منتهية الصلاحية
    $expiredItems = $db->select("SELECT * FROM inventory WHERE expiry_date < CURDATE() ORDER BY name ASC");
    echo "<p>✅ تم الحصول على الأدوية منتهية الصلاحية بنجاح</p>";
    echo "<p>عدد الأدوية منتهية الصلاحية: " . count($expiredItems) . "</p>";
    
    // الأدوية التي تنتهي قريباً
    $expiringSoonItems = $db->select("SELECT * FROM inventory WHERE expiry_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY) ORDER BY expiry_date ASC");
    echo "<p>✅ تم الحصول على الأدوية التي تنتهي قريباً بنجاح</p>";
    echo "<p>عدد الأدوية التي تنتهي قريباً: " . count($expiringSoonItems) . "</p>";
    
    echo "<h2 style='color: green;'>✅ جميع الاختبارات نجحت!</h2>";
    echo "<p>صفحة المخزون جاهزة للعمل</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<p>تفاصيل الخطأ: " . $e->getTraceAsString() . "</p>";
}
?> 