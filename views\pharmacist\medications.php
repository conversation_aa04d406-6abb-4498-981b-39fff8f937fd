<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">إدارة الأدوية</h1>
        <p class="text-muted">إدارة قاعدة بيانات الأدوية والمعلومات الطبية</p>
    </div>
    <div>
        <a href="<?= App::url('pharmacist/add-medication') ?>" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>
            إضافة دواء جديد
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['total_medications'] ?? 0 ?></h4>
                        <p class="mb-0">إجمالي الأدوية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-pills display-6 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['active_medications'] ?? 0 ?></h4>
                        <p class="mb-0">الأدوية النشطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle display-6 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['prescription_required'] ?? 0 ?></h4>
                        <p class="mb-0">تحتاج وصفة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-prescription display-6 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['otc_medications'] ?? 0 ?></h4>
                        <p class="mb-0">بدون وصفة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-shop display-6 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-secondary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['inactive_medications'] ?? 0 ?></h4>
                        <p class="mb-0">غير نشطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-pause-circle display-6 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card bg-dark text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['new_this_month'] ?? 0 ?></h4>
                        <p class="mb-0">جدد هذا الشهر</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-plus display-6 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- البحث والفلترة -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-search me-2"></i>
                    البحث في الأدوية
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" action="<?= App::url('pharmacist/medications') ?>" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control" 
                               placeholder="البحث في اسم الدواء أو الاسم العلمي..."
                               value="<?= htmlspecialchars($search) ?>">
                    </div>
                    <div class="col-md-3">
                        <select name="category" class="form-select">
                            <option value="">جميع الفئات</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= htmlspecialchars($category['category']) ?>" 
                                        <?= $currentCategory === $category['category'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($category['category']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="active" <?= $currentStatus === 'active' ? 'selected' : '' ?>>نشط</option>
                            <option value="inactive" <?= $currentStatus === 'inactive' ? 'selected' : '' ?>>غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-search"></i>
                            بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- الأدوية الشائعة -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-star me-2"></i>
                    الأدوية الشائعة
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($popularMedications)): ?>
                    <?php foreach ($popularMedications as $medication): ?>
                        <div class="d-flex align-items-center mb-2">
                            <div class="flex-shrink-0">
                                <i class="bi bi-pills text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="mb-0"><?= htmlspecialchars($medication['name']) ?></h6>
                                <small class="text-muted">
                                    <?= $medication['prescription_count'] ?? 0 ?> وصفة
                                </small>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-muted text-center">
                        <i class="bi bi-inbox display-4"></i>
                        <p class="mt-2">لا توجد بيانات</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- جدول الأدوية -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-list-ul me-2"></i>
            قائمة الأدوية
        </h5>
        <span class="badge bg-primary"><?= count($medications) ?> دواء</span>
    </div>
    <div class="card-body">
        <?php if (!empty($medications)): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم الدواء</th>
                            <th>الاسم العلمي</th>
                            <th>الفئة</th>
                            <th>الشكل والقوة</th>
                            <th>الوصفة</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($medications as $medication): ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?= htmlspecialchars($medication['name']) ?></strong>
                                        <?php if (!empty($medication['description'])): ?>
                                            <br><small class="text-muted"><?= htmlspecialchars(substr($medication['description'], 0, 50)) ?>...</small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="text-muted"><?= htmlspecialchars($medication['generic_name']) ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary"><?= htmlspecialchars($medication['category']) ?></span>
                                </td>
                                <td>
                                    <div>
                                        <strong><?= htmlspecialchars($medication['dosage_form']) ?></strong>
                                        <br><small class="text-muted"><?= htmlspecialchars($medication['strength']) ?></small>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($medication['prescription_required']): ?>
                                        <span class="badge bg-warning">تحتاج وصفة</span>
                                    <?php else: ?>
                                        <span class="badge bg-success">بدون وصفة</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($medication['status'] === 'active'): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary"
                                                data-bs-toggle="modal"
                                                data-bs-target="#viewModal<?= $medication['id'] ?>">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-warning"
                                                data-bs-toggle="modal"
                                                data-bs-target="#editModal<?= $medication['id'] ?>">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <a href="<?= App::url('pharmacist/delete-medication/' . $medication['id']) ?>"
                                           class="btn btn-sm btn-outline-danger"
                                           onclick="return confirm('هل أنت متأكد من حذف هذا الدواء؟')">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="bi bi-pills display-1 text-muted"></i>
                <h4 class="mt-3">لا توجد أدوية</h4>
                <p class="text-muted">لم يتم العثور على أدوية مطابقة لمعايير البحث</p>
                <a href="<?= App::url('pharmacist/add-medication') ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة أول دواء
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modals for View and Edit -->
<?php foreach ($medications as $medication): ?>
    <!-- View Modal -->
    <div class="modal fade" id="viewModal<?= $medication['id'] ?>" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل الدواء: <?= htmlspecialchars($medication['name']) ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <strong>اسم الدواء:</strong>
                            <p><?= htmlspecialchars($medication['name']) ?></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>الاسم العلمي:</strong>
                            <p><?= htmlspecialchars($medication['generic_name']) ?></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>الفئة:</strong>
                            <p><?= htmlspecialchars($medication['category']) ?></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>شكل الجرعة:</strong>
                            <p><?= htmlspecialchars($medication['dosage_form']) ?></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>القوة:</strong>
                            <p><?= htmlspecialchars($medication['strength']) ?></p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>الوصفة المطلوبة:</strong>
                            <p><?= $medication['prescription_required'] ? 'نعم' : 'لا' ?></p>
                        </div>
                        <div class="col-12 mb-3">
                            <strong>الوصف:</strong>
                            <p><?= nl2br(htmlspecialchars($medication['description'])) ?></p>
                        </div>
                        <?php if (!empty($medication['side_effects'])): ?>
                        <div class="col-12 mb-3">
                            <strong>الآثار الجانبية:</strong>
                            <p><?= nl2br(htmlspecialchars($medication['side_effects'])) ?></p>
                        </div>
                        <?php endif; ?>
                        <?php if (!empty($medication['contraindications'])): ?>
                        <div class="col-12 mb-3">
                            <strong>موانع الاستخدام:</strong>
                            <p><?= nl2br(htmlspecialchars($medication['contraindications'])) ?></p>
                        </div>
                        <?php endif; ?>
                        <?php if (!empty($medication['storage_conditions'])): ?>
                        <div class="col-12 mb-3">
                            <strong>ظروف التخزين:</strong>
                            <p><?= nl2br(htmlspecialchars($medication['storage_conditions'])) ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-warning" 
                            data-bs-toggle="modal" 
                            data-bs-target="#editModal<?= $medication['id'] ?>"
                            data-bs-dismiss="modal">
                        <i class="bi bi-pencil me-2"></i>
                        تعديل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div class="modal fade" id="editModal<?= $medication['id'] ?>" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل معلومات الدواء</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="<?= App::url('pharmacist/edit-medication/' . $medication['id']) ?>" method="POST">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم الدواء <span class="text-danger">*</span></label>
                                <input type="text" name="name" class="form-control" 
                                       value="<?= htmlspecialchars($medication['name']) ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الاسم العلمي <span class="text-danger">*</span></label>
                                <input type="text" name="generic_name" class="form-control" 
                                       value="<?= htmlspecialchars($medication['generic_name']) ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الفئة <span class="text-danger">*</span></label>
                                <select name="category" class="form-select" required>
                                    <option value="">اختر الفئة</option>
                                    <option value="مسكنات" <?= $medication['category'] === 'مسكنات' ? 'selected' : '' ?>>مسكنات</option>
                                    <option value="مضادات حيوية" <?= $medication['category'] === 'مضادات حيوية' ? 'selected' : '' ?>>مضادات حيوية</option>
                                    <option value="مضادات الحموضة" <?= $medication['category'] === 'مضادات الحموضة' ? 'selected' : '' ?>>مضادات الحموضة</option>
                                    <option value="فيتامينات" <?= $medication['category'] === 'فيتامينات' ? 'selected' : '' ?>>فيتامينات</option>
                                    <option value="أدوية القلب" <?= $medication['category'] === 'أدوية القلب' ? 'selected' : '' ?>>أدوية القلب</option>
                                    <option value="أدوية السكري" <?= $medication['category'] === 'أدوية السكري' ? 'selected' : '' ?>>أدوية السكري</option>
                                    <option value="أدوية الضغط" <?= $medication['category'] === 'أدوية الضغط' ? 'selected' : '' ?>>أدوية الضغط</option>
                                    <option value="أدوية الحساسية" <?= $medication['category'] === 'أدوية الحساسية' ? 'selected' : '' ?>>أدوية الحساسية</option>
                                    <option value="أخرى" <?= $medication['category'] === 'أخرى' ? 'selected' : '' ?>>أخرى</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">شكل الجرعة <span class="text-danger">*</span></label>
                                <select name="dosage_form" class="form-select" required>
                                    <option value="">اختر الشكل</option>
                                    <option value="أقراص" <?= $medication['dosage_form'] === 'أقراص' ? 'selected' : '' ?>>أقراص</option>
                                    <option value="كبسولات" <?= $medication['dosage_form'] === 'كبسولات' ? 'selected' : '' ?>>كبسولات</option>
                                    <option value="شراب" <?= $medication['dosage_form'] === 'شراب' ? 'selected' : '' ?>>شراب</option>
                                    <option value="حقن" <?= $medication['dosage_form'] === 'حقن' ? 'selected' : '' ?>>حقن</option>
                                    <option value="كريم" <?= $medication['dosage_form'] === 'كريم' ? 'selected' : '' ?>>كريم</option>
                                    <option value="مرهم" <?= $medication['dosage_form'] === 'مرهم' ? 'selected' : '' ?>>مرهم</option>
                                    <option value="قطرات" <?= $medication['dosage_form'] === 'قطرات' ? 'selected' : '' ?>>قطرات</option>
                                    <option value="أخرى" <?= $medication['dosage_form'] === 'أخرى' ? 'selected' : '' ?>>أخرى</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">القوة <span class="text-danger">*</span></label>
                                <input type="text" name="strength" class="form-control" 
                                       value="<?= htmlspecialchars($medication['strength']) ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحالة</label>
                                <select name="status" class="form-select">
                                    <option value="active" <?= $medication['status'] === 'active' ? 'selected' : '' ?>>نشط</option>
                                    <option value="inactive" <?= $medication['status'] === 'inactive' ? 'selected' : '' ?>>غير نشط</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" name="prescription_required" 
                                           value="1" <?= $medication['prescription_required'] ? 'checked' : '' ?>>
                                    <label class="form-check-label">
                                        يحتاج وصفة طبية
                                    </label>
                                </div>
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea name="description" class="form-control" rows="3"><?= htmlspecialchars($medication['description']) ?></textarea>
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label">الآثار الجانبية</label>
                                <textarea name="side_effects" class="form-control" rows="3"><?= htmlspecialchars($medication['side_effects']) ?></textarea>
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label">موانع الاستخدام</label>
                                <textarea name="contraindications" class="form-control" rows="3"><?= htmlspecialchars($medication['contraindications']) ?></textarea>
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label">ظروف التخزين</label>
                                <textarea name="storage_conditions" class="form-control" rows="2"><?= htmlspecialchars($medication['storage_conditions']) ?></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php endforeach; ?> 