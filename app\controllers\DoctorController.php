<?php

/**
 * متحكم الطبيب
 * يعالج جميع العمليات المتعلقة بالأطباء
 */
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../models/Appointment.php';
require_once __DIR__ . '/../models/Prescription.php';
require_once __DIR__ . '/../models/MedicalRecord.php';
require_once __DIR__ . '/../models/Notification.php';
require_once __DIR__ . '/../models/Message.php';

class DoctorController extends Controller
{
    private $userModel;
    private $appointmentModel;
    private $prescriptionModel;
    private $medicalRecordModel;
    private $notificationModel;
    private $messageModel;

    public function __construct()
    {
        parent::__construct();
        
        // التحقق من تسجيل الدخول ونوع المستخدم
        $this->requireAuth();
        $this->requireUserType('doctor');
        
        // تحميل النماذج
        $this->userModel = new User();
        $this->appointmentModel = new Appointment();
        $this->prescriptionModel = new Prescription();
        $this->medicalRecordModel = new MedicalRecord();
        $this->notificationModel = new Notification();
        $this->messageModel = new Message();
    }

    /**
     * لوحة تحكم الطبيب
     */
    public function dashboard()
    {
        $doctorId = $this->currentUser['id'];
        
        // الحصول على البيانات للوحة التحكم
        $data = [
            'title' => 'لوحة تحكم الطبيب',
            'doctor' => $this->currentUser,
            'todayAppointments' => $this->appointmentModel->getToday($doctorId),
            'upcomingAppointments' => $this->appointmentModel->getUpcoming($doctorId, 'doctor', 5),
            'recentPrescriptions' => $this->prescriptionModel->getByDoctor($doctorId, 5),
            'recentMedicalRecords' => $this->medicalRecordModel->getByDoctor($doctorId, 5),
            'notifications' => $this->notificationModel->getByUser($doctorId, false, 5),
            'unreadNotifications' => $this->notificationModel->getUnreadCount($doctorId),
            'stats' => [
                'appointments' => $this->appointmentModel->getStats($doctorId),
                'prescriptions' => $this->prescriptionModel->getStats($doctorId),
                'medical_records' => $this->medicalRecordModel->getStats($doctorId)
            ]
        ];

        $this->view('doctor/dashboard', $data);
    }

    /**
     * المواعيد
     */
    public function appointments()
    {
        $doctorId = $this->currentUser['id'];
        $date = App::get('date', date('Y-m-d'));
        $status = App::get('status');
        
        $data = [
            'title' => 'المواعيد',
            'appointments' => $this->appointmentModel->getByDoctor($doctorId, $date, $status),
            'currentDate' => $date,
            'currentStatus' => $status,
            'todayCount' => count($this->appointmentModel->getToday($doctorId))
        ];

        $this->view('doctor/appointments', $data);
    }

    /**
     * تأكيد الموعد
     */
    public function confirmAppointment()
    {
        $appointmentId = App::post('appointment_id');
        
        if (!$appointmentId) {
            $this->json(['success' => false, 'message' => 'معرف الموعد مطلوب'], 400);
            return;
        }

        // التحقق من ملكية الموعد
        $appointment = $this->appointmentModel->findById($appointmentId);
        if (!$appointment || $appointment['doctor_id'] != $this->currentUser['id']) {
            $this->json(['success' => false, 'message' => 'الموعد غير موجود'], 404);
            return;
        }

        if ($this->appointmentModel->confirm($appointmentId)) {
            $this->json(['success' => true, 'message' => 'تم تأكيد الموعد بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في تأكيد الموعد'], 500);
        }
    }

    /**
     * إكمال الموعد
     */
    public function completeAppointment()
    {
        $appointmentId = App::post('appointment_id');
        $notes = App::post('notes');
        
        if (!$appointmentId) {
            $this->json(['success' => false, 'message' => 'معرف الموعد مطلوب'], 400);
            return;
        }

        // التحقق من ملكية الموعد
        $appointment = $this->appointmentModel->findById($appointmentId);
        if (!$appointment || $appointment['doctor_id'] != $this->currentUser['id']) {
            $this->json(['success' => false, 'message' => 'الموعد غير موجود'], 404);
            return;
        }

        if ($this->appointmentModel->complete($appointmentId, $notes)) {
            $this->json(['success' => true, 'message' => 'تم إكمال الموعد بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في إكمال الموعد'], 500);
        }
    }

    /**
     * الجدول الزمني
     */
    public function schedule()
    {
        $doctorId = $this->currentUser['id'];
        $date = App::get('date', date('Y-m-d'));
        $week = App::get('week', date('Y-W'));
        
        // الحصول على المواعيد للأسبوع المحدد
        $startOfWeek = date('Y-m-d', strtotime('monday this week', strtotime($date)));
        $endOfWeek = date('Y-m-d', strtotime('sunday this week', strtotime($date)));
        
        $appointments = $this->appointmentModel->getByDoctor($doctorId, null, null);
        
        // تنظيم المواعيد حسب اليوم والوقت
        $schedule = [];
        $timeSlots = [
            '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
            '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30',
            '16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30'
        ];
        
        $daysOfWeek = [
            'monday' => 'الاثنين',
            'tuesday' => 'الثلاثاء', 
            'wednesday' => 'الأربعاء',
            'thursday' => 'الخميس',
            'friday' => 'الجمعة',
            'saturday' => 'السبت',
            'sunday' => 'الأحد'
        ];
        
        // تهيئة الجدول
        foreach ($daysOfWeek as $dayKey => $dayName) {
            $dayDate = date('Y-m-d', strtotime($dayKey . ' this week', strtotime($date)));
            $schedule[$dayKey] = [
                'date' => $dayDate,
                'name' => $dayName,
                'appointments' => []
            ];
            
            foreach ($timeSlots as $time) {
                $schedule[$dayKey]['appointments'][$time] = null;
            }
        }
        
        // ملء المواعيد في الجدول
        foreach ($appointments as $appointment) {
            $appointmentDate = $appointment['appointment_date'];
            $appointmentTime = $appointment['appointment_time'];
            
            if ($appointmentDate >= $startOfWeek && $appointmentDate <= $endOfWeek) {
                $dayKey = strtolower(date('l', strtotime($appointmentDate)));
                $timeKey = date('H:i', strtotime($appointmentTime));
                
                if (isset($schedule[$dayKey]['appointments'][$timeKey])) {
                    $schedule[$dayKey]['appointments'][$timeKey] = $appointment;
                }
            }
        }
        
        $data = [
            'title' => 'الجدول الزمني',
            'schedule' => $schedule,
            'currentDate' => $date,
            'startOfWeek' => $startOfWeek,
            'endOfWeek' => $endOfWeek,
            'timeSlots' => $timeSlots,
            'stats' => [
                'total' => count($appointments),
                'this_week' => count(array_filter($appointments, function($a) use ($startOfWeek, $endOfWeek) {
                    return $a['appointment_date'] >= $startOfWeek && $a['appointment_date'] <= $endOfWeek;
                })),
                'today' => count($this->appointmentModel->getToday($doctorId))
            ]
        ];

        $this->view('doctor/schedule', $data);
    }

    /**
     * حجز موعد جديد
     */
    public function scheduleAppointment()
    {
        $doctorId = $this->currentUser['id'];
        
        if (App::isPost()) {
            $this->processScheduleAppointment();
            return;
        }
        
        // الحصول على المرضى
        $patients = $this->userModel->getPatientsByDoctor($doctorId);
        
        // الحصول على الأوقات المتاحة
        $selectedDate = App::get('date', date('Y-m-d'));
        $availableTimeSlots = $this->getAvailableTimeSlots($doctorId, $selectedDate);
        
        // الحصول على الإحصائيات
        $stats = [
            'today_appointments' => count($this->appointmentModel->getToday($doctorId)),
            'available_slots' => count($availableTimeSlots),
            'total_patients' => count($patients),
            'pending_appointments' => count($this->appointmentModel->getByDoctor($doctorId, null, 'pending'))
        ];
        
        $data = [
            'title' => 'حجز موعد جديد',
            'patients' => $patients,
            'availableTimeSlots' => $availableTimeSlots,
            'selectedDate' => $selectedDate,
            'stats' => $stats,
            'selectedPatient' => App::get('patient_id'),
            'selectedTime' => App::get('time'),
            'appointmentType' => App::get('type'),
            'duration' => App::get('duration', '30'),
            'priority' => App::get('priority', 'normal'),
            'reason' => App::get('reason'),
            'notes' => App::get('notes')
        ];

        $this->view('doctor/schedule-appointment', $data);
    }

    /**
     * معالجة حجز الموعد
     */
    private function processScheduleAppointment()
    {
        $doctorId = $this->currentUser['id'];
        
        // التحقق من البيانات المطلوبة
        $requiredFields = ['patient_id', 'appointment_type', 'appointment_date', 'appointment_time', 'reason'];
        foreach ($requiredFields as $field) {
            if (empty(App::post($field))) {
                $this->setFlashMessage("حقل $field مطلوب", 'error');
                $this->redirect('doctor/schedule-appointment');
                return;
            }
        }
        
        // التحقق من عدم وجود تعارض في المواعيد
        $appointmentDate = App::post('appointment_date');
        $appointmentTime = App::post('appointment_time');
        
        $existingAppointment = $this->appointmentModel->findByDateTime($doctorId, $appointmentDate, $appointmentTime);
        if ($existingAppointment) {
            $this->setFlashMessage('هذا الوقت محجوز بالفعل، يرجى اختيار وقت آخر', 'error');
            $this->redirect('doctor/schedule-appointment');
            return;
        }
        
        // إنشاء الموعد
        $appointmentData = [
            'doctor_id' => $doctorId,
            'patient_id' => App::post('patient_id'),
            'appointment_date' => $appointmentDate,
            'appointment_time' => $appointmentTime,
            'appointment_type' => App::post('appointment_type'),
            'reason' => App::post('reason'),
            'notes' => App::post('notes'),
            'duration' => App::post('duration', 30),
            'priority' => App::post('priority', 'normal'),
            'status' => 'scheduled',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $appointmentId = $this->appointmentModel->create($appointmentData);
        
        if ($appointmentId) {
            // إرسال إشعار للمريض إذا كان مطلوباً
            if (App::post('send_notification')) {
                $this->notificationModel->create([
                    'user_id' => App::post('patient_id'),
                    'title' => 'موعد جديد',
                    'message' => 'تم حجز موعد جديد لك في ' . $appointmentDate . ' الساعة ' . $appointmentTime,
                    'type' => 'appointment',
                    'related_id' => $appointmentId
                ]);
            }
            
            $this->setFlashMessage('تم حجز الموعد بنجاح', 'success');
            $this->redirect('doctor/appointments');
        } else {
            $this->setFlashMessage('فشل في حجز الموعد، يرجى المحاولة مرة أخرى', 'error');
            $this->redirect('doctor/schedule-appointment');
        }
    }

    /**
     * الحصول على الأوقات المتاحة
     */
    private function getAvailableTimeSlots($doctorId, $date)
    {
        $allTimeSlots = [
            '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
            '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30',
            '16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30'
        ];
        
        // الحصول على المواعيد المحجوزة في هذا اليوم
        $bookedAppointments = $this->appointmentModel->getByDoctor($doctorId, $date);
        $bookedTimes = array_column($bookedAppointments, 'appointment_time');
        
        // إزالة الأوقات المحجوزة
        $availableSlots = array_diff($allTimeSlots, $bookedTimes);
        
        return array_values($availableSlots);
    }

    /**
     * الحصول على معلومات المريض (AJAX)
     */
    public function getPatientInfo($patientId)
    {
        $patient = $this->userModel->findById($patientId);
        
        if (!$patient) {
            $this->json(['success' => false, 'message' => 'المريض غير موجود']);
            return;
        }
        
        $html = '
        <div class="d-flex align-items-center mb-3">
            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                <i class="bi bi-person text-primary"></i>
            </div>
            <div>
                <h6 class="mb-0">' . htmlspecialchars(User::getFullName($patient)) . '</h6>
                <small class="text-muted">رقم الهوية: ' . htmlspecialchars($patient['national_id'] ?? 'غير محدد') . '</small>
            </div>
        </div>
        <div class="row">
            <div class="col-6">
                <small class="text-muted">البريد الإلكتروني:</small><br>
                <strong>' . htmlspecialchars($patient['email']) . '</strong>
            </div>
            <div class="col-6">
                <small class="text-muted">رقم الهاتف:</small><br>
                <strong>' . htmlspecialchars($patient['phone']) . '</strong>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-6">
                <small class="text-muted">تاريخ الميلاد:</small><br>
                <strong>' . ($patient['date_of_birth'] ? date('Y-m-d', strtotime($patient['date_of_birth'])) : 'غير محدد') . '</strong>
            </div>
            <div class="col-6">
                <small class="text-muted">الجنس:</small><br>
                <strong>' . ($patient['gender'] === 'male' ? 'ذكر' : ($patient['gender'] === 'female' ? 'أنثى' : 'غير محدد')) . '</strong>
            </div>
        </div>';
        
        $this->json(['success' => true, 'html' => $html]);
    }

    /**
     * الحصول على الأوقات المتاحة (AJAX)
     */
    public function getAvailableSlots()
    {
        $doctorId = $this->currentUser['id'];
        $date = App::get('date', date('Y-m-d'));
        
        $slots = $this->getAvailableTimeSlots($doctorId, $date);
        
        $this->json(['success' => true, 'slots' => $slots]);
    }

    /**
     * إضافة مريض جديد (AJAX)
     */
    public function addPatient()
    {
        $patientData = [
            'first_name' => App::post('first_name'),
            'last_name' => App::post('last_name'),
            'email' => App::post('email'),
            'phone' => App::post('phone'),
            'date_of_birth' => App::post('date_of_birth'),
            'gender' => App::post('gender'),
            'address' => App::post('address'),
            'user_type' => 'patient',
            'is_active' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        // التحقق من صحة البيانات
        $validation = $this->userModel->validate($patientData);
        if (!$validation['valid']) {
            $this->json(['success' => false, 'message' => implode(', ', $validation['errors'])]);
            return;
        }
        
        // التحقق من عدم وجود البريد الإلكتروني مسبقاً
        $existingUser = $this->userModel->findByEmail($patientData['email']);
        if ($existingUser) {
            $this->json(['success' => false, 'message' => 'البريد الإلكتروني مستخدم بالفعل']);
            return;
        }
        
        $patientId = $this->userModel->create($patientData);
        
        if ($patientId) {
            $patientName = $patientData['first_name'] . ' ' . $patientData['last_name'];
            $this->json([
                'success' => true, 
                'patient_id' => $patientId,
                'patient_name' => $patientName . ' (' . $patientData['phone'] . ')'
            ]);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في إضافة المريض']);
        }
    }

    /**
     * الوصفات الطبية
     */
    public function prescriptions()
    {
        $doctorId = $this->currentUser['id'];
        $search = App::get('search', '');
        $status = App::get('status', '');
        $date = App::get('date', '');
        
        // الحصول على الوصفات الطبية للطبيب
        $prescriptions = $this->prescriptionModel->getByDoctor($doctorId);
        
        // تطبيق الفلاتر
        if (!empty($search)) {
            $prescriptions = array_filter($prescriptions, function($prescription) use ($search) {
                return stripos($prescription['patient_name'], $search) !== false ||
                       stripos($prescription['medication_name'], $search) !== false ||
                       stripos($prescription['diagnosis'], $search) !== false;
            });
        }
        
        if (!empty($status)) {
            $prescriptions = array_filter($prescriptions, function($prescription) use ($status) {
                return $prescription['status'] === $status;
            });
        }
        
        if (!empty($date)) {
            $prescriptions = array_filter($prescriptions, function($prescription) use ($date) {
                return $prescription['prescription_date'] === $date;
            });
        }
        
        // الحصول على المرضى للطبيب
        $patients = $this->userModel->getPatientsByDoctor($doctorId);
        
        $data = [
            'title' => 'الوصفات الطبية',
            'prescriptions' => $prescriptions,
            'patients' => $patients,
            'search' => $search,
            'selectedStatus' => $status,
            'selectedDate' => $date,
            'stats' => [
                'total' => count($prescriptions),
                'active' => count(array_filter($prescriptions, function($p) { return $p['status'] === 'active'; })),
                'completed' => count(array_filter($prescriptions, function($p) { return $p['status'] === 'completed'; })),
                'cancelled' => count(array_filter($prescriptions, function($p) { return $p['status'] === 'cancelled'; }))
            ]
        ];

        $this->view('doctor/prescriptions', $data);
    }

    /**
     * إنشاء وصفة طبية جديدة
     */
    public function createPrescription()
    {
        if (App::isPost()) {
            $this->processCreatePrescription();
            return;
        }
        
        $doctorId = $this->currentUser['id'];
        $patientId = App::get('patient_id');
        
        if (!$patientId) {
            // عرض قائمة المرضى للاختيار
            $patients = $this->userModel->getPatientsByDoctor($doctorId);
            $data = [
                'title' => 'اختيار مريض لإنشاء وصفة طبية',
                'patients' => $patients,
                'isSelectPatient' => true
            ];
            $this->view('doctor/select_patient_prescription', $data);
            return;
        }
        
        // الحصول على بيانات المريض
        $patient = $this->userModel->getById($patientId);
        if (!$patient) {
            SessionHelper::setFlash('error', 'المريض غير موجود');
            App::redirect('doctor/prescriptions');
        }
        
        // الحصول على السجلات الطبية للمريض
        $medicalRecords = $this->medicalRecordModel->getByPatient($patientId);
        
        $data = [
            'title' => 'إنشاء وصفة طبية جديدة',
            'patient' => $patient,
            'medicalRecords' => $medicalRecords,
            'medications' => [
                'مسكنات الألم' => ['باراسيتامول', 'إيبوبروفين', 'ديكلوفيناك'],
                'مضادات حيوية' => ['أموكسيسيلين', 'أزيثروميسين', 'سيفالكسين'],
                'أدوية القلب' => ['أسبيرين', 'أتينولول', 'أملوديبين'],
                'أدوية السكري' => ['ميتفورمين', 'غليبيزيد', 'إنسولين'],
                'أدوية أخرى' => ['فيتامين د', 'كالسيوم', 'حديد']
            ]
        ];

        $this->view('doctor/create_prescription', $data);
    }

    /**
     * معالجة إنشاء وصفة طبية
     */
    private function processCreatePrescription()
    {
        $doctorId = $this->currentUser['id'];
        $patientId = App::post('patient_id');
        $medicationName = App::post('medication_name');
        $dosage = App::post('dosage');
        $frequency = App::post('frequency');
        $duration = App::post('duration');
        $diagnosis = App::post('diagnosis');
        $instructions = App::post('instructions');
        $prescriptionDate = App::post('prescription_date', date('Y-m-d'));
        
        // التحقق من البيانات
        if (empty($patientId) || empty($medicationName) || empty($dosage)) {
            SessionHelper::setFlash('error', 'جميع الحقول المطلوبة يجب ملؤها');
            App::redirect('doctor/create-prescription?patient_id=' . $patientId);
        }
        
        // إنشاء الوصفة الطبية
        $prescriptionData = [
            'doctor_id' => $doctorId,
            'patient_id' => $patientId,
            'medication_name' => $medicationName,
            'dosage' => $dosage,
            'frequency' => $frequency,
            'duration' => $duration,
            'diagnosis' => $diagnosis,
            'instructions' => $instructions,
            'prescription_date' => $prescriptionDate,
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        if ($this->prescriptionModel->create($prescriptionData)) {
            SessionHelper::setFlash('success', 'تم إنشاء الوصفة الطبية بنجاح');
            App::redirect('doctor/prescriptions');
        } else {
            SessionHelper::setFlash('error', 'فشل في إنشاء الوصفة الطبية');
            App::redirect('doctor/create-prescription?patient_id=' . $patientId);
        }
    }

    /**
     * المرضى
     */
    public function patients()
    {
        $doctorId = $this->currentUser['id'];
        $search = App::get('search', '');
        
        $patients = $this->userModel->getPatientsByDoctor($doctorId);
        
        // تطبيق البحث إذا تم تحديده
        if (!empty($search)) {
            $patients = array_filter($patients, function($patient) use ($search) {
                return stripos($patient['first_name'] . ' ' . $patient['last_name'], $search) !== false ||
                       stripos($patient['email'], $search) !== false ||
                       stripos($patient['phone'], $search) !== false;
            });
        }

        $data = [
            'title' => 'المرضى',
            'patients' => $patients,
            'search' => $search
        ];

        $this->view('doctor/patients', $data);
    }

    /**
     * عرض ملف المريض
     */
    public function viewPatient($patientId)
    {
        // التحقق من وجود المريض
        $patient = $this->userModel->findById($patientId);
        if (!$patient || $patient['user_type'] !== 'patient') {
            $this->setFlashMessage('المريض غير موجود', 'error');
            $this->redirect('doctor/patients');
            return;
        }

        $data = [
            'title' => 'ملف المريض - ' . User::getFullName($patient),
            'patient' => $patient,
            'medicalRecords' => $this->medicalRecordModel->getByPatient($patientId),
            'prescriptions' => $this->prescriptionModel->getByPatient($patientId),
            'appointments' => $this->appointmentModel->getByPatient($patientId),
            'allergies' => $this->medicalRecordModel->getAllergies($patientId),
            'labTests' => $this->medicalRecordModel->getLabTests($patientId),
            'summary' => $this->medicalRecordModel->getPatientSummary($patientId)
        ];

        $this->view('doctor/patient_profile', $data);
    }

    /**
     * إضافة سجل طبي
     */
    public function addMedicalRecord()
    {
        if (App::isPost()) {
            $this->processAddMedicalRecord();
            return;
        }

        $patientId = App::get('patient_id');
        
        // إذا لم يتم تحديد مريض، عرض قائمة المرضى للاختيار
        if (!$patientId) {
            $doctorId = $this->currentUser['id'];
            $patients = $this->userModel->getPatientsByDoctor($doctorId);
            
            $data = [
                'title' => 'اختيار مريض لإضافة سجل طبي',
                'patients' => $patients,
                'isSelectPatient' => true
            ];

            $this->view('doctor/select_patient', $data);
            return;
        }

        $patient = $this->userModel->findById($patientId);
        if (!$patient) {
            $this->setFlashMessage('المريض غير موجود', 'error');
            $this->redirect('doctor/patients');
            return;
        }

        $data = [
            'title' => 'إضافة سجل طبي',
            'patient' => $patient
        ];

        $this->view('doctor/add_medical_record', $data);
    }

    /**
     * معالجة إضافة السجل الطبي
     */
    private function processAddMedicalRecord()
    {
        $doctorId = $this->currentUser['id'];
        $postData = App::post();
        
        // التحقق من صحة البيانات
        $errors = $this->medicalRecordModel->validate(array_merge($postData, ['doctor_id' => $doctorId]));

        if (!empty($errors)) {
            SessionHelper::setValidationErrors($errors);
            SessionHelper::setOldInput($postData);
            $this->redirect('doctor/add-medical-record?patient_id=' . $postData['patient_id']);
            return;
        }

        // إعداد البيانات
        $recordData = [
            'patient_id' => $postData['patient_id'],
            'doctor_id' => $doctorId,
            'visit_date' => $postData['visit_date'] ?? date('Y-m-d'),
            'chief_complaint' => $postData['chief_complaint'],
            'diagnosis' => $postData['diagnosis'],
            'treatment_plan' => $postData['treatment_plan'],
            'notes' => $postData['notes'] ?? null
        ];

        // إضافة العلامات الحيوية إذا تم إدخالها
        if (!empty($postData['vital_signs'])) {
            $recordData['vital_signs'] = $postData['vital_signs'];
        }

        $recordId = $this->medicalRecordModel->create($recordData);

        if ($recordId) {
            $this->setFlashMessage('تم إضافة السجل الطبي بنجاح', 'success');
            $this->redirect('doctor/patient/' . $postData['patient_id']);
        } else {
            $this->setFlashMessage('فشل في إضافة السجل الطبي', 'error');
            $this->redirect('doctor/add-medical-record?patient_id=' . $postData['patient_id']);
        }
    }



    /**
     * عرض تفاصيل الوصفة
     */
    public function viewPrescription($prescriptionId)
    {
        // التحقق من ملكية الوصفة
        $prescription = $this->prescriptionModel->findById($prescriptionId);
        if (!$prescription || $prescription['doctor_id'] != $this->currentUser['id']) {
            $this->setFlashMessage('الوصفة غير موجودة', 'error');
            $this->redirect('doctor/prescriptions');
            return;
        }

        $data = [
            'title' => 'تفاصيل الوصفة',
            'prescription' => $prescription,
            'medications' => $this->prescriptionModel->getMedications($prescriptionId)
        ];

        $this->view('doctor/prescription_details', $data);
    }

    /**
     * البحث في المرضى
     */
    public function searchPatients()
    {
        $query = App::get('q', '');
        
        if (empty($query)) {
            $this->json(['patients' => []]);
            return;
        }

        $patients = $this->userModel->search($query, 'patient');
        
        $this->json(['patients' => $patients]);
    }

    /**
     * إضافة فحص مخبري
     */
    public function addLabTest()
    {
        if (!App::isPost()) {
            $this->json(['success' => false, 'message' => 'طريقة غير مسموحة'], 405);
            return;
        }

        $doctorId = $this->currentUser['id'];
        $postData = App::post();
        
        $testData = [
            'patient_id' => $postData['patient_id'],
            'doctor_id' => $doctorId,
            'test_name' => $postData['test_name'],
            'test_date' => $postData['test_date'] ?? date('Y-m-d'),
            'notes' => $postData['notes'] ?? null,
            'status' => 'ordered'
        ];

        $testId = $this->medicalRecordModel->addLabTest($testData);

        if ($testId) {
            $this->json(['success' => true, 'message' => 'تم طلب الفحص المخبري بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في طلب الفحص المخبري'], 500);
        }
    }

    /**
     * تحديث نتيجة الفحص المخبري
     */
    public function updateLabTestResult()
    {
        if (!App::isPost()) {
            $this->json(['success' => false, 'message' => 'طريقة غير مسموحة'], 405);
            return;
        }

        $testId = App::post('test_id');
        $results = App::post('results');
        
        if (!$testId || !$results) {
            $this->json(['success' => false, 'message' => 'البيانات المطلوبة ناقصة'], 400);
            return;
        }

        if ($this->medicalRecordModel->updateLabTestResult($testId, $results)) {
            $this->json(['success' => true, 'message' => 'تم تحديث نتيجة الفحص بنجاح']);
        } else {
            $this->json(['success' => false, 'message' => 'فشل في تحديث نتيجة الفحص'], 500);
        }
    }

    /**
     * السجلات الطبية
     */
    public function medicalRecords()
    {
        $doctorId = $this->currentUser['id'];
        $search = App::get('search', '');
        $patientId = App::get('patient_id');
        $date = App::get('date');
        
        // الحصول على السجلات الطبية
        $records = $this->medicalRecordModel->getByDoctor($doctorId);
        
        // تطبيق الفلاتر
        if (!empty($search)) {
            $records = array_filter($records, function($record) use ($search) {
                return stripos($record['patient_name'], $search) !== false ||
                       stripos($record['chief_complaint'], $search) !== false ||
                       stripos($record['diagnosis'], $search) !== false;
            });
        }
        
        if (!empty($patientId)) {
            $records = array_filter($records, function($record) use ($patientId) {
                return $record['patient_id'] == $patientId;
            });
        }
        
        if (!empty($date)) {
            $records = array_filter($records, function($record) use ($date) {
                return $record['visit_date'] == $date;
            });
        }
        
        // الحصول على قائمة المرضى للفلتر
        $patients = $this->userModel->getPatientsByDoctor($doctorId);
        
        $data = [
            'title' => 'السجلات الطبية',
            'records' => $records,
            'patients' => $patients,
            'search' => $search,
            'selectedPatient' => $patientId,
            'selectedDate' => $date,
            'stats' => $this->medicalRecordModel->getStats($doctorId)
        ];

        $this->view('doctor/medical-records', $data);
    }

    /**
     * الإشعارات
     */
    public function notifications()
    {
        $doctorId = $this->currentUser['id'];
        
        $data = [
            'title' => 'الإشعارات',
            'notifications' => $this->notificationModel->getByUser($doctorId, false, 50),
            'unreadCount' => $this->notificationModel->getUnreadCount($doctorId)
        ];

        $this->view('doctor/notifications', $data);
    }

    /**
     * التقارير
     */
    public function reports()
    {
        $doctorId = $this->currentUser['id'];
        $startDate = App::get('start_date', date('Y-m-01'));
        $endDate = App::get('end_date', date('Y-m-t'));
        
        $data = [
            'title' => 'التقارير',
            'startDate' => $startDate,
            'endDate' => $endDate,
            'appointmentReport' => $this->appointmentModel->getReport($startDate, $endDate, $doctorId),
            'prescriptionReport' => $this->prescriptionModel->getReport($startDate, $endDate, $doctorId),
            'stats' => [
                'appointments' => $this->appointmentModel->getStats($doctorId),
                'prescriptions' => $this->prescriptionModel->getStats($doctorId),
                'medical_records' => $this->medicalRecordModel->getStats($doctorId)
            ]
        ];

        $this->view('doctor/reports', $data);
    }

    /**
     * التحليلات
     */
    public function analytics()
    {
        $doctorId = $this->currentUser['id'];
        $period = App::get('period', '30');
        $startDate = App::get('start_date', date('Y-m-d', strtotime("-{$period} days")));
        $endDate = App::get('end_date', date('Y-m-d'));
        
        // حساب التواريخ للفترات السابقة للمقارنة
        $previousStartDate = date('Y-m-d', strtotime($startDate . ' -' . $period . ' days'));
        $previousEndDate = date('Y-m-d', strtotime($startDate . ' -1 day'));
        
        $data = [
            'title' => 'التحليلات',
            'period' => $period,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'stats' => [
                'appointments' => $this->getAppointmentStats($doctorId, $startDate, $endDate, $previousStartDate, $previousEndDate),
                'prescriptions' => $this->getPrescriptionStats($doctorId, $startDate, $endDate, $previousStartDate, $previousEndDate),
                'new_patients' => $this->getNewPatientsStats($doctorId, $startDate, $endDate, $previousStartDate, $previousEndDate),
                'occupancy_rate' => $this->getOccupancyRate($doctorId, $startDate, $endDate, $previousStartDate, $previousEndDate)
            ],
            'chartData' => [
                'appointments' => $this->getAppointmentsChartData($doctorId, $startDate, $endDate),
                'appointmentTypes' => $this->getAppointmentTypesChartData($doctorId, $startDate, $endDate),
                'prescriptions' => $this->getPrescriptionsChartData($doctorId, $startDate, $endDate),
                'patients' => $this->getPatientsChartData($doctorId, $startDate, $endDate)
            ],
            'peakHours' => $this->getPeakHours($doctorId, $startDate, $endDate),
            'commonDiagnoses' => $this->getCommonDiagnoses($doctorId, $startDate, $endDate),
            'performance' => $this->getPerformanceMetrics($doctorId, $startDate, $endDate)
        ];

        $this->view('doctor/analytics', $data);
    }

    /**
     * الحصول على إحصائيات المواعيد
     */
    private function getAppointmentStats($doctorId, $startDate, $endDate, $previousStartDate, $previousEndDate)
    {
        $currentStats = $this->appointmentModel->getStats($doctorId);
        $previousStats = $this->appointmentModel->getStats($doctorId, $previousStartDate, $previousEndDate);
        
        $growth = $previousStats['total'] > 0 ? 
            (($currentStats['total'] - $previousStats['total']) / $previousStats['total']) * 100 : 0;
        
        return [
            'total' => $currentStats['total'],
            'growth' => round($growth, 1)
        ];
    }

    /**
     * الحصول على إحصائيات الوصفات الطبية
     */
    private function getPrescriptionStats($doctorId, $startDate, $endDate, $previousStartDate, $previousEndDate)
    {
        $currentStats = $this->prescriptionModel->getStats($doctorId);
        $previousStats = $this->prescriptionModel->getStats($doctorId, $previousStartDate, $previousEndDate);
        
        $growth = $previousStats['total'] > 0 ? 
            (($currentStats['total'] - $previousStats['total']) / $previousStats['total']) * 100 : 0;
        
        return [
            'total' => $currentStats['total'],
            'growth' => round($growth, 1)
        ];
    }

    /**
     * الحصول على إحصائيات المرضى الجدد
     */
    private function getNewPatientsStats($doctorId, $startDate, $endDate, $previousStartDate, $previousEndDate)
    {
        $currentCount = $this->appointmentModel->getNewPatientsCount($doctorId, $startDate, $endDate);
        $previousCount = $this->appointmentModel->getNewPatientsCount($doctorId, $previousStartDate, $previousEndDate);
        
        $growth = $previousCount > 0 ? (($currentCount - $previousCount) / $previousCount) * 100 : 0;
        
        return [
            'total' => $currentCount,
            'growth' => round($growth, 1)
        ];
    }

    /**
     * الحصول على معدل الإشغال
     */
    private function getOccupancyRate($doctorId, $startDate, $endDate, $previousStartDate, $previousEndDate)
    {
        $currentRate = $this->appointmentModel->getOccupancyRate($doctorId, $startDate, $endDate);
        $previousRate = $this->appointmentModel->getOccupancyRate($doctorId, $previousStartDate, $previousEndDate);
        
        $change = $previousRate > 0 ? $currentRate - $previousRate : 0;
        
        return [
            'percentage' => round($currentRate, 1),
            'change' => round($change, 1)
        ];
    }

    /**
     * الحصول على بيانات الرسم البياني للمواعيد
     */
    private function getAppointmentsChartData($doctorId, $startDate, $endDate)
    {
        $data = $this->appointmentModel->getChartData($doctorId, $startDate, $endDate);
        
        return [
            'labels' => array_column($data, 'date'),
            'data' => array_column($data, 'count')
        ];
    }

    /**
     * الحصول على بيانات الرسم البياني لأنواع المواعيد
     */
    private function getAppointmentTypesChartData($doctorId, $startDate, $endDate)
    {
        $data = $this->appointmentModel->getTypesChartData($doctorId, $startDate, $endDate);
        
        return [
            'labels' => array_column($data, 'type'),
            'data' => array_column($data, 'count')
        ];
    }

    /**
     * الحصول على بيانات الرسم البياني للوصفات الطبية
     */
    private function getPrescriptionsChartData($doctorId, $startDate, $endDate)
    {
        $data = $this->prescriptionModel->getChartData($doctorId, $startDate, $endDate);
        
        return [
            'labels' => array_column($data, 'month'),
            'data' => array_column($data, 'count')
        ];
    }

    /**
     * الحصول على بيانات الرسم البياني للمرضى
     */
    private function getPatientsChartData($doctorId, $startDate, $endDate)
    {
        $data = $this->appointmentModel->getPatientsChartData($doctorId, $startDate, $endDate);
        
        return [
            'labels' => array_column($data, 'month'),
            'data' => array_column($data, 'count')
        ];
    }

    /**
     * الحصول على أفضل الأوقات
     */
    private function getPeakHours($doctorId, $startDate, $endDate)
    {
        return $this->appointmentModel->getPeakHours($doctorId, $startDate, $endDate);
    }

    /**
     * الحصول على أكثر الأمراض شيوعاً
     */
    private function getCommonDiagnoses($doctorId, $startDate, $endDate)
    {
        return $this->prescriptionModel->getCommonDiagnoses($doctorId, $startDate, $endDate);
    }

    /**
     * الحصول على مؤشرات الأداء
     */
    private function getPerformanceMetrics($doctorId, $startDate, $endDate)
    {
        return [
            'satisfaction_rate' => $this->appointmentModel->getSatisfactionRate($doctorId, $startDate, $endDate),
            'avg_appointment_time' => $this->appointmentModel->getAverageAppointmentTime($doctorId, $startDate, $endDate),
            'attendance_rate' => $this->appointmentModel->getAttendanceRate($doctorId, $startDate, $endDate)
        ];
    }

    /**
     * الرسائل
     */
    public function messages()
    {
        $doctorId = $this->currentUser['id'];
        
        // الحصول على قوائم المستخدمين
        $patients = $this->userModel->getPatientsByDoctor($doctorId);
        $doctors = $this->userModel->getAllDoctors();
        
        $data = [
            'title' => 'الرسائل',
            'inboxMessages' => $this->messageModel->getInbox($doctorId, false, 20),
            'sentMessages' => $this->messageModel->getSent($doctorId, 20),
            'allMessages' => $this->messageModel->getByUser($doctorId, 'all', 20),
            'unreadCount' => $this->messageModel->getUnreadCount($doctorId),
            'stats' => $this->messageModel->getStats($doctorId),
            'patients' => $patients,
            'doctors' => $doctors
        ];

        $this->view('doctor/messages', $data);
    }

    /**
     * الملف الشخصي
     */
    public function profile()
    {
        if (App::isPost()) {
            $this->updateProfile();
            return;
        }

        $data = [
            'title' => 'الملف الشخصي',
            'doctor' => $this->userModel->findById($this->currentUser['id'])
        ];

        $this->view('doctor/profile', $data);
    }

    /**
     * تحديث الملف الشخصي
     */
    private function updateProfile()
    {
        $doctorId = $this->currentUser['id'];
        $postData = App::post();
        
        // التحقق من صحة البيانات
        $errors = $this->userModel->validate($postData, true);

        if (!empty($errors)) {
            SessionHelper::setValidationErrors($errors);
            SessionHelper::setOldInput($postData);
            $this->redirect('doctor/profile');
            return;
        }

        // تحديث البيانات
        $updateData = [
            'first_name' => $postData['first_name'],
            'last_name' => $postData['last_name'],
            'phone' => $postData['phone'],
            'email' => $postData['email'],
            'address' => $postData['address'] ?? null
        ];

        if ($this->userModel->update($doctorId, $updateData)) {
            // تحديث بيانات الجلسة
            $_SESSION['user_name'] = $postData['first_name'] . ' ' . $postData['last_name'];
            
            $this->setFlashMessage('تم تحديث الملف الشخصي بنجاح', 'success');
        } else {
            $this->setFlashMessage('حدث خطأ أثناء تحديث الملف الشخصي', 'error');
        }

        $this->redirect('doctor/profile');
    }
}
