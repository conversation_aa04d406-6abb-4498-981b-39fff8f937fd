<?php
/**
 * اختبار مفصل لمعرفة الدالة المسببة للمشكلة
 */

require_once 'config.php';
require_once 'app/models/Message.php';

echo "<h1>اختبار مفصل لنظام الرسائل</h1>";

try {
    $messageModel = new Message();
    
    echo "<h2>1. اختبار دالة getStats</h2>";
    try {
        $stats = $messageModel->getStats(2);
        echo "✅ تم الحصول على الإحصائيات: " . json_encode($stats, JSON_UNESCAPED_UNICODE) . "<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في getStats: " . $e->getMessage() . "<br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h2>2. اختبار دالة getInbox</h2>";
    try {
        $inbox = $messageModel->getInbox(2, false, 5);
        echo "✅ عدد الرسائل الواردة: " . count($inbox) . "<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في getInbox: " . $e->getMessage() . "<br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h2>3. اختبار دالة getSent</h2>";
    try {
        $sent = $messageModel->getSent(2, 5);
        echo "✅ عدد الرسائل الصادرة: " . count($sent) . "<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في getSent: " . $e->getMessage() . "<br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h2>4. اختبار دالة getUnreadCount</h2>";
    try {
        $unreadCount = $messageModel->getUnreadCount(2);
        echo "✅ عدد الرسائل غير المقروءة: " . $unreadCount . "<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في getUnreadCount: " . $e->getMessage() . "<br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h2>5. اختبار دالة getByUser</h2>";
    try {
        $allMessages = $messageModel->getByUser(2, 'all', 5);
        echo "✅ عدد جميع الرسائل: " . count($allMessages) . "<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في getByUser: " . $e->getMessage() . "<br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h2>6. اختبار دالة search</h2>";
    try {
        $searchResults = $messageModel->search('رسالة', 2, 'all');
        echo "✅ عدد نتائج البحث: " . count($searchResults) . "<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في search: " . $e->getMessage() . "<br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h2>7. اختبار دالة create</h2>";
    try {
        $messageId = $messageModel->create(2, 1, 'رسالة اختبار', 'هذه رسالة اختبار', 'personal', 'normal');
        if ($messageId) {
            echo "✅ تم إنشاء رسالة جديدة برقم: " . $messageId . "<br>";
        } else {
            echo "❌ فشل في إنشاء رسالة جديدة<br>";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في create: " . $e->getMessage() . "<br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h2>✅ انتهى الاختبار</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ عام</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 