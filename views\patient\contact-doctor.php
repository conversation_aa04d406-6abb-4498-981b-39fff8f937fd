<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-chat-dots me-2"></i>
            التواصل مع الطبيب
        </h1>
        <p class="text-muted">اختر الطبيب وأرسل رسالة مباشرة</p>
    </div>
    <div>
        <a href="<?= App::url('patient/messages') ?>" class="btn btn-outline-primary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للرسائل
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-4">
        <!-- Doctor Selection Card -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-person-badge me-2"></i>
                    اختيار الطبيب
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="doctor_search" class="form-label">البحث عن طبيب</label>
                    <input type="text" class="form-control" id="doctor_search" 
                           placeholder="اكتب اسم الطبيب أو التخصص...">
                </div>
                
                <div class="doctor-list" id="doctor_list">
                    <?php if (!empty($doctors)): ?>
                        <?php foreach ($doctors as $doctor): ?>
                            <div class="doctor-item mb-3 p-3 border rounded cursor-pointer" 
                                 data-doctor-id="<?= $doctor['id'] ?>"
                                 onclick="selectDoctor(<?= $doctor['id'] ?>, '<?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>', '<?= htmlspecialchars($doctor['specialization'] ?? '') ?>')">
                                <div class="d-flex align-items-center">
                                    <div class="doctor-avatar me-3">
                                        <div class="avatar-circle bg-primary text-white">
                                            <i class="bi bi-person"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">د. <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?></h6>
                                        <?php if (!empty($doctor['specialization'])): ?>
                                            <small class="text-muted"><?= htmlspecialchars($doctor['specialization']) ?></small>
                                        <?php endif; ?>
                                        <?php if (!empty($doctor['hospital_affiliation'])): ?>
                                            <small class="text-muted d-block"><?= htmlspecialchars($doctor['hospital_affiliation']) ?></small>
                                        <?php endif; ?>
                                    </div>
                                    <div class="doctor-status">
                                        <?php if ($doctor['is_available']): ?>
                                            <span class="badge bg-success">متاح</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">غير متاح</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-person-x fs-1 mb-3"></i>
                            <p>لا يوجد أطباء متاحون حالياً</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-8">
        <!-- Contact Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-envelope me-2"></i>
                    رسالة للطبيب
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= App::url('patient/contact-doctor') ?>" id="contact_form">
                    <input type="hidden" id="selected_doctor_id" name="doctor_id" required>
                    
                    <!-- Selected Doctor Info -->
                    <div class="selected-doctor-info mb-4" id="selected_doctor_info" style="display: none;">
                        <div class="alert alert-info">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-person-check me-3 fs-4"></i>
                                <div>
                                    <strong>الطبيب المختار:</strong>
                                    <span id="selected_doctor_name"></span>
                                    <span id="selected_doctor_specialization"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="message_type" class="form-label">نوع الرسالة</label>
                            <select class="form-select" id="message_type" name="message_type" required>
                                <option value="">اختر نوع الرسالة</option>
                                <option value="consultation">استشارة طبية</option>
                                <option value="appointment">طلب موعد</option>
                                <option value="prescription">استفسار عن وصفة</option>
                                <option value="follow_up">متابعة حالة</option>
                                <option value="emergency">حالة طارئة</option>
                                <option value="general">استفسار عام</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="priority" class="form-label">الأولوية</label>
                            <select class="form-select" id="priority" name="priority" required>
                                <option value="normal">عادية</option>
                                <option value="high">عالية</option>
                                <option value="urgent">عاجلة</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="subject" class="form-label">الموضوع <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="subject" name="subject" 
                               placeholder="أدخل موضوع الرسالة" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="content" class="form-label">محتوى الرسالة <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="content" name="content" rows="8" 
                                  placeholder="اكتب رسالتك هنا... يمكنك تضمين تفاصيل حالتك الصحية والأعراض التي تعاني منها" required></textarea>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            يرجى تضمين التفاصيل المهمة مثل الأعراض والتاريخ الطبي إن أمكن
                        </div>
                    </div>
                    
                    <!-- Quick Templates -->
                    <div class="mb-3">
                        <label class="form-label">قوالب سريعة</label>
                        <div class="btn-group-vertical w-100" role="group">
                            <button type="button" class="btn btn-outline-secondary btn-sm mb-1" onclick="useTemplate('consultation')">
                                طلب استشارة طبية
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm mb-1" onclick="useTemplate('appointment')">
                                طلب موعد جديد
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm mb-1" onclick="useTemplate('follow_up')">
                                متابعة حالة صحية
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="useTemplate('prescription')">
                                استفسار عن وصفة طبية
                            </button>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-secondary" id="saveDraft">
                            <i class="bi bi-save me-2"></i>
                            حفظ كمسودة
                        </button>
                        <div>
                            <button type="button" class="btn btn-outline-secondary me-2" id="preview">
                                <i class="bi bi-eye me-2"></i>
                                معاينة
                            </button>
                            <button type="submit" class="btn btn-primary" id="sendButton" disabled>
                                <i class="bi bi-send me-2"></i>
                                إرسال الرسالة
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معاينة الرسالة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="sendMessage()">إرسال الرسالة</button>
            </div>
        </div>
    </div>
</div>

<style>
.doctor-item {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.doctor-item:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.doctor-item.selected {
    background-color: #e3f2fd;
    border-color: #2196f3;
}

.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.cursor-pointer {
    cursor: pointer;
}

#sendButton:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}
</style>

<script>
let selectedDoctorId = null;

// البحث في الأطباء
document.getElementById('doctor_search').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const doctorItems = document.querySelectorAll('.doctor-item');
    
    doctorItems.forEach(item => {
        const doctorName = item.querySelector('h6').textContent.toLowerCase();
        const specialization = item.querySelector('small')?.textContent.toLowerCase() || '';
        
        if (doctorName.includes(searchTerm) || specialization.includes(searchTerm)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
});

// اختيار الطبيب
function selectDoctor(doctorId, doctorName, specialization) {
    selectedDoctorId = doctorId;
    
    // إزالة التحديد من جميع الأطباء
    document.querySelectorAll('.doctor-item').forEach(item => {
        item.classList.remove('selected');
    });
    
    // تحديد الطبيب المختار
    event.currentTarget.classList.add('selected');
    
    // تحديث معلومات الطبيب المختار
    document.getElementById('selected_doctor_id').value = doctorId;
    document.getElementById('selected_doctor_name').textContent = doctorName;
    document.getElementById('selected_doctor_specialization').textContent = specialization ? ` - ${specialization}` : '';
    document.getElementById('selected_doctor_info').style.display = 'block';
    
    // تفعيل زر الإرسال
    document.getElementById('sendButton').disabled = false;
}

// استخدام قوالب سريعة
function useTemplate(type) {
    const subjectField = document.getElementById('subject');
    const contentField = document.getElementById('content');
    const typeField = document.getElementById('message_type');
    
    const templates = {
        consultation: {
            subject: 'طلب استشارة طبية',
            content: 'السلام عليكم ورحمة الله وبركاته،\n\nأحتاج إلى استشارة طبية بخصوص:\n\n- الأعراض التي أعاني منها:\n- مدة ظهور الأعراض:\n- الأدوية التي أتناولها حالياً:\n\nأشكركم مقدماً على مساعدتكم.',
            type: 'consultation'
        },
        appointment: {
            subject: 'طلب موعد جديد',
            content: 'السلام عليكم ورحمة الله وبركاته،\n\nأود حجز موعد جديد للكشف الطبي.\n\n- نوع الكشف المطلوب:\n- الأعراض أو الشكوى:\n- الأوقات المناسبة لي:\n\nأشكركم على مساعدتكم.',
            type: 'appointment'
        },
        follow_up: {
            subject: 'متابعة حالة صحية',
            content: 'السلام عليكم ورحمة الله وبركاته،\n\nأود متابعة حالتي الصحية بعد الزيارة السابقة.\n\n- التحسن في الحالة:\n- الأعراض الجديدة إن وجدت:\n- الالتزام بالعلاج:\n\nأشكركم على المتابعة.',
            type: 'follow_up'
        },
        prescription: {
            subject: 'استفسار عن وصفة طبية',
            content: 'السلام عليكم ورحمة الله وبركاته،\n\nلدي استفسار بخصوص الوصفة الطبية:\n\n- اسم الدواء:\n- الاستفسار:\n- الآثار الجانبية التي أعاني منها:\n\nأشكركم على التوضيح.',
            type: 'prescription'
        }
    };
    
    if (templates[type]) {
        subjectField.value = templates[type].subject;
        contentField.value = templates[type].content;
        typeField.value = templates[type].type;
    }
}

// معاينة الرسالة
document.getElementById('preview').addEventListener('click', function() {
    if (!selectedDoctorId) {
        alert('يرجى اختيار الطبيب أولاً');
        return;
    }
    
    const subject = document.getElementById('subject').value;
    const content = document.getElementById('content').value;
    const type = document.getElementById('message_type').value;
    const priority = document.getElementById('priority').value;
    
    if (!subject || !content) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    const previewContent = `
        <div class="border rounded p-3">
            <h6>إلى: ${document.getElementById('selected_doctor_name').textContent}</h6>
            <hr>
            <p><strong>الموضوع:</strong> ${subject}</p>
            <p><strong>النوع:</strong> ${type}</p>
            <p><strong>الأولوية:</strong> ${priority}</p>
            <hr>
            <p><strong>المحتوى:</strong></p>
            <div class="border rounded p-3 bg-light">
                ${content.replace(/\n/g, '<br>')}
            </div>
        </div>
    `;
    
    document.getElementById('previewContent').innerHTML = previewContent;
    new bootstrap.Modal(document.getElementById('previewModal')).show();
});

// إرسال الرسالة
function sendMessage() {
    document.getElementById('contact_form').submit();
}

// حفظ كمسودة
document.getElementById('saveDraft').addEventListener('click', function() {
    // يمكن إضافة منطق حفظ المسودة هنا
    alert('تم حفظ الرسالة كمسودة');
});

// التحقق من صحة النموذج
document.getElementById('contact_form').addEventListener('submit', function(e) {
    if (!selectedDoctorId) {
        e.preventDefault();
        alert('يرجى اختيار الطبيب أولاً');
        return;
    }
    
    const subject = document.getElementById('subject').value.trim();
    const content = document.getElementById('content').value.trim();
    
    if (!subject || !content) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
});
</script> 