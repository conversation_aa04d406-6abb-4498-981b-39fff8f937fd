<?php
/**
 * ملف اختبار صفحة المرضى للطبيب
 * يمكن الوصول إليه عبر: http://localhost/HealthKey/test_doctor_patients.php
 */

// تضمين ملفات النظام
require_once 'app/core/App.php';
require_once 'app/helpers/SessionHelper.php';
require_once 'app/models/User.php';
require_once 'app/models/Appointment.php';

// محاكاة تسجيل دخول الطبيب
session_start();
$_SESSION['user_id'] = 2; // افتراض أن الطبيب له معرف 2
$_SESSION['user_type'] = 'doctor';
$_SESSION['user_name'] = 'د. أحمد محمد';

// تحميل النماذج
$userModel = new User();
$appointmentModel = new Appointment();

// الحصول على بيانات الطبيب
$doctor = $userModel->findById(2);
$patients = $userModel->getPatientsByDoctor(2);

// محاكاة البحث
$search = isset($_GET['search']) ? $_GET['search'] : '';
if (!empty($search)) {
    $patients = array_filter($patients, function($patient) use ($search) {
        return stripos($patient['first_name'] . ' ' . $patient['last_name'], $search) !== false ||
               stripos($patient['email'], $search) !== false ||
               stripos($patient['phone'], $search) !== false;
    });
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحة المرضى للطبيب - HealthKey</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .test-header { background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%); }
        .test-card { border: none; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
        .avatar-sm { width: 40px; height: 40px; }
    </style>
</head>
<body>
    <div class="container py-4">
        <!-- Header -->
        <div class="test-header text-white p-4 rounded mb-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-2">
                        <i class="bi bi-people me-2"></i>
                        اختبار صفحة المرضى للطبيب
                    </h1>
                    <p class="mb-0 opacity-75">اختبار نظام إدارة المرضى للطبيب</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex align-items-center justify-content-md-end">
                        <div class="me-3">
                            <small class="d-block opacity-75">الطبيب المسجل</small>
                            <strong><?= $_SESSION['user_name'] ?></strong>
                        </div>
                        <div class="avatar-sm bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center">
                            <i class="bi bi-person-badge text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="row">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-check-circle me-2"></i>
                            نتائج الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>معلومات الطبيب:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>الاسم:</strong> <?= $doctor ? User::getFullName($doctor) : 'غير محدد' ?></li>
                                    <li><strong>البريد الإلكتروني:</strong> <?= $doctor['email'] ?? 'غير محدد' ?></li>
                                    <li><strong>التخصص:</strong> <?= $doctor['specialization'] ?? 'غير محدد' ?></li>
                                    <li><strong>عدد المرضى:</strong> <?= count($patients) ?></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>روابط الاختبار:</h6>
                                <div class="d-grid gap-2">
                                    <a href="doctor/patients" class="btn btn-primary">
                                        <i class="bi bi-people me-2"></i>
                                        صفحة المرضى
                                    </a>
                                    <a href="doctor/dashboard" class="btn btn-outline-primary">
                                        <i class="bi bi-speedometer2 me-2"></i>
                                        لوحة تحكم الطبيب
                                    </a>
                                    <a href="doctor/appointments" class="btn btn-outline-success">
                                        <i class="bi bi-calendar-check me-2"></i>
                                        المواعيد
                                    </a>
                                </div>
                                
                                <hr>
                                
                                <h6>الميزات المختبرة:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle text-success me-2"></i>عرض قائمة المرضى</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>البحث في المرضى</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>عرض معلومات المريض</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>إضافة سجل طبي</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>إنشاء وصفة طبية</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>عرض ملف المريض</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Patients List Preview -->
        <?php if (!empty($patients)): ?>
        <div class="row mt-4">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>
                            معاينة قائمة المرضى
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>المريض</th>
                                        <th>معلومات الاتصال</th>
                                        <th>المعلومات الشخصية</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($patients, 0, 5) as $patient): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                                        <i class="bi bi-person text-primary"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-0"><?= htmlspecialchars(User::getFullName($patient)) ?></h6>
                                                        <small class="text-muted">
                                                            رقم الهوية: <?= htmlspecialchars($patient['national_id'] ?? 'غير محدد') ?>
                                                        </small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <div class="mb-1">
                                                        <i class="bi bi-envelope text-muted me-1"></i>
                                                        <?= htmlspecialchars($patient['email']) ?>
                                                    </div>
                                                    <div>
                                                        <i class="bi bi-telephone text-muted me-1"></i>
                                                        <?= htmlspecialchars($patient['phone']) ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div>
                                                    <div class="mb-1">
                                                        <i class="bi bi-calendar text-muted me-1"></i>
                                                        <?= $patient['date_of_birth'] ? date('Y-m-d', strtotime($patient['date_of_birth'])) : 'غير محدد' ?>
                                                    </div>
                                                    <div>
                                                        <i class="bi bi-gender-ambiguous text-muted me-1"></i>
                                                        <?= $patient['gender'] === 'male' ? 'ذكر' : ($patient['gender'] === 'female' ? 'أنثى' : 'غير محدد') ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($patient['is_active']): ?>
                                                    <span class="badge bg-success">
                                                        <i class="bi bi-check-circle me-1"></i>
                                                        نشط
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">
                                                        <i class="bi bi-x-circle me-1"></i>
                                                        غير نشط
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php if (count($patients) > 5): ?>
                            <div class="text-center mt-3">
                                <small class="text-muted">
                                    عرض 5 من <?= count($patients) ?> مريض
                                </small>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Instructions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-warning text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            تعليمات الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>انقر على "صفحة المرضى" لفتح الصفحة الكاملة</li>
                            <li>اختبر وظيفة البحث في المرضى</li>
                            <li>اختبر عرض تفاصيل المريض</li>
                            <li>اختبر إضافة سجل طبي جديد</li>
                            <li>اختبر إنشاء وصفة طبية</li>
                            <li>اختبر التنقل بين الصفحات المختلفة</li>
                        </ol>
                        
                        <div class="alert alert-info">
                            <i class="bi bi-lightbulb me-2"></i>
                            <strong>نصيحة:</strong> تأكد من وجود مرضى في قاعدة البيانات للطبيب المسجل لاختبار جميع الميزات.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="text-center text-muted">
                    <small>
                        <i class="bi bi-code-slash me-1"></i>
                        نظام HealthKey - اختبار صفحة المرضى للطبيب
                        <span class="mx-2">|</span>
                        <i class="bi bi-calendar me-1"></i>
                        <?= date('Y-m-d H:i') ?>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 