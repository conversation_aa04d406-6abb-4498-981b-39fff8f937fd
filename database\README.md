# إعداد وتشغيل قاعدة البيانات HealthKey

## 📋 المتطلبات

- **PHP 7.4+** مع امتدادات PDO و MySQL
- **MySQL 5.7+** أو **MariaDB 10.2+**
- **XAMPP/WAMP/LAMP** أو خادم ويب محلي

## 🚀 طرق التشغيل

### الطريقة الأولى: تشغيل تلقائي (Windows)
```bash
# تشغيل ملف الدفعة
database/run_setup.bat
```

### الطريقة الثانية: تشغيل تلقائي (Linux/Mac)
```bash
# إعطاء صلاحيات التنفيذ
chmod +x database/run_setup.sh

# تشغيل الملف
./database/run_setup.sh
```

### الطريقة الثالثة: تشغيل يدوي
```bash
# 1. إعد<PERSON> قاعدة البيانات
php database/setup.php

# 2. اختب<PERSON>ر الاتصال
php database/test_connection.php
```

### الطريقة الرابعة: استيراد SQL مباشرة
```bash
# استيراد ملف SQL عبر سطر الأوامر
mysql -u root -p < database/healthkey_database.sql

# أو عبر phpMyAdmin
# 1. افتح phpMyAdmin
# 2. أنشئ قاعدة بيانات جديدة باسم 'healthkey'
# 3. استورد ملف 'healthkey_database.sql'
```

## ⚙️ إعداد التكوين

تأكد من تحديث ملف `config.php` بالإعدادات الصحيحة:

```php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'healthkey');
define('DB_USER', 'root');
define('DB_PASS', ''); // كلمة مرور MySQL
```

## 📊 الملفات المتضمنة

### 1. `healthkey_database.sql`
- ملف SQL كامل لإنشاء قاعدة البيانات
- يحتوي على جميع الجداول والبيانات الأولية
- المشاهدات والمحفزات والإجراءات المخزنة

### 2. `setup.php`
- ملف PHP لإعداد قاعدة البيانات تلقائياً
- ينشئ قاعدة البيانات وينفذ ملف SQL
- يتحقق من الجداول والبيانات الأولية

### 3. `test_connection.php`
- ملف اختبار شامل لقاعدة البيانات
- يختبر الاتصال والجداول والعمليات
- يقيس الأداء ويتحقق من سلامة البيانات

### 4. `run_setup.bat` (Windows)
- ملف دفعة لتشغيل الإعداد تلقائياً
- يتحقق من وجود PHP
- ينفذ الإعداد والاختبارات

### 5. `run_setup.sh` (Linux/Mac)
- ملف shell script للأنظمة Unix
- نفس وظائف ملف Windows
- يحتاج صلاحيات تنفيذ

## 🔐 الحسابات الافتراضية

بعد إعداد قاعدة البيانات، ستكون الحسابات التالية متاحة:

| النوع | البريد الإلكتروني | كلمة المرور |
|-------|------------------|-------------|
| مدير | <EMAIL> | password |
| طبيب | <EMAIL> | password |
| مريض | <EMAIL> | password |
| صيدلي | <EMAIL> | password |

## 🗃️ الجداول المنشأة

### الجداول الأساسية:
- `users` - المستخدمين الرئيسي
- `doctors` - بيانات الأطباء
- `pharmacists` - بيانات الصيادلة
- `appointments` - المواعيد
- `medical_records` - السجلات الطبية
- `prescriptions` - الوصفات الطبية
- `prescription_medications` - أدوية الوصفات

### الجداول المساعدة:
- `allergies` - الحساسيات
- `lab_tests` - الفحوصات المخبرية
- `notifications` - الإشعارات
- `attachments` - الملفات المرفقة
- `activity_logs` - سجل النشاطات
- `system_settings` - إعدادات النظام
- `user_sessions` - جلسات المستخدمين

## 🔍 التحقق من نجاح الإعداد

### 1. التحقق من الجداول:
```sql
SHOW TABLES;
```

### 2. التحقق من البيانات:
```sql
SELECT user_type, COUNT(*) FROM users GROUP BY user_type;
```

### 3. التحقق من الإعدادات:
```sql
SELECT COUNT(*) FROM system_settings;
```

## 🛠️ استكشاف الأخطاء

### خطأ الاتصال بقاعدة البيانات:
- تحقق من تشغيل MySQL/MariaDB
- تأكد من صحة بيانات الاتصال في `config.php`
- تحقق من صلاحيات المستخدم

### خطأ في إنشاء الجداول:
- تأكد من وجود صلاحيات CREATE
- تحقق من إصدار MySQL (5.7+ مطلوب)
- تأكد من دعم UTF8MB4

### خطأ في البيانات الأولية:
- تحقق من تنفيذ جميع استعلامات INSERT
- تأكد من عدم وجود تضارب في البيانات
- راجع سجل الأخطاء

## 📈 تحسين الأداء

### الفهارس المنشأة:
- فهارس أساسية لجميع المفاتيح الأساسية
- فهارس مركبة للاستعلامات المعقدة
- فهارس النص الكامل للبحث

### المشاهدات المتاحة:
- `appointments_view` - مواعيد مع تفاصيل المستخدمين
- `prescriptions_view` - وصفات مع تفاصيل كاملة

### الإجراءات المخزنة:
- `GetPatientStats(patient_id)` - إحصائيات المريض
- `GetDoctorStats(doctor_id)` - إحصائيات الطبيب

## 🔄 النسخ الاحتياطي

### إنشاء نسخة احتياطية:
```bash
mysqldump -u root -p healthkey > backup_$(date +%Y%m%d_%H%M%S).sql
```

### استعادة من نسخة احتياطية:
```bash
mysql -u root -p healthkey < backup_file.sql
```

## 📞 الدعم

في حالة مواجهة مشاكل:
1. تحقق من متطلبات النظام
2. راجع ملف الأخطاء
3. تأكد من صحة إعدادات قاعدة البيانات
4. جرب إعادة تشغيل الإعداد

---

**ملاحظة:** تأكد من تغيير كلمات المرور الافتراضية قبل النشر في بيئة الإنتاج!
