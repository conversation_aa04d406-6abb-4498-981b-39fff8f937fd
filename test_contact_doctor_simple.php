<?php
/**
 * اختبار بسيط لصفحة التواصل مع الطبيب
 */

// تضمين الملفات المطلوبة
require_once 'app/core/App.php';
require_once 'app/core/Database.php';
require_once 'app/helpers/SessionHelper.php';
require_once 'app/models/User.php';

// تهيئة التطبيق
App::init();

// محاكاة تسجيل دخول المريض
SessionHelper::set('user_id', 3);
SessionHelper::set('user_type', 'patient');
SessionHelper::set('user', [
    'id' => 3,
    'first_name' => 'فاطمة',
    'last_name' => 'علي',
    'email' => '<EMAIL>',
    'user_type' => 'patient'
]);

echo "<h1>اختبار صفحة التواصل مع الطبيب</h1>";

// اختبار الحصول على قائمة الأطباء
echo "<h2>1. اختبار الحصول على قائمة الأطباء</h2>";
$userModel = new User();
$doctors = $userModel->getDoctorsWithDetails();

if ($doctors) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>✅ تم العثور على " . count($doctors) . " طبيب</h3>";
    echo "<ul>";
    foreach ($doctors as $doctor) {
        echo "<li>";
        echo "<strong>الاسم:</strong> د. " . htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']);
        if (!empty($doctor['specialization'])) {
            echo " - <strong>التخصص:</strong> " . htmlspecialchars($doctor['specialization']);
        }
        echo " - <strong>الحالة:</strong> " . ($doctor['is_available'] ? 'متاح' : 'غير متاح');
        echo "</li>";
    }
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>❌ لا يوجد أطباء متاحون</h3>";
    echo "</div>";
}

// اختبار المسار
echo "<h2>2. اختبار المسار</h2>";
$url = App::url('patient/contact-doctor');
echo "<p><strong>مسار الصفحة:</strong> <a href='$url' target='_blank'>$url</a></p>";

// اختبار وجود الملف
echo "<h2>3. اختبار وجود ملف العرض</h2>";
$viewFile = 'views/patient/contact-doctor.php';
if (file_exists($viewFile)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>✅ ملف العرض موجود: $viewFile</h3>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>❌ ملف العرض غير موجود: $viewFile</h3>";
    echo "</div>";
}

// اختبار وجود الدالة في المتحكم
echo "<h2>4. اختبار وجود الدالة في المتحكم</h2>";
if (method_exists('PatientController', 'contactDoctor')) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>✅ دالة contactDoctor موجودة في PatientController</h3>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>❌ دالة contactDoctor غير موجودة في PatientController</h3>";
    echo "</div>";
}

// اختبار وجود الدالة في النموذج
echo "<h2>5. اختبار وجود الدالة في النموذج</h2>";
if (method_exists('User', 'getDoctorsWithDetails')) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>✅ دالة getDoctorsWithDetails موجودة في User</h3>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h3>❌ دالة getDoctorsWithDetails غير موجودة في User</h3>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='index.php' style='color: #007bff; text-decoration: none;'>← العودة للصفحة الرئيسية</a></p>";
echo "<p><a href='$url' style='color: #28a745; text-decoration: none;' target='_blank'>→ الانتقال لصفحة التواصل مع الطبيب</a></p>";
?> 