# حل مشكلة عمود rating المفقود

## المشكلة
```
خطأ في استعلام SELECT: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'rating' in 'field list'
```

## السبب
عمود `rating` غير موجود في جدول المواعيد، وكذلك عمود `duration` المطلوب لحساب متوسط وقت الموعد.

## الحل

### 1. إضافة الأعمدة المفقودة
تم إنشاء ملف `add_missing_columns.sql` لإضافة الأعمدة التالية:

- `rating` - تقييم رضا المرضى (DECIMAL(3,1))
- `duration` - مدة الموعد بالدقائق (INT)
- `appointment_type` - نوع الموعد (ENUM)
- `priority` - أولوية الموعد (ENUM)
- `room_number` - رقم الغرفة (VARCHAR)
- `insurance_info` - معلومات التأمين (TEXT)

### 2. ملفات الحل

#### أ. `test_fix_rating_column.php`
- فحص بنية جدول المواعيد
- إضافة الأعمدة المفقودة
- اختبار الدوال المحدثة
- اختبار صفحة التحليلات

#### ب. `add_missing_columns.sql`
- ملف SQL لإضافة الأعمدة
- إضافة فهارس للأعمدة الجديدة
- تحديث بيانات تجريبية

#### ج. `run_sql_fix.php`
- تشغيل ملف SQL تلقائياً
- فحص الأعمدة الجديدة
- اختبار الصفحات

## كيفية الاستخدام

### الطريقة الأولى: تشغيل ملف الاختبار
```bash
http://localhost/HealthKey/test_fix_rating_column.php
```

### الطريقة الثانية: تشغيل ملف SQL مباشرة
```bash
http://localhost/HealthKey/run_sql_fix.php
```

### الطريقة الثالثة: تشغيل SQL يدوياً
```sql
-- في phpMyAdmin أو أي أداة SQL
USE healthkey;

ALTER TABLE appointments ADD COLUMN rating DECIMAL(3,1) NULL AFTER notes;
ALTER TABLE appointments ADD COLUMN duration INT NULL AFTER rating;
```

## الأعمدة المضافة

| العمود | النوع | الوصف |
|--------|-------|-------|
| rating | DECIMAL(3,1) | تقييم رضا المرضى (1.0 - 5.0) |
| duration | INT | مدة الموعد بالدقائق |
| appointment_type | ENUM | نوع الموعد (consultation, examination, follow_up) |
| priority | ENUM | أولوية الموعد (normal, urgent, emergency) |
| room_number | VARCHAR(10) | رقم الغرفة |
| insurance_info | TEXT | معلومات التأمين |

## الدوال المتأثرة

### في نموذج Appointment:
1. `getSatisfactionRate()` - يستخدم عمود `rating`
2. `getAverageAppointmentTime()` - يستخدم عمود `duration`

### في واجهات المستخدم:
1. صفحة التحليلات - عرض معدل رضا المرضى
2. صفحة التقارير - عرض متوسط وقت الموعد

## اختبار الحل

بعد تشغيل الملفات، يمكن اختبار الحل عبر:

1. **صفحة التحليلات:**
   ```
   http://localhost/HealthKey/doctor/analytics
   ```

2. **صفحة التقارير:**
   ```
   http://localhost/HealthKey/doctor/reports
   ```

## النتيجة المتوقعة

- ✅ إضافة عمود `rating` بنجاح
- ✅ إضافة عمود `duration` بنجاح
- ✅ عدم ظهور أخطاء في صفحة التحليلات
- ✅ عرض معدل رضا المرضى بشكل صحيح
- ✅ عرض متوسط وقت الموعد بشكل صحيح

## الملفات المضافة:
1. `test_fix_rating_column.php` - ملف الاختبار
2. `add_missing_columns.sql` - ملف SQL
3. `run_sql_fix.php` - ملف التشغيل
4. `RATING_COLUMN_FIX_README.md` - هذا الملف 