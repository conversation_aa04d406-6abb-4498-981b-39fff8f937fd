<!-- Sidebar for Admin -->
<div class="sidebar-admin">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <div class="sidebar-brand">
            <i class="bi bi-shield-check text-primary"></i>
            <span class="brand-text">HealthKey</span>
        </div>
        <button class="sidebar-toggle d-lg-none">
            <i class="bi bi-x-lg"></i>
        </button>
    </div>

    <!-- User Info -->
    <div class="sidebar-user">
        <div class="user-avatar">
            <i class="bi bi-person-circle"></i>
        </div>
        <div class="user-info">
            <div class="user-name"><?= $currentUser['first_name'] . ' ' . $currentUser['last_name'] ?></div>
            <div class="user-role">مدير النظام</div>
        </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="sidebar-nav">
        <ul class="nav flex-column">
            <!-- Dashboard Section -->
            <li class="nav-section">
                <span class="nav-section-title">الرئيسية</span>
            </li>
            
            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('admin/dashboard') ? 'active' : '' ?>" 
                   href="<?= App::url('admin/dashboard') ?>" data-bs-toggle="tooltip" title="لوحة التحكم">
                    <i class="bi bi-speedometer2"></i>
                    <span>لوحة التحكم</span>
                    <span class="nav-badge">جديد</span>
                </a>
            </li>

            <!-- Management Section -->
            <li class="nav-section">
                <span class="nav-section-title">الإدارة</span>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('admin/users') ? 'active' : '' ?>" 
                   href="<?= App::url('admin/users') ?>" data-bs-toggle="tooltip" title="إدارة المستخدمين">
                    <i class="bi bi-people"></i>
                    <span>المستخدمين</span>
                    <span class="nav-badge bg-success"><?= isset($stats['users']['total']) ? $stats['users']['total'] : 0 ?></span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('admin/appointments') ? 'active' : '' ?>" 
                   href="<?= App::url('admin/appointments') ?>" data-bs-toggle="tooltip" title="إدارة المواعيد">
                    <i class="bi bi-calendar-check"></i>
                    <span>المواعيد</span>
                    <span class="nav-badge bg-info"><?= isset($stats['appointments']['total']) ? $stats['appointments']['total'] : 0 ?></span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('admin/prescriptions') ? 'active' : '' ?>" 
                   href="<?= App::url('admin/prescriptions') ?>" data-bs-toggle="tooltip" title="إدارة الوصفات">
                    <i class="bi bi-prescription2"></i>
                    <span>الوصفات</span>
                    <span class="nav-badge bg-warning"><?= isset($stats['prescriptions']['total']) ? $stats['prescriptions']['total'] : 0 ?></span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('admin/medical-records') ? 'active' : '' ?>" 
                   href="<?= App::url('admin/medical-records') ?>" data-bs-toggle="tooltip" title="السجلات الطبية">
                    <i class="bi bi-file-earmark-medical"></i>
                    <span>السجلات الطبية</span>
                </a>
            </li>

            <!-- Communication Section -->
            <li class="nav-section">
                <span class="nav-section-title">التواصل</span>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('admin/notifications') ? 'active' : '' ?>" 
                   href="<?= App::url('admin/notifications') ?>" data-bs-toggle="tooltip" title="إدارة الإشعارات">
                    <i class="bi bi-bell"></i>
                    <span>الإشعارات</span>
                    <?php if (SessionHelper::get('unread_notifications', 0) > 0): ?>
                        <span class="nav-badge bg-danger"><?= SessionHelper::get('unread_notifications') ?></span>
                    <?php endif; ?>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('admin/messages') ? 'active' : '' ?>" 
                   href="<?= App::url('admin/messages') ?>" data-bs-toggle="tooltip" title="الرسائل">
                    <i class="bi bi-envelope"></i>
                    <span>الرسائل</span>
                </a>
            </li>

            <!-- Analytics Section -->
            <li class="nav-section">
                <span class="nav-section-title">التحليلات</span>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('admin/reports') ? 'active' : '' ?>" 
                   href="<?= App::url('admin/reports') ?>" data-bs-toggle="tooltip" title="التقارير والإحصائيات">
                    <i class="bi bi-file-earmark-text"></i>
                    <span>التقارير</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('admin/analytics') ? 'active' : '' ?>" 
                   href="<?= App::url('admin/analytics') ?>" data-bs-toggle="tooltip" title="التحليلات المتقدمة">
                    <i class="bi bi-graph-up"></i>
                    <span>التحليلات</span>
                    <span class="nav-badge bg-primary">جديد</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('admin/activity-log') ? 'active' : '' ?>" 
                   href="<?= App::url('admin/activity-log') ?>" data-bs-toggle="tooltip" title="سجل النشاطات">
                    <i class="bi bi-clock-history"></i>
                    <span>سجل النشاطات</span>
                </a>
            </li>

            <!-- System Section -->
            <li class="nav-section">
                <span class="nav-section-title">النظام</span>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('admin/settings') ? 'active' : '' ?>" 
                   href="<?= App::url('admin/settings') ?>" data-bs-toggle="tooltip" title="إعدادات النظام">
                    <i class="bi bi-gear"></i>
                    <span>الإعدادات</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('admin/backup') ? 'active' : '' ?>" 
                   href="<?= App::url('admin/backup') ?>" data-bs-toggle="tooltip" title="النسخ الاحتياطي">
                    <i class="bi bi-shield-check"></i>
                    <span>النسخ الاحتياطي</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('admin/logs') ? 'active' : '' ?>" 
                   href="<?= App::url('admin/logs') ?>" data-bs-toggle="tooltip" title="سجلات النظام">
                    <i class="bi bi-file-text"></i>
                    <span>سجلات النظام</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?= App::isCurrentPath('admin/maintenance') ? 'active' : '' ?>" 
                   href="<?= App::url('admin/maintenance') ?>" data-bs-toggle="tooltip" title="وضع الصيانة">
                    <i class="bi bi-tools"></i>
                    <span>الصيانة</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Logout Section -->
    <div class="sidebar-logout">
        <a href="<?= App::url('logout') ?>" class="logout-btn" data-bs-toggle="tooltip" title="تسجيل الخروج">
            <i class="bi bi-box-arrow-right"></i>
            <span>تسجيل الخروج</span>
        </a>
    </div>

    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
        <div class="system-status">
            <div class="status-item">
                <i class="bi bi-circle-fill text-success"></i>
                <span>النظام متصل</span>
            </div>
            <div class="status-item">
                <i class="bi bi-clock"></i>
                <span><?= date('H:i') ?></span>
            </div>
        </div>
    </div>
</div>

<style>
/* Sidebar Admin Styles */
.sidebar-admin {
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    height: 100vh;
    width: 280px;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    box-shadow: -4px 0 20px rgba(0,0,0,0.15);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Sidebar Header */
.sidebar-header {
    padding: 1.5rem 1rem 1rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.sidebar-brand i {
    font-size: 1.5rem;
    color: #2c5aa0;
}

.brand-text {
    font-size: 1.25rem;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: rgba(255,255,255,0.7);
    font-size: 1.1rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.sidebar-toggle:hover {
    color: #ffffff;
    background: rgba(255,255,255,0.1);
}

/* User Info */
.sidebar-user {
    padding: 1rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #2c5aa0, #4a7bc8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 1.25rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.user-info {
    flex: 1;
}

.user-name {
    font-weight: 600;
    color: #ffffff;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.user-role {
    font-size: 0.75rem;
    color: rgba(255,255,255,0.7);
}

/* Navigation Sections */
.nav-section {
    padding: 0.75rem 1rem 0.5rem;
    margin-top: 0.5rem;
}

.nav-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: rgba(255,255,255,0.5);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: 0.5rem;
}

/* Navigation Items */
.sidebar-nav {
    flex: 1;
    padding: 0.5rem 0;
}

.sidebar-admin .nav-item {
    margin: 0.125rem 0.75rem;
}

.sidebar-admin .nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.sidebar-admin .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1));
    transition: width 0.3s ease;
}

.sidebar-admin .nav-link:hover::before {
    width: 100%;
}

.sidebar-admin .nav-link:hover {
    color: #ffffff;
    background: rgba(255,255,255,0.1);
    transform: translateX(-4px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.sidebar-admin .nav-link.active {
    background: linear-gradient(135deg, #2c5aa0, #4a7bc8);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(44, 90, 160, 0.4);
    transform: translateX(-4px);
}

.sidebar-admin .nav-link.active::before {
    display: none;
}

.sidebar-admin .nav-link i {
    font-size: 1.1rem;
    min-width: 20px;
    text-align: center;
}

.sidebar-admin .nav-link span {
    flex: 1;
    font-weight: 500;
}

/* Navigation Badges */
.nav-badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.7rem;
    font-weight: 600;
    border-radius: 12px;
    background: rgba(255,255,255,0.2);
    color: #ffffff;
    min-width: 20px;
    text-align: center;
}

.nav-badge.bg-success {
    background: #198754 !important;
}

.nav-badge.bg-info {
    background: #0dcaf0 !important;
}

.nav-badge.bg-warning {
    background: #ffc107 !important;
    color: #212529;
}

.nav-badge.bg-danger {
    background: #dc3545 !important;
}

/* Logout Section */
.sidebar-logout {
    padding: 0.75rem 1rem;
    border-top: 1px solid rgba(255,255,255,0.1);
    border-bottom: 1px solid rgba(255,255,255,0.1);
    background: rgba(220, 53, 69, 0.1);
}

.logout-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #dc3545;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.logout-btn::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(220, 53, 69, 0.2));
    transition: width 0.3s ease;
}

.logout-btn:hover::before {
    width: 100%;
}

.logout-btn:hover {
    color: #ffffff;
    background: rgba(220, 53, 69, 0.3);
    border-color: rgba(220, 53, 69, 0.5);
    transform: translateX(-4px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.logout-btn i {
    font-size: 1.1rem;
    min-width: 20px;
    text-align: center;
}

.logout-btn span {
    flex: 1;
    font-weight: 500;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid rgba(255,255,255,0.1);
    background: rgba(0,0,0,0.1);
}

.system-status {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: rgba(255,255,255,0.8);
}

.status-item i {
    font-size: 0.7rem;
}

.status-item .text-success {
    color: #28a745 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar-admin {
        transform: translateX(100%);
        width: 280px;
        position: fixed;
        top: 0;
        right: 0;
        z-index: 1050;
    }
    
    .sidebar-admin.show {
        transform: translateX(0);
        box-shadow: -4px 0 20px rgba(0,0,0,0.3);
    }
    
    .sidebar-admin.collapsed {
        transform: translateX(100%);
    }
}

/* Scrollbar Styling */
.sidebar-admin::-webkit-scrollbar {
    width: 6px;
}

.sidebar-admin::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
    border-radius: 3px;
}

.sidebar-admin::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
}

.sidebar-admin::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5);
}
</style>

<script>
// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Auto-hide sidebar on mobile when clicking a link
document.querySelectorAll('.sidebar-admin .nav-link').forEach(link => {
    link.addEventListener('click', function() {
        if (window.innerWidth <= 768) {
            document.querySelector('.sidebar-admin').classList.remove('show');
            document.body.classList.remove('sidebar-open');
        }
    });
});

// Update time every minute
setInterval(function() {
    const timeElement = document.querySelector('.status-item:last-child span');
    if (timeElement) {
        timeElement.textContent = new Date().toLocaleTimeString('ar-SA', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
}, 60000);
</script> 