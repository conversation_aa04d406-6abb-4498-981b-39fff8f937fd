<?php
/**
 * اختبار مفصل لنظام الرسائل
 */

require_once 'config.php';
require_once 'app/models/Message.php';
require_once 'app/models/User.php';

echo "<h1>اختبار مفصل لنظام الرسائل</h1>";

try {
    $messageModel = new Message();
    $userModel = new User();
    
    echo "<h2>1. اختبار الحصول على بيانات الطبيب</h2>";
    try {
        $doctor = $userModel->findById(2);
        if ($doctor) {
            echo "✅ تم الحصول على بيانات الطبيب: " . User::getFullName($doctor) . "<br>";
        } else {
            echo "❌ فشل في الحصول على بيانات الطبيب<br>";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في الحصول على بيانات الطبيب: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>2. اختبار الحصول على المرضى</h2>";
    try {
        $patients = $userModel->getPatientsByDoctor(2);
        echo "✅ عدد المرضى: " . count($patients) . "<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في الحصول على المرضى: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>3. اختبار الحصول على الأطباء</h2>";
    try {
        $doctors = $userModel->getAllDoctors();
        echo "✅ عدد الأطباء: " . count($doctors) . "<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في الحصول على الأطباء: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>4. اختبار الحصول على الرسائل الواردة</h2>";
    try {
        $inboxMessages = $messageModel->getInbox(2, false, 5);
        echo "✅ عدد الرسائل الواردة: " . count($inboxMessages) . "<br>";
        
        if (!empty($inboxMessages)) {
            echo "<h4>أمثلة على الرسائل الواردة:</h4>";
            echo "<ul>";
            foreach (array_slice($inboxMessages, 0, 3) as $message) {
                echo "<li><strong>" . htmlspecialchars($message['subject']) . "</strong> - من: " . htmlspecialchars($message['sender_name']) . "</li>";
            }
            echo "</ul>";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في الحصول على الرسائل الواردة: " . $e->getMessage() . "<br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h2>5. اختبار الحصول على الرسائل الصادرة</h2>";
    try {
        $sentMessages = $messageModel->getSent(2, 5);
        echo "✅ عدد الرسائل الصادرة: " . count($sentMessages) . "<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في الحصول على الرسائل الصادرة: " . $e->getMessage() . "<br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h2>6. اختبار إحصائيات الرسائل</h2>";
    try {
        $stats = $messageModel->getStats(2);
        echo "✅ الإحصائيات: " . json_encode($stats, JSON_UNESCAPED_UNICODE) . "<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في الحصول على إحصائيات الرسائل: " . $e->getMessage() . "<br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h2>7. اختبار عدد الرسائل غير المقروءة</h2>";
    try {
        $unreadCount = $messageModel->getUnreadCount(2);
        echo "✅ عدد الرسائل غير المقروءة: " . $unreadCount . "<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في الحصول على عدد الرسائل غير المقروءة: " . $e->getMessage() . "<br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h2>8. اختبار إنشاء رسالة جديدة</h2>";
    try {
        $messageId = $messageModel->create(2, 1, 'رسالة اختبار مفصل', 'هذه رسالة اختبار مفصل من الطبيب', 'personal', 'normal');
        if ($messageId) {
            echo "✅ تم إنشاء رسالة جديدة برقم: " . $messageId . "<br>";
            
            // إرسال الرسالة
            if ($messageModel->send($messageId)) {
                echo "✅ تم إرسال الرسالة بنجاح<br>";
            } else {
                echo "❌ فشل في إرسال الرسالة<br>";
            }
        } else {
            echo "❌ فشل في إنشاء رسالة جديدة<br>";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في إنشاء رسالة جديدة: " . $e->getMessage() . "<br>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    echo "<h2>✅ الاختبارات المكتملة</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ عام في الاختبار</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 