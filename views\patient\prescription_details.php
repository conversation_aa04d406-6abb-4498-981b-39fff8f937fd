<div class="prescription-details">
    <div class="row">
        <div class="col-md-8">
            <!-- Prescription Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-prescription2 me-2"></i>
                        معلومات الوصفة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">تاريخ الوصفة:</label>
                                <p class="mb-0"><?= $prescription['issue_date'] ? date('Y-m-d', strtotime($prescription['issue_date'])) : 'غير محدد' ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">تاريخ الانتهاء:</label>
                                <p class="mb-0">
                                    <?php if ($prescription['expiry_date']): ?>
                                        <?= date('Y-m-d', strtotime($prescription['expiry_date'])) ?>
                                        <?php if (strtotime($prescription['expiry_date']) < time()): ?>
                                            <span class="badge bg-danger ms-2">منتهية</span>
                                        <?php else: ?>
                                            <span class="badge bg-success ms-2">صالحة</span>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        غير محدد
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الطبيب:</label>
                                <p class="mb-0"><?= htmlspecialchars($prescription['doctor_name'] ?? 'غير محدد') ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الحالة:</label>
                                <p class="mb-0">
                                    <span class="badge bg-<?= Prescription::getStatusColor($prescription['status']) ?>">
                                        <?= Prescription::getStatusLabel($prescription['status']) ?>
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <?php if (!empty($prescription['diagnosis'])): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold">التشخيص:</label>
                            <p class="mb-0"><?= nl2br(htmlspecialchars($prescription['diagnosis'])) ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($prescription['notes'])): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold">ملاحظات:</label>
                            <p class="mb-0"><?= nl2br(htmlspecialchars($prescription['notes'])) ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Medications -->
            <?php if (!empty($medications)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-capsule me-2"></i>
                            الأدوية الموصوفة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>الدواء</th>
                                        <th>الكمية</th>
                                        <th>الجرعة</th>
                                        <th>المدة</th>
                                        <th>التعليمات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($medications as $medication): ?>
                                        <tr>
                                            <td>
                                                <strong><?= htmlspecialchars($medication['medication_name']) ?></strong>
                                                <?php if (!empty($medication['generic_name'])): ?>
                                                    <br><small class="text-muted"><?= htmlspecialchars($medication['generic_name']) ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?= htmlspecialchars($medication['quantity']) ?> 
                                                <?= htmlspecialchars($medication['unit']) ?>
                                            </td>
                                            <td>
                                                <?= htmlspecialchars($medication['dosage']) ?>
                                                <?php if (!empty($medication['dosage_unit'])): ?>
                                                    <?= htmlspecialchars($medication['dosage_unit']) ?>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?= htmlspecialchars($medication['duration']) ?>
                                                <?php if (!empty($medication['duration_unit'])): ?>
                                                    <?= htmlspecialchars($medication['duration_unit']) ?>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (!empty($medication['instructions'])): ?>
                                                    <?= nl2br(htmlspecialchars($medication['instructions'])) ?>
                                                <?php else: ?>
                                                    <span class="text-muted">لا توجد تعليمات خاصة</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Special Instructions -->
            <?php if (!empty($prescription['special_instructions'])): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            تعليمات خاصة
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <?= nl2br(htmlspecialchars($prescription['special_instructions'])) ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="col-md-4">
            <!-- Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-gear me-2"></i>
                        الإجراءات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                            <i class="bi bi-printer me-2"></i>
                            طباعة الوصفة
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="downloadPrescription(<?= $prescription['id'] ?>)">
                            <i class="bi bi-download me-2"></i>
                            تحميل الوصفة
                        </button>
                        <a href="<?= App::url('patient/prescriptions') ?>" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-left me-2"></i>
                            العودة للوصفات
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Prescription Timeline -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-clock-history me-2"></i>
                        الجدول الزمني
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم إنشاء الوصفة</h6>
                                                                    <small class="text-muted">
                                        <?= $prescription['created_at'] ? date('Y-m-d H:i', strtotime($prescription['created_at'])) : 'غير محدد' ?>
                                    </small>
                            </div>
                        </div>
                        
                        <?php if ($prescription['status'] === 'active'): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">الوصفة نشطة</h6>
                                    <small class="text-muted">صالحة للاستخدام</small>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($prescription['status'] === 'dispensed'): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">تم صرف الوصفة</h6>
                                    <small class="text-muted">
                                        <?= $prescription['updated_at'] ? date('Y-m-d H:i', strtotime($prescription['updated_at'])) : 'غير محدد' ?>
                                    </small>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($prescription['status'] === 'expired'): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-danger"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">انتهت صلاحية الوصفة</h6>
                                    <small class="text-muted">
                                        <?= $prescription['expiry_date'] ? date('Y-m-d', strtotime($prescription['expiry_date'])) : 'غير محدد' ?>
                                    </small>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($prescription['status'] === 'cancelled'): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-danger"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">تم إلغاء الوصفة</h6>
                                    <small class="text-muted">
                                        <?= $prescription['updated_at'] ? date('Y-m-d H:i', strtotime($prescription['updated_at'])) : 'غير محدد' ?>
                                    </small>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Prescription Status Info -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        معلومات إضافية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h6 class="mb-1 text-primary"><?= Prescription::getStatusLabel($prescription['status']) ?></h6>
                                <small class="text-muted">حالة الوصفة</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h6 class="mb-1 text-success"><?= count($medications) ?></h6>
                            <small class="text-muted">عدد الأدوية</small>
                        </div>
                    </div>
                    
                    <?php if ($prescription['expiry_date']): ?>
                        <hr>
                        <div class="text-center">
                            <?php 
                            $daysLeft = ceil((strtotime($prescription['expiry_date']) - time()) / (60 * 60 * 24));
                            $statusClass = $daysLeft > 0 ? 'text-success' : 'text-danger';
                            ?>
                            <small class="<?= $statusClass ?>">
                                <?php if ($daysLeft > 0): ?>
                                    متبقي <?= $daysLeft ?> يوم
                                <?php else: ?>
                                    منتهية منذ <?= abs($daysLeft) ?> يوم
                                <?php endif; ?>
                            </small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Download Prescription
function downloadPrescription(prescriptionId) {
    window.open('<?= App::url('patient/download-prescription') ?>/' + prescriptionId, '_blank');
}

// Show Alert
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert
    $('.container-fluid').prepend(alertHtml);
    
    // Auto dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-content {
    padding-left: 10px;
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.timeline-content small {
    font-size: 0.8rem;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

@media print {
    .btn, .card-header {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}
</style> 