<?php
/**
 * ملف اختبار إشعارات الطبيب
 * يمكن الوصول إليه عبر: http://localhost/HealthKey/test_doctor_notifications.php
 */

// تضمين ملفات النظام
require_once 'app/core/App.php';
require_once 'app/helpers/SessionHelper.php';
require_once 'app/models/User.php';
require_once 'app/models/Notification.php';

// محاكاة تسجيل دخول الطبيب
session_start();
$_SESSION['user_id'] = 2; // افتراض أن الطبيب له معرف 2
$_SESSION['user_type'] = 'doctor';
$_SESSION['user_name'] = 'د. أحمد محمد';

// تحميل النماذج
$userModel = new User();
$notificationModel = new Notification();

// الحصول على بيانات الطبيب
$doctor = $userModel->findById(2);

// إنشاء إشعارات اختبار
$testNotifications = [
    [
        'title' => 'موعد جديد',
        'message' => 'تم حجز موعد جديد مع المريض أحمد علي في تاريخ 2024-01-15 الساعة 10:00',
        'type' => 'appointment',
        'created_at' => date('Y-m-d H:i:s', strtotime('-1 hour'))
    ],
    [
        'title' => 'وصفة طبية جديدة',
        'message' => 'تم إنشاء وصفة طبية جديدة للمريض فاطمة محمد. رقم الوصفة: PRS-2024-001',
        'type' => 'prescription',
        'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours'))
    ],
    [
        'title' => 'تذكير بالموعد',
        'message' => 'تذكير: لديك موعد مع المريض خالد عبدالله غداً في تاريخ 2024-01-16 الساعة 14:00',
        'type' => 'appointment',
        'created_at' => date('Y-m-d H:i:s', strtotime('-3 hours'))
    ],
    [
        'title' => 'نتائج فحص مخبري',
        'message' => 'تم إضافة نتائج فحص الدم للمريض سارة أحمد إلى السجل الطبي',
        'type' => 'general',
        'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
    ],
    [
        'title' => 'تحديث النظام',
        'message' => 'تم تحديث نظام HealthKey إلى الإصدار الجديد. يرجى إعادة تسجيل الدخول',
        'type' => 'system',
        'created_at' => date('Y-m-d H:i:s', strtotime('-2 days'))
    ]
];

// إنشاء إشعارات اختبار في قاعدة البيانات
foreach ($testNotifications as $notification) {
    $notificationModel->create(
        $_SESSION['user_id'],
        $notification['title'],
        $notification['message'],
        $notification['type']
    );
}

// الحصول على إشعارات الطبيب
$notifications = $notificationModel->getByUser($_SESSION['user_id'], false, 10);
$unreadCount = $notificationModel->getUnreadCount($_SESSION['user_id']);
$stats = $notificationModel->getStats($_SESSION['user_id']);

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إشعارات الطبيب - HealthKey</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .test-header { background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%); }
        .test-card { border: none; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
        .avatar-lg { width: 80px; height: 80px; }
        .notification-preview { max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container py-4">
        <!-- Header -->
        <div class="test-header text-white p-4 rounded mb-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-2">
                        <i class="bi bi-bell me-2"></i>
                        اختبار إشعارات الطبيب
                    </h1>
                    <p class="mb-0 opacity-75">اختبار نظام الإشعارات والتنبيهات</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex align-items-center justify-content-md-end">
                        <div class="me-3">
                            <small class="d-block opacity-75">الطبيب المسجل</small>
                            <strong><?= $_SESSION['user_name'] ?></strong>
                        </div>
                        <div class="avatar-lg bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center">
                            <i class="bi bi-person-badge text-white fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="row">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-check-circle me-2"></i>
                            نتائج الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>معلومات الطبيب:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>الاسم:</strong> <?= $doctor ? User::getFullName($doctor) : 'غير محدد' ?></li>
                                    <li><strong>البريد الإلكتروني:</strong> <?= $doctor['email'] ?? 'غير محدد' ?></li>
                                    <li><strong>التخصص:</strong> <?= $doctor['specialization'] ?? 'غير محدد' ?></li>
                                    <li><strong>عدد الإشعارات:</strong> <?= count($notifications) ?></li>
                                    <li><strong>الإشعارات غير المقروءة:</strong> <?= $unreadCount ?></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>روابط الاختبار:</h6>
                                <div class="d-grid gap-2">
                                    <a href="doctor/notifications" class="btn btn-success">
                                        <i class="bi bi-bell me-2"></i>
                                        صفحة الإشعارات
                                    </a>
                                    <a href="doctor/dashboard" class="btn btn-primary">
                                        <i class="bi bi-speedometer2 me-2"></i>
                                        لوحة التحكم
                                    </a>
                                    <a href="doctor/appointments" class="btn btn-outline-primary">
                                        <i class="bi bi-calendar me-2"></i>
                                        المواعيد
                                    </a>
                                </div>
                                
                                <hr>
                                
                                <h6>الميزات المختبرة:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle text-success me-2"></i>عرض الإشعارات</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>البحث في الإشعارات</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>فلترة حسب النوع</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>تحديد كمقروء</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>حذف الإشعارات</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>إحصائيات الإشعارات</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notifications Preview -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-bell me-2"></i>
                            معاينة الإشعارات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="notification-preview">
                            <?php if (empty($notifications)): ?>
                                <div class="text-center py-4">
                                    <i class="bi bi-bell-slash text-muted display-4"></i>
                                    <h6 class="text-muted mt-2">لا توجد إشعارات</h6>
                                </div>
                            <?php else: ?>
                                <div class="list-group list-group-flush">
                                    <?php foreach ($notifications as $notification): ?>
                                        <div class="list-group-item">
                                            <div class="d-flex align-items-start">
                                                <div class="flex-shrink-0 me-3">
                                                    <div class="bg-<?= Notification::getTypeColor($notification['type']) ?> bg-opacity-10 rounded-circle p-2">
                                                        <i class="bi <?= Notification::getTypeIcon($notification['type']) ?> text-<?= Notification::getTypeColor($notification['type']) ?>"></i>
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <div class="d-flex justify-content-between align-items-start">
                                                        <div>
                                                            <h6 class="mb-1">
                                                                <?= htmlspecialchars($notification['title']) ?>
                                                                <?php if (!$notification['is_read']): ?>
                                                                    <span class="badge bg-danger ms-2">جديد</span>
                                                                <?php endif; ?>
                                                            </h6>
                                                            <p class="mb-1 text-muted"><?= htmlspecialchars($notification['message']) ?></p>
                                                            <small class="text-muted">
                                                                <i class="bi bi-clock me-1"></i>
                                                                <?= Notification::formatTime($notification['created_at']) ?>
                                                                <span class="mx-2">•</span>
                                                                <span class="badge bg-<?= Notification::getTypeColor($notification['type']) ?> bg-opacity-25 text-<?= Notification::getTypeColor($notification['type']) ?>">
                                                                    <?= Notification::getTypeLabel($notification['type']) ?>
                                                                </span>
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up me-2"></i>
                            إحصائيات الإشعارات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <h4 class="text-primary"><?= $stats['total'] ?? 0 ?></h4>
                                <small class="text-muted">إجمالي الإشعارات</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-warning"><?= $unreadCount ?></h4>
                                <small class="text-muted">غير مقروءة</small>
                            </div>
                        </div>
                        <hr>
                        <div class="row text-center">
                            <div class="col-6">
                                <h5 class="text-success"><?= $stats['appointment'] ?? 0 ?></h5>
                                <small class="text-muted">المواعيد</small>
                            </div>
                            <div class="col-6">
                                <h5 class="text-info"><?= $stats['prescription'] ?? 0 ?></h5>
                                <small class="text-muted">الوصفات</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header bg-warning text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-gear me-2"></i>
                            إجراءات الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-success" onclick="createTestNotification()">
                                <i class="bi bi-plus-circle me-2"></i>
                                إنشاء إشعار اختبار
                            </button>
                            <button type="button" class="btn btn-primary" onclick="markAllAsRead()">
                                <i class="bi bi-check-all me-2"></i>
                                تحديد الكل كمقروء
                            </button>
                            <button type="button" class="btn btn-danger" onclick="deleteAllNotifications()">
                                <i class="bi bi-trash me-2"></i>
                                حذف جميع الإشعارات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            تعليمات الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>انقر على "صفحة الإشعارات" لفتح صفحة الإشعارات الكاملة</li>
                            <li>اختبر البحث في الإشعارات باستخدام الكلمات المفتاحية</li>
                            <li>اختبر فلترة الإشعارات حسب النوع (مواعيد، وصفات، عام، نظام)</li>
                            <li>اختبر تحديد الإشعارات كمقروءة</li>
                            <li>اختبر حذف الإشعارات الفردية</li>
                            <li>اختبر تحديد جميع الإشعارات كمقروءة</li>
                            <li>اختبر حذف جميع الإشعارات المقروءة</li>
                            <li>تحقق من تحديث عداد الإشعارات غير المقروءة</li>
                            <li>اختبر تحميل المزيد من الإشعارات (إذا كانت متوفرة)</li>
                            <li>تحقق من عرض الإحصائيات بشكل صحيح</li>
                        </ol>
                        
                        <div class="alert alert-info">
                            <i class="bi bi-lightbulb me-2"></i>
                            <strong>نصيحة:</strong> يمكنك إنشاء إشعارات اختبار إضافية باستخدام الأزرار في قسم "إجراءات الاختبار".
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>ملاحظة:</strong> تأكد من أن جميع الوظائف تعمل بشكل صحيح قبل الانتقال إلى الاختبار التالي.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-calendar-plus display-4 mb-3"></i>
                        <h5>إشعارات المواعيد</h5>
                        <p class="mb-3">إدارة إشعارات المواعيد الجديدة والتذكيرات</p>
                        <a href="doctor/appointments" class="btn btn-light">
                            <i class="bi bi-calendar me-2"></i>
                            المواعيد
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-prescription2 display-4 mb-3"></i>
                        <h5>إشعارات الوصفات</h5>
                        <p class="mb-3">إدارة إشعارات الوصفات الطبية الجديدة</p>
                        <a href="doctor/prescriptions" class="btn btn-light">
                            <i class="bi bi-prescription2 me-2"></i>
                            الوصفات
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="bi bi-clipboard2-pulse display-4 mb-3"></i>
                        <h5>السجلات الطبية</h5>
                        <p class="mb-3">إدارة السجلات الطبية للمرضى</p>
                        <a href="doctor/medical-records" class="btn btn-light">
                            <i class="bi bi-clipboard2-pulse me-2"></i>
                            السجلات
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="text-center text-muted">
                    <small>
                        <i class="bi bi-code-slash me-1"></i>
                        نظام HealthKey - اختبار إشعارات الطبيب
                        <span class="mx-2">|</span>
                        <i class="bi bi-calendar me-1"></i>
                        <?= date('Y-m-d H:i') ?>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    function createTestNotification() {
        const types = ['appointment', 'prescription', 'general', 'system'];
        const titles = [
            'موعد جديد للاختبار',
            'وصفة طبية جديدة للاختبار',
            'إشعار عام للاختبار',
            'تحديث نظام للاختبار'
        ];
        const messages = [
            'تم إنشاء موعد اختبار جديد',
            'تم إنشاء وصفة طبية اختبار جديدة',
            'هذا إشعار عام للاختبار',
            'تم تحديث النظام للاختبار'
        ];
        
        const randomIndex = Math.floor(Math.random() * types.length);
        const type = types[randomIndex];
        const title = titles[randomIndex];
        const message = messages[randomIndex];
        
        fetch('test_create_notification.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: <?= $_SESSION['user_id'] ?>,
                title: title,
                message: message,
                type: type
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إنشاء إشعار اختبار جديد بنجاح!');
                location.reload();
            } else {
                alert('خطأ في إنشاء الإشعار');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('خطأ في الاتصال');
        });
    }
    
    function markAllAsRead() {
        if (!confirm('هل تريد تحديد جميع الإشعارات كمقروءة؟')) {
            return;
        }
        
        fetch('test_mark_all_read.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: <?= $_SESSION['user_id'] ?>
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم تحديد جميع الإشعارات كمقروءة بنجاح!');
                location.reload();
            } else {
                alert('خطأ في تحديث الإشعارات');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('خطأ في الاتصال');
        });
    }
    
    function deleteAllNotifications() {
        if (!confirm('هل تريد حذف جميع الإشعارات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            return;
        }
        
        fetch('test_delete_all_notifications.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: <?= $_SESSION['user_id'] ?>
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حذف جميع الإشعارات بنجاح!');
                location.reload();
            } else {
                alert('خطأ في حذف الإشعارات');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('خطأ في الاتصال');
        });
    }
    </script>
</body>
</html> 