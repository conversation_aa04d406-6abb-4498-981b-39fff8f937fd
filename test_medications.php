<?php
require_once 'config.php';

echo "<h1>اختبار صفحة الأدوية</h1>";

try {
    // اختبار إنشاء نموذج الأدوية
    echo "<h2>1. اختبار إنشاء نموذج الأدوية</h2>";
    $medicationModel = new Medication();
    echo "✅ تم إنشاء نموذج الأدوية بنجاح<br>";
    
    // اختبار دالة getAll
    echo "<h2>2. اختبار دالة getAll</h2>";
    $items = $medicationModel->getAll(5);
    echo "✅ عدد الأدوية: " . count($items) . "<br>";
    
    // اختبار دالة getStats
    echo "<h2>3. اختبار دالة getStats</h2>";
    $stats = $medicationModel->getStats();
    echo "✅ الإحصائيات: " . json_encode($stats, JSON_UNESCAPED_UNICODE) . "<br>";
    
    // اختبار دالة getCategories
    echo "<h2>4. اختبار دالة getCategories</h2>";
    $categories = $medicationModel->getCategories();
    echo "✅ فئات الأدوية: " . count($categories) . "<br>";
    
    // اختبار دالة getDosageForms
    echo "<h2>5. اختبار دالة getDosageForms</h2>";
    $dosageForms = $medicationModel->getDosageForms();
    echo "✅ أشكال الجرعات: " . count($dosageForms) . "<br>";
    
    // اختبار دالة getPrescriptionRequired
    echo "<h2>6. اختبار دالة getPrescriptionRequired</h2>";
    $prescriptionRequired = $medicationModel->getPrescriptionRequired(3);
    echo "✅ الأدوية التي تحتاج وصفة: " . count($prescriptionRequired) . "<br>";
    
    // اختبار دالة getOverTheCounter
    echo "<h2>7. اختبار دالة getOverTheCounter</h2>";
    $otc = $medicationModel->getOverTheCounter(3);
    echo "✅ الأدوية بدون وصفة: " . count($otc) . "<br>";
    
    // اختبار إنشاء متحكم الصيدلي
    echo "<h2>8. اختبار إنشاء متحكم الصيدلي</h2>";
    
    // محاكاة تسجيل دخول صيدلي
    session_start();
    $_SESSION['user_id'] = 1;
    $_SESSION['user_type'] = 'pharmacist';
    $_SESSION['user'] = [
        'id' => 1,
        'first_name' => 'أحمد',
        'last_name' => 'محمد',
        'email' => '<EMAIL>'
    ];
    
    // اختبار إنشاء متحكم الصيدلي
    $controller = new PharmacistController();
    echo "✅ تم إنشاء متحكم الصيدلي بنجاح<br>";
    
    // اختبار دالة medications
    echo "<h2>9. اختبار دالة medications</h2>";
    try {
        $controller->medications();
        echo "✅ دالة medications تعمل بنجاح<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في دالة medications: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>✅ انتهى الاختبار بنجاح</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 