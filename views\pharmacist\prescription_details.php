<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">تفاصيل الوصفة</h1>
        <p class="text-muted">كود الوصفة: <?= htmlspecialchars($prescription['prescription_code']) ?></p>
    </div>
    <div>
        <a href="<?= App::url('pharmacist/prescriptions') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right me-1"></i>
            العودة للبحث
        </a>
    </div>
</div>

<!-- Prescription Information -->
<div class="row">
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-prescription2 me-2"></i>
                    معلومات الوصفة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>كود الوصفة:</strong></td>
                                <td><?= htmlspecialchars($prescription['prescription_code']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>المريض:</strong></td>
                                <td><?= htmlspecialchars($prescription['patient_name']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>الطبيب:</strong></td>
                                <td><?= htmlspecialchars($prescription['doctor_name']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>التخصص:</strong></td>
                                <td><?= htmlspecialchars($prescription['doctor_specialization'] ?? 'غير محدد') ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>تاريخ الإصدار:</strong></td>
                                <td><?= DateHelper::formatArabic($prescription['issue_date']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ انتهاء الصلاحية:</strong></td>
                                <td>
                                    <?php 
                                    $expiryDate = strtotime($prescription['expiry_date']);
                                    $isExpired = $expiryDate < time();
                                    ?>
                                    <span class="<?= $isExpired ? 'text-danger' : 'text-success' ?>">
                                        <?= DateHelper::formatArabic($prescription['expiry_date']) ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    <?php if ($isExpired): ?>
                                        <span class="badge bg-danger">منتهية الصلاحية</span>
                                    <?php else: ?>
                                        <span class="badge bg-success">نشطة</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    <?php 
                                    $dispensedCount = 0;
                                    $totalCount = count($medications);
                                    foreach ($medications as $med) {
                                        if ($med['is_dispensed']) $dispensedCount++;
                                    }
                                    $dispensedPercentage = $totalCount > 0 ? ($dispensedCount / $totalCount) * 100 : 0;
                                    ?>
                                    <div class="d-flex align-items-center">
                                        <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                            <div class="progress-bar bg-success" style="width: <?= $dispensedPercentage ?>%"></div>
                                        </div>
                                        <small><?= $dispensedCount ?>/<?= $totalCount ?></small>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                <?php if (!empty($prescription['diagnosis'])): ?>
                    <hr>
                    <h6 class="text-primary">التشخيص</h6>
                    <p><?= htmlspecialchars($prescription['diagnosis']) ?></p>
                <?php endif; ?>

                <?php if (!empty($prescription['notes'])): ?>
                    <hr>
                    <h6 class="text-primary">ملاحظات الطبيب</h6>
                    <p><?= htmlspecialchars($prescription['notes']) ?></p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Medications -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-capsule me-2"></i>
                    الأدوية
                </h5>
                <?php if (!$isExpired && $dispensedCount < $totalCount): ?>
                    <button class="btn btn-primary btn-sm" id="dispenseAllBtn">
                        <i class="bi bi-capsule me-1"></i>
                        صرف جميع الأدوية
                    </button>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if (!empty($medications)): ?>
                    <?php foreach ($medications as $medication): ?>
                        <div class="card mb-3 <?= $medication['is_dispensed'] ? 'border-success' : 'border-warning' ?>">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <h6 class="card-title"><?= htmlspecialchars($medication['medication_name']) ?></h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p class="mb-1"><strong>الجرعة:</strong> <?= htmlspecialchars($medication['dosage']) ?></p>
                                                <p class="mb-1"><strong>التكرار:</strong> <?= htmlspecialchars($medication['frequency']) ?></p>
                                                <p class="mb-1"><strong>المدة:</strong> <?= htmlspecialchars($medication['duration']) ?></p>
                                            </div>
                                            <div class="col-md-6">
                                                <p class="mb-1"><strong>الكمية:</strong> <?= htmlspecialchars($medication['quantity']) ?></p>
                                                <?php if ($medication['is_dispensed']): ?>
                                                    <p class="mb-1"><strong>الكمية المصروفة:</strong> <?= htmlspecialchars($medication['dispensed_quantity']) ?></p>
                                                    <p class="mb-1"><strong>تاريخ الصرف:</strong> <?= DateHelper::formatArabic($medication['dispensed_at']) ?></p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <?php if (!empty($medication['instructions'])): ?>
                                            <p class="mb-1"><strong>التعليمات:</strong> <?= htmlspecialchars($medication['instructions']) ?></p>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <?php if ($medication['is_dispensed']): ?>
                                            <span class="badge bg-success mb-2">
                                                <i class="bi bi-check-circle me-1"></i>
                                                تم الصرف
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-warning mb-2">
                                                <i class="bi bi-clock me-1"></i>
                                                لم يتم الصرف
                                            </span>
                                            <?php if (!$isExpired): ?>
                                                <button class="btn btn-primary btn-sm dispense-medication" 
                                                        data-medication-id="<?= $medication['id'] ?>"
                                                        data-medication-name="<?= htmlspecialchars($medication['medication_name']) ?>">
                                                    <i class="bi bi-capsule me-1"></i>
                                                    صرف الدواء
                                                </button>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-capsule display-4 text-muted mb-3"></i>
                        <p class="text-muted">لا توجد أدوية في هذه الوصفة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?= App::url('pharmacist/prescriptions') ?>" class="btn btn-outline-primary">
                        <i class="bi bi-search me-2"></i>
                        البحث في وصفات أخرى
                    </a>
                    <a href="<?= App::url('pharmacist/dispensing-report') ?>" class="btn btn-outline-success">
                        <i class="bi bi-file-earmark-text me-2"></i>
                        تقرير الصرف
                    </a>
                    <button class="btn btn-outline-info" onclick="window.print()">
                        <i class="bi bi-printer me-2"></i>
                        طباعة الوصفة
                    </button>
                </div>
            </div>
        </div>

        <!-- Prescription Status -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    حالة الوصفة
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>التقدم في الصرف</h6>
                    <div class="progress mb-2" style="height: 10px;">
                        <div class="progress-bar bg-success" style="width: <?= $dispensedPercentage ?>%"></div>
                    </div>
                    <small class="text-muted"><?= $dispensedCount ?> من <?= $totalCount ?> دواء تم صرفه</small>
                </div>

                <div class="mb-3">
                    <h6>صلاحية الوصفة</h6>
                    <?php if ($isExpired): ?>
                        <div class="alert alert-danger mb-0">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            منتهية الصلاحية
                        </div>
                    <?php else: ?>
                        <?php 
                        $daysLeft = ceil(($expiryDate - time()) / (24 * 60 * 60));
                        $alertClass = $daysLeft <= 7 ? 'warning' : 'success';
                        ?>
                        <div class="alert alert-<?= $alertClass ?> mb-0">
                            <i class="bi bi-calendar-check me-2"></i>
                            متبقى <?= $daysLeft ?> يوم
                        </div>
                    <?php endif; ?>
                </div>

                <?php if (!$isExpired && $dispensedCount < $totalCount): ?>
                    <div class="alert alert-info mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        يمكن صرف الأدوية المتبقية
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Dispensing -->
<div class="modal fade" id="dispenseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">صرف الدواء</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="dispenseForm">
                    <div class="mb-3">
                        <label for="medicationName" class="form-label">اسم الدواء</label>
                        <input type="text" class="form-control" id="medicationName" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="dispensedQuantity" class="form-label">الكمية المصروفة</label>
                        <input type="number" class="form-control" id="dispensedQuantity" required min="1">
                        <div class="form-text">أدخل الكمية المصروفة للمريض</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmDispense">
                    <i class="bi bi-capsule me-1"></i>
                    تأكيد الصرف
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const dispenseModal = new bootstrap.Modal(document.getElementById('dispenseModal'));
    const dispenseForm = document.getElementById('dispenseForm');
    const medicationNameInput = document.getElementById('medicationName');
    const dispensedQuantityInput = document.getElementById('dispensedQuantity');
    const confirmDispenseBtn = document.getElementById('confirmDispense');
    let currentMedicationId = null;

    // صرف دواء واحد
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('dispense-medication')) {
            const medicationId = e.target.dataset.medicationId;
            const medicationName = e.target.dataset.medicationName;
            
            currentMedicationId = medicationId;
            medicationNameInput.value = medicationName;
            dispensedQuantityInput.value = '';
            
            dispenseModal.show();
        }
    });

    // صرف جميع الأدوية
    document.getElementById('dispenseAllBtn')?.addEventListener('click', function() {
        if (confirm('هل تريد صرف جميع الأدوية غير المصروفة؟')) {
            const undispensedMedications = [];
            document.querySelectorAll('.dispense-medication').forEach(btn => {
                undispensedMedications.push(btn.dataset.medicationId);
            });
            
            dispenseAllMedications(undispensedMedications);
        }
    });

    // تأكيد صرف الدواء
    confirmDispenseBtn.addEventListener('click', function() {
        const quantity = dispensedQuantityInput.value;
        
        if (!quantity || quantity < 1) {
            alert('يرجى إدخال كمية صحيحة');
            return;
        }
        
        dispenseMedication(currentMedicationId, quantity);
        dispenseModal.hide();
    });

    // دالة صرف دواء واحد
    function dispenseMedication(medicationId, quantity) {
        fetch('<?= App::url('pharmacist/dispense-medication') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `medication_id=${medicationId}&dispensed_quantity=${quantity}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم صرف الدواء بنجاح');
                location.reload();
            } else {
                alert('فشل في صرف الدواء: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في صرف الدواء');
        });
    }

    // دالة صرف جميع الأدوية
    function dispenseAllMedications(medicationIds) {
        let processed = 0;
        const total = medicationIds.length;
        
        medicationIds.forEach(medicationId => {
            dispenseMedication(medicationId, 1, () => {
                processed++;
                if (processed === total) {
                    alert('تم صرف جميع الأدوية بنجاح');
                    location.reload();
                }
            });
        });
    }
});
</script>

<style>
@media print {
    .btn, .modal, .card-header {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .col-lg-4 {
        display: none !important;
    }
    
    .col-lg-8 {
        width: 100% !important;
    }
}
</style> 