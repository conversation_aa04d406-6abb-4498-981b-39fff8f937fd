<?php
/**
 * ملف إعداد وتشغيل قاعدة البيانات
 * Database Setup and Execution Script
 */

// تضمين ملف التكوين
require_once __DIR__ . '/../config.php';

class DatabaseSetup
{
    private $host;
    private $username;
    private $password;
    private $database;
    private $connection;

    public function __construct()
    {
        $this->host = DB_HOST;
        $this->username = DB_USER;
        $this->password = DB_PASS;
        $this->database = DB_NAME;
    }

    /**
     * الاتصال بقاعدة البيانات
     */
    public function connect()
    {
        try {
            // الاتصال بدون تحديد قاعدة بيانات أولاً
            $this->connection = new PDO(
                "mysql:host={$this->host};charset=utf8mb4",
                $this->username,
                $this->password,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
                ]
            );
            
            echo "✅ تم الاتصال بخادم قاعدة البيانات بنجاح\n";
            return true;
        } catch (PDOException $e) {
            echo "❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * إنشاء قاعدة البيانات
     */
    public function createDatabase()
    {
        try {
            $sql = "CREATE DATABASE IF NOT EXISTS `{$this->database}` 
                    DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            
            $this->connection->exec($sql);
            echo "✅ تم إنشاء قاعدة البيانات '{$this->database}' بنجاح\n";
            
            // الاتصال بقاعدة البيانات المحددة
            $this->connection->exec("USE `{$this->database}`");
            echo "✅ تم الاتصال بقاعدة البيانات '{$this->database}'\n";
            
            return true;
        } catch (PDOException $e) {
            echo "❌ خطأ في إنشاء قاعدة البيانات: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * تنفيذ ملف SQL
     */
    public function executeSQLFile($filePath)
    {
        if (!file_exists($filePath)) {
            echo "❌ ملف SQL غير موجود: $filePath\n";
            return false;
        }

        try {
            $sql = file_get_contents($filePath);
            
            // تقسيم الاستعلامات
            $statements = $this->splitSQLStatements($sql);
            
            $successCount = 0;
            $errorCount = 0;
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (empty($statement) || $statement === ';') {
                    continue;
                }
                
                try {
                    $this->connection->exec($statement);
                    $successCount++;
                } catch (PDOException $e) {
                    echo "⚠️ خطأ في تنفيذ الاستعلام: " . $e->getMessage() . "\n";
                    echo "الاستعلام: " . substr($statement, 0, 100) . "...\n";
                    $errorCount++;
                }
            }
            
            echo "✅ تم تنفيذ $successCount استعلام بنجاح\n";
            if ($errorCount > 0) {
                echo "⚠️ فشل في تنفيذ $errorCount استعلام\n";
            }
            
            return $errorCount === 0;
        } catch (Exception $e) {
            echo "❌ خطأ في قراءة ملف SQL: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * تقسيم استعلامات SQL
     */
    private function splitSQLStatements($sql)
    {
        // إزالة التعليقات
        $sql = preg_replace('/--.*$/m', '', $sql);
        $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);
        
        // تقسيم حسب الفاصلة المنقوطة
        $statements = [];
        $current = '';
        $inString = false;
        $stringChar = '';
        $inDelimiter = false;
        
        $lines = explode("\n", $sql);
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            // تحقق من DELIMITER
            if (preg_match('/^DELIMITER\s+(.+)$/i', $line, $matches)) {
                $delimiter = trim($matches[1]);
                $inDelimiter = ($delimiter !== ';');
                continue;
            }
            
            if (empty($line)) continue;
            
            $current .= $line . "\n";
            
            // تحقق من نهاية الاستعلام
            if ($inDelimiter) {
                if (strpos($line, '$$') !== false) {
                    $statements[] = $current;
                    $current = '';
                    $inDelimiter = false;
                }
            } else {
                if (substr(rtrim($line), -1) === ';') {
                    $statements[] = $current;
                    $current = '';
                }
            }
        }
        
        if (!empty(trim($current))) {
            $statements[] = $current;
        }
        
        return $statements;
    }

    /**
     * التحقق من وجود الجداول
     */
    public function checkTables()
    {
        try {
            $sql = "SHOW TABLES";
            $stmt = $this->connection->query($sql);
            $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $expectedTables = [
                'users', 'doctors', 'pharmacists', 'appointments', 
                'medical_records', 'prescriptions', 'prescription_medications',
                'allergies', 'lab_tests', 'notifications', 'attachments',
                'activity_logs', 'system_settings', 'user_sessions', 'messages'
            ];
            
            echo "\n📋 الجداول الموجودة:\n";
            foreach ($tables as $table) {
                $status = in_array($table, $expectedTables) ? "✅" : "ℹ️";
                echo "$status $table\n";
            }
            
            $missingTables = array_diff($expectedTables, $tables);
            if (!empty($missingTables)) {
                echo "\n⚠️ الجداول المفقودة:\n";
                foreach ($missingTables as $table) {
                    echo "❌ $table\n";
                }
                return false;
            }
            
            echo "\n✅ جميع الجداول المطلوبة موجودة\n";
            return true;
        } catch (PDOException $e) {
            echo "❌ خطأ في التحقق من الجداول: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * التحقق من البيانات الأولية
     */
    public function checkInitialData()
    {
        try {
            echo "\n📊 التحقق من البيانات الأولية:\n";
            
            // التحقق من المستخدمين
            $stmt = $this->connection->query("SELECT COUNT(*) as count, user_type FROM users GROUP BY user_type");
            $userCounts = $stmt->fetchAll();
            
            foreach ($userCounts as $count) {
                echo "👥 {$count['user_type']}: {$count['count']} مستخدم\n";
            }
            
            // التحقق من الإعدادات
            $stmt = $this->connection->query("SELECT COUNT(*) as count FROM system_settings");
            $settingsCount = $stmt->fetch()['count'];
            echo "⚙️ إعدادات النظام: $settingsCount إعداد\n";
            
            // التحقق من وجود المدير
            $stmt = $this->connection->query("SELECT COUNT(*) as count FROM users WHERE user_type = 'admin'");
            $adminCount = $stmt->fetch()['count'];
            
            if ($adminCount === 0) {
                echo "⚠️ لا يوجد مدير في النظام\n";
                return false;
            }
            
            echo "✅ البيانات الأولية موجودة\n";
            return true;
        } catch (PDOException $e) {
            echo "❌ خطأ في التحقق من البيانات: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * اختبار الاتصال النهائي
     */
    public function testConnection()
    {
        try {
            // اختبار استعلام بسيط
            $stmt = $this->connection->query("SELECT 1 as test");
            $result = $stmt->fetch();
            
            if ($result['test'] == 1) {
                echo "✅ اختبار الاتصال نجح\n";
                return true;
            }
            
            return false;
        } catch (PDOException $e) {
            echo "❌ فشل اختبار الاتصال: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * إغلاق الاتصال
     */
    public function close()
    {
        $this->connection = null;
        echo "🔒 تم إغلاق الاتصال بقاعدة البيانات\n";
    }

    /**
     * تشغيل الإعداد الكامل
     */
    public function runSetup()
    {
        echo "🚀 بدء إعداد قاعدة البيانات HealthKey\n";
        echo str_repeat("=", 50) . "\n";
        
        // الخطوة 1: الاتصال
        if (!$this->connect()) {
            return false;
        }
        
        // الخطوة 2: إنشاء قاعدة البيانات
        if (!$this->createDatabase()) {
            return false;
        }
        
        // الخطوة 3: تنفيذ ملف SQL
        $sqlFile = __DIR__ . '/healthkey_database.sql';
        echo "\n📁 تنفيذ ملف SQL: $sqlFile\n";
        if (!$this->executeSQLFile($sqlFile)) {
            echo "⚠️ تم تنفيذ الملف مع بعض الأخطاء\n";
        }
        
        // الخطوة 4: التحقق من الجداول
        if (!$this->checkTables()) {
            echo "⚠️ بعض الجداول مفقودة\n";
        }
        
        // الخطوة 5: التحقق من البيانات
        if (!$this->checkInitialData()) {
            echo "⚠️ بعض البيانات الأولية مفقودة\n";
        }
        
        // الخطوة 6: اختبار الاتصال
        $this->testConnection();
        
        echo "\n" . str_repeat("=", 50) . "\n";
        echo "🎉 تم إكمال إعداد قاعدة البيانات!\n";
        echo "\n📋 معلومات الدخول الافتراضية:\n";
        echo "👨‍💼 المدير: <EMAIL>\n";
        echo "👨‍⚕️ الطبيب: <EMAIL>\n";
        echo "👤 المريض: <EMAIL>\n";
        echo "💊 الصيدلي: <EMAIL>\n";
        echo "🔑 كلمة المرور لجميع الحسابات: password\n";
        
        return true;
    }
}

// تشغيل الإعداد إذا تم استدعاء الملف مباشرة
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $setup = new DatabaseSetup();
    $setup->runSetup();
    $setup->close();
}
