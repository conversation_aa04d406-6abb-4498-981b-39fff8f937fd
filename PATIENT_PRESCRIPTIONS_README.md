# صفحة الوصفات الطبية للمريض - HealthKey

## نظرة عامة
تم إنشاء صفحة الوصفات الطبية للمرضى في نظام HealthKey، تتيح للمرضى إدارة وعرض وصفاتهم الطبية بطريقة متقدمة مع إمكانيات البحث والتصفية والترتيب والطباعة والتصدير.

## المميزات الرئيسية

### 🔍 **البحث والتصفية المتقدمة**
- **تصفية حسب الحالة**: نشطة، مكتملة، منتهية، ملغية
- **تصفية حسب التاريخ**: البحث في تاريخ محدد
- **البحث النصي**: البحث في محتوى الوصفات والأدوية
- **ترتيب متعدد**: حسب التاريخ، الطبيب، الحالة

### 📊 **عرض منظم ومتقدم**
- **إحصائيات شاملة**: عرض الإحصائيات في البطاقات العلوية
- **قائمة منظمة**: عرض الوصفات بطريقة منظمة وواضحة
- **أزرار الإجراءات**: عرض التفاصيل، الطباعة، التحميل، التصدير
- **تصميم متجاوب**: يعمل على جميع الأجهزة

### 🎯 **وظائف تفاعلية**
- **عرض التفاصيل**: النقر لعرض التفاصيل الكاملة
- **الطباعة المباشرة**: طباعة الوصفة مباشرة
- **التحميل**: تحميل الوصفة بصيغة HTML
- **التصدير**: تصدير جميع الوصفات
- **الترتيب الديناميكي**: تغيير الترتيب حسب الحاجة

### 📱 **تجربة مستخدم محسنة**
- **تصفية فورية**: نتائج فورية عند التصفية
- **بحث سريع**: البحث في الوقت الفعلي
- **تنقل سهل**: سهولة التنقل بين الأقسام
- **ألوان مميزة**: تمييز بصري للحالات المختلفة

## الملفات المضافة/المعدلة

### 1. **الصفحة الرئيسية** (`views/patient/prescriptions.php`)
```php
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count($prescriptions) ?></h4>
                        <p class="mb-0">إجمالي الوصفات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-prescription2 display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- More statistics cards... -->
</div>
```

### 2. **صفحة التفاصيل** (`views/patient/prescription_details.php`)
```php
<!-- Prescription Information -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-prescription2 me-2"></i>
            معلومات الوصفة
        </h5>
    </div>
    <div class="card-body">
        <!-- Prescription details content -->
    </div>
</div>
```

### 3. **صفحة التحميل** (`views/patient/download_prescription.php`)
```html
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <title>الوصفة الطبية - <?= htmlspecialchars($patient['name']) ?></title>
    <!-- CSS styles for print-friendly layout -->
</head>
<body>
    <!-- Prescription content in HTML format -->
</body>
</html>
```

### 4. **صفحة التصدير** (`views/patient/export_prescriptions.php`)
```html
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <title>الوصفات الطبية - <?= htmlspecialchars($patient['name']) ?></title>
    <!-- CSS styles for comprehensive report -->
</head>
<body>
    <!-- Complete prescriptions report -->
</body>
</html>
```

### 5. **المسارات الجديدة** (`app/core/App.php`)
```php
'patient/view-prescription' => ['controller' => 'PatientController', 'method' => 'viewPrescription'],
'patient/download-prescription' => ['controller' => 'PatientController', 'method' => 'downloadPrescription'],
'patient/export-prescriptions' => ['controller' => 'PatientController', 'method' => 'exportPrescriptions'],
```

### 6. **الطرق الجديدة** (`app/controllers/PatientController.php`)
```php
public function viewPrescription($prescriptionId)
{
    // التحقق من ملكية الوصفة
    $prescription = $this->prescriptionModel->findById($prescriptionId);
    if (!$prescription || $prescription['patient_id'] != $this->currentUser['id']) {
        $this->setFlashMessage('الوصفة غير موجودة', 'error');
        $this->redirect('patient/prescriptions');
        return;
    }

    $data = [
        'title' => 'تفاصيل الوصفة',
        'prescription' => $prescription,
        'medications' => $this->prescriptionModel->getMedications($prescriptionId)
    ];

    $this->view('patient/prescription_details', $data);
}

public function downloadPrescription($prescriptionId)
{
    // التحقق من ملكية الوصفة
    $prescription = $this->prescriptionModel->findById($prescriptionId);
    if (!$prescription || $prescription['patient_id'] != $this->currentUser['id']) {
        $this->setFlashMessage('الوصفة غير موجودة', 'error');
        $this->redirect('patient/prescriptions');
        return;
    }

    $data = [
        'prescription' => $prescription,
        'medications' => $this->prescriptionModel->getMedications($prescriptionId),
        'patient' => $this->currentUser
    ];

    // إعداد headers للتحميل
    header('Content-Type: text/html; charset=utf-8');
    header('Content-Disposition: attachment; filename="prescription_' . $prescriptionId . '_' . date('Y-m-d') . '.html"');

    $this->view('patient/download_prescription', $data);
}

public function exportPrescriptions()
{
    $patientId = $this->currentUser['id'];
    
    $data = [
        'prescriptions' => $this->prescriptionModel->getByPatient($patientId),
        'patient' => $this->currentUser
    ];

    // إعداد headers للتصدير
    header('Content-Type: text/html; charset=utf-8');
    header('Content-Disposition: attachment; filename="prescriptions_' . date('Y-m-d') . '.html"');

    $this->view('patient/export_prescriptions', $data);
}
```

## الوظائف التقنية

### 🔧 **JavaScript التفاعلي**
```javascript
// Filter and Search Functions
$('#statusFilter').change(function() {
    currentFilter = $(this).val();
    filterPrescriptions();
});

$('#dateFilter').change(function() {
    filterPrescriptions();
});

$('#searchFilter').on('input', function() {
    filterPrescriptions();
});

function filterPrescriptions() {
    const statusFilter = $('#statusFilter').val();
    const dateFilter = $('#dateFilter').val();
    const searchFilter = $('#searchFilter').val().toLowerCase();
    
    $('.prescription-item').each(function() {
        let show = true;
        const $item = $(this);
        const status = $item.data('status');
        const date = $item.data('date');
        const doctor = $item.data('doctor') || '';
        const medications = $item.data('medications') || '';
        const text = $item.text().toLowerCase();
        
        // Apply filters
        if (statusFilter && status !== statusFilter) show = false;
        if (dateFilter && date !== dateFilter) show = false;
        if (searchFilter && !text.includes(searchFilter) && !doctor.toLowerCase().includes(searchFilter) && !medications.toLowerCase().includes(searchFilter)) {
            show = false;
        }
        
        $item.toggle(show);
    });
}

// Sort Functions
function sortPrescriptions(sortBy) {
    currentSort = sortBy;
    const $list = $('#prescriptionsList');
    const $items = $list.children('.prescription-item').get();
    
    $items.sort(function(a, b) {
        const $a = $(a);
        const $b = $(b);
        
        if (sortBy === 'date') {
            return new Date($b.data('date')) - new Date($a.data('date'));
        } else if (sortBy === 'doctor') {
            return ($a.data('doctor') || '').localeCompare($b.data('doctor') || '');
        } else if (sortBy === 'status') {
            return ($a.data('status') || '').localeCompare($b.data('status') || '');
        }
        
        return 0;
    });
    
    $list.empty().append($items);
}

// View Prescription Details
function viewPrescription(prescriptionId) {
    $.get('<?= App::url('patient/view-prescription') ?>/' + prescriptionId, function(response) {
        $('#prescriptionDetails').html(response);
        $('#prescriptionModal').modal('show');
    }).fail(function() {
        showAlert('حدث خطأ في تحميل تفاصيل الوصفة', 'error');
    });
}

// Download Prescription
function downloadPrescription(prescriptionId) {
    window.open('<?= App::url('patient/download-prescription') ?>/' + prescriptionId, '_blank');
}

// Export Prescriptions
function exportPrescriptions() {
    window.open('<?= App::url('patient/export-prescriptions') ?>', '_blank');
}
```

### 📊 **معالجة البيانات**
```php
// في PatientController::prescriptions()
$data = [
    'title' => 'وصفاتي الطبية',
    'prescriptions' => $this->prescriptionModel->getByPatient($patientId)
];

// في PatientController::viewPrescription()
$data = [
    'title' => 'تفاصيل الوصفة',
    'prescription' => $prescription,
    'medications' => $this->prescriptionModel->getMedications($prescriptionId)
];
```

## كيفية الاستخدام

### 1. **الوصول للصفحة**
```
http://localhost:8000/patient/prescriptions
```

### 2. **البحث والتصفية**
- **تصفية حسب الحالة**: اختر حالة الوصفة (نشطة، مكتملة، منتهية، ملغية)
- **تصفية حسب التاريخ**: اختر تاريخ محدد
- **البحث النصي**: اكتب للبحث في محتوى الوصفات

### 3. **الترتيب**
- **حسب التاريخ**: الأحدث أولاً
- **حسب الطبيب**: ترتيب أبجدي للطبيب
- **حسب الحالة**: ترتيب حسب حالة الوصفة

### 4. **عرض التفاصيل**
- **عرض التفاصيل**: النقر على زر العين لعرض التفاصيل الكاملة
- **الطباعة**: النقر على زر الطباعة لطباعة الوصفة
- **التحميل**: تحميل الوصفة بصيغة HTML
- **التصدير**: تصدير جميع الوصفات

### 5. **تفاصيل الوصفة**
- **معلومات الوصفة**: التاريخ، الطبيب، الحالة، تاريخ الانتهاء
- **الأدوية الموصوفة**: قائمة مفصلة بالأدوية والجرعات
- **التشخيص والملاحظات**: معلومات إضافية
- **الجدول الزمني**: تاريخ إنشاء وتحديث الوصفة

## المميزات الإضافية

### 🎨 **التصميم المتجاوب**
- يعمل على جميع الأجهزة (الهواتف، الأجهزة اللوحية، الحواسيب)
- تصميم حديث وأنيق
- ألوان متناسقة مع باقي النظام

### 📱 **تجربة مستخدم محسنة**
- نتائج فورية عند التصفية
- بحث في الوقت الفعلي
- تنقل سهل بين الأقسام
- أزرار واضحة للإجراءات

### 🔔 **الإشعارات والتنبيهات**
- رسائل تأكيد واضحة
- تنبيهات في حالة حدوث خطأ
- إشعارات عند اكتمال العمليات

### 🖨️ **الطباعة والتصدير**
- **طباعة مباشرة**: طباعة الوصفة من المتصفح
- **تحميل HTML**: تحميل الوصفة بصيغة HTML قابلة للطباعة
- **تصدير شامل**: تصدير جميع الوصفات في تقرير واحد
- **تصميم مطبوع**: تصميم محسن للطباعة

## الأمان والتحقق

### ✅ **التحقق من البيانات**
- التحقق من ملكية الوصفات الطبية
- حماية من الوصول غير المصرح
- التحقق من صحة البيانات المعروضة
- التحقق من صلاحية الوصفات

### 🛡️ **الحماية**
- التحقق من تسجيل دخول المريض
- حماية من الوصول غير المصرح
- التحقق من ملكية البيانات
- تشفير البيانات الحساسة

## الاختبار

### 🧪 **اختبار الوظائف**
1. **اختبار التصفية حسب الحالة**
2. **اختبار التصفية حسب التاريخ**
3. **اختبار البحث النصي**
4. **اختبار الترتيب**
5. **اختبار عرض التفاصيل**
6. **اختبار الطباعة والتحميل والتصدير**

### 📱 **اختبار التجاوب**
- اختبار على الهواتف الذكية
- اختبار على الأجهزة اللوحية
- اختبار على الحواسيب المكتبية

## التطوير المستقبلي

### 🚀 **مميزات مقترحة**
- إضافة إشعارات للوصفات القريبة من انتهاء الصلاحية
- إضافة تذكيرات للأدوية
- إضافة مشاركة الوصفات مع الصيدليات
- إضافة نظام تقييم للأدوية
- إضافة تاريخ الأدوية السابقة

### 🔧 **تحسينات تقنية**
- تحسين سرعة تحميل البيانات
- إضافة ذاكرة مؤقتة للبيانات
- تحسين تجربة المستخدم على الأجهزة المحمولة
- إضافة دعم للوضع المظلم
- إضافة إشعارات push

## الخلاصة

تم إنشاء صفحة وصفات طبية شاملة ومتقدمة تتيح للمرضى إدارة وعرض وصفاتهم الطبية بطريقة متطورة مع إمكانيات البحث والتصفية والترتيب والطباعة والتصدير. الصفحة تتميز بواجهة مستخدم حديثة ووظائف متقدمة مع ضمان الأمان والتحقق من صحة البيانات.

🎉 **تم إنشاء الصفحة بنجاح وجاهزة للاستخدام!** 