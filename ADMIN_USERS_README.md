# صفحة إدارة المستخدمين - HealthKey

## نظرة عامة

تم إنشاء صفحة إدارة المستخدمين المتكاملة للمدير في نظام HealthKey. هذه الصفحة تتيح للمدير إدارة جميع مستخدمي النظام بطريقة شاملة ومتقدمة.

## الميزات المتوفرة

### 📊 إحصائيات شاملة
- **إجمالي المستخدمين**: عرض العدد الكلي للمستخدمين
- **المستخدمين النشطين**: عرض المستخدمين المفعلين
- **المستخدمين غير النشطين**: عرض المستخدمين المعطلين
- **المستخدمين الجدد**: عرض المستخدمين المسجلين هذا الشهر

### 🔍 فلاتر بحث متقدمة
- **البحث النصي**: البحث بالاسم، البريد الإلكتروني، أو الهاتف
- **فلتر نوع المستخدم**: تصفية حسب نوع المستخدم (مريض، طبيب، صيدلي، مدير)
- **فلتر الحالة**: تصفية حسب حالة النشاط (نشط، غير نشط)

### 📋 عرض البيانات
- **جدول تفاعلي**: عرض جميع بيانات المستخدمين في جدول منظم
- **معلومات شاملة**: الاسم، النوع، معلومات الاتصال، تاريخ التسجيل، آخر تسجيل دخول
- **حالة المستخدم**: عرض حالة النشاط بوضوح
- **تحديد متعدد**: إمكانية تحديد عدة مستخدمين في نفس الوقت

### ⚡ إجراءات سريعة
- **تعديل المستخدم**: تعديل بيانات أي مستخدم
- **تفعيل/إلغاء التفعيل**: تغيير حالة نشاط المستخدم
- **حذف المستخدم**: حذف المستخدم من النظام
- **إجراءات جماعية**: تطبيق إجراءات على عدة مستخدمين في نفس الوقت

### 📤 تصدير البيانات
- **تصدير CSV**: تصدير بيانات المستخدمين بصيغة CSV
- **فلترة التصدير**: تصدير البيانات المفلترة فقط

## كيفية الوصول للصفحة

### عبر المتصفح
```
http://localhost/HealthKey/admin/users
```

### بيانات الدخول الافتراضية
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: password

## الوظائف المتاحة

### 1. عرض المستخدمين
- عرض قائمة بجميع المستخدمين مع معلوماتهم الأساسية
- ترتيب البيانات حسب التاريخ أو النوع
- عرض حالة النشاط بوضوح

### 2. البحث والفلترة
```php
// مثال على البحث
GET /admin/users?search=أحمد&user_type=doctor&status=active
```

### 3. تعديل المستخدم
- النقر على زر التعديل بجانب أي مستخدم
- تعديل البيانات الشخصية والمعلومات الأساسية
- تغيير نوع المستخدم إذا لزم الأمر

### 4. تفعيل/إلغاء التفعيل
```javascript
// تفعيل المستخدم
toggleUserStatus(userId);

// إلغاء تفعيل المستخدم
toggleUserStatus(userId);
```

### 5. حذف المستخدم
```javascript
// حذف المستخدم مع تأكيد
deleteUser(userId);
```

### 6. الإجراءات الجماعية
```javascript
// تحديد عدة مستخدمين
selectAllUsers();

// تفعيل المحددين
bulkActivate();

// إلغاء تفعيل المحددين
bulkDeactivate();

// حذف المحددين
bulkDelete();
```

## API Endpoints

### الحصول على إحصائيات المستخدمين
```
GET /admin/api/users/stats
```

**الاستجابة:**
```json
{
    "success": true,
    "stats": {
        "total": 150,
        "active": 120,
        "inactive": 30,
        "new_this_month": 15
    }
}
```

### تغيير حالة المستخدم
```
POST /admin/toggle-user-status
Content-Type: application/x-www-form-urlencoded

user_id=123
```

### حذف المستخدم
```
POST /admin/delete-user
Content-Type: application/x-www-form-urlencoded

user_id=123
```

### الإجراءات الجماعية
```
POST /admin/bulk-user-action
Content-Type: application/json

{
    "action": "activate",
    "user_ids": [1, 2, 3, 4, 5]
}
```

## الأمان والحماية

### التحقق من الصلاحيات
- التحقق من تسجيل دخول المدير
- منع حذف المدير الحالي
- التحقق من صحة البيانات المدخلة

### حماية البيانات
```php
// التحقق من الصلاحيات
$this->requireAuth();
$this->requireUserType('admin');

// منع حذف المدير الحالي
if ($userId == $this->currentUser['id']) {
    $this->json(['success' => false, 'message' => 'لا يمكن حذف حسابك الخاص'], 400);
    return;
}
```

## التخصيص والتطوير

### إضافة حقول جديدة
```php
// في نموذج User.php
public function getAll($filters = [])
{
    $query = "SELECT u.*, d.specialization, p.pharmacy_name 
              FROM users u 
              LEFT JOIN doctors d ON u.id = d.user_id 
              LEFT JOIN pharmacists p ON u.id = p.user_id";
    // ... باقي الكود
}
```

### إضافة فلاتر جديدة
```php
// في AdminController.php
public function users()
{
    $filters = [
        'limit' => $limit,
        'offset' => $offset,
        'user_type' => App::get('user_type', ''),
        'search' => App::get('search', ''),
        'status' => App::get('status', ''),
        'date_from' => App::get('date_from', ''), // فلتر جديد
        'date_to' => App::get('date_to', '')       // فلتر جديد
    ];
    // ... باقي الكود
}
```

### إضافة إجراءات جديدة
```php
// في AdminController.php
public function bulkUserAction()
{
    switch ($action) {
        case 'activate':
            // تفعيل المستخدمين
            break;
        case 'deactivate':
            // إلغاء تفعيل المستخدمين
            break;
        case 'delete':
            // حذف المستخدمين
            break;
        case 'export':
            // تصدير البيانات
            break;
        case 'send_notification':
            // إرسال إشعارات
            break;
    }
}
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في الاتصال بقاعدة البيانات
```php
// التحقق من إعدادات قاعدة البيانات في config.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'healthkey');
define('DB_USER', 'root');
define('DB_PASS', '');
```

#### 2. خطأ في الصلاحيات
```php
// التأكد من تسجيل دخول المدير
if (!SessionHelper::isLoggedIn() || !SessionHelper::isAdmin()) {
    App::redirect('auth/login');
}
```

#### 3. خطأ في عرض البيانات
```php
// التحقق من وجود البيانات
if (empty($users)) {
    echo '<div class="alert alert-info">لا توجد مستخدمين</div>';
}
```

## التحديثات المستقبلية

### الميزات المخطط إضافتها
- [ ] تصدير البيانات بصيغ مختلفة (PDF, Excel)
- [ ] إرسال إشعارات جماعية للمستخدمين
- [ ] تقارير مفصلة عن نشاط المستخدمين
- [ ] نظام صلاحيات متقدم
- [ ] سجل التغييرات والمراجعة
- [ ] دعم الصور الشخصية للمستخدمين
- [ ] نظام تتبع الجلسات
- [ ] إحصائيات متقدمة وتحليلات

### تحسينات الأداء
- [ ] تخزين مؤقت للبيانات
- [ ] تحسين استعلامات قاعدة البيانات
- [ ] تحميل البيانات تدريجياً (Lazy Loading)
- [ ] ضغط البيانات المرسلة

## الدعم والمساعدة

### للمطورين
- راجع ملفات الكود المصدري للفهم العميق
- استخدم أدوات التطوير في المتصفح لفحص الشبكة
- راجع سجلات الأخطاء في الخادم

### للمستخدمين
- تأكد من صحة بيانات الدخول
- تحقق من اتصال الإنترنت
- امسح ذاكرة التخزين المؤقت للمتصفح

---

**تم تطوير هذه الصفحة بواسطة فريق HealthKey**
**آخر تحديث: ديسمبر 2024** 