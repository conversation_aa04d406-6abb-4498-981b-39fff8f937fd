<?php
// اختبار دالة updateQuantity
require_once 'config.php';

try {
    $db = Database::getInstance();
    
    echo "<h1>اختبار دالة updateQuantity</h1>";
    
    // اختبار تحديث كمية عنصر موجود
    $testId = 1; // استخدام أول عنصر في الجدول
    $newQuantity = 200;
    
    echo "<h3>اختبار تحديث الكمية:</h3>";
    echo "<p>تحديث العنصر ID: $testId إلى الكمية: $newQuantity</p>";
    
    // تحديث الكمية
    $result = $db->update("UPDATE inventory SET quantity = ? WHERE id = ?", [$newQuantity, $testId]);
    
    if ($result) {
        echo "<p style='color: green;'>✅ تم تحديث الكمية بنجاح</p>";
        
        // التحقق من التحديث
        $item = $db->selectOne("SELECT * FROM inventory WHERE id = ?", [$testId]);
        if ($item) {
            echo "<p>الكمية الجديدة: " . $item['quantity'] . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ فشل في تحديث الكمية</p>";
    }
    
    // اختبار الحصول على البيانات
    echo "<h3>اختبار الحصول على البيانات:</h3>";
    $items = $db->select("SELECT id, name, quantity FROM inventory LIMIT 5");
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>الاسم</th><th>الكمية</th></tr>";
    foreach ($items as $item) {
        echo "<tr>";
        echo "<td>" . $item['id'] . "</td>";
        echo "<td>" . htmlspecialchars($item['name']) . "</td>";
        echo "<td>" . $item['quantity'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2 style='color: green;'>✅ الاختبار اكتمل بنجاح!</h2>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<p>تفاصيل الخطأ: " . $e->getTraceAsString() . "</p>";
}
?> 