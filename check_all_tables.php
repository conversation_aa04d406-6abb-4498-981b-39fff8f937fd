<?php
/**
 * فحص جميع الجداول الموجودة في قاعدة البيانات
 */

require_once 'config.php';
require_once 'app/core/Database.php';

echo "<h1>فحص جميع الجداول الموجودة</h1>";

try {
    $db = Database::getInstance();
    
    // الحصول على قائمة جميع الجداول
    $tables = $db->select("SHOW TABLES");
    
    echo "<h3>الجداول الموجودة:</h3>";
    echo "<ul>";
    foreach ($tables as $table) {
        $tableName = array_values($table)[0];
        echo "<li>$tableName</li>";
    }
    echo "</ul>";
    
    // فحص جدول prescriptions إذا كان موجود
    if (in_array('prescriptions', array_column($tables, array_keys($tables)[0]))) {
        echo "<h3>هيكل جدول prescriptions:</h3>";
        $columns = $db->select("DESCRIBE prescriptions");
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // عرض عدد السجلات
        $count = $db->selectOne("SELECT COUNT(*) as count FROM prescriptions")['count'];
        echo "✅ عدد السجلات في جدول prescriptions: $count<br>";
    }
    
    // فحص جدول medications إذا كان موجود
    if (in_array('medications', array_column($tables, array_keys($tables)[0]))) {
        echo "<h3>هيكل جدول medications:</h3>";
        $columns = $db->select("DESCRIBE medications");
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // عرض عدد السجلات
        $count = $db->selectOne("SELECT COUNT(*) as count FROM medications")['count'];
        echo "✅ عدد السجلات في جدول medications: $count<br>";
    }
    
    // فحص جدول prescription_medications إذا كان موجود
    if (in_array('prescription_medications', array_column($tables, array_keys($tables)[0]))) {
        echo "<h3>هيكل جدول prescription_medications:</h3>";
        $columns = $db->select("DESCRIBE prescription_medications");
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // عرض عدد السجلات
        $count = $db->selectOne("SELECT COUNT(*) as count FROM prescription_medications")['count'];
        echo "✅ عدد السجلات في جدول prescription_medications: $count<br>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في فحص الجداول</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 