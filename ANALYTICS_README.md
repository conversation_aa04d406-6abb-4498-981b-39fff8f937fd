# صفحة التحليلات والإحصائيات (Admin Analytics)

## نظرة عامة

صفحة التحليلات والإحصائيات تقدم تحليلاً شاملاً لأداء النظام والمستخدمين، مع رسوم بيانية تفاعلية وجداول إحصائية مفصلة.

## الميزات الرئيسية

### 1. البطاقات الإحصائية
- **إجمالي المستخدمين**: عدد المستخدمين المسجلين في الفترة المحددة
- **المواعيد النشطة**: عدد المواعيد المؤكدة
- **الوصفات الطبية**: إجمالي الوصفات الطبية
- **معدل النمو**: نسبة نمو المستخدمين مقارنة بالفترة السابقة

### 2. الرسوم البيانية
- **مخطط النشاط اليومي**: يعرض نشاط النظام على مدار الوقت
- **مخطط توزيع المستخدمين**: يعرض توزيع المستخدمين حسب النوع

### 3. الجداول الإحصائية
- **أفضل الأطباء**: قائمة بأفضل الأطباء حسب عدد المواعيد والتقييم
- **الأمراض الشائعة**: قائمة بأكثر الأمراض شيوعاً

### 4. مؤشرات الأداء (KPIs)
- **متوسط وقت الموعد**: الوقت المتوسط للمواعيد
- **رضا المرضى**: نسبة رضا المرضى
- **وقت تشغيل النظام**: نسبة الوقت الذي يعمل فيه النظام
- **وقت الاستجابة**: متوسط وقت استجابة النظام

## الفلاتر والتحكم

### فلاتر التاريخ
- **آخر 7 أيام**: عرض البيانات للسبعة أيام الماضية
- **آخر 30 يوم**: عرض البيانات للثلاثين يوم الماضية
- **آخر 3 أشهر**: عرض البيانات للثلاثة أشهر الماضية
- **آخر سنة**: عرض البيانات للسنة الماضية
- **فترة مخصصة**: تحديد فترة زمنية مخصصة

### أنواع التحليل
- **نظرة عامة**: إحصائيات شاملة
- **المستخدمين**: تحليل مفصل للمستخدمين
- **المواعيد**: تحليل المواعيد والحجوزات
- **الوصفات الطبية**: تحليل الوصفات الطبية
- **الأداء**: مؤشرات أداء النظام

## الوظائف المتاحة

### 1. تصدير التقارير
- تصدير البيانات التحليلية بصيغة JSON
- تصدير الرسوم البيانية كصور
- طباعة التقارير

### 2. تحديث البيانات
- تحديث فوري للبيانات
- إعادة تحميل الرسوم البيانية
- تحديث الجداول الإحصائية

### 3. عرض التفاصيل
- عرض تفاصيل مفصلة لكل نوع تحليل
- إمكانية تصدير التفاصيل
- عرض البيانات في مودال تفاعلي

## الملفات المطلوبة

### 1. ملف العرض
```
views/admin/analytics.php
```

### 2. دوال التحكم
```
app/controllers/AdminController.php
- analytics()
- handleAjaxAnalyticsRequest()
- getAnalyticsData()
- getAnalyticsStats()
- getActivityData()
- getUsersData()
- getTopDoctors()
- getCommonDiseases()
- exportAnalytics()
- exportAnalyticsDetails()
```

### 3. المسار
```
app/core/App.php
- إضافة مسار 'admin/analytics'
```

## الاستعلامات SQL المستخدمة

### 1. إحصائيات المستخدمين
```sql
SELECT COUNT(*) as count FROM users 
WHERE created_at BETWEEN ? AND ?
```

### 2. إحصائيات المواعيد
```sql
SELECT COUNT(*) as count FROM appointments 
WHERE status = 'confirmed' AND appointment_date BETWEEN ? AND ?
```

### 3. إحصائيات الوصفات الطبية
```sql
SELECT COUNT(*) as count FROM prescriptions 
WHERE created_at BETWEEN ? AND ?
```

### 4. بيانات النشاط
```sql
SELECT DATE(created_at) as date, COUNT(*) as count 
FROM users 
WHERE created_at BETWEEN ? AND ? 
GROUP BY DATE(created_at) 
ORDER BY date
```

### 5. بيانات المستخدمين
```sql
SELECT user_type, COUNT(*) as count 
FROM users 
WHERE created_at BETWEEN ? AND ? 
GROUP BY user_type
```

### 6. أفضل الأطباء
```sql
SELECT u.first_name, u.last_name, COUNT(a.id) as appointments, AVG(a.rating) as rating
FROM users u
LEFT JOIN appointments a ON u.id = a.doctor_id
WHERE u.user_type = 'doctor' 
AND (a.appointment_date BETWEEN ? AND ? OR a.appointment_date IS NULL)
GROUP BY u.id, u.first_name, u.last_name
ORDER BY appointments DESC, rating DESC
LIMIT 10
```

## المكتبات المستخدمة

### 1. Chart.js
- رسم الرسوم البيانية التفاعلية
- دعم أنواع مختلفة من الرسوم البيانية
- إمكانية التصدير والطباعة

### 2. Bootstrap 5
- تصميم متجاوب
- مكونات UI جاهزة
- دعم اللغة العربية

### 3. Bootstrap Icons
- أيقونات جميلة ومتسقة
- دعم جميع المتصفحات

## الأمان والصلاحيات

### 1. التحقق من الصلاحيات
```php
if (!SessionHelper::isLoggedIn() || SessionHelper::getUserType() !== 'admin') {
    App::redirect('auth/login');
}
```

### 2. التحقق من طلبات AJAX
```php
if (App::isAjax()) {
    $this->handleAjaxAnalyticsRequest();
    return;
}
```

### 3. تنظيف المدخلات
- استخدام `App::get()` للحصول على المعاملات
- تنظيف التواريخ والبيانات
- التحقق من صحة البيانات

## اختبار الصفحة

### 1. ملف الاختبار
```
test_analytics.php
```

### 2. خطوات الاختبار
1. تشغيل ملف الاختبار
2. مراجعة النتائج المعروضة
3. التأكد من عدم وجود أخطاء
4. الانتقال إلى صفحة التحليلات

### 3. اختبار الوظائف
- اختبار فلاتر التاريخ
- اختبار أنواع التحليل
- اختبار تصدير التقارير
- اختبار الرسوم البيانية

## استكشاف الأخطاء

### 1. مشاكل قاعدة البيانات
- التأكد من اتصال قاعدة البيانات
- التحقق من وجود الجداول المطلوبة
- مراجعة الاستعلامات SQL

### 2. مشاكل JavaScript
- فتح وحدة تحكم المتصفح (F12)
- مراجعة أخطاء JavaScript
- التأكد من تحميل Chart.js

### 3. مشاكل العرض
- التأكد من تحميل Bootstrap
- مراجعة CSS المخصص
- اختبار التجاوب

## التطوير المستقبلي

### 1. ميزات إضافية
- إضافة المزيد من أنواع الرسوم البيانية
- دعم تصدير بصيغ أخرى (PDF, Excel)
- إضافة تنبيهات وإشعارات

### 2. تحسينات الأداء
- تخزين مؤقت للبيانات
- تحسين الاستعلامات SQL
- تحميل البيانات بشكل تدريجي

### 3. تحسينات الواجهة
- إضافة المزيد من التفاعلات
- تحسين تجربة المستخدم
- دعم الوضع المظلم

## ملاحظات مهمة

1. **البيانات الافتراضية**: بعض البيانات (مثل الأمراض الشائعة) افتراضية لأن الجداول المطلوبة قد لا تكون موجودة.

2. **الأداء**: قد تكون الاستعلامات بطيئة مع البيانات الكبيرة، يُنصح بإضافة فهارس مناسبة.

3. **التحديث**: البيانات تُحدث في الوقت الفعلي، ولكن قد تحتاج إلى تحديث يدوي للحصول على أحدث البيانات.

4. **التصدير**: التقارير المُصدَّرة تحتوي على البيانات الكاملة ويمكن استخدامها للتحليل المتقدم.

## الدعم والمساعدة

إذا واجهت أي مشاكل أو تحتاج إلى مساعدة:

1. راجع ملف الاختبار `test_analytics.php`
2. تحقق من وحدة تحكم المتصفح للأخطاء
3. راجع سجلات PHP للأخطاء
4. تأكد من صحة إعدادات قاعدة البيانات 