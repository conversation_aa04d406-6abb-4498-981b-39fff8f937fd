<?php
require_once 'config.php';

echo "<h1>اختبار صفحة الموردين</h1>";

try {
    // اختبار إنشاء نموذج الموردين
    echo "<h2>1. اختبار إنشاء نموذج الموردين</h2>";
    $supplierModel = new Supplier();
    echo "✅ تم إنشاء نموذج الموردين بنجاح<br>";
    
    // اختبار دالة getAll
    echo "<h2>2. اختبار دالة getAll</h2>";
    $suppliers = $supplierModel->getAll(5);
    echo "✅ عدد الموردين: " . count($suppliers) . "<br>";
    
    // اختبار دالة getStats
    echo "<h2>3. اختبار دالة getStats</h2>";
    $stats = $supplierModel->getStats();
    echo "✅ الإحصائيات: " . json_encode($stats, JSON_UNESCAPED_UNICODE) . "<br>";
    
    // اختبار دالة getTopSuppliers
    echo "<h2>4. اختبار دالة getTopSuppliers</h2>";
    $topSuppliers = $supplierModel->getTopSuppliers(3);
    echo "✅ أفضل الموردين: " . count($topSuppliers) . "<br>";
    
    // اختبار دالة getCategories
    echo "<h2>5. اختبار دالة getCategories</h2>";
    $categories = $supplierModel->getCategories();
    echo "✅ فئات الموردين: " . count($categories) . "<br>";
    
    // اختبار إنشاء متحكم الصيدلي
    echo "<h2>6. اختبار إنشاء متحكم الصيدلي</h2>";
    
    // محاكاة تسجيل دخول صيدلي
    session_start();
    $_SESSION['user_id'] = 1;
    $_SESSION['user_type'] = 'pharmacist';
    $_SESSION['user'] = [
        'id' => 1,
        'first_name' => 'أحمد',
        'last_name' => 'محمد',
        'email' => '<EMAIL>'
    ];
    
    // اختبار إنشاء متحكم الصيدلي
    $controller = new PharmacistController();
    echo "✅ تم إنشاء متحكم الصيدلي بنجاح<br>";
    
    // اختبار دالة suppliers
    echo "<h2>7. اختبار دالة suppliers</h2>";
    try {
        $controller->suppliers();
        echo "✅ دالة suppliers تعمل بنجاح<br>";
    } catch (Exception $e) {
        echo "❌ خطأ في دالة suppliers: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>✅ انتهى الاختبار بنجاح</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 