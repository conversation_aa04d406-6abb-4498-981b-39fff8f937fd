<?php
/**
 * اختبار صفحة ساعات العمل
 */

// محاكاة تسجيل دخول الصيدلي
session_start();
$_SESSION['user_id'] = 2;
$_SESSION['user_type'] = 'pharmacist';
$_SESSION['user'] = [
    'id' => 2,
    'first_name' => 'أحمد',
    'last_name' => 'محمد',
    'email' => '<EMAIL>'
];

// تضمين الملفات المطلوبة
require_once 'config.php';
require_once 'app/core/App.php';
require_once 'app/core/Controller.php';
require_once 'app/core/Database.php';
require_once 'app/controllers/PharmacistController.php';

echo "<h1>اختبار صفحة ساعات العمل</h1>";

try {
    // إنشاء متحكم الصيدلي
    $controller = new PharmacistController();
    
    echo "✅ تم إنشاء متحكم الصيدلي بنجاح<br>";
    
    // اختبار دالة workingHours
    echo "<h2>اختبار دالة workingHours</h2>";
    
    // محاكاة طلب GET
    $_SERVER['REQUEST_METHOD'] = 'GET';
    
    // استدعاء الدالة
    $controller->workingHours();
    
    echo "✅ تم استدعاء دالة workingHours بنجاح<br>";
    
    echo "<h3>رابط صفحة ساعات العمل:</h3>";
    echo "<a href='index.php?url=pharmacist/working-hours' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>فتح صفحة ساعات العمل</a>";
    
    echo "<h2>✅ انتهى الاختبار بنجاح</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في الاختبار</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 