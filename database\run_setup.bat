@echo off
chcp 65001 >nul
echo ========================================
echo       HealthKey Database Setup
echo ========================================
echo.

echo 🚀 بدء إعداد قاعدة البيانات...
echo.

REM التحقق من وجود PHP
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PHP غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت PHP أولاً
    pause
    exit /b 1
)

echo ✅ تم العثور على PHP
echo.

REM تشغيل إعداد قاعدة البيانات
echo 📁 تشغيل ملف الإعداد...
php "%~dp0setup.php"

if %errorlevel% equ 0 (
    echo.
    echo ✅ تم إعداد قاعدة البيانات بنجاح!
    echo.
    echo 🧪 تشغيل اختبارات قاعدة البيانات...
    echo.
    php "%~dp0test_connection.php"
) else (
    echo.
    echo ❌ فشل في إعداد قاعدة البيانات
)

echo.
echo ========================================
echo           انتهى الإعداد
echo ========================================
pause
