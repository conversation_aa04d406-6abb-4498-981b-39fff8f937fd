<?php
// تصحيح خطأ SQL
require_once 'config.php';
require_once 'app/core/App.php';
require_once 'app/core/Database.php';

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>تصحيح خطأ SQL</h1>";

try {
    $db = Database::getInstance();
    
    // اختبار الاتصال
    echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";
    $result = $db->selectOne("SELECT 1 as test");
    echo "الاتصال ناجح: " . ($result ? 'نعم' : 'لا') . "<br>";
    
    // اختبار استعلام بسيط
    echo "<h2>اختبار استعلام بسيط</h2>";
    $result = $db->selectOne("SELECT COUNT(*) as count FROM appointments", []);
    echo "عدد المواعيد: " . ($result['count'] ?? 'خطأ') . "<br>";
    
    // اختبار استعلام مع معاملات
    echo "<h2>اختبار استعلام مع معاملات</h2>";
    $result = $db->selectOne(
        "SELECT COUNT(*) as count FROM appointments WHERE appointment_date BETWEEN :start_date AND :end_date",
        [':start_date' => '2025-01-01', ':end_date' => '2025-12-31']
    );
    echo "عدد المواعيد في الفترة: " . ($result['count'] ?? 'خطأ') . "<br>";
    
    // اختبار استعلام معقد
    echo "<h2>اختبار استعلام معقد</h2>";
    $result = $db->selectOne(
        "SELECT COUNT(DISTINCT patient_id) as total FROM appointments WHERE appointment_date BETWEEN :start_date AND :end_date",
        [':start_date' => '2025-01-01', ':end_date' => '2025-12-31']
    );
    echo "عدد المرضى المميزين: " . ($result['total'] ?? 'خطأ') . "<br>";
    
    // اختبار استعلام مع JOIN
    echo "<h2>اختبار استعلام مع JOIN</h2>";
    $result = $db->select(
        "SELECT CONCAT(u.first_name, ' ', u.last_name) as doctor_name, COUNT(*) as count 
         FROM appointments a
         JOIN users u ON a.doctor_id = u.id
         WHERE a.appointment_date BETWEEN :start_date AND :end_date 
         GROUP BY a.doctor_id
         ORDER BY count DESC
         LIMIT 5",
        [':start_date' => '2025-01-01', ':end_date' => '2025-12-31']
    );
    echo "عدد الأطباء: " . count($result) . "<br>";
    
    // اختبار استعلام مع CASE
    echo "<h2>اختبار استعلام مع CASE</h2>";
    $result = $db->selectOne(
        "SELECT 
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as attended,
            COUNT(CASE WHEN status = 'no_show' THEN 1 END) as no_show,
            COUNT(*) as total
         FROM appointments 
         WHERE appointment_date BETWEEN :start_date AND :end_date",
        [':start_date' => '2025-01-01', ':end_date' => '2025-12-31']
    );
    echo "المواعيد المكتملة: " . ($result['attended'] ?? 'خطأ') . "<br>";
    echo "المواعيد الملغاة: " . ($result['no_show'] ?? 'خطأ') . "<br>";
    
} catch (Exception $e) {
    echo "<h2>خطأ في الاختبار</h2>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "<p>الملف: " . $e->getFile() . "</p>";
    echo "<p>السطر: " . $e->getLine() . "</p>";
    echo "<p>التتبع:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 