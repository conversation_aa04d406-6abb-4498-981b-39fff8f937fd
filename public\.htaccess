# إعدادات Apache لنظام HealthKey
# يقوم بإعادة توجيه جميع الطلبات إلى index.php

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# منع الوصول للملفات الحساسة
<Files "*.php">
    <RequireAll>
        Require all granted
    </RequireAll>
</Files>

# منع الوصول لملفات التكوين
<FilesMatch "\.(env|ini|log|sh|sql)$">
    Require all denied
</FilesMatch>

# منع الوصول للمجلدات المخفية
RedirectMatch 403 /\..*$

# إعدادات الأمان
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Referrer-Policy "strict-origin-when-cross-origin"

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات الثابتة (مؤقتاً معطل للتطوير)
<IfModule mod_expires.c>
    ExpiresActive Off
    # ExpiresByType text/css "access plus 1 month"
    # ExpiresByType application/javascript "access plus 1 month"
    # ExpiresByType image/png "access plus 1 month"
    # ExpiresByType image/jpg "access plus 1 month"
    # ExpiresByType image/jpeg "access plus 1 month"
    # ExpiresByType image/gif "access plus 1 month"
    # ExpiresByType image/ico "access plus 1 month"
    # ExpiresByType image/icon "access plus 1 month"
    # ExpiresByType text/x-icon "access plus 1 month"
    # ExpiresByType application/x-icon "access plus 1 month"
</IfModule>

# إعادة توجيه الطلبات
# السماح بالوصول المباشر للملفات الثابتة
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule ^(css|js|img|uploads)/(.*)$ $1/$2 [L]

# إذا كان الملف أو المجلد موجود، عرضه مباشرة
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# إعادة توجيه جميع الطلبات الأخرى إلى index.php
RewriteRule ^(.*)$ index.php?url=$1 [QSA,L]

# إعدادات PHP (إذا كان مسموح)
<IfModule mod_php.c>
    # زيادة حد الذاكرة
    php_value memory_limit 256M
    
    # زيادة حد رفع الملفات
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    
    # تفعيل عرض الأخطاء في بيئة التطوير
    # php_flag display_errors On
    # php_flag display_startup_errors On
    
    # إعدادات الجلسة
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 0
    php_value session.use_strict_mode 1
</IfModule>

# منع الوصول المباشر لمجلدات معينة
RedirectMatch 403 ^/app/.*$
RedirectMatch 403 ^/views/.*$
RedirectMatch 403 ^/config\.php$

# صفحات الأخطاء المخصصة
ErrorDocument 403 /error/403.php
ErrorDocument 500 /error/500.php

# إعدادات MIME
AddType application/javascript .js
AddType text/css .css

# منع التخزين المؤقت للملفات CSS و JS (للتطوير)
<FilesMatch "\.(css|js)$">
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires "0"
</FilesMatch>

# منع hotlinking للصور
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?yourdomain.com [NC]
RewriteCond %{REQUEST_URI} \.(jpg|jpeg|png|gif)$ [NC]
RewriteRule \.(jpg|jpeg|png|gif)$ - [F]
