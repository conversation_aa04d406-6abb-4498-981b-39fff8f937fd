<?php
/**
 * ملف اختبار إضافة مستخدم جديد
 * يمكن الوصول إليه عبر: http://localhost/HealthKey/test_add_user.php
 */

// تضمين ملفات النظام
require_once 'app/core/App.php';
require_once 'app/helpers/SessionHelper.php';
require_once 'app/models/User.php';

// محاكاة تسجيل دخول المدير
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'admin';
$_SESSION['user_name'] = 'مدير النظام';

// بيانات اختبار إضافة مستخدم
$testUserData = [
    'first_name' => 'أحمد',
    'last_name' => 'محمد',
    'email' => '<EMAIL>',
    'phone' => '**********',
    'user_type' => 'patient',
    'national_id' => '**********',
    'date_of_birth' => '1990-01-15',
    'gender' => 'male',
    'address' => 'الرياض، المملكة العربية السعودية',
    'emergency_contact' => 'فاطمة محمد',
    'emergency_phone' => '**********',
    'is_active' => 1
];

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إضافة مستخدم جديد - HealthKey</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .test-header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .test-card { border: none; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
    </style>
</head>
<body>
    <div class="container py-4">
        <!-- Header -->
        <div class="test-header text-white p-4 rounded mb-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-2">
                        <i class="bi bi-person-plus me-2"></i>
                        اختبار إضافة مستخدم جديد
                    </h1>
                    <p class="mb-0 opacity-75">اختبار نظام إضافة المستخدمين</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex align-items-center justify-content-md-end">
                        <div class="me-3">
                            <small class="d-block opacity-75">المدير المسجل</small>
                            <strong><?= $_SESSION['user_name'] ?></strong>
                        </div>
                        <div class="avatar-sm bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center">
                            <i class="bi bi-person text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="row">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-check-circle me-2"></i>
                            نتائج الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>بيانات الاختبار:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>الاسم:</strong> <?= $testUserData['first_name'] ?> <?= $testUserData['last_name'] ?></li>
                                    <li><strong>البريد الإلكتروني:</strong> <?= $testUserData['email'] ?></li>
                                    <li><strong>رقم الهاتف:</strong> <?= $testUserData['phone'] ?></li>
                                    <li><strong>نوع المستخدم:</strong> <?= $testUserData['user_type'] ?></li>
                                    <li><strong>رقم الهوية:</strong> <?= $testUserData['national_id'] ?></li>
                                    <li><strong>تاريخ الميلاد:</strong> <?= $testUserData['date_of_birth'] ?></li>
                                    <li><strong>الجنس:</strong> <?= $testUserData['gender'] === 'male' ? 'ذكر' : 'أنثى' ?></li>
                                    <li><strong>العنوان:</strong> <?= $testUserData['address'] ?></li>
                                    <li><strong>جهة الاتصال في الطوارئ:</strong> <?= $testUserData['emergency_contact'] ?></li>
                                    <li><strong>هاتف الطوارئ:</strong> <?= $testUserData['emergency_phone'] ?></li>
                                    <li><strong>الحالة:</strong> <?= $testUserData['is_active'] ? 'نشط' : 'غير نشط' ?></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>روابط الاختبار:</h6>
                                <div class="d-grid gap-2">
                                    <a href="admin/add-user" class="btn btn-primary">
                                        <i class="bi bi-person-plus me-2"></i>
                                        صفحة إضافة مستخدم
                                    </a>
                                    <a href="admin/users" class="btn btn-outline-primary">
                                        <i class="bi bi-people me-2"></i>
                                        صفحة إدارة المستخدمين
                                    </a>
                                    <a href="admin/dashboard" class="btn btn-outline-success">
                                        <i class="bi bi-speedometer2 me-2"></i>
                                        لوحة التحكم
                                    </a>
                                </div>
                                
                                <hr>
                                
                                <h6>الميزات المختبرة:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle text-success me-2"></i>إضافة مستخدم جديد</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>إنشاء كلمة مرور عشوائية</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>حفظ البيانات الأساسية</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>حفظ بيانات الطوارئ</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>تفعيل/إلغاء تفعيل المستخدم</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>التحقق من صحة البيانات</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>عرض رسائل الخطأ</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            تعليمات الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>انقر على "صفحة إضافة مستخدم" لفتح النموذج</li>
                            <li>املأ البيانات المطلوبة (الاسم الأول، الاسم الأخير، البريد الإلكتروني، نوع المستخدم)</li>
                            <li>أضف البيانات الإضافية حسب الحاجة</li>
                            <li>انقر على "إضافة المستخدم"</li>
                            <li>تحقق من أن المستخدم تم إضافته بنجاح في صفحة إدارة المستخدمين</li>
                            <li>تحقق من أن كلمة المرور تم إنشاؤها تلقائياً</li>
                        </ol>
                        
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>ملاحظة:</strong> سيتم إنشاء كلمة مرور عشوائية تلقائياً للمستخدم الجديد. في التطبيق الحقيقي، سيتم إرسال كلمة المرور عبر البريد الإلكتروني.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="text-center text-muted">
                    <small>
                        <i class="bi bi-code-slash me-1"></i>
                        نظام HealthKey - اختبار إضافة مستخدم جديد
                        <span class="mx-2">|</span>
                        <i class="bi bi-calendar me-1"></i>
                        <?= date('Y-m-d H:i') ?>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 