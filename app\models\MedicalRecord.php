<?php

/**
 * نموذج السجل الطبي
 * يتعامل مع جداول السجل الطبي والحساسيات والفحوصات المخبرية
 */
class MedicalRecord
{
    private $db;
    private $table = 'medical_records';
    private $allergiesTable = 'allergies';
    private $labTestsTable = 'lab_tests';

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * الحصول على سجل طبي بواسطة ID
     */
    public function findById($id)
    {
        $query = "SELECT mr.*, 
                         CONCAT(patient.first_name, ' ', patient.last_name) as patient_name,
                         CONCAT(doctor.first_name, ' ', doctor.last_name) as doctor_name,
                         d.specialization as doctor_specialization
                  FROM {$this->table} mr
                  LEFT JOIN users patient ON mr.patient_id = patient.id
                  LEFT JOIN users doctor ON mr.doctor_id = doctor.id
                  LEFT JOIN doctors d ON doctor.id = d.user_id
                  WHERE mr.id = :id";
        
        return $this->db->selectOne($query, [':id' => $id]);
    }

    /**
     * إنشاء سجل طبي جديد
     */
    public function create($data)
    {
        $query = "INSERT INTO {$this->table} (patient_id, doctor_id, visit_date, chief_complaint, 
                                            diagnosis, treatment_plan, vital_signs, notes, created_at) 
                  VALUES (:patient_id, :doctor_id, :visit_date, :chief_complaint, 
                          :diagnosis, :treatment_plan, :vital_signs, :notes, NOW())";

        $params = [
            ':patient_id' => $data['patient_id'],
            ':doctor_id' => $data['doctor_id'],
            ':visit_date' => $data['visit_date'] ?? date('Y-m-d'),
            ':chief_complaint' => $data['chief_complaint'] ?? null,
            ':diagnosis' => $data['diagnosis'] ?? null,
            ':treatment_plan' => $data['treatment_plan'] ?? null,
            ':vital_signs' => !empty($data['vital_signs']) ? json_encode($data['vital_signs']) : null,
            ':notes' => $data['notes'] ?? null
        ];

        return $this->db->insert($query, $params);
    }

    /**
     * تحديث السجل الطبي
     */
    public function update($id, $data)
    {
        $fields = [];
        $params = [':id' => $id];

        $allowedFields = ['chief_complaint', 'diagnosis', 'treatment_plan', 'vital_signs', 'notes'];
        
        foreach ($data as $key => $value) {
            if (in_array($key, $allowedFields)) {
                if ($key === 'vital_signs' && is_array($value)) {
                    $fields[] = "$key = :$key";
                    $params[":$key"] = json_encode($value);
                } else {
                    $fields[] = "$key = :$key";
                    $params[":$key"] = $value;
                }
            }
        }

        if (empty($fields)) {
            return false;
        }

        $query = "UPDATE {$this->table} SET " . implode(', ', $fields) . ", updated_at = NOW() WHERE id = :id";
        return $this->db->update($query, $params) > 0;
    }

    /**
     * حذف السجل الطبي
     */
    public function delete($id)
    {
        $query = "DELETE FROM {$this->table} WHERE id = :id";
        return $this->db->delete($query, [':id' => $id]) > 0;
    }

    /**
     * الحصول على السجل الطبي للمريض
     */
    public function getByPatient($patientId, $limit = null)
    {
        $query = "SELECT mr.*, 
                         CONCAT(doctor.first_name, ' ', doctor.last_name) as doctor_name,
                         d.specialization as doctor_specialization
                  FROM {$this->table} mr
                  LEFT JOIN users doctor ON mr.doctor_id = doctor.id
                  LEFT JOIN doctors d ON doctor.id = d.user_id
                  WHERE mr.patient_id = :patient_id
                  ORDER BY mr.visit_date DESC, mr.created_at DESC";
        
        if ($limit) {
            $query .= " LIMIT " . (int)$limit;
        }

        $records = $this->db->select($query, [':patient_id' => $patientId]);

        // فك تشفير العلامات الحيوية
        foreach ($records as &$record) {
            if ($record['vital_signs']) {
                $record['vital_signs'] = json_decode($record['vital_signs'], true);
            }
        }

        return $records;
    }

    /**
     * الحصول على السجلات الطبية للطبيب
     */
    public function getByDoctor($doctorId, $limit = null)
    {
        $query = "SELECT mr.*, 
                         CONCAT(patient.first_name, ' ', patient.last_name) as patient_name
                  FROM {$this->table} mr
                  LEFT JOIN users patient ON mr.patient_id = patient.id
                  WHERE mr.doctor_id = :doctor_id
                  ORDER BY mr.visit_date DESC, mr.created_at DESC";
        
        if ($limit) {
            $query .= " LIMIT " . (int)$limit;
        }

        $records = $this->db->select($query, [':doctor_id' => $doctorId]);

        // فك تشفير العلامات الحيوية
        foreach ($records as &$record) {
            if ($record['vital_signs']) {
                $record['vital_signs'] = json_decode($record['vital_signs'], true);
            }
        }

        return $records;
    }

    /**
     * إضافة حساسية للمريض
     */
    public function addAllergy($patientId, $allergen, $reaction = null, $severity = 'mild', $notes = null)
    {
        $query = "INSERT INTO {$this->allergiesTable} (patient_id, allergen, reaction, severity, notes, created_at) 
                  VALUES (:patient_id, :allergen, :reaction, :severity, :notes, NOW())";

        $params = [
            ':patient_id' => $patientId,
            ':allergen' => $allergen,
            ':reaction' => $reaction,
            ':severity' => $severity,
            ':notes' => $notes
        ];

        return $this->db->insert($query, $params);
    }

    /**
     * الحصول على حساسيات المريض
     */
    public function getAllergies($patientId)
    {
        $query = "SELECT * FROM {$this->allergiesTable} WHERE patient_id = :patient_id ORDER BY created_at DESC";
        return $this->db->select($query, [':patient_id' => $patientId]);
    }

    /**
     * حذف حساسية
     */
    public function deleteAllergy($allergyId)
    {
        $query = "DELETE FROM {$this->allergiesTable} WHERE id = :id";
        return $this->db->delete($query, [':id' => $allergyId]) > 0;
    }

    /**
     * إضافة فحص مخبري
     */
    public function addLabTest($data)
    {
        $query = "INSERT INTO {$this->labTestsTable} (patient_id, doctor_id, test_name, test_date, 
                                                     results, file_path, status, notes, created_at) 
                  VALUES (:patient_id, :doctor_id, :test_name, :test_date, 
                          :results, :file_path, :status, :notes, NOW())";

        $params = [
            ':patient_id' => $data['patient_id'],
            ':doctor_id' => $data['doctor_id'],
            ':test_name' => $data['test_name'],
            ':test_date' => $data['test_date'] ?? date('Y-m-d'),
            ':results' => $data['results'] ?? null,
            ':file_path' => $data['file_path'] ?? null,
            ':status' => $data['status'] ?? 'ordered',
            ':notes' => $data['notes'] ?? null
        ];

        return $this->db->insert($query, $params);
    }

    /**
     * الحصول على الفحوصات المخبرية للمريض
     */
    public function getLabTests($patientId, $status = null)
    {
        $query = "SELECT lt.*, 
                         CONCAT(doctor.first_name, ' ', doctor.last_name) as doctor_name
                  FROM {$this->labTestsTable} lt
                  LEFT JOIN users doctor ON lt.doctor_id = doctor.id
                  WHERE lt.patient_id = :patient_id";
        
        $params = [':patient_id' => $patientId];

        if ($status) {
            $query .= " AND lt.status = :status";
            $params[':status'] = $status;
        }

        $query .= " ORDER BY lt.test_date DESC, lt.created_at DESC";

        return $this->db->select($query, $params);
    }

    /**
     * تحديث نتيجة الفحص المخبري
     */
    public function updateLabTestResult($testId, $results, $filePath = null)
    {
        $query = "UPDATE {$this->labTestsTable} 
                  SET results = :results, status = 'completed', updated_at = NOW()";
        
        $params = [
            ':id' => $testId,
            ':results' => $results
        ];

        if ($filePath) {
            $query .= ", file_path = :file_path";
            $params[':file_path'] = $filePath;
        }

        $query .= " WHERE id = :id";

        return $this->db->update($query, $params) > 0;
    }

    /**
     * البحث في السجلات الطبية
     */
    public function search($term, $filters = [])
    {
        $query = "SELECT mr.*,
                         CONCAT(patient.first_name, ' ', patient.last_name) as patient_name,
                         CONCAT(doctor.first_name, ' ', doctor.last_name) as doctor_name
                  FROM {$this->table} mr
                  LEFT JOIN users patient ON mr.patient_id = patient.id
                  LEFT JOIN users doctor ON mr.doctor_id = doctor.id
                  WHERE 1=1";

        $params = [];

        // إضافة شرط البحث فقط إذا كان المصطلح غير فارغ
        if (!empty($term)) {
            $query .= " AND (mr.diagnosis LIKE :term OR mr.chief_complaint LIKE :term OR mr.notes LIKE :term)";
            $params[':term'] = "%$term%";
        }

        // تطبيق المرشحات
        if (!empty($filters['patient_id'])) {
            $query .= " AND mr.patient_id = :patient_id";
            $params[':patient_id'] = $filters['patient_id'];
        }

        if (!empty($filters['doctor_id'])) {
            $query .= " AND mr.doctor_id = :doctor_id";
            $params[':doctor_id'] = $filters['doctor_id'];
        }

        if (!empty($filters['date_from'])) {
            $query .= " AND mr.visit_date >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }

        if (!empty($filters['date_to'])) {
            $query .= " AND mr.visit_date <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }

        $query .= " ORDER BY mr.visit_date DESC LIMIT 20";

        return $this->db->select($query, $params);
    }

    /**
     * الحصول على ملخص السجل الطبي للمريض
     */
    public function getPatientSummary($patientId)
    {
        $summary = [];

        // آخر زيارة
        $lastVisit = $this->db->selectOne(
            "SELECT mr.*, CONCAT(doctor.first_name, ' ', doctor.last_name) as doctor_name
             FROM {$this->table} mr
             LEFT JOIN users doctor ON mr.doctor_id = doctor.id
             WHERE mr.patient_id = :patient_id
             ORDER BY mr.visit_date DESC, mr.created_at DESC
             LIMIT 1",
            [':patient_id' => $patientId]
        );

        $summary['last_visit'] = $lastVisit;

        // الحساسيات
        $summary['allergies'] = $this->getAllergies($patientId);

        // آخر الفحوصات
        $summary['recent_lab_tests'] = $this->getLabTests($patientId, null);

        // إحصائيات
        $stats = $this->db->selectOne(
            "SELECT COUNT(*) as total_visits FROM {$this->table} WHERE patient_id = :patient_id",
            [':patient_id' => $patientId]
        );
        $summary['total_visits'] = (int)$stats['total_visits'];

        return $summary;
    }

    /**
     * إحصائيات السجلات الطبية
     */
    public function getStats($doctorId = null)
    {
        $stats = [];
        $whereClause = $doctorId ? "WHERE doctor_id = :doctor_id" : "";
        $params = $doctorId ? [':doctor_id' => $doctorId] : [];

        // إجمالي السجلات
        $total = $this->db->selectOne("SELECT COUNT(*) as count FROM {$this->table} $whereClause", $params);
        $stats['total'] = (int)$total['count'];

        // السجلات هذا الشهر
        $thisMonth = $this->db->selectOne(
            "SELECT COUNT(*) as count FROM {$this->table} 
             WHERE MONTH(visit_date) = MONTH(NOW()) AND YEAR(visit_date) = YEAR(NOW()) 
             " . ($doctorId ? "AND doctor_id = :doctor_id" : ""),
            $params
        );
        $stats['this_month'] = (int)$thisMonth['count'];

        // إجمالي الحساسيات
        $allergies = $this->db->selectOne("SELECT COUNT(*) as count FROM {$this->allergiesTable}");
        $stats['total_allergies'] = (int)$allergies['count'];

        // إجمالي الفحوصات
        $labTests = $this->db->selectOne("SELECT COUNT(*) as count FROM {$this->labTestsTable}");
        $stats['total_lab_tests'] = (int)$labTests['count'];

        return $stats;
    }

    /**
     * التحقق من صحة البيانات
     */
    public function validate($data)
    {
        $errors = [];

        // التحقق من الحقول المطلوبة
        $required = ['patient_id', 'doctor_id'];
        foreach ($required as $field) {
            if (empty($data[$field])) {
                $errors[$field] = "حقل $field مطلوب";
            }
        }

        // التحقق من تاريخ الزيارة
        if (!empty($data['visit_date'])) {
            if (strtotime($data['visit_date']) > time()) {
                $errors['visit_date'] = 'تاريخ الزيارة لا يمكن أن يكون في المستقبل';
            }
        }

        // التحقق من العلامات الحيوية
        if (!empty($data['vital_signs']) && is_array($data['vital_signs'])) {
            $vitalSigns = $data['vital_signs'];
            
            if (!empty($vitalSigns['temperature']) && ($vitalSigns['temperature'] < 30 || $vitalSigns['temperature'] > 45)) {
                $errors['vital_signs[temperature]'] = 'درجة الحرارة غير منطقية';
            }
            
            if (!empty($vitalSigns['blood_pressure_systolic']) && ($vitalSigns['blood_pressure_systolic'] < 60 || $vitalSigns['blood_pressure_systolic'] > 250)) {
                $errors['vital_signs[blood_pressure_systolic]'] = 'ضغط الدم الانقباضي غير منطقي';
            }
            
            if (!empty($vitalSigns['heart_rate']) && ($vitalSigns['heart_rate'] < 30 || $vitalSigns['heart_rate'] > 200)) {
                $errors['vital_signs[heart_rate]'] = 'معدل ضربات القلب غير منطقي';
            }
        }

        return $errors;
    }

    /**
     * تنسيق العلامات الحيوية للعرض
     */
    public static function formatVitalSigns($vitalSigns)
    {
        if (!is_array($vitalSigns)) {
            return [];
        }

        $formatted = [];

        if (!empty($vitalSigns['temperature'])) {
            $formatted['درجة الحرارة'] = $vitalSigns['temperature'] . ' °C';
        }

        if (!empty($vitalSigns['blood_pressure_systolic']) && !empty($vitalSigns['blood_pressure_diastolic'])) {
            $formatted['ضغط الدم'] = $vitalSigns['blood_pressure_systolic'] . '/' . $vitalSigns['blood_pressure_diastolic'] . ' mmHg';
        }

        if (!empty($vitalSigns['heart_rate'])) {
            $formatted['معدل ضربات القلب'] = $vitalSigns['heart_rate'] . ' bpm';
        }

        if (!empty($vitalSigns['respiratory_rate'])) {
            $formatted['معدل التنفس'] = $vitalSigns['respiratory_rate'] . ' /min';
        }

        if (!empty($vitalSigns['weight'])) {
            $formatted['الوزن'] = $vitalSigns['weight'] . ' kg';
        }

        if (!empty($vitalSigns['height'])) {
            $formatted['الطول'] = $vitalSigns['height'] . ' cm';
        }

        return $formatted;
    }

    /**
     * الحصول على شدة الحساسية باللغة العربية
     */
    public static function getAllergySeverityLabel($severity)
    {
        $severities = [
            'mild' => 'خفيفة',
            'moderate' => 'متوسطة',
            'severe' => 'شديدة'
        ];

        return $severities[$severity] ?? $severity;
    }

    /**
     * الحصول على حالة الفحص المخبري باللغة العربية
     */
    public static function getLabTestStatusLabel($status)
    {
        $statuses = [
            'ordered' => 'مطلوب',
            'completed' => 'مكتمل',
            'cancelled' => 'ملغي'
        ];

        return $statuses[$status] ?? $status;
    }
}
