<!-- CSS Links -->
<link href="<?= App::url('public/css/style.css') ?>?v=<?= time() ?>" rel="stylesheet">
<link href="<?= App::url('public/css/admin.css') ?>?v=<?= time() ?>" rel="stylesheet">

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="fw-bold text-primary mb-2">
            <i class="bi bi-gear-fill me-2"></i>
            إعدادات النظام
        </h1>
        <p class="text-muted mb-0">إدارة إعدادات النظام والتكوين</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-outline-success" onclick="saveAllSettings()">
            <i class="bi bi-check-circle me-2"></i>
            حفظ جميع الإعدادات
        </button>
        <button class="btn btn-outline-secondary" onclick="resetToDefaults()">
            <i class="bi bi-arrow-clockwise me-2"></i>
            إعادة تعيين
        </button>
        <a href="<?= App::url('admin/dashboard') ?>" class="btn btn-outline-primary">
            <i class="bi bi-speedometer2"></i>
            لوحة التحكم
        </a>
    </div>
</div>

<!-- Flash Messages -->
<?php if (isset($_SESSION['flash_message'])): ?>
    <div class="alert alert-<?= $_SESSION['flash_type'] ?? 'info' ?> alert-dismissible fade show" role="alert">
        <i class="bi bi-<?= $_SESSION['flash_type'] === 'success' ? 'check-circle' : ($_SESSION['flash_type'] === 'error' ? 'exclamation-triangle' : 'info-circle') ?> me-2"></i>
        <?= $_SESSION['flash_message'] ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php unset($_SESSION['flash_message'], $_SESSION['flash_type']); ?>
<?php endif; ?>

<form method="POST" action="<?= App::url('admin/settings') ?>" id="settingsForm">
    <!-- General Settings -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="bi bi-info-circle me-2"></i>
                الإعدادات العامة
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="app_name" class="form-label">
                        <i class="bi bi-building me-1"></i>
                        اسم التطبيق
                    </label>
                    <input type="text" class="form-control" id="app_name" name="app_name" 
                           value="<?= APP_NAME ?>" required>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="app_version" class="form-label">
                        <i class="bi bi-tag me-1"></i>
                        إصدار التطبيق
                    </label>
                    <input type="text" class="form-control" id="app_version" name="app_version" 
                           value="<?= APP_VERSION ?>" readonly>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="app_url" class="form-label">
                        <i class="bi bi-link-45deg me-1"></i>
                        رابط التطبيق
                    </label>
                    <input type="url" class="form-control" id="app_url" name="app_url" 
                           value="<?= APP_URL ?>" required>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="timezone" class="form-label">
                        <i class="bi bi-clock me-1"></i>
                        المنطقة الزمنية
                    </label>
                    <select class="form-select" id="timezone" name="timezone">
                        <option value="Asia/Riyadh" <?= DEFAULT_TIMEZONE === 'Asia/Riyadh' ? 'selected' : '' ?>>الرياض (GMT+3)</option>
                        <option value="Asia/Dubai" <?= DEFAULT_TIMEZONE === 'Asia/Dubai' ? 'selected' : '' ?>>دبي (GMT+4)</option>
                        <option value="Asia/Kuwait" <?= DEFAULT_TIMEZONE === 'Asia/Kuwait' ? 'selected' : '' ?>>الكويت (GMT+3)</option>
                        <option value="Asia/Qatar" <?= DEFAULT_TIMEZONE === 'Asia/Qatar' ? 'selected' : '' ?>>قطر (GMT+3)</option>
                        <option value="Asia/Bahrain" <?= DEFAULT_TIMEZONE === 'Asia/Bahrain' ? 'selected' : '' ?>>البحرين (GMT+3)</option>
                        <option value="Asia/Oman" <?= DEFAULT_TIMEZONE === 'Asia/Oman' ? 'selected' : '' ?>>عمان (GMT+4)</option>
                    </select>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="date_format" class="form-label">
                        <i class="bi bi-calendar me-1"></i>
                        تنسيق التاريخ
                    </label>
                    <select class="form-select" id="date_format" name="date_format">
                        <option value="Y-m-d" <?= DATE_FORMAT === 'Y-m-d' ? 'selected' : '' ?>>2024-01-15</option>
                        <option value="d/m/Y" <?= DATE_FORMAT === 'd/m/Y' ? 'selected' : '' ?>>15/01/2024</option>
                        <option value="m/d/Y" <?= DATE_FORMAT === 'm/d/Y' ? 'selected' : '' ?>>01/15/2024</option>
                        <option value="Y/m/d" <?= DATE_FORMAT === 'Y/m/d' ? 'selected' : '' ?>>2024/01/15</option>
                    </select>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="datetime_format" class="form-label">
                        <i class="bi bi-calendar-event me-1"></i>
                        تنسيق التاريخ والوقت
                    </label>
                    <select class="form-select" id="datetime_format" name="datetime_format">
                        <option value="Y-m-d H:i:s" <?= DATETIME_FORMAT === 'Y-m-d H:i:s' ? 'selected' : '' ?>>2024-01-15 14:30:00</option>
                        <option value="d/m/Y H:i" <?= DATETIME_FORMAT === 'd/m/Y H:i' ? 'selected' : '' ?>>15/01/2024 14:30</option>
                        <option value="m/d/Y g:i A" <?= DATETIME_FORMAT === 'm/d/Y g:i A' ? 'selected' : '' ?>>01/15/2024 2:30 PM</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Security Settings -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-warning text-dark">
            <h5 class="mb-0">
                <i class="bi bi-shield-lock me-2"></i>
                إعدادات الأمان
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="session_lifetime" class="form-label">
                        <i class="bi bi-clock-history me-1"></i>
                        مدة الجلسة (بالثواني)
                    </label>
                    <input type="number" class="form-control" id="session_lifetime" name="session_lifetime" 
                           value="<?= SESSION_LIFETIME ?>" min="300" max="86400">
                    <div class="form-text">الحد الأدنى: 5 دقائق، الحد الأقصى: 24 ساعة</div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="hash_algo" class="form-label">
                        <i class="bi bi-key me-1"></i>
                        خوارزمية التشفير
                    </label>
                    <select class="form-select" id="hash_algo" name="hash_algo">
                        <option value="sha256" <?= HASH_ALGO === 'sha256' ? 'selected' : '' ?>>SHA-256</option>
                        <option value="sha512" <?= HASH_ALGO === 'sha512' ? 'selected' : '' ?>>SHA-512</option>
                        <option value="bcrypt" <?= HASH_ALGO === 'bcrypt' ? 'selected' : '' ?>>BCrypt</option>
                    </select>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="encryption_key" class="form-label">
                        <i class="bi bi-lock me-1"></i>
                        مفتاح التشفير
                    </label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="encryption_key" name="encryption_key" 
                               value="<?= ENCRYPTION_KEY ?>" required>
                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('encryption_key')">
                            <i class="bi bi-eye"></i>
                        </button>
                    </div>
                    <div class="form-text">يجب أن يكون طويلاً وعشوائياً</div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="max_login_attempts" class="form-label">
                        <i class="bi bi-exclamation-triangle me-1"></i>
                        الحد الأقصى لمحاولات تسجيل الدخول
                    </label>
                    <input type="number" class="form-control" id="max_login_attempts" name="max_login_attempts" 
                           value="5" min="3" max="10">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="password_min_length" class="form-label">
                        <i class="bi bi-lock-fill me-1"></i>
                        الحد الأدنى لطول كلمة المرور
                    </label>
                    <input type="number" class="form-control" id="password_min_length" name="password_min_length" 
                           value="8" min="6" max="20">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="require_strong_password" class="form-label">
                        <i class="bi bi-shield-check me-1"></i>
                        كلمة مرور قوية
                    </label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="require_strong_password" 
                               name="require_strong_password" value="1" checked>
                        <label class="form-check-label" for="require_strong_password">
                            تتطلب أحرف كبيرة وصغيرة وأرقام ورموز
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- File Upload Settings -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">
                <i class="bi bi-file-earmark-arrow-up me-2"></i>
                إعدادات رفع الملفات
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="max_file_size" class="form-label">
                        <i class="bi bi-hdd me-1"></i>
                        الحد الأقصى لحجم الملف (ميجابايت)
                    </label>
                    <input type="number" class="form-control" id="max_file_size" name="max_file_size" 
                           value="<?= MAX_FILE_SIZE / (1024 * 1024) ?>" min="1" max="50">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="upload_path" class="form-label">
                        <i class="bi bi-folder me-1"></i>
                        مسار رفع الملفات
                    </label>
                    <input type="text" class="form-control" id="upload_path" name="upload_path" 
                           value="<?= UPLOAD_PATH ?>" readonly>
                </div>
                
                <div class="col-md-12 mb-3">
                    <label for="allowed_file_types" class="form-label">
                        <i class="bi bi-file-earmark me-1"></i>
                        أنواع الملفات المسموحة
                    </label>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="pdf_files" name="allowed_types[]" 
                                       value="pdf" <?= in_array('pdf', ALLOWED_FILE_TYPES) ? 'checked' : '' ?>>
                                <label class="form-check-label" for="pdf_files">PDF</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="jpg_files" name="allowed_types[]" 
                                       value="jpg" <?= in_array('jpg', ALLOWED_FILE_TYPES) ? 'checked' : '' ?>>
                                <label class="form-check-label" for="jpg_files">JPG</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="jpeg_files" name="allowed_types[]" 
                                       value="jpeg" <?= in_array('jpeg', ALLOWED_FILE_TYPES) ? 'checked' : '' ?>>
                                <label class="form-check-label" for="jpeg_files">JPEG</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="png_files" name="allowed_types[]" 
                                       value="png" <?= in_array('png', ALLOWED_FILE_TYPES) ? 'checked' : '' ?>>
                                <label class="form-check-label" for="png_files">PNG</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Settings -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">
                <i class="bi bi-bell me-2"></i>
                إعدادات الإشعارات
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="email_notifications" class="form-label">
                        <i class="bi bi-envelope me-1"></i>
                        إشعارات البريد الإلكتروني
                    </label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="email_notifications" 
                               name="email_notifications" value="1" checked>
                        <label class="form-check-label" for="email_notifications">
                            تفعيل إشعارات البريد الإلكتروني
                        </label>
                    </div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="sms_notifications" class="form-label">
                        <i class="bi bi-phone me-1"></i>
                        إشعارات الرسائل النصية
                    </label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="sms_notifications" 
                               name="sms_notifications" value="1">
                        <label class="form-check-label" for="sms_notifications">
                            تفعيل إشعارات الرسائل النصية
                        </label>
                    </div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="appointment_reminder_hours" class="form-label">
                        <i class="bi bi-clock me-1"></i>
                        تذكير المواعيد (بالساعات)
                    </label>
                    <input type="number" class="form-control" id="appointment_reminder_hours" 
                           name="appointment_reminder_hours" value="24" min="1" max="72">
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="prescription_expiry_days" class="form-label">
                        <i class="bi bi-calendar-x me-1"></i>
                        تنبيه انتهاء صلاحية الوصفة (بالأيام)
                    </label>
                    <input type="number" class="form-control" id="prescription_expiry_days" 
                           name="prescription_expiry_days" value="7" min="1" max="30">
                </div>
            </div>
        </div>
    </div>

    <!-- System Settings -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0">
                <i class="bi bi-cpu me-2"></i>
                إعدادات النظام
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="app_debug" class="form-label">
                        <i class="bi bi-bug me-1"></i>
                        وضع التطوير
                    </label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="app_debug" 
                               name="app_debug" value="1" <?= APP_DEBUG ? 'checked' : '' ?>>
                        <label class="form-check-label" for="app_debug">
                            تفعيل وضع التطوير (عرض الأخطاء)
                        </label>
                    </div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="log_errors" class="form-label">
                        <i class="bi bi-journal-text me-1"></i>
                        تسجيل الأخطاء
                    </label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="log_errors" 
                               name="log_errors" value="1" <?= LOG_ERRORS ? 'checked' : '' ?>>
                        <label class="form-check-label" for="log_errors">
                            تسجيل الأخطاء في ملف
                        </label>
                    </div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="auto_backup" class="form-label">
                        <i class="bi bi-cloud-arrow-up me-1"></i>
                        النسخ الاحتياطي التلقائي
                    </label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="auto_backup" 
                               name="auto_backup" value="1">
                        <label class="form-check-label" for="auto_backup">
                            إنشاء نسخة احتياطية يومياً
                        </label>
                    </div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="maintenance_mode" class="form-label">
                        <i class="bi bi-tools me-1"></i>
                        وضع الصيانة
                    </label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="maintenance_mode" 
                               name="maintenance_mode" value="1">
                        <label class="form-check-label" for="maintenance_mode">
                            تفعيل وضع الصيانة
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="d-flex gap-2 justify-content-end">
                <button type="button" class="btn btn-outline-secondary" onclick="resetToDefaults()">
                    <i class="bi bi-arrow-clockwise me-2"></i>
                    إعادة تعيين للافتراضي
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-circle me-2"></i>
                    حفظ الإعدادات
                </button>
            </div>
        </div>
    </div>
</form>

<script>
// Toggle password visibility
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'bi bi-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'bi bi-eye';
    }
}

// Save all settings
function saveAllSettings() {
    document.getElementById('settingsForm').submit();
}

// Reset to defaults
function resetToDefaults() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟')) {
        // Reset form fields to default values
        document.getElementById('app_name').value = 'HealthKey';
        document.getElementById('app_url').value = 'http://localhost/HealthKey';
        document.getElementById('timezone').value = 'Asia/Riyadh';
        document.getElementById('date_format').value = 'Y-m-d';
        document.getElementById('datetime_format').value = 'Y-m-d H:i:s';
        document.getElementById('session_lifetime').value = '3600';
        document.getElementById('hash_algo').value = 'sha256';
        document.getElementById('max_file_size').value = '5';
        document.getElementById('appointment_reminder_hours').value = '24';
        document.getElementById('prescription_expiry_days').value = '7';
        
        // Reset checkboxes
        document.getElementById('require_strong_password').checked = true;
        document.getElementById('email_notifications').checked = true;
        document.getElementById('sms_notifications').checked = false;
        document.getElementById('app_debug').checked = true;
        document.getElementById('log_errors').checked = true;
        document.getElementById('auto_backup').checked = false;
        document.getElementById('maintenance_mode').checked = false;
        
        // Reset file type checkboxes
        document.getElementById('pdf_files').checked = true;
        document.getElementById('jpg_files').checked = true;
        document.getElementById('jpeg_files').checked = true;
        document.getElementById('png_files').checked = true;
        
        alert('تم إعادة تعيين الإعدادات للقيم الافتراضية');
    }
}

// Form validation
document.getElementById('settingsForm').addEventListener('submit', function(e) {
    const appName = document.getElementById('app_name').value.trim();
    const appUrl = document.getElementById('app_url').value.trim();
    const encryptionKey = document.getElementById('encryption_key').value.trim();
    
    if (!appName || !appUrl || !encryptionKey) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return false;
    }
    
    if (encryptionKey.length < 10) {
        e.preventDefault();
        alert('مفتاح التشفير يجب أن يكون طويلاً (10 أحرف على الأقل)');
        return false;
    }
    
    // Show loading
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>جاري الحفظ...';
    submitBtn.disabled = true;
    
    // Re-enable after 3 seconds if form doesn't submit
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 3000);
});

// Auto-save settings on change
document.querySelectorAll('#settingsForm input, #settingsForm select').forEach(input => {
    input.addEventListener('change', function() {
        // Add visual feedback
        this.classList.add('border-success');
        setTimeout(() => {
            this.classList.remove('border-success');
        }, 1000);
    });
});
</script>

<style>
.form-check-input:checked {
    background-color: #198754;
    border-color: #198754;
}

.form-check-input:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.border-success {
    border-color: #198754 !important;
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25) !important;
}

.card-header {
    border-bottom: none;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

.input-group .btn {
    border-left: none;
}

.input-group .form-control:focus {
    border-right: none;
    box-shadow: none;
}

.input-group .form-control:focus + .btn {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
</style> 