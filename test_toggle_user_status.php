<?php
/**
 * اختبار تفعيل/إلغاء تفعيل المستخدمين
 */

// تضمين ملفات النظام
require_once 'config.php';
require_once 'app/core/App.php';
require_once 'app/core/Database.php';
require_once 'app/models/User.php';

// محاكاة تسجيل دخول المدير
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'admin';
$_SESSION['user_name'] = 'مدير النظام';

echo "<h2>اختبار تفعيل/إلغاء تفعيل المستخدمين</h2>";

// إنشاء نموذج المستخدم
$userModel = new User();

// الحصول على مستخدم للاختبار
$users = $userModel->getAll(['limit' => 5]);
if (empty($users)) {
    echo "<p style='color: red;'>لا توجد مستخدمين للاختبار</p>";
    exit;
}

$testUser = $users[0];
echo "<h3>المستخدم المختار للاختبار:</h3>";
echo "<p><strong>الاسم:</strong> " . User::getFullName($testUser) . "</p>";
echo "<p><strong>البريد الإلكتروني:</strong> " . htmlspecialchars($testUser['email']) . "</p>";
echo "<p><strong>الحالة الحالية:</strong> " . ($testUser['is_active'] ? 'نشط' : 'غير نشط') . "</p>";

// اختبار تغيير الحالة
echo "<h3>اختبار تغيير الحالة:</h3>";
$userId = $testUser['id'];
$oldStatus = $testUser['is_active'];

if ($userModel->toggleActive($userId)) {
    echo "<p style='color: green;'>✓ تم تغيير الحالة بنجاح</p>";
    
    // التحقق من التغيير
    $updatedUser = $userModel->findById($userId);
    $newStatus = $updatedUser['is_active'];
    
    echo "<p><strong>الحالة الجديدة:</strong> " . ($newStatus ? 'نشط' : 'غير نشط') . "</p>";
    
    if ($oldStatus != $newStatus) {
        echo "<p style='color: green;'>✓ تم تغيير الحالة من " . ($oldStatus ? 'نشط' : 'غير نشط') . " إلى " . ($newStatus ? 'نشط' : 'غير نشط') . "</p>";
    } else {
        echo "<p style='color: orange;'>⚠ لم يتغير شيء (قد يكون هناك خطأ في المنطق)</p>";
    }
    
    // إعادة الحالة إلى ما كانت عليه
    $userModel->toggleActive($userId);
    echo "<p style='color: blue;'>تم إعادة الحالة إلى حالتها الأصلية</p>";
    
} else {
    echo "<p style='color: red;'>✗ فشل في تغيير الحالة</p>";
}

echo "<hr>";
echo "<h3>اختبار AJAX Request:</h3>";
echo "<p>يمكنك اختبار الطلب عبر AJAX باستخدام هذا الكود:</p>";
echo "<pre>";
echo "fetch('" . App::url('admin/toggle-user-status') . "', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: 'user_id=' + " . $userId . "
})
.then(response => response.json())
.then(data => {
    console.log(data);
    if (data.success) {
        alert(data.message);
        location.reload();
    } else {
        alert('خطأ: ' + data.message);
    }
})
.catch(error => {
    console.error('خطأ:', error);
    alert('حدث خطأ في الاتصال');
});";
echo "</pre>";

echo "<hr>";
echo "<p><a href='admin/users'>العودة لصفحة إدارة المستخدمين</a></p>";
?> 