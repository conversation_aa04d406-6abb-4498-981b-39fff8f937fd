<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>السجل الطبي - <?= htmlspecialchars($patient['first_name'] . ' ' . $patient['last_name']) ?></title>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #fff;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 2rem;
        }
        
        .header p {
            color: #666;
            margin: 5px 0;
        }
        
        .patient-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .patient-info h3 {
            color: #007bff;
            margin-bottom: 15px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
        }
        
        .info-value {
            color: #333;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section-title {
            color: #007bff;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        
        .record-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: #fff;
        }
        
        .record-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .record-date {
            font-weight: bold;
            color: #007bff;
        }
        
        .record-type {
            background-color: #007bff;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        .record-content {
            margin-bottom: 15px;
        }
        
        .content-item {
            margin-bottom: 10px;
        }
        
        .content-label {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        
        .content-value {
            color: #333;
            line-height: 1.5;
        }
        
        .vital-signs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .vital-item {
            text-align: center;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        
        .vital-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 5px;
        }
        
        .vital-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #007bff;
        }
        
        .allergy-card {
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #fff3cd;
        }
        
        .allergy-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .allergy-name {
            font-weight: bold;
            color: #856404;
        }
        
        .severity-badge {
            background-color: #ffc107;
            color: #856404;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        
        .test-card {
            border: 1px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #d4edda;
        }
        
        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .test-name {
            font-weight: bold;
            color: #155724;
        }
        
        .status-badge {
            background-color: #28a745;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .results-table th,
        .results-table td {
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            text-align: center;
        }
        
        .results-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .normal {
            background-color: #d4edda;
        }
        
        .high {
            background-color: #f8d7da;
        }
        
        .low {
            background-color: #fff3cd;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #666;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }
            
            .header {
                border-bottom-color: #000;
            }
            
            .section-title {
                border-bottom-color: #000;
            }
            
            .record-card,
            .allergy-card,
            .test-card {
                break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>السجل الطبي</h1>
        <p>نظام HealthKey لإدارة الرعاية الصحية</p>
        <p>تاريخ التصدير: <?= date('Y-m-d H:i') ?></p>
    </div>
    
    <!-- Patient Information -->
    <div class="patient-info">
        <h3>معلومات المريض</h3>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">الاسم:</span>
                <span class="info-value"><?= htmlspecialchars($patient['first_name'] . ' ' . $patient['last_name']) ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">رقم الهوية:</span>
                <span class="info-value"><?= htmlspecialchars($patient['national_id'] ?? 'غير محدد') ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">تاريخ الميلاد:</span>
                <span class="info-value"><?= $patient['date_of_birth'] ? date('Y-m-d', strtotime($patient['date_of_birth'])) : 'غير محدد' ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">الجنس:</span>
                <span class="info-value"><?= $patient['gender'] === 'male' ? 'ذكر' : ($patient['gender'] === 'female' ? 'أنثى' : 'غير محدد') ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">رقم الهاتف:</span>
                <span class="info-value"><?= htmlspecialchars($patient['phone'] ?? 'غير محدد') ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">البريد الإلكتروني:</span>
                <span class="info-value"><?= htmlspecialchars($patient['email'] ?? 'غير محدد') ?></span>
            </div>
        </div>
    </div>
    
    <!-- Medical Summary -->
    <div class="section">
        <h2 class="section-title">ملخص طبي</h2>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">إجمالي الزيارات:</span>
                <span class="info-value"><?= $summary['total_visits'] ?? 0 ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">عدد السجلات الطبية:</span>
                <span class="info-value"><?= count($medicalRecords) ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">عدد الحساسيات:</span>
                <span class="info-value"><?= count($allergies) ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">عدد فحوصات المختبر:</span>
                <span class="info-value"><?= count($labTests) ?></span>
            </div>
        </div>
    </div>
    
    <!-- Medical Records -->
    <?php if (!empty($medicalRecords)): ?>
        <div class="section">
            <h2 class="section-title">السجلات الطبية</h2>
            <?php foreach ($medicalRecords as $record): ?>
                <div class="record-card">
                    <div class="record-header">
                        <span class="record-date"><?= date('Y-m-d', strtotime($record['visit_date'])) ?></span>
                        <span class="record-type"><?= MedicalRecord::getVisitTypeLabel($record['visit_type']) ?></span>
                    </div>
                    
                    <div class="record-content">
                        <div class="content-item">
                            <div class="content-label">الطبيب:</div>
                            <div class="content-value"><?= htmlspecialchars($record['doctor_name']) ?></div>
                        </div>
                        
                        <?php if (!empty($record['chief_complaint'])): ?>
                            <div class="content-item">
                                <div class="content-label">الشكوى الرئيسية:</div>
                                <div class="content-value"><?= htmlspecialchars($record['chief_complaint']) ?></div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($record['diagnosis'])): ?>
                            <div class="content-item">
                                <div class="content-label">التشخيص:</div>
                                <div class="content-value"><?= htmlspecialchars($record['diagnosis']) ?></div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($record['treatment_plan'])): ?>
                            <div class="content-item">
                                <div class="content-label">خطة العلاج:</div>
                                <div class="content-value"><?= htmlspecialchars($record['treatment_plan']) ?></div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($record['vital_signs'])): ?>
                            <div class="content-item">
                                <div class="content-label">العلامات الحيوية:</div>
                                <div class="vital-signs">
                                    <?php if (!empty($record['vital_signs']['temperature'])): ?>
                                        <div class="vital-item">
                                            <div class="vital-label">درجة الحرارة</div>
                                            <div class="vital-value"><?= $record['vital_signs']['temperature'] ?>°C</div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($record['vital_signs']['blood_pressure_systolic'])): ?>
                                        <div class="vital-item">
                                            <div class="vital-label">ضغط الدم</div>
                                            <div class="vital-value"><?= $record['vital_signs']['blood_pressure_systolic'] ?>/<?= $record['vital_signs']['blood_pressure_diastolic'] ?> mmHg</div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($record['vital_signs']['heart_rate'])): ?>
                                        <div class="vital-item">
                                            <div class="vital-label">نبض القلب</div>
                                            <div class="vital-value"><?= $record['vital_signs']['heart_rate'] ?> bpm</div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($record['vital_signs']['weight'])): ?>
                                        <div class="vital-item">
                                            <div class="vital-label">الوزن</div>
                                            <div class="vital-value"><?= $record['vital_signs']['weight'] ?> kg</div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if (!empty($record['vital_signs']['height'])): ?>
                                        <div class="vital-item">
                                            <div class="vital-label">الطول</div>
                                            <div class="vital-value"><?= $record['vital_signs']['height'] ?> cm</div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($record['notes'])): ?>
                            <div class="content-item">
                                <div class="content-label">ملاحظات:</div>
                                <div class="content-value"><?= htmlspecialchars($record['notes']) ?></div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
    
    <!-- Allergies -->
    <?php if (!empty($allergies)): ?>
        <div class="section">
            <h2 class="section-title">الحساسيات</h2>
            <?php foreach ($allergies as $allergy): ?>
                <div class="allergy-card">
                    <div class="allergy-header">
                        <span class="allergy-name"><?= htmlspecialchars($allergy['allergen']) ?></span>
                        <span class="severity-badge"><?= MedicalRecord::getAllergySeverityLabel($allergy['severity']) ?></span>
                    </div>
                    <div class="content-value"><?= htmlspecialchars($allergy['reaction']) ?></div>
                    <?php if (!empty($allergy['notes'])): ?>
                        <div class="content-value" style="margin-top: 10px; font-style: italic;"><?= htmlspecialchars($allergy['notes']) ?></div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
    
    <!-- Lab Tests -->
    <?php if (!empty($labTests)): ?>
        <div class="section">
            <h2 class="section-title">فحوصات المختبر</h2>
            <?php foreach ($labTests as $test): ?>
                <div class="test-card">
                    <div class="test-header">
                        <span class="test-name"><?= htmlspecialchars($test['test_name']) ?></span>
                        <span class="status-badge"><?= MedicalRecord::getLabTestStatusLabel($test['status']) ?></span>
                    </div>
                    <div class="content-value"><?= htmlspecialchars($test['test_description']) ?></div>
                    <div class="content-value">تاريخ الفحص: <?= date('Y-m-d', strtotime($test['test_date'])) ?></div>
                    
                    <?php if ($test['status'] === 'completed' && !empty($test['results'])): ?>
                        <table class="results-table">
                            <thead>
                                <tr>
                                    <th>المعامل</th>
                                    <th>النتيجة</th>
                                    <th>النطاق الطبيعي</th>
                                    <th>الوحدة</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($test['results'] as $result): ?>
                                    <tr class="<?= $result['status'] ?? 'normal' ?>">
                                        <td><?= htmlspecialchars($result['parameter']) ?></td>
                                        <td><strong><?= htmlspecialchars($result['value']) ?></strong></td>
                                        <td><?= htmlspecialchars($result['normal_range']) ?></td>
                                        <td><?= htmlspecialchars($result['unit']) ?></td>
                                        <td>
                                            <?php
                                            $status = $result['status'] ?? 'normal';
                                            $statusLabel = $status === 'normal' ? 'طبيعي' : ($status === 'high' ? 'مرتفع' : 'منخفض');
                                            ?>
                                            <?= $statusLabel ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        
                        <?php if (!empty($test['interpretation'])): ?>
                            <div class="content-item" style="margin-top: 15px;">
                                <div class="content-label">تفسير النتائج:</div>
                                <div class="content-value"><?= htmlspecialchars($test['interpretation']) ?></div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($test['recommendations'])): ?>
                            <div class="content-item">
                                <div class="content-label">التوصيات:</div>
                                <div class="content-value"><?= htmlspecialchars($test['recommendations']) ?></div>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
    
    <!-- Footer -->
    <div class="footer">
        <p>تم إنشاء هذا التقرير بواسطة نظام HealthKey</p>
        <p>جميع الحقوق محفوظة &copy; <?= date('Y') ?></p>
    </div>
</body>
</html> 