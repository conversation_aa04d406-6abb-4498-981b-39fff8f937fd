<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-exclamation-triangle me-2"></i>
            الحساسيات
        </h1>
        <p class="text-muted">إدارة وتتبع الحساسيات الخاصة بك</p>
    </div>
    <div>
        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
            <i class="bi bi-printer me-2"></i>
            طباعة
        </button>
        <a href="<?= App::url('patient/dashboard') ?>" class="btn btn-outline-primary ms-2">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $totalAllergies ?></h4>
                        <p class="mb-0">إجمالي الحساسيات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-exclamation-triangle display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $allergiesStats['by_severity']['severe'] ?></h4>
                        <p class="mb-0">حساسيات شديدة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-shield-exclamation display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $allergiesStats['by_severity']['moderate'] ?></h4>
                        <p class="mb-0">حساسيات متوسطة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-exclamation-circle display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $allergiesStats['by_severity']['mild'] ?></h4>
                        <p class="mb-0">حساسيات خفيفة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-info-circle display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Add Allergy Form -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة حساسية جديدة
                </h5>
            </div>
            <div class="card-body">
                <form id="add_allergy_form">
                    <div class="mb-3">
                        <label for="allergen" class="form-label">المادة المسببة للحساسية *</label>
                        <input type="text" class="form-control" id="allergen" name="allergen" 
                               placeholder="مثال: البنسلين، الفول السوداني، الغبار..." required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="reaction" class="form-label">رد الفعل</label>
                        <textarea class="form-control" id="reaction" name="reaction" rows="3" 
                                  placeholder="وصف رد الفعل الذي يحدث عند التعرض للمادة..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="severity" class="form-label">شدة الحساسية</label>
                        <select class="form-select" id="severity" name="severity">
                            <option value="mild">خفيفة</option>
                            <option value="moderate">متوسطة</option>
                            <option value="severe">شديدة</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2" 
                                  placeholder="أي معلومات إضافية مهمة..."></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة الحساسية
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Allergies List -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-list-ul me-2"></i>
                    قائمة الحساسيات
                </h5>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportAllergies()">
                        <i class="bi bi-download me-1"></i>
                        تصدير
                    </button>
                </div>
            </div>
            <div class="card-body">
                <?php if (!empty($allergies)): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>المادة المسببة</th>
                                <th>رد الفعل</th>
                                <th>الشدة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($allergies as $allergy): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-exclamation-triangle me-2 text-warning"></i>
                                        <strong><?= htmlspecialchars($allergy['allergen']) ?></strong>
                                    </div>
                                </td>
                                <td>
                                    <?php if (!empty($allergy['reaction'])): ?>
                                        <span class="text-muted"><?= htmlspecialchars($allergy['reaction']) ?></span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $severityClass = 'bg-success';
                                    $severityText = 'خفيفة';
                                    if ($allergy['severity'] === 'moderate') {
                                        $severityClass = 'bg-warning';
                                        $severityText = 'متوسطة';
                                    } elseif ($allergy['severity'] === 'severe') {
                                        $severityClass = 'bg-danger';
                                        $severityText = 'شديدة';
                                    }
                                    ?>
                                    <span class="badge <?= $severityClass ?>"><?= $severityText ?></span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?= date('Y/m/d', strtotime($allergy['created_at'])) ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-info" 
                                                onclick="viewAllergyDetails(<?= $allergy['id'] ?>)" 
                                                title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteAllergy(<?= $allergy['id'] ?>, '<?= htmlspecialchars($allergy['allergen']) ?>')" 
                                                title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-5">
                    <i class="bi bi-exclamation-triangle display-1 text-muted"></i>
                    <h5 class="mt-3">لا توجد حساسيات مسجلة</h5>
                    <p class="text-muted">يمكنك إضافة حساسياتك من النموذج على اليسار</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Common Allergens Statistics -->
<?php if (!empty($allergiesStats['common_allergens'])): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    المواد المسببة للحساسية الشائعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($allergiesStats['common_allergens'] as $allergen => $count): ?>
                    <div class="col-md-4 col-sm-6 mb-3">
                        <div class="d-flex align-items-center p-3 border rounded">
                            <div class="me-3">
                                <i class="bi bi-exclamation-triangle fs-2 text-warning"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1"><?= htmlspecialchars(ucfirst($allergen)) ?></h6>
                                <p class="mb-0 text-muted"><?= $count ?> حساسية</p>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Allergy Details Modal -->
<div class="modal fade" id="allergyDetailsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الحساسية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="allergyDetailsContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
// إضافة حساسية جديدة
document.getElementById('add_allergy_form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('<?= App::url("patient/allergies") ?>', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ أثناء إضافة الحساسية');
    });
});

// حذف حساسية
function deleteAllergy(allergyId, allergenName) {
    if (confirm(`هل أنت متأكد من حذف الحساسية: ${allergenName}؟`)) {
        const formData = new FormData();
        formData.append('allergy_id', allergyId);
        
        fetch('<?= App::url("patient/delete-allergy") ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('danger', 'حدث خطأ أثناء حذف الحساسية');
        });
    }
}

// عرض تفاصيل الحساسية
function viewAllergyDetails(allergyId) {
    // يمكن إضافة عرض تفاصيل الحساسية هنا
    alert('سيتم إضافة عرض تفاصيل الحساسية قريباً');
}

// تصدير الحساسيات
function exportAllergies() {
    // يمكن إضافة وظيفة تصدير البيانات هنا
    alert('سيتم إضافة وظيفة التصدير قريباً');
}

// عرض التنبيهات
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script> 