<?php
/**
 * إنشاء جدول الرسائل
 */

require_once 'config.php';

try {
    $db = new PDO(
        'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4',
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n";
    
    // إنشاء جدول الرسائل
    $createTableSQL = "
    CREATE TABLE IF NOT EXISTS `messages` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `sender_id` int(11) NOT NULL COMMENT 'معرف المرسل',
        `sender_type` varchar(50) NOT NULL DEFAULT 'admin' COMMENT 'نوع المرسل',
        `recipient_id` int(11) NOT NULL COMMENT 'معرف المستقبل',
        `recipient_type` varchar(50) NOT NULL COMMENT 'نوع المستقبل',
        `subject` varchar(200) NOT NULL COMMENT 'موضوع الرسالة',
        `content` text NOT NULL COMMENT 'محتوى الرسالة',
        `type` varchar(50) NOT NULL DEFAULT 'system' COMMENT 'نوع الرسالة',
        `priority` varchar(20) NOT NULL DEFAULT 'normal' COMMENT 'أولوية الرسالة',
        `send_method` varchar(50) NOT NULL DEFAULT 'in_app' COMMENT 'طريقة الإرسال',
        `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT 'حالة الرسالة',
        `scheduled_time` datetime NULL COMMENT 'وقت الإرسال المجدول',
        `sent_time` datetime NULL COMMENT 'وقت الإرسال الفعلي',
        `read_time` datetime NULL COMMENT 'وقت القراءة',
        `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
        `updated_at` datetime NULL ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
        PRIMARY KEY (`id`),
        KEY `idx_sender` (`sender_id`, `sender_type`),
        KEY `idx_recipient` (`recipient_id`, `recipient_type`),
        KEY `idx_status` (`status`),
        KEY `idx_type` (`type`),
        KEY `idx_created_at` (`created_at`),
        KEY `idx_scheduled_time` (`scheduled_time`),
        CONSTRAINT `fk_messages_sender` FOREIGN KEY (`sender_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
        CONSTRAINT `fk_messages_recipient` FOREIGN KEY (`recipient_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الرسائل'
    ";
    
    $db->exec($createTableSQL);
    echo "✅ تم إنشاء جدول الرسائل بنجاح\n";
    
    // إضافة بيانات تجريبية
    $insertDataSQL = "
    INSERT INTO `messages` (`sender_id`, `sender_type`, `recipient_id`, `recipient_type`, `subject`, `content`, `type`, `priority`, `send_method`, `status`, `created_at`) VALUES
    (1, 'admin', 2, 'patient', 'مرحباً بك في HealthKey', 'مرحباً بك في نظام HealthKey! نتمنى لك تجربة طبية ممتازة.', 'system', 'normal', 'in_app', 'sent', NOW()),
    (1, 'admin', 3, 'doctor', 'تحديث النظام', 'تم تحديث النظام إلى الإصدار الجديد مع ميزات جديدة.', 'notification', 'high', 'in_app', 'sent', NOW()),
    (1, 'admin', 4, 'pharmacist', 'تذكير بالمواعيد', 'يرجى مراجعة المواعيد المعلقة لهذا الأسبوع.', 'reminder', 'normal', 'in_app', 'pending', NOW()),
    (1, 'admin', 2, 'patient', 'تحديث البيانات الشخصية', 'يرجى تحديث بياناتك الشخصية في النظام.', 'alert', 'high', 'in_app', 'read', NOW()),
    (1, 'admin', 3, 'doctor', 'إحصائيات الأسبوع', 'إحصائيات الأسبوع الماضي متاحة الآن في لوحة التحكم.', 'announcement', 'normal', 'in_app', 'sent', NOW())
    ";
    
    $db->exec($insertDataSQL);
    echo "✅ تم إضافة البيانات التجريبية بنجاح\n";
    
    // التحقق من إنشاء الجدول
    $stmt = $db->query("SELECT COUNT(*) as count FROM messages");
    $result = $stmt->fetch();
    echo "✅ عدد الرسائل في الجدول: " . $result['count'] . "\n";
    
    echo "\n🎉 تم إنشاء جدول الرسائل بنجاح!\n";
    
} catch (PDOException $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
}
?> 