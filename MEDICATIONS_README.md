# صفحة الأدوية للمريض - HealthKey

## نظرة عامة
تم إنشاء صفحة الأدوية للمريض (`patient/medications`) لتوفير وصول سهل ومعلومات شاملة عن الأدوية المتاحة في النظام.

## الميزات الرئيسية

### 1. الصفحة الرئيسية للأدوية (`patient/medications`)
- **إحصائيات شاملة**: عرض إجمالي الأدوية، الأدوية النشطة، الأدوية بدون وصفة، والأدوية التي تحتاج وصفة طبية
- **بحث متقدم**: إمكانية البحث بالاسم، الفئة، ونوع الوصفة
- **فلترة ذكية**: تصفية الأدوية حسب الفئة ونوع الوصفة
- **ترتيب ديناميكي**: إمكانية ترتيب الأدوية حسب الاسم، الفئة، أو نوع الوصفة
- **أدوية شائعة**: عرض الأدوية الأكثر شيوعاً
- **أدوية بدون وصفة**: عرض الأدوية المتاحة بدون وصفة طبية

### 2. صفحة تفاصيل الدواء (`patient/view-medication`)
- **معلومات شاملة**: اسم الدواء، الاسم العلمي، الفئة، القوة، شكل الجرعة
- **الآثار الجانبية**: عرض تحذيرات وآثار جانبية محتملة
- **موانع الاستخدام**: تحذيرات مهمة حول الحالات التي لا يجب فيها استخدام الدواء
- **شروط التخزين**: معلومات حول كيفية تخزين الدواء
- **إجراءات سريعة**: روابط لحجز موعد أو البحث عن صيدلية
- **أدوية مشابهة**: اقتراح أدوية مشابهة في نفس الفئة

## البنية التقنية

### الملفات المضافة/المعدلة:

#### 1. المتحكم (Controller)
```php
// app/controllers/PatientController.php
- medications() // الصفحة الرئيسية للأدوية
- viewMedication($medicationId) // صفحة تفاصيل الدواء
```

#### 2. الطرق (Routes)
```php
// app/core/App.php
'patient/medications' => ['controller' => 'PatientController', 'method' => 'medications'],
'patient/view-medication' => ['controller' => 'PatientController', 'method' => 'viewMedication'],
```

#### 3. صفحات العرض (Views)
```php
// views/patient/medications.php - الصفحة الرئيسية للأدوية
// views/patient/medication_details.php - صفحة تفاصيل الدواء
```

#### 4. Sidebar
```php
// views/partials/sidebar_patient.php - إضافة رابط الأدوية
```

### النماذج المستخدمة:
- `Medication` - للتعامل مع بيانات الأدوية
- `Prescription` - للوصفات الطبية المرتبطة

## الميزات التفاعلية

### 1. البحث والفلترة
- **بحث نصي**: البحث في اسم الدواء، الاسم العلمي، والوصف
- **فلترة بالفئة**: تصفية الأدوية حسب الفئة الطبية
- **فلترة بنوع الوصفة**: تمييز الأدوية التي تحتاج وصفة طبية عن تلك المتاحة بدون وصفة

### 2. الترتيب
- **ترتيب أبجدي**: حسب اسم الدواء
- **ترتيب بالفئة**: حسب الفئة الطبية
- **ترتيب بنوع الوصفة**: الأدوية التي تحتاج وصفة أولاً

### 3. التفاعل
- **عرض التفاصيل**: النقر على أي دواء لعرض تفاصيله الكاملة
- **طباعة المعلومات**: إمكانية طباعة معلومات الدواء
- **حجز موعد**: للأدوية التي تحتاج وصفة طبية
- **البحث عن صيدلية**: للأدوية المتاحة بدون وصفة

## الأمان والتحقق

### 1. التحقق من الصلاحيات
- التأكد من أن المستخدم مسجل دخول
- التحقق من نوع المستخدم (مريض فقط)

### 2. حماية البيانات
- تنظيف جميع المدخلات باستخدام `htmlspecialchars()`
- التحقق من وجود الدواء قبل عرض التفاصيل

## الاستخدام

### للمرضى:
1. **الوصول للصفحة**: من خلال sidebar المريض → "الأدوية"
2. **البحث**: استخدام شريط البحث للعثور على دواء معين
3. **الفلترة**: استخدام الفلاتر لتضييق نطاق البحث
4. **عرض التفاصيل**: النقر على أي دواء لعرض معلوماته الكاملة
5. **الإجراءات**: استخدام الأزرار المتاحة للحجز أو البحث عن صيدلية

### للمطورين:
1. **إضافة أدوية جديدة**: من خلال لوحة تحكم المدير
2. **تعديل المعلومات**: تحديث بيانات الأدوية من قاعدة البيانات
3. **إضافة فئات جديدة**: إضافة فئات أدوية جديدة حسب الحاجة

## التطوير المستقبلي

### الميزات المخطط إضافتها:
1. **مقارنة الأدوية**: إمكانية مقارنة دواءين أو أكثر
2. **تفاعلات الأدوية**: فحص التفاعلات بين الأدوية
3. **تقييمات المرضى**: إضافة نظام تقييم للأدوية
4. **أسعار الأدوية**: إضافة معلومات الأسعار (إذا كان متاحاً)
5. **تذكيرات الدواء**: نظام تذكير بمواعيد تناول الدواء
6. **خريطة الصيدليات**: تكامل مع خرائط Google للعثور على صيدليات قريبة

### التحسينات التقنية:
1. **تحسين الأداء**: إضافة cache للاستعلامات المتكررة
2. **البحث المتقدم**: إضافة بحث صوتي أو بالصورة
3. **API خارجي**: ربط مع قواعد بيانات أدوية خارجية
4. **التطبيق المحمول**: تطوير تطبيق جوال مخصص

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

1. **عدم ظهور الأدوية**:
   - تأكد من وجود بيانات في جدول `medications`
   - تحقق من حالة الأدوية (active/inactive)

2. **مشاكل في البحث**:
   - تأكد من صحة استعلام SQL في نموذج `Medication`
   - تحقق من ترميز النصوص (UTF-8)

3. **مشاكل في التصميم**:
   - تأكد من تحميل ملفات CSS و JavaScript
   - تحقق من توافق المتصفح

## الدعم والمساعدة

للمساعدة التقنية أو الإبلاغ عن مشاكل:
- تحقق من ملفات السجلات في النظام
- راجع قاعدة البيانات للتأكد من سلامة البيانات
- تأكد من إعدادات الخادم والصلاحيات

---

**تاريخ الإنشاء**: يوليو 2025  
**الإصدار**: 1.0  
**المطور**: HealthKey Team 