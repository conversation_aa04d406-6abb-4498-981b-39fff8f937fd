# إصلاح صفحة المخزون في الشريط الجانبي - HealthKey

## المشكلة

كانت هناك مشكلة في صفحة المخزون حيث يظهر الخطأ:
```
Undefined property: PharmacistController::$inventoryModel
Call to a member function getAll() on null
```

## الحلول المطبقة

### 1. إضافة نموذج المخزون إلى متحكم الصيدلي

**الملف:** `app/controllers/PharmacistController.php`

**التغيير:**
```php
// قبل
// $this->inventoryModel = new Inventory(); // سيتم إضافة نموذج المخزون لاحقاً

// بعد
$this->inventoryModel = new Inventory();
```

### 2. تنفيذ دوال إدارة المخزون

تم استبدال الدوال المؤقتة بالتنفيذ الكامل:

#### دالة `inventory()`
```php
public function inventory()
{
    $search = App::get('search', '');
    $category = App::get('category', '');
    
    $inventory = [];
    
    if (!empty($search) || !empty($category)) {
        $inventory = $this->inventoryModel->search($search, $category);
    } else {
        $inventory = $this->inventoryModel->getAll(50);
    }

    $data = [
        'title' => 'إدارة المخزون',
        'inventory' => $inventory,
        'search' => $search,
        'currentCategory' => $category,
        'stats' => $this->inventoryModel->getStats(),
        'categories' => $this->inventoryModel->getCategories(),
        'lowStock' => $this->inventoryModel->getLowStock(5),
        'expired' => $this->inventoryModel->getExpired(5),
        'expiringSoon' => $this->inventoryModel->getExpiringSoon(30, 5),
        'inventoryStats' => $this->getInventoryStats()
    ];

    $this->view('pharmacist/inventory', $data);
}
```

#### دالة `addInventory()`
```php
public function addInventory()
{
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $data = [
            'name' => $_POST['name'] ?? '',
            'generic_name' => $_POST['generic_name'] ?? '',
            'category' => $_POST['category'] ?? '',
            'dosage_form' => $_POST['dosage_form'] ?? '',
            'strength' => $_POST['strength'] ?? '',
            'manufacturer' => $_POST['manufacturer'] ?? '',
            'description' => $_POST['description'] ?? '',
            'quantity' => (int)($_POST['quantity'] ?? 0),
            'unit_price' => (float)($_POST['unit_price'] ?? 0),
            'reorder_level' => (int)($_POST['reorder_level'] ?? 0),
            'expiry_date' => $_POST['expiry_date'] ?? '',
            'batch_number' => $_POST['batch_number'] ?? '',
            'location' => $_POST['location'] ?? ''
        ];

        if ($this->inventoryModel->create($data)) {
            $this->setFlashMessage('تم إضافة الدواء بنجاح', 'success');
        } else {
            $this->setFlashMessage('حدث خطأ أثناء إضافة الدواء', 'error');
        }
        
        $this->redirect('pharmacist/inventory');
        return;
    }

    $data = [
        'title' => 'إضافة دواء جديد',
        'categories' => $this->inventoryModel->getCategories(),
        'dosageForms' => $this->inventoryModel->getDosageForms(),
        'inventoryStats' => $this->getInventoryStats()
    ];

    $this->view('pharmacist/add_inventory', $data);
}
```

#### دالة `editInventory($id)`
```php
public function editInventory($id)
{
    $item = $this->inventoryModel->findById($id);
    
    if (!$item) {
        $this->setFlashMessage('الدواء غير موجود', 'error');
        $this->redirect('pharmacist/inventory');
        return;
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $data = [
            'name' => $_POST['name'] ?? '',
            'generic_name' => $_POST['generic_name'] ?? '',
            'category' => $_POST['category'] ?? '',
            'dosage_form' => $_POST['dosage_form'] ?? '',
            'strength' => $_POST['strength'] ?? '',
            'manufacturer' => $_POST['manufacturer'] ?? '',
            'description' => $_POST['description'] ?? '',
            'quantity' => (int)($_POST['quantity'] ?? 0),
            'unit_price' => (float)($_POST['unit_price'] ?? 0),
            'reorder_level' => (int)($_POST['reorder_level'] ?? 0),
            'expiry_date' => $_POST['expiry_date'] ?? '',
            'batch_number' => $_POST['batch_number'] ?? '',
            'location' => $_POST['location'] ?? ''
        ];

        if ($this->inventoryModel->update($id, $data)) {
            $this->setFlashMessage('تم تحديث معلومات الدواء بنجاح', 'success');
        } else {
            $this->setFlashMessage('حدث خطأ أثناء تحديث معلومات الدواء', 'error');
        }
        
        $this->redirect('pharmacist/inventory');
        return;
    }

    $data = [
        'title' => 'تعديل معلومات الدواء',
        'item' => $item,
        'categories' => $this->inventoryModel->getCategories(),
        'dosageForms' => $this->inventoryModel->getDosageForms(),
        'inventoryStats' => $this->getInventoryStats()
    ];

    $this->view('pharmacist/edit_inventory', $data);
}
```

#### دالة `deleteInventory($id)`
```php
public function deleteInventory($id)
{
    if ($this->inventoryModel->delete($id)) {
        $this->setFlashMessage('تم حذف الدواء بنجاح', 'success');
    } else {
        $this->setFlashMessage('حدث خطأ أثناء حذف الدواء', 'error');
    }
    
    $this->redirect('pharmacist/inventory');
}
```

#### دالة `updateQuantity()`
```php
public function updateQuantity()
{
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $id = $_POST['id'] ?? 0;
        $quantity = (int)($_POST['quantity'] ?? 0);
        
        if ($this->inventoryModel->updateQuantity($id, $quantity)) {
            echo json_encode(['success' => true, 'message' => 'تم تحديث الكمية بنجاح']);
        } else {
            echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء تحديث الكمية']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'طلب غير صحيح']);
    }
}
```

### 3. إضافة دالة إحصائيات المخزون

**الملف:** `app/controllers/PharmacistController.php`

```php
/**
 * الحصول على إحصائيات المخزون للشريط الجانبي
 */
public function getInventoryStats()
{
    $stats = [
        'low_stock' => 0,
        'expired' => 0,
        'expiring_soon' => 0
    ];

    // الأدوية منخفضة المخزون
    $lowStock = $this->inventoryModel->getLowStock();
    $stats['low_stock'] = count($lowStock);

    // الأدوية منتهية الصلاحية
    $expired = $this->inventoryModel->getExpired();
    $stats['expired'] = count($expired);

    // الأدوية التي ستنتهي صلاحيتها قريباً
    $expiringSoon = $this->inventoryModel->getExpiringSoon(30);
    $stats['expiring_soon'] = count($expiringSoon);

    return $stats;
}
```

### 4. إضافة إحصائيات المخزون لجميع الصفحات

تم إضافة `'inventoryStats' => $this->getInventoryStats()` لجميع دوال الصيدلي:
- `dashboard()`
- `prescriptions()`
- `viewPrescription()`
- `dispensing()`
- `dispensingReport()`
- `notifications()`
- `profile()`
- `workingHours()`
- `inventory()`
- `addInventory()`
- `editInventory()`

### 5. إصلاح الشريط الجانبي

**الملف:** `views/partials/sidebar_pharmacist.php`

**التغيير:**
```php
// قبل
<span class="nav-badge bg-info"><?= isset($stats['inventory']['low_stock']) ? $stats['inventory']['low_stock'] : 0 ?></span>

// بعد
<span class="nav-badge bg-info"><?= isset($inventoryStats['low_stock']) ? $inventoryStats['low_stock'] : 0 ?></span>
```

## النتائج

✅ **تم إصلاح المشكلة بنجاح**

- صفحة المخزون تعمل بشكل صحيح
- الشريط الجانبي يعرض عدد الأدوية منخفضة المخزون
- جميع دوال إدارة المخزون تعمل (إضافة، تعديل، حذف، تحديث الكمية)
- إحصائيات المخزون متوفرة في جميع الصفحات

## الاختبار

تم إنشاء ملفات اختبار للتأكد من صحة الإصلاحات:

1. `test_inventory_fix.php` - اختبار نموذج المخزون ومتحكم الصيدلي
2. `test_inventory_page_simple.php` - اختبار عرض بيانات المخزون

## كيفية الوصول

يمكن الوصول لصفحة المخزون عبر:
```
http://localhost/HealthKey/pharmacist/inventory
```

## ملاحظات تقنية

- تم استخدام `$this->inventoryModel` بدلاً من `$this->db` مباشرة
- تم جعل دالة `getInventoryStats()` عامة (public) للاختبار
- تم إضافة إحصائيات المخزون لجميع الصفحات لضمان عمل الشريط الجانبي
- تم استخدام دوال النموذج للحصول على الإحصائيات بدلاً من استعلامات SQL مباشرة 