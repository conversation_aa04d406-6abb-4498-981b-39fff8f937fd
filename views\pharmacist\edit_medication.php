<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">تعديل معلومات الدواء</h1>
        <p class="text-muted">تعديل معلومات الدواء: <?= htmlspecialchars($medication['name']) ?></p>
    </div>
    <div>
        <a href="<?= App::url('pharmacist/medications') ?>" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            العودة للأدوية
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-pencil me-2"></i>
                    تعديل معلومات الدواء
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= App::url('pharmacist/edit-medication/' . $medication['id']) ?>">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم الدواء <span class="text-danger">*</span></label>
                            <input type="text" name="name" class="form-control" 
                                   value="<?= htmlspecialchars($medication['name']) ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الاسم العلمي <span class="text-danger">*</span></label>
                            <input type="text" name="generic_name" class="form-control" 
                                   value="<?= htmlspecialchars($medication['generic_name']) ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الفئة <span class="text-danger">*</span></label>
                            <select name="category" class="form-select" required>
                                <option value="">اختر الفئة</option>
                                <option value="مسكنات" <?= $medication['category'] === 'مسكنات' ? 'selected' : '' ?>>مسكنات</option>
                                <option value="مضادات حيوية" <?= $medication['category'] === 'مضادات حيوية' ? 'selected' : '' ?>>مضادات حيوية</option>
                                <option value="مضادات الحموضة" <?= $medication['category'] === 'مضادات الحموضة' ? 'selected' : '' ?>>مضادات الحموضة</option>
                                <option value="فيتامينات" <?= $medication['category'] === 'فيتامينات' ? 'selected' : '' ?>>فيتامينات</option>
                                <option value="أدوية القلب" <?= $medication['category'] === 'أدوية القلب' ? 'selected' : '' ?>>أدوية القلب</option>
                                <option value="أدوية السكري" <?= $medication['category'] === 'أدوية السكري' ? 'selected' : '' ?>>أدوية السكري</option>
                                <option value="أدوية الضغط" <?= $medication['category'] === 'أدوية الضغط' ? 'selected' : '' ?>>أدوية الضغط</option>
                                <option value="أدوية الحساسية" <?= $medication['category'] === 'أدوية الحساسية' ? 'selected' : '' ?>>أدوية الحساسية</option>
                                <option value="أخرى" <?= $medication['category'] === 'أخرى' ? 'selected' : '' ?>>أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">شكل الجرعة <span class="text-danger">*</span></label>
                            <select name="dosage_form" class="form-select" required>
                                <option value="">اختر الشكل</option>
                                <option value="أقراص" <?= $medication['dosage_form'] === 'أقراص' ? 'selected' : '' ?>>أقراص</option>
                                <option value="كبسولات" <?= $medication['dosage_form'] === 'كبسولات' ? 'selected' : '' ?>>كبسولات</option>
                                <option value="شراب" <?= $medication['dosage_form'] === 'شراب' ? 'selected' : '' ?>>شراب</option>
                                <option value="حقن" <?= $medication['dosage_form'] === 'حقن' ? 'selected' : '' ?>>حقن</option>
                                <option value="كريم" <?= $medication['dosage_form'] === 'كريم' ? 'selected' : '' ?>>كريم</option>
                                <option value="مرهم" <?= $medication['dosage_form'] === 'مرهم' ? 'selected' : '' ?>>مرهم</option>
                                <option value="قطرات" <?= $medication['dosage_form'] === 'قطرات' ? 'selected' : '' ?>>قطرات</option>
                                <option value="أخرى" <?= $medication['dosage_form'] === 'أخرى' ? 'selected' : '' ?>>أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">القوة <span class="text-danger">*</span></label>
                            <input type="text" name="strength" class="form-control" 
                                   value="<?= htmlspecialchars($medication['strength']) ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الحالة</label>
                            <select name="status" class="form-select">
                                <option value="active" <?= $medication['status'] === 'active' ? 'selected' : '' ?>>نشط</option>
                                <option value="inactive" <?= $medication['status'] === 'inactive' ? 'selected' : '' ?>>غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check mt-4">
                                <input class="form-check-input" type="checkbox" name="prescription_required" 
                                       value="1" <?= $medication['prescription_required'] ? 'checked' : '' ?>>
                                <label class="form-check-label">
                                    يحتاج وصفة طبية
                                </label>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">الوصف</label>
                            <textarea name="description" class="form-control" rows="4"><?= htmlspecialchars($medication['description']) ?></textarea>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">الآثار الجانبية</label>
                            <textarea name="side_effects" class="form-control" rows="3"><?= htmlspecialchars($medication['side_effects']) ?></textarea>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">موانع الاستخدام</label>
                            <textarea name="contraindications" class="form-control" rows="3"><?= htmlspecialchars($medication['contraindications']) ?></textarea>
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">ظروف التخزين</label>
                            <textarea name="storage_conditions" class="form-control" rows="2"><?= htmlspecialchars($medication['storage_conditions']) ?></textarea>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="<?= App::url('pharmacist/medications') ?>" class="btn btn-secondary">
                            <i class="bi bi-x-circle me-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- معلومات الدواء الحالية -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    معلومات الدواء الحالية
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>اسم الدواء:</strong>
                    <p class="mb-0"><?= htmlspecialchars($medication['name']) ?></p>
                </div>
                <div class="mb-3">
                    <strong>الاسم العلمي:</strong>
                    <p class="mb-0"><?= htmlspecialchars($medication['generic_name']) ?></p>
                </div>
                <div class="mb-3">
                    <strong>الفئة:</strong>
                    <span class="badge bg-primary"><?= htmlspecialchars($medication['category']) ?></span>
                </div>
                <div class="mb-3">
                    <strong>شكل الجرعة:</strong>
                    <p class="mb-0"><?= htmlspecialchars($medication['dosage_form']) ?></p>
                </div>
                <div class="mb-3">
                    <strong>القوة:</strong>
                    <p class="mb-0"><?= htmlspecialchars($medication['strength']) ?></p>
                </div>
                <div class="mb-3">
                    <strong>الوصفة المطلوبة:</strong>
                    <?php if ($medication['prescription_required']): ?>
                        <span class="badge bg-warning">تحتاج وصفة</span>
                    <?php else: ?>
                        <span class="badge bg-success">بدون وصفة</span>
                    <?php endif; ?>
                </div>
                <div class="mb-3">
                    <strong>الحالة:</strong>
                    <?php if ($medication['status'] === 'active'): ?>
                        <span class="badge bg-success">نشط</span>
                    <?php else: ?>
                        <span class="badge bg-secondary">غير نشط</span>
                    <?php endif; ?>
                </div>
                <div class="mb-3">
                    <strong>تاريخ الإنشاء:</strong>
                    <p class="mb-0"><?= date('Y-m-d', strtotime($medication['created_at'])) ?></p>
                </div>
                <?php if (!empty($medication['updated_at'])): ?>
                <div class="mb-3">
                    <strong>آخر تحديث:</strong>
                    <p class="mb-0"><?= date('Y-m-d', strtotime($medication['updated_at'])) ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- إجراءات سريعة -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?= App::url('pharmacist/medications') ?>" class="btn btn-outline-primary">
                        <i class="bi bi-list me-2"></i>
                        عرض جميع الأدوية
                    </a>
                    <a href="<?= App::url('pharmacist/add-medication') ?>" class="btn btn-outline-success">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة دواء جديد
                    </a>
                    <a href="<?= App::url('pharmacist/delete-medication/' . $medication['id']) ?>" 
                       class="btn btn-outline-danger"
                       onclick="return confirm('هل أنت متأكد من حذف هذا الدواء؟')">
                        <i class="bi bi-trash me-2"></i>
                        حذف الدواء
                    </a>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    إحصائيات الأدوية
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>إجمالي الأدوية:</span>
                    <span class="badge bg-primary"><?= $stats['total_medications'] ?? 0 ?></span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>الأدوية النشطة:</span>
                    <span class="badge bg-success"><?= $stats['active_medications'] ?? 0 ?></span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>تحتاج وصفة:</span>
                    <span class="badge bg-warning"><?= $stats['prescription_required'] ?? 0 ?></span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>بدون وصفة:</span>
                    <span class="badge bg-info"><?= $stats['otc_medications'] ?? 0 ?></span>
                </div>
                <div class="d-flex justify-content-between">
                    <span>جدد هذا الشهر:</span>
                    <span class="badge bg-dark"><?= $stats['new_this_month'] ?? 0 ?></span>
                </div>
            </div>
        </div>
    </div>
</div> 