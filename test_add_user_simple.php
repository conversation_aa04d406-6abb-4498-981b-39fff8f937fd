<?php
/**
 * اختبار بسيط لإضافة مستخدم
 */

// تضمين ملفات النظام
require_once 'config.php';
require_once 'app/core/App.php';
require_once 'app/core/Database.php';
require_once 'app/models/User.php';
require_once 'app/helpers/SessionHelper.php';

// محاكاة تسجيل دخول المدير
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'admin';
$_SESSION['user_name'] = 'مدير النظام';

// اختبار البيانات
$testData = [
    'first_name' => 'أحمد',
    'last_name' => 'محمد',
    'email' => '<EMAIL>',
    'user_type' => 'patient',
    'phone' => '**********',
    'national_id' => '**********',
    'date_of_birth' => '1990-01-01',
    'gender' => 'male',
    'address' => 'الرياض، المملكة العربية السعودية',
    'emergency_contact' => 'أبو أحمد',
    'emergency_phone' => '**********',
    'is_active' => 1
];

echo "<h2>اختبار إضافة مستخدم</h2>";
echo "<h3>البيانات المراد إضافتها:</h3>";
echo "<pre>";
print_r($testData);
echo "</pre>";

// إنشاء نموذج المستخدم
$userModel = new User();

// اختبار التحقق من صحة البيانات
echo "<h3>نتيجة التحقق من صحة البيانات:</h3>";
$errors = $userModel->validate($testData);
if (empty($errors)) {
    echo "<p style='color: green;'>✓ البيانات صحيحة</p>";
} else {
    echo "<p style='color: red;'>✗ هناك أخطاء:</p>";
    echo "<pre>";
    print_r($errors);
    echo "</pre>";
}

// اختبار إنشاء المستخدم
if (empty($errors)) {
    echo "<h3>محاولة إنشاء المستخدم:</h3>";
    $userId = $userModel->create($testData);
    
    if ($userId) {
        echo "<p style='color: green;'>✓ تم إنشاء المستخدم بنجاح. معرف المستخدم: $userId</p>";
        
        // التحقق من وجود المستخدم
        $user = $userModel->findById($userId);
        if ($user) {
            echo "<h3>بيانات المستخدم المُنشأ:</h3>";
            echo "<pre>";
            print_r($user);
            echo "</pre>";
        }
    } else {
        echo "<p style='color: red;'>✗ فشل في إنشاء المستخدم</p>";
    }
}

echo "<hr>";
echo "<p><a href='admin/add-user'>العودة لصفحة إضافة المستخدم</a></p>";
?> 