<?php
/**
 * إنشاء جدول inventory
 */

require_once 'config.php';
require_once 'app/core/Database.php';

echo "<h1>إنشاء جدول inventory</h1>";

try {
    $db = Database::getInstance();
    
    // إنشاء جدول inventory
    $sql = "
    CREATE TABLE IF NOT EXISTS inventory (
        id INT AUTO_INCREMENT PRIMARY KEY,
        medication_id INT NOT NULL,
        quantity INT NOT NULL DEFAULT 0,
        unit_price DECIMAL(10,2) DEFAULT 0.00,
        reorder_level INT DEFAULT 10,
        expiry_date DATE,
        batch_number VARCHAR(100),
        location VARCHAR(255),
        supplier_id INT,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (medication_id) REFERENCES medications(id) ON DELETE CASCADE,
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    $db->getConnection()->exec($sql);
    echo "✅ تم إنشاء جدول inventory بنجاح<br>";
    
    // إضافة بيانات تجريبية
    $sampleInventory = [
        [
            'medication_id' => 1, // باراسيتامول
            'quantity' => 150,
            'unit_price' => 5.00,
            'reorder_level' => 50,
            'expiry_date' => '2025-12-31',
            'batch_number' => 'BATCH-001-2024',
            'location' => 'رف A - صف 1',
            'supplier_id' => 1,
            'notes' => 'مخزون جيد'
        ],
        [
            'medication_id' => 2, // أموكسيسيلين
            'quantity' => 80,
            'unit_price' => 15.00,
            'reorder_level' => 30,
            'expiry_date' => '2025-06-30',
            'batch_number' => 'BATCH-002-2024',
            'location' => 'رف B - صف 2',
            'supplier_id' => 1,
            'notes' => 'مخزون متوسط'
        ],
        [
            'medication_id' => 3, // أوميبرازول
            'quantity' => 45,
            'unit_price' => 25.00,
            'reorder_level' => 20,
            'expiry_date' => '2025-09-15',
            'batch_number' => 'BATCH-003-2024',
            'location' => 'رف C - صف 1',
            'supplier_id' => 2,
            'notes' => 'مخزون منخفض'
        ],
        [
            'medication_id' => 4, // فيتامين د
            'quantity' => 200,
            'unit_price' => 30.00,
            'reorder_level' => 40,
            'expiry_date' => '2026-03-31',
            'batch_number' => 'BATCH-004-2024',
            'location' => 'رف A - صف 3',
            'supplier_id' => 2,
            'notes' => 'مخزون ممتاز'
        ],
        [
            'medication_id' => 5, // إيبوبروفين
            'quantity' => 25,
            'unit_price' => 8.00,
            'reorder_level' => 35,
            'expiry_date' => '2025-11-30',
            'batch_number' => 'BATCH-005-2024',
            'location' => 'رف B - صف 1',
            'supplier_id' => 1,
            'notes' => 'مخزون منخفض - يحتاج طلب'
        ]
    ];
    
    foreach ($sampleInventory as $item) {
        $insertSql = "INSERT INTO inventory (medication_id, quantity, unit_price, reorder_level, expiry_date, batch_number, location, supplier_id, notes) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $db->insert($insertSql, [
            $item['medication_id'],
            $item['quantity'],
            $item['unit_price'],
            $item['reorder_level'],
            $item['expiry_date'],
            $item['batch_number'],
            $item['location'],
            $item['supplier_id'],
            $item['notes']
        ]);
    }
    
    echo "✅ تم إضافة البيانات التجريبية بنجاح<br>";
    
    // التحقق من إنشاء الجدول
    $count = $db->selectOne("SELECT COUNT(*) as count FROM inventory")['count'];
    echo "✅ عدد العناصر في الجدول: $count<br>";
    
    // عرض البيانات المضافة
    echo "<h3>البيانات المضافة:</h3>";
    $inventory = $db->select("
        SELECT i.*, m.name as medication_name, m.generic_name, s.name as supplier_name
        FROM inventory i
        JOIN medications m ON i.medication_id = m.id
        LEFT JOIN suppliers s ON i.supplier_id = s.id
        ORDER BY i.id
    ");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Medication</th><th>Quantity</th><th>Unit Price</th><th>Reorder Level</th><th>Expiry Date</th><th>Location</th></tr>";
    foreach ($inventory as $item) {
        echo "<tr>";
        echo "<td>" . $item['id'] . "</td>";
        echo "<td>" . htmlspecialchars($item['medication_name']) . "</td>";
        echo "<td>" . $item['quantity'] . "</td>";
        echo "<td>" . $item['unit_price'] . " ريال</td>";
        echo "<td>" . $item['reorder_level'] . "</td>";
        echo "<td>" . $item['expiry_date'] . "</td>";
        echo "<td>" . htmlspecialchars($item['location']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>✅ تم إنشاء جدول inventory بنجاح!</h2>";
    echo "<p>يمكنك الآن الوصول لصفحة إدارة المخزون:</p>";
    echo "<a href='index.php?url=pharmacist/inventory' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>فتح صفحة إدارة المخزون</a>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في إنشاء الجدول</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 