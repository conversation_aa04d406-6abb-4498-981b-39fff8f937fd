<?php
// اختبار كل دالة إحصائيات على حدة
require_once 'config.php';
require_once 'app/core/App.php';
require_once 'app/core/Database.php';
require_once 'app/controllers/AdminController.php';

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

// محاكاة تسجيل دخول المدير
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'admin';
$_SESSION['is_logged_in'] = true;

echo "<h1>اختبار كل دالة إحصائيات على حدة</h1>";

try {
    $adminController = new AdminController();
    $startDate = '2025-01-01';
    $endDate = '2025-12-31';
    
    echo "<h2>1. اختبار getPerformanceStats</h2>";
    try {
        $stats = $adminController->getPerformanceStats($startDate, $endDate);
        echo "<pre>" . print_r($stats, true) . "</pre>";
        echo "✅ نجح اختبار getPerformanceStats<br>";
    } catch (Exception $e) {
        echo "❌ فشل اختبار getPerformanceStats: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>2. اختبار getFinancialStats</h2>";
    try {
        $stats = $adminController->getFinancialStats($startDate, $endDate);
        echo "<pre>" . print_r($stats, true) . "</pre>";
        echo "✅ نجح اختبار getFinancialStats<br>";
    } catch (Exception $e) {
        echo "❌ فشل اختبار getFinancialStats: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>3. اختبار getQualityStats</h2>";
    try {
        $stats = $adminController->getQualityStats($startDate, $endDate);
        echo "<pre>" . print_r($stats, true) . "</pre>";
        echo "✅ نجح اختبار getQualityStats<br>";
    } catch (Exception $e) {
        echo "❌ فشل اختبار getQualityStats: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>4. اختبار getActivityStats</h2>";
    try {
        $stats = $adminController->getActivityStats($startDate, $endDate);
        echo "<pre>" . print_r($stats, true) . "</pre>";
        echo "✅ نجح اختبار getActivityStats<br>";
    } catch (Exception $e) {
        echo "❌ فشل اختبار getActivityStats: " . $e->getMessage() . "<br>";
    }
    
    echo "<h2>5. اختبار getChartsData</h2>";
    try {
        $stats = $adminController->getChartsData($startDate, $endDate);
        echo "<pre>" . print_r($stats, true) . "</pre>";
        echo "✅ نجح اختبار getChartsData<br>";
    } catch (Exception $e) {
        echo "❌ فشل اختبار getChartsData: " . $e->getMessage() . "<br>";
    }
    
} catch (Exception $e) {
    echo "<h2>خطأ عام</h2>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "<p>الملف: " . $e->getFile() . "</p>";
    echo "<p>السطر: " . $e->getLine() . "</p>";
}
?> 