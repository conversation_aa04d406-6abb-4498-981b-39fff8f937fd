<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-people me-2"></i>
            إدارة المستخدمين
        </h1>
        <p class="text-muted">إدارة جميع مستخدمي النظام والتحكم في صلاحياتهم</p>
    </div>
    <div class="d-flex gap-2">
        <button id="bulkActionsButton" class="btn btn-outline-warning" style="display: none;" 
                data-bs-toggle="modal" data-bs-target="#bulkActionsModal">
            <i class="bi bi-gear"></i>
            إجراءات جماعية
        </button>
        <button class="btn btn-outline-info" onclick="exportUsers()">
            <i class="bi bi-download"></i>
            تصدير البيانات
        </button>
        <a href="<?= App::url('admin/add-user') ?>" class="btn btn-primary">
            <i class="bi bi-person-plus"></i>
            إضافة مستخدم جديد
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-0"><?= $totalUsers ?></h4>
                        <small>إجمالي المستخدمين</small>
                    </div>
                    <i class="bi bi-people-fill fs-1 opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-gradient-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-0"><?= count(array_filter($users, fn($u) => $u['is_active'])) ?></h4>
                        <small>المستخدمين النشطين</small>
                    </div>
                    <i class="bi bi-check-circle-fill fs-1 opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-gradient-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-0"><?= count(array_filter($users, fn($u) => !$u['is_active'])) ?></h4>
                        <small>المستخدمين غير النشطين</small>
                    </div>
                    <i class="bi bi-pause-circle-fill fs-1 opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-gradient-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-0"><?= count(array_filter($users, fn($u) => $u['created_at'] >= date('Y-m-01'))) ?></h4>
                        <small>جدد هذا الشهر</small>
                    </div>
                    <i class="bi bi-person-plus-fill fs-1 opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-funnel me-2"></i>
            فلاتر البحث
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="<?= App::url('admin/users') ?>" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?= htmlspecialchars($filters['search']) ?>" 
                       placeholder="الاسم، البريد الإلكتروني، أو الهاتف">
            </div>
            <div class="col-md-2">
                <label for="user_type" class="form-label">نوع المستخدم</label>
                <select class="form-select" id="user_type" name="user_type">
                    <option value="">جميع الأنواع</option>
                    <option value="patient" <?= $filters['user_type'] === 'patient' ? 'selected' : '' ?>>مريض</option>
                    <option value="doctor" <?= $filters['user_type'] === 'doctor' ? 'selected' : '' ?>>طبيب</option>
                    <option value="pharmacist" <?= $filters['user_type'] === 'pharmacist' ? 'selected' : '' ?>>صيدلي</option>
                    <option value="admin" <?= $filters['user_type'] === 'admin' ? 'selected' : '' ?>>مدير</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="active" <?= $filters['status'] === 'active' ? 'selected' : '' ?>>نشط</option>
                    <option value="inactive" <?= $filters['status'] === 'inactive' ? 'selected' : '' ?>>غير نشط</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i>
                        بحث
                    </button>
                    <a href="<?= App::url('admin/users') ?>" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise"></i>
                        إعادة تعيين
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-table me-2"></i>
            قائمة المستخدمين
        </h5>
        <div class="d-flex align-items-center gap-2">
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="selectAll" onchange="selectAllUsers()">
                <label class="form-check-label" for="selectAll">
                    تحديد الكل
                </label>
            </div>
        </div>
    </div>
    <div class="card-body">
        <?php if (!empty($users)): ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th width="50">
                                <i class="bi bi-check2-square"></i>
                            </th>
                            <th>المستخدم</th>
                            <th>النوع</th>
                            <th>معلومات الاتصال</th>
                            <th>تاريخ التسجيل</th>
                            <th>آخر تسجيل دخول</th>
                            <th>الحالة</th>
                            <th width="150">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input user-checkbox" 
                                           value="<?= $user['id'] ?>" onchange="toggleUserSelection(<?= $user['id'] ?>)">
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-md bg-light rounded-circle me-3 d-flex align-items-center justify-content-center">
                                            <i class="bi bi-person text-muted"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?= User::getFullName($user) ?></h6>
                                            <small class="text-muted">ID: <?= $user['id'] ?></small>
                                            <?php if ($user['id'] == $currentUser['id']): ?>
                                                <br><span class="badge bg-info">أنت</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?= User::getTypeColor($user['user_type']) ?>">
                                        <?= User::getTypeLabel($user['user_type']) ?>
                                    </span>
                                </td>
                                <td>
                                    <div>
                                        <i class="bi bi-envelope me-1"></i>
                                        <small><?= htmlspecialchars($user['email']) ?></small>
                                    </div>
                                    <?php if (!empty($user['phone'])): ?>
                                        <div>
                                            <i class="bi bi-telephone me-1"></i>
                                            <small><?= htmlspecialchars($user['phone']) ?></small>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small><?= DateHelper::formatArabic($user['created_at']) ?></small>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?= !empty($user['last_login']) ? DateHelper::formatArabic($user['last_login'], 'short') : 'لم يسجل دخول' ?>
                                    </small>
                                </td>
                                <td>
                                    <span class="badge bg-<?= $user['is_active'] ? 'success' : 'secondary' ?>">
                                        <?= $user['is_active'] ? 'نشط' : 'غير نشط' ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="<?= App::url('admin/edit-user/' . $user['id']) ?>" 
                                           class="btn btn-outline-primary" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button class="btn btn-outline-<?= $user['is_active'] ? 'warning' : 'success' ?>" 
                                                onclick="toggleUserStatus(<?= $user['id'] ?>)" 
                                                title="<?= $user['is_active'] ? 'إلغاء التفعيل' : 'تفعيل' ?>">
                                            <i class="bi bi-<?= $user['is_active'] ? 'pause' : 'play' ?>"></i>
                                        </button>
                                        <?php if ($user['id'] != $currentUser['id']): ?>
                                            <button class="btn btn-outline-danger" 
                                                    onclick="deleteUser(<?= $user['id'] ?>)" 
                                                    title="حذف">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <nav aria-label="تصفح الصفحات" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <?php if ($pagination['prev']): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= $pagination['prev'] ?>">السابق</a>
                            </li>
                        <?php endif; ?>

                        <?php foreach ($pagination['pages'] as $page): ?>
                            <li class="page-item <?= $page['current'] ? 'active' : '' ?>">
                                <a class="page-link" href="<?= $page['url'] ?>"><?= $page['number'] ?></a>
                            </li>
                        <?php endforeach; ?>

                        <?php if ($pagination['next']): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?= $pagination['next'] ?>">التالي</a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>

        <?php else: ?>
            <div class="text-center py-5">
                <i class="bi bi-person-x display-1 text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد مستخدمين</h4>
                <p class="text-muted">لم يتم العثور على مستخدمين مطابقين لمعايير البحث</p>
                <a href="<?= App::url('admin/add-user') ?>" class="btn btn-primary">
                    <i class="bi bi-person-plus"></i>
                    إضافة مستخدم جديد
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Bulk Actions Modal -->
<div class="modal fade" id="bulkActionsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-gear me-2"></i>
                    إجراءات جماعية
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>اختر الإجراء المطلوب تطبيقه على المستخدمين المحددين:</p>
                <div class="d-grid gap-2">
                    <button class="btn btn-success" onclick="bulkActivate()">
                        <i class="bi bi-check-circle"></i>
                        تفعيل المحددين
                    </button>
                    <button class="btn btn-warning" onclick="bulkDeactivate()">
                        <i class="bi bi-pause-circle"></i>
                        إلغاء تفعيل المحددين
                    </button>
                    <button class="btn btn-danger" onclick="bulkDelete()">
                        <i class="bi bi-trash"></i>
                        حذف المحددين
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// متغير عام لتخزين المستخدمين المحددين
let selectedUsers = [];

// دوال مساعدة للواجهة
function showLoading() {
    // إنشاء عنصر التحميل إذا لم يكن موجوداً
    if (!document.getElementById('loadingOverlay')) {
        const overlay = document.createElement('div');
        overlay.id = 'loadingOverlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        `;
        overlay.innerHTML = `
            <div class="text-center text-white">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <div class="mt-2">جاري التحميل...</div>
            </div>
        `;
        document.body.appendChild(overlay);
    } else {
        document.getElementById('loadingOverlay').style.display = 'flex';
    }
}

function hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}

function showAlert(message, type = 'info') {
    // إزالة التنبيهات السابقة
    const existingAlerts = document.querySelectorAll('.alert-floating');
    existingAlerts.forEach(alert => alert.remove());
    
    // إنشاء تنبيه جديد
    const alert = document.createElement('div');
    alert.className = `alert alert-${type === 'error' ? 'danger' : type} alert-floating`;
    alert.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    alert.innerHTML = `
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <strong>${type === 'success' ? 'نجح' : type === 'error' ? 'خطأ' : type === 'warning' ? 'تحذير' : 'معلومات'}</strong>
                <br>
                ${message}
            </div>
            <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;
    
    document.body.appendChild(alert);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alert.parentElement) {
            alert.remove();
        }
    }, 5000);
}

function toggleUserStatus(userId) {
    if (confirm('هل تريد تغيير حالة هذا المستخدم؟')) {
        showLoading();
        fetch('<?= App::url('admin/toggle-user-status') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'user_id=' + userId
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showAlert(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert(data.message, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showAlert('حدث خطأ في الاتصال', 'error');
        });
    }
}

function deleteUser(userId) {
    if (confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        showLoading();
        fetch('<?= App::url('admin/delete-user') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'user_id=' + userId
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showAlert(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert(data.message, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showAlert('حدث خطأ في الاتصال', 'error');
        });
    }
}

// تحديد متعدد للمستخدمين
function toggleUserSelection(userId) {
    const index = selectedUsers.indexOf(userId);
    if (index > -1) {
        selectedUsers.splice(index, 1);
    } else {
        selectedUsers.push(userId);
    }
    updateBulkActionsButton();
}

function selectAllUsers() {
    const checkboxes = document.querySelectorAll('.user-checkbox');
    const selectAll = document.getElementById('selectAll').checked;
    
    selectedUsers = [];
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll;
        if (selectAll) {
            selectedUsers.push(parseInt(checkbox.value));
        }
    });
    updateBulkActionsButton();
}

function updateBulkActionsButton() {
    const bulkButton = document.getElementById('bulkActionsButton');
    if (bulkButton) {
        if (selectedUsers.length > 0) {
            bulkButton.style.display = 'block';
            bulkButton.innerHTML = `<i class="bi bi-gear"></i> إجراءات جماعية (${selectedUsers.length})`;
        } else {
            bulkButton.style.display = 'none';
        }
    }
}

// الإجراءات الجماعية
function bulkActivate() {
    performBulkAction('activate', 'تفعيل');
}

function bulkDeactivate() {
    performBulkAction('deactivate', 'إلغاء تفعيل');
}

function bulkDelete() {
    if (confirm('هل أنت متأكد من حذف المستخدمين المحددين؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        performBulkAction('delete', 'حذف');
    }
}

function performBulkAction(action, actionName) {
    if (selectedUsers.length === 0) {
        showAlert('يرجى تحديد مستخدمين أولاً', 'warning');
        return;
    }

    showLoading();
    fetch('<?= App::url('admin/bulk-user-action') ?>', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: action,
            user_ids: selectedUsers
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showAlert(`تم ${actionName} ${data.count} مستخدم بنجاح`, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert(data.message, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showAlert('حدث خطأ في الاتصال', 'error');
    });

    // إغلاق المودال
    const modal = bootstrap.Modal.getInstance(document.getElementById('bulkActionsModal'));
    modal.hide();
}

// تصدير البيانات
function exportUsers() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'csv');
    window.location.href = '<?= App::url('admin/users') ?>?' + params.toString();
}

// تحديث تلقائي للصفحة كل 30 ثانية
setInterval(function() {
    // تحديث الإحصائيات فقط إذا لم تكن هناك تغييرات
    if (selectedUsers.length === 0) {
        fetch('<?= App::url('admin/api/users/stats') ?>')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث الإحصائيات في البطاقات
                    const statsCards = document.querySelectorAll('.card h4');
                    if (statsCards.length >= 4) {
                        statsCards[0].textContent = data.stats.total || 0;
                        statsCards[1].textContent = data.stats.active || 0;
                        statsCards[2].textContent = data.stats.inactive || 0;
                        statsCards[3].textContent = data.stats.new_this_month || 0;
                    }
                }
            })
            .catch(error => console.log('خطأ في تحديث الإحصائيات:', error));
    }
}, 30000);
</script>
