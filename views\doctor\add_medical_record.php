<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-file-earmark-medical me-2"></i>
            إضافة سجل طبي جديد
        </h1>
        <p class="text-muted">إضافة سجل طبي جديد للمريض</p>
    </div>
    <div>
        <a href="<?= App::url('doctor/patients') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة لقائمة المرضى
        </a>
        <a href="<?= App::url('doctor/medical-records') ?>" class="btn btn-outline-primary">
            <i class="bi bi-list me-2"></i>
            السجلات الطبية
        </a>
    </div>
</div>

<!-- Patient Information Card -->
<div class="card mb-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">
            <i class="bi bi-person me-2"></i>
            معلومات المريض
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="d-flex align-items-center mb-3">
                    <div class="avatar-lg bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                        <i class="bi bi-person text-primary fs-1"></i>
                    </div>
                    <div>
                        <h4 class="mb-1"><?= htmlspecialchars(User::getFullName($patient) ?? 'مريض غير محدد') ?></h4>
                        <p class="text-muted mb-0">
                            معرف المريض: <?= $patient['id'] ?>
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="row">
                    <div class="col-6">
                        <small class="text-muted d-block">البريد الإلكتروني</small>
                        <strong><?= htmlspecialchars($patient['email'] ?? 'غير محدد') ?></strong>
                    </div>
                    <div class="col-6">
                        <small class="text-muted d-block">رقم الهاتف</small>
                        <strong><?= htmlspecialchars($patient['phone'] ?? 'غير محدد') ?></strong>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-6">
                        <small class="text-muted d-block">تاريخ الميلاد</small>
                        <strong><?= $patient['date_of_birth'] ? date('Y-m-d', strtotime($patient['date_of_birth'])) : 'غير محدد' ?></strong>
                    </div>
                    <div class="col-6">
                        <small class="text-muted d-block">الجنس</small>
                        <strong><?= $patient['gender'] === 'male' ? 'ذكر' : ($patient['gender'] === 'female' ? 'أنثى' : 'غير محدد') ?></strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Medical Record Form -->
<div class="card">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0">
            <i class="bi bi-plus-circle me-2"></i>
            بيانات السجل الطبي
        </h5>
    </div>
    <div class="card-body">
        <form method="POST" action="<?= App::url('doctor/add-medical-record') ?>" id="medicalRecordForm">
            <input type="hidden" name="patient_id" value="<?= $patient['id'] ?>">
            
            <!-- Visit Information -->
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-primary mb-3">
                        <i class="bi bi-calendar-event me-2"></i>
                        معلومات الزيارة
                    </h6>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">تاريخ الزيارة <span class="text-danger">*</span></label>
                        <input type="date" 
                               class="form-control <?= SessionHelper::hasError('visit_date') ? 'is-invalid' : '' ?>" 
                               name="visit_date" 
                               value="<?= SessionHelper::getOldInput('visit_date', date('Y-m-d')) ?>"
                               required>
                        <?php if (SessionHelper::hasError('visit_date')): ?>
                            <div class="invalid-feedback"><?= SessionHelper::getError('visit_date') ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">نوع الزيارة</label>
                        <select class="form-select" name="visit_type">
                            <option value="consultation" <?= SessionHelper::getOldInput('visit_type') === 'consultation' ? 'selected' : '' ?>>استشارة</option>
                            <option value="examination" <?= SessionHelper::getOldInput('visit_type') === 'examination' ? 'selected' : '' ?>>فحص طبي</option>
                            <option value="follow_up" <?= SessionHelper::getOldInput('visit_type') === 'follow_up' ? 'selected' : '' ?>>متابعة</option>
                            <option value="emergency" <?= SessionHelper::getOldInput('visit_type') === 'emergency' ? 'selected' : '' ?>>طوارئ</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Chief Complaint -->
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-primary mb-3">
                        <i class="bi bi-chat-text me-2"></i>
                        الشكوى الرئيسية
                    </h6>
                </div>
                <div class="col-12">
                    <div class="mb-3">
                        <label class="form-label">الشكوى الرئيسية <span class="text-danger">*</span></label>
                        <textarea class="form-control <?= SessionHelper::hasError('chief_complaint') ? 'is-invalid' : '' ?>" 
                                  name="chief_complaint" 
                                  rows="3" 
                                  placeholder="وصف الشكوى الرئيسية للمريض..."
                                  required><?= SessionHelper::getOldInput('chief_complaint') ?></textarea>
                        <?php if (SessionHelper::hasError('chief_complaint')): ?>
                            <div class="invalid-feedback"><?= SessionHelper::getError('chief_complaint') ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Vital Signs -->
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-primary mb-3">
                        <i class="bi bi-heart-pulse me-2"></i>
                        العلامات الحيوية
                    </h6>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">درجة الحرارة (°C)</label>
                        <input type="number" 
                               class="form-control <?= SessionHelper::hasError('vital_signs[temperature]') ? 'is-invalid' : '' ?>" 
                               name="vital_signs[temperature]" 
                               step="0.1" 
                               min="30" 
                               max="45"
                               value="<?= SessionHelper::getOldInput('vital_signs.temperature') ?>"
                               placeholder="36.5">
                        <?php if (SessionHelper::hasError('vital_signs[temperature]')): ?>
                            <div class="invalid-feedback"><?= SessionHelper::getError('vital_signs[temperature]') ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">ضغط الدم الانقباضي (mmHg)</label>
                        <input type="number" 
                               class="form-control <?= SessionHelper::hasError('vital_signs[blood_pressure_systolic]') ? 'is-invalid' : '' ?>" 
                               name="vital_signs[blood_pressure_systolic]" 
                               min="60" 
                               max="250"
                               value="<?= SessionHelper::getOldInput('vital_signs.blood_pressure_systolic') ?>"
                               placeholder="120">
                        <?php if (SessionHelper::hasError('vital_signs[blood_pressure_systolic]')): ?>
                            <div class="invalid-feedback"><?= SessionHelper::getError('vital_signs[blood_pressure_systolic]') ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">ضغط الدم الانبساطي (mmHg)</label>
                        <input type="number" 
                               class="form-control <?= SessionHelper::hasError('vital_signs[blood_pressure_diastolic]') ? 'is-invalid' : '' ?>" 
                               name="vital_signs[blood_pressure_diastolic]" 
                               min="40" 
                               max="150"
                               value="<?= SessionHelper::getOldInput('vital_signs.blood_pressure_diastolic') ?>"
                               placeholder="80">
                        <?php if (SessionHelper::hasError('vital_signs[blood_pressure_diastolic]')): ?>
                            <div class="invalid-feedback"><?= SessionHelper::getError('vital_signs[blood_pressure_diastolic]') ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">معدل ضربات القلب (bpm)</label>
                        <input type="number" 
                               class="form-control <?= SessionHelper::hasError('vital_signs[heart_rate]') ? 'is-invalid' : '' ?>" 
                               name="vital_signs[heart_rate]" 
                               min="30" 
                               max="200"
                               value="<?= SessionHelper::getOldInput('vital_signs.heart_rate') ?>"
                               placeholder="72">
                        <?php if (SessionHelper::hasError('vital_signs[heart_rate]')): ?>
                            <div class="invalid-feedback"><?= SessionHelper::getError('vital_signs[heart_rate]') ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">معدل التنفس (/min)</label>
                        <input type="number" 
                               class="form-control" 
                               name="vital_signs[respiratory_rate]" 
                               min="8" 
                               max="40"
                               value="<?= SessionHelper::getOldInput('vital_signs.respiratory_rate') ?>"
                               placeholder="16">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">الوزن (kg)</label>
                        <input type="number" 
                               class="form-control" 
                               name="vital_signs[weight]" 
                               step="0.1" 
                               min="10" 
                               max="300"
                               value="<?= SessionHelper::getOldInput('vital_signs.weight') ?>"
                               placeholder="70">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">الطول (cm)</label>
                        <input type="number" 
                               class="form-control" 
                               name="vital_signs[height]" 
                               step="0.1" 
                               min="50" 
                               max="250"
                               value="<?= SessionHelper::getOldInput('vital_signs.height') ?>"
                               placeholder="170">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="mb-3">
                        <label class="form-label">مؤشر كتلة الجسم</label>
                        <input type="text" 
                               class="form-control" 
                               id="bmi" 
                               readonly 
                               placeholder="سيتم حسابه تلقائياً">
                    </div>
                </div>
            </div>

            <!-- Diagnosis -->
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-primary mb-3">
                        <i class="bi bi-clipboard2-pulse me-2"></i>
                        التشخيص والعلاج
                    </h6>
                </div>
                <div class="col-12">
                    <div class="mb-3">
                        <label class="form-label">التشخيص <span class="text-danger">*</span></label>
                        <textarea class="form-control <?= SessionHelper::hasError('diagnosis') ? 'is-invalid' : '' ?>" 
                                  name="diagnosis" 
                                  rows="3" 
                                  placeholder="التشخيص الطبي..."
                                  required><?= SessionHelper::getOldInput('diagnosis') ?></textarea>
                        <?php if (SessionHelper::hasError('diagnosis')): ?>
                            <div class="invalid-feedback"><?= SessionHelper::getError('diagnosis') ?></div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="col-12">
                    <div class="mb-3">
                        <label class="form-label">خطة العلاج <span class="text-danger">*</span></label>
                        <textarea class="form-control <?= SessionHelper::hasError('treatment_plan') ? 'is-invalid' : '' ?>" 
                                  name="treatment_plan" 
                                  rows="4" 
                                  placeholder="خطة العلاج الموصوفة..."
                                  required><?= SessionHelper::getOldInput('treatment_plan') ?></textarea>
                        <?php if (SessionHelper::hasError('treatment_plan')): ?>
                            <div class="invalid-feedback"><?= SessionHelper::getError('treatment_plan') ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="row mb-4">
                <div class="col-12">
                    <h6 class="text-primary mb-3">
                        <i class="bi bi-info-circle me-2"></i>
                        معلومات إضافية
                    </h6>
                </div>
                <div class="col-12">
                    <div class="mb-3">
                        <label class="form-label">ملاحظات إضافية</label>
                        <textarea class="form-control" 
                                  name="notes" 
                                  rows="3" 
                                  placeholder="أي ملاحظات إضافية أو تعليمات خاصة..."><?= SessionHelper::getOldInput('notes') ?></textarea>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="row">
                <div class="col-12">
                    <hr>
                    <div class="d-flex justify-content-between">
                        <div>
                            <a href="<?= App::url('doctor/patients') ?>" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-right me-2"></i>
                                إلغاء
                            </a>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-info me-2" onclick="saveAsDraft()">
                                <i class="bi bi-save me-2"></i>
                                حفظ كمسودة
                            </button>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle me-2"></i>
                                حفظ السجل الطبي
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="bi bi-prescription2 display-4 mb-3"></i>
                <h5>إنشاء وصفة طبية</h5>
                <p class="mb-3">إنشاء وصفة طبية للمريض</p>
                <a href="<?= App::url('doctor/create-prescription?patient_id=' . $patient['id']) ?>" class="btn btn-light">
                    <i class="bi bi-plus-circle me-2"></i>
                    إنشاء وصفة
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="bi bi-clipboard2-pulse display-4 mb-3"></i>
                <h5>فحص مخبري</h5>
                <p class="mb-3">طلب فحص مخبري للمريض</p>
                <button type="button" class="btn btn-light" onclick="addLabTest()">
                    <i class="bi bi-plus-circle me-2"></i>
                    طلب فحص
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="bi bi-calendar-plus display-4 mb-3"></i>
                <h5>حجز موعد</h5>
                <p class="mb-3">حجز موعد متابعة للمريض</p>
                <a href="<?= App::url('doctor/appointments?patient_id=' . $patient['id']) ?>" class="btn btn-light">
                    <i class="bi bi-calendar-plus me-2"></i>
                    حجز موعد
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// حساب مؤشر كتلة الجسم تلقائياً
function calculateBMI() {
    const weight = document.querySelector('input[name="vital_signs[weight]"]').value;
    const height = document.querySelector('input[name="vital_signs[height]"]').value;
    
    if (weight && height) {
        const heightInMeters = height / 100;
        const bmi = (weight / (heightInMeters * heightInMeters)).toFixed(1);
        document.getElementById('bmi').value = bmi;
    }
}

// إضافة مستمعي الأحداث لحساب BMI
document.querySelector('input[name="vital_signs[weight]"]').addEventListener('input', calculateBMI);
document.querySelector('input[name="vital_signs[height]"]').addEventListener('input', calculateBMI);

// حفظ كمسودة
function saveAsDraft() {
    const form = document.getElementById('medicalRecordForm');
    const draftInput = document.createElement('input');
    draftInput.type = 'hidden';
    draftInput.name = 'is_draft';
    draftInput.value = '1';
    form.appendChild(draftInput);
    form.submit();
}

// إضافة فحص مخبري
function addLabTest() {
    const patientId = <?= $patient['id'] ?>;
    const testName = prompt('أدخل اسم الفحص المخبري:');
    
    if (testName) {
        const formData = new FormData();
        formData.append('patient_id', patientId);
        formData.append('test_name', testName);
        formData.append('test_date', new Date().toISOString().split('T')[0]);
        
        fetch('<?= App::url('doctor/add-lab-test') ?>', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إضافة الفحص المخبري بنجاح');
            } else {
                alert('فشل في إضافة الفحص المخبري: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء إضافة الفحص المخبري');
        });
    }
}

// التحقق من صحة النموذج
document.getElementById('medicalRecordForm').addEventListener('submit', function(e) {
    const requiredFields = ['visit_date', 'chief_complaint', 'diagnosis', 'treatment_plan'];
    let isValid = true;
    
    requiredFields.forEach(field => {
        const element = document.querySelector(`[name="${field}"]`);
        if (!element.value.trim()) {
            element.classList.add('is-invalid');
            isValid = false;
        } else {
            element.classList.remove('is-invalid');
        }
    });
    
    if (!isValid) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
    }
});

// تحميل البيانات المحفوظة مسبقاً
document.addEventListener('DOMContentLoaded', function() {
    // حساب BMI إذا كانت البيانات موجودة
    calculateBMI();
    
    // إضافة تنسيق تلقائي للتاريخ
    const visitDateInput = document.querySelector('input[name="visit_date"]');
    if (!visitDateInput.value) {
        visitDateInput.value = new Date().toISOString().split('T')[0];
    }
});
</script> 