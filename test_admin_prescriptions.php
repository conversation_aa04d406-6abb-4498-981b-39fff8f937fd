<?php
/**
 * ملف اختبار صفحة إدارة الوصفات الطبية للمدير
 * يمكن الوصول إليه عبر: http://localhost/HealthKey/test_admin_prescriptions.php
 */

// تضمين ملفات النظام
require_once 'app/core/App.php';
require_once 'app/helpers/SessionHelper.php';
require_once 'app/models/User.php';
require_once 'app/models/Prescription.php';

// محاكاة تسجيل دخول المدير
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'admin';
$_SESSION['user_name'] = 'مدير النظام';

// بيانات اختبار الوصفات الطبية
$testPrescriptions = [
    [
        'id' => 1,
        'prescription_code' => 'PRESC-2024-001',
        'patient_name' => 'أحمد محمد',
        'patient_id' => 2,
        'doctor_name' => 'د. فاطمة علي',
        'doctor_specialization' => 'طب عام',
        'diagnosis' => 'نزلة برد خفيفة',
        'notes' => 'يجب تناول الدواء مع الطعام',
        'issue_date' => '2024-01-15',
        'expiry_date' => '2024-02-15',
        'status' => 'active',
        'created_at' => '2024-01-15 10:30:00'
    ],
    [
        'id' => 2,
        'prescription_code' => 'PRESC-2024-002',
        'patient_name' => 'سارة أحمد',
        'patient_id' => 3,
        'doctor_name' => 'د. محمد حسن',
        'doctor_specialization' => 'طب القلب',
        'diagnosis' => 'ارتفاع ضغط الدم',
        'notes' => 'مراقبة الضغط يومياً',
        'issue_date' => '2024-01-10',
        'expiry_date' => '2024-02-10',
        'status' => 'dispensed',
        'created_at' => '2024-01-10 14:20:00'
    ],
    [
        'id' => 3,
        'prescription_code' => 'PRESC-2024-003',
        'patient_name' => 'علي محمود',
        'patient_id' => 4,
        'doctor_name' => 'د. خديجة عبدالله',
        'doctor_specialization' => 'طب الأطفال',
        'diagnosis' => 'حمى وإسهال',
        'notes' => 'شرب الكثير من السوائل',
        'issue_date' => '2024-01-05',
        'expiry_date' => '2024-01-20',
        'status' => 'expired',
        'created_at' => '2024-01-05 09:15:00'
    ]
];

$testDoctors = [
    ['id' => 1, 'first_name' => 'فاطمة', 'last_name' => 'علي', 'specialization' => 'طب عام'],
    ['id' => 2, 'first_name' => 'محمد', 'last_name' => 'حسن', 'specialization' => 'طب القلب'],
    ['id' => 3, 'first_name' => 'خديجة', 'last_name' => 'عبدالله', 'specialization' => 'طب الأطفال']
];

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحة إدارة الوصفات الطبية - HealthKey</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .test-header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .test-card { border: none; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
        .prescription-status { font-size: 0.875rem; }
    </style>
</head>
<body>
    <div class="container py-4">
        <!-- Header -->
        <div class="test-header text-white p-4 rounded mb-4">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="h3 mb-2">
                        <i class="bi bi-prescription2 me-2"></i>
                        اختبار صفحة إدارة الوصفات الطبية
                    </h1>
                    <p class="mb-0 opacity-75">اختبار نظام إدارة الوصفات الطبية للمدير</p>
                </div>
                <div class="col-md-4 text-md-end">
                    <div class="d-flex align-items-center justify-content-md-end">
                        <div class="me-3">
                            <small class="d-block opacity-75">المدير المسجل</small>
                            <strong><?= $_SESSION['user_name'] ?></strong>
                        </div>
                        <div class="avatar-sm bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center">
                            <i class="bi bi-person text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-gradient-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-0"><?= count($testPrescriptions) ?></h4>
                                <small>إجمالي الوصفات</small>
                            </div>
                            <i class="bi bi-prescription2 fs-1 opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-gradient-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-0"><?= count(array_filter($testPrescriptions, fn($p) => $p['status'] === 'active')) ?></h4>
                                <small>الوصفات النشطة</small>
                            </div>
                            <i class="bi bi-check-circle-fill fs-1 opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-gradient-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-0"><?= count(array_filter($testPrescriptions, fn($p) => $p['status'] === 'dispensed')) ?></h4>
                                <small>الوصفات الموزعة</small>
                            </div>
                            <i class="bi bi-box-seam-fill fs-1 opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card bg-gradient-danger text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-0"><?= count(array_filter($testPrescriptions, fn($p) => $p['status'] === 'expired')) ?></h4>
                                <small>الوصفات المنتهية</small>
                            </div>
                            <i class="bi bi-exclamation-triangle-fill fs-1 opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-funnel me-2"></i>
                    فلاتر البحث
                </h5>
            </div>
            <div class="card-body">
                <form class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">البحث</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               placeholder="كود الوصفة، اسم المريض، أو الطبيب">
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">الحالة</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشطة</option>
                            <option value="dispensed">موزعة</option>
                            <option value="expired">منتهية</option>
                            <option value="cancelled">ملغية</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="doctor_id" class="form-label">الطبيب</label>
                        <select class="form-select" id="doctor_id" name="doctor_id">
                            <option value="">جميع الأطباء</option>
                            <?php foreach ($testDoctors as $doctor): ?>
                                <option value="<?= $doctor['id'] ?>">
                                    <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="date_from" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="date_from" name="date_from">
                    </div>
                    <div class="col-md-2">
                        <label for="date_to" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="date_to" name="date_to">
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="bi bi-search me-1"></i>
                            بحث
                        </button>
                        <button type="reset" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise me-1"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Prescriptions Table -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-list-ul me-2"></i>
                    قائمة الوصفات الطبية
                </h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-warning" style="display: none;">
                        <i class="bi bi-gear"></i>
                        إجراءات جماعية
                    </button>
                    <button class="btn btn-outline-info">
                        <i class="bi bi-download"></i>
                        تصدير البيانات
                    </button>
                    <button class="btn btn-outline-success">
                        <i class="bi bi-plus-circle"></i>
                        إضافة وصفة جديدة
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="50">
                                    <input type="checkbox" class="form-check-input">
                                </th>
                                <th>كود الوصفة</th>
                                <th>المريض</th>
                                <th>الطبيب</th>
                                <th>التشخيص</th>
                                <th>تاريخ الإصدار</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الحالة</th>
                                <th width="150">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($testPrescriptions as $prescription): ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input">
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?= htmlspecialchars($prescription['prescription_code']) ?></span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <i class="bi bi-person text-primary"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?= htmlspecialchars($prescription['patient_name']) ?></div>
                                                <small class="text-muted">ID: <?= $prescription['patient_id'] ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-success bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-2">
                                                <i class="bi bi-person-badge text-success"></i>
                                            </div>
                                            <div>
                                                <div class="fw-bold"><?= htmlspecialchars($prescription['doctor_name']) ?></div>
                                                <small class="text-muted"><?= htmlspecialchars($prescription['doctor_specialization']) ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if (!empty($prescription['diagnosis'])): ?>
                                            <span class="text-truncate d-inline-block" style="max-width: 150px;" 
                                                  title="<?= htmlspecialchars($prescription['diagnosis']) ?>">
                                                <?= htmlspecialchars($prescription['diagnosis']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?= date('Y-m-d', strtotime($prescription['issue_date'])) ?></span>
                                    </td>
                                    <td>
                                        <?php 
                                        $expiryDate = strtotime($prescription['expiry_date']);
                                        $isExpired = $expiryDate < time();
                                        $isNearExpiry = $expiryDate < strtotime('+7 days');
                                        ?>
                                        <span class="badge <?= $isExpired ? 'bg-danger' : ($isNearExpiry ? 'bg-warning' : 'bg-success') ?>">
                                            <?= date('Y-m-d', $expiryDate) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $statusClass = match($prescription['status']) {
                                            'active' => 'bg-success',
                                            'dispensed' => 'bg-info',
                                            'expired' => 'bg-danger',
                                            'cancelled' => 'bg-secondary',
                                            default => 'bg-secondary'
                                        };
                                        $statusText = match($prescription['status']) {
                                            'active' => 'نشطة',
                                            'dispensed' => 'موزعة',
                                            'expired' => 'منتهية',
                                            'cancelled' => 'ملغية',
                                            default => 'غير محدد'
                                        };
                                        ?>
                                        <span class="badge <?= $statusClass ?>"><?= $statusText ?></span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button" class="btn btn-outline-primary" title="عرض التفاصيل">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-success" title="تعديل">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" title="حذف">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-check-circle me-2"></i>
                            نتائج الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>بيانات الاختبار:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>عدد الوصفات:</strong> <?= count($testPrescriptions) ?></li>
                                    <li><strong>الوصفات النشطة:</strong> <?= count(array_filter($testPrescriptions, fn($p) => $p['status'] === 'active')) ?></li>
                                    <li><strong>الوصفات الموزعة:</strong> <?= count(array_filter($testPrescriptions, fn($p) => $p['status'] === 'dispensed')) ?></li>
                                    <li><strong>الوصفات المنتهية:</strong> <?= count(array_filter($testPrescriptions, fn($p) => $p['status'] === 'expired')) ?></li>
                                    <li><strong>عدد الأطباء:</strong> <?= count($testDoctors) ?></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>روابط الاختبار:</h6>
                                <div class="d-grid gap-2">
                                    <a href="admin/prescriptions" class="btn btn-primary">
                                        <i class="bi bi-prescription2 me-2"></i>
                                        صفحة إدارة الوصفات الطبية
                                    </a>
                                    <a href="admin/dashboard" class="btn btn-outline-success">
                                        <i class="bi bi-speedometer2 me-2"></i>
                                        لوحة التحكم
                                    </a>
                                    <a href="admin/users" class="btn btn-outline-info">
                                        <i class="bi bi-people me-2"></i>
                                        إدارة المستخدمين
                                    </a>
                                </div>
                                
                                <hr>
                                
                                <h6>الميزات المختبرة:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check-circle text-success me-2"></i>عرض قائمة الوصفات الطبية</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>فلاتر البحث والتصفية</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>إحصائيات الوصفات</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>عرض تفاصيل الوصفة</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>إضافة وصفة جديدة</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>تعديل الوصفة</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>حذف الوصفة</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>الإجراءات الجماعية</li>
                                    <li><i class="bi bi-check-circle text-success me-2"></i>تصدير البيانات</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card test-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            تعليمات الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>انقر على "صفحة إدارة الوصفات الطبية" لفتح الصفحة الحقيقية</li>
                            <li>اختبر فلاتر البحث (الحالة، الطبيب، التاريخ)</li>
                            <li>اختبر إضافة وصفة طبية جديدة</li>
                            <li>اختبر عرض تفاصيل الوصفة</li>
                            <li>اختبر تعديل وحذف الوصفات</li>
                            <li>اختبر الإجراءات الجماعية</li>
                            <li>اختبر تصدير البيانات</li>
                        </ol>
                        
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>ملاحظة:</strong> هذه الصفحة تعرض بيانات تجريبية للاختبار. في التطبيق الحقيقي، ستتم قراءة البيانات من قاعدة البيانات.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="text-center text-muted">
                    <small>
                        <i class="bi bi-code-slash me-1"></i>
                        نظام HealthKey - اختبار صفحة إدارة الوصفات الطبية
                        <span class="mx-2">|</span>
                        <i class="bi bi-calendar me-1"></i>
                        <?= date('Y-m-d H:i') ?>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 