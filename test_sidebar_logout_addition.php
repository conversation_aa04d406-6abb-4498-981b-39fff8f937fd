<?php
/**
 * اختبار إضافة خانة تسجيل الخروج للشريط الجانبي
 */

require_once 'config.php';

echo "<h1>اختبار إضافة خانة تسجيل الخروج للشريط الجانبي</h1>";

try {
    echo "<h2>1. اختبار الشريط الجانبي للطبيب</h2>";
    echo "<p>تم إضافة خانة تسجيل الخروج للشريط الجانبي للطبيب</p>";
    echo "<p><a href='public/index.php?url=doctor/dashboard' target='_blank'>فتح لوحة تحكم الطبيب</a></p>";
    
    echo "<h2>2. اختبار الشريط الجانبي للمريض</h2>";
    echo "<p>تم إضافة خانة تسجيل الخروج للشريط الجانبي للمريض</p>";
    echo "<p><a href='public/index.php?url=patient/dashboard' target='_blank'>فتح لوحة تحكم المريض</a></p>";
    
    echo "<h2>3. اختبار الشريط الجانبي للصيدلي</h2>";
    echo "<p>تم إضافة خانة تسجيل الخروج للشريط الجانبي للصيدلي</p>";
    echo "<p><a href='public/index.php?url=pharmacist/dashboard' target='_blank'>فتح لوحة تحكم الصيدلي</a></p>";
    
    echo "<h2>4. التغييرات المطبقة</h2>";
    echo "<ul>";
    echo "<li>✅ إضافة قسم 'الحساب' في الشريط الجانبي للطبيب</li>";
    echo "<li>✅ إضافة رابط 'تسجيل الخروج' في الشريط الجانبي للطبيب</li>";
    echo "<li>✅ إضافة قسم 'الحساب' في الشريط الجانبي للمريض</li>";
    echo "<li>✅ إضافة رابط 'تسجيل الخروج' في الشريط الجانبي للمريض</li>";
    echo "<li>✅ إضافة قسم 'الحساب' في الشريط الجانبي للصيدلي</li>";
    echo "<li>✅ إضافة رابط 'تسجيل الخروج' في الشريط الجانبي للصيدلي</li>";
    echo "<li>✅ إضافة تنسيق CSS خاص برابط تسجيل الخروج</li>";
    echo "<li>✅ إضافة تأكيد قبل تسجيل الخروج</li>";
    echo "</ul>";
    
    echo "<h2>5. ميزات خانة تسجيل الخروج</h2>";
    echo "<ul>";
    echo "<li>🔴 لون أحمر مميز لرابط تسجيل الخروج</li>";
    echo "<li>📱 أيقونة box-arrow-right</li>";
    echo "<li>⚠️ تأكيد قبل تسجيل الخروج</li>";
    echo "<li>🎨 تأثيرات hover جذابة</li>";
    echo "<li>📍 موقع في نهاية الشريط الجانبي</li>";
    echo "</ul>";
    
    echo "<h2>6. الروابط المحدثة في الشريط الجانبي</h2>";
    echo "<h3>للطبيب:</h3>";
    echo "<ul>";
    echo "<li>لوحة التحكم</li>";
    echo "<li>المواعيد</li>";
    echo "<li>المرضى</li>";
    echo "<li>الوصفات الطبية</li>";
    echo "<li>السجلات الطبية</li>";
    echo "<li>التقارير</li>";
    echo "<li>التحليلات</li>";
    echo "<li>الرسائل</li>";
    echo "<li>الإشعارات</li>";
    echo "<li>الملف الشخصي</li>";
    echo "<li><strong>تسجيل الخروج</strong> 🔴</li>";
    echo "</ul>";
    
    echo "<h3>للمريض:</h3>";
    echo "<ul>";
    echo "<li>لوحة التحكم</li>";
    echo "<li>المواعيد</li>";
    echo "<li>الوصفات الطبية</li>";
    echo "<li>السجلات الطبية</li>";
    echo "<li>الرسائل</li>";
    echo "<li>الإشعارات</li>";
    echo "<li>الملف الشخصي</li>";
    echo "<li>جهات الاتصال في الطوارئ</li>";
    echo "<li><strong>تسجيل الخروج</strong> 🔴</li>";
    echo "</ul>";
    
    echo "<h3>للصيدلي:</h3>";
    echo "<ul>";
    echo "<li>لوحة التحكم</li>";
    echo "<li>الوصفات الطبية</li>";
    echo "<li>تقارير الصرف</li>";
    echo "<li>الإشعارات</li>";
    echo "<li>الملف الشخصي</li>";
    echo "<li>ساعات العمل</li>";
    echo "<li><strong>تسجيل الخروج</strong> 🔴</li>";
    echo "</ul>";
    
    echo "<h2>7. اختبار وظيفة تسجيل الخروج</h2>";
    echo "<p>يمكن اختبار وظيفة تسجيل الخروج من خلال:</p>";
    echo "<ul>";
    echo "<li>النقر على رابط 'تسجيل الخروج'</li>";
    echo "<li>تأكيد العملية في النافذة المنبثقة</li>";
    echo "<li>التحقق من إعادة التوجيه إلى صفحة تسجيل الدخول</li>";
    echo "</ul>";
    
    echo "<h2>✅ انتهى الاختبار</h2>";
    echo "<p>تم إضافة خانة تسجيل الخروج بنجاح لجميع الشريط الجانبي</p>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 