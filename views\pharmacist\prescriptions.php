<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">البحث في الوصفات</h1>
        <p class="text-muted">ابحث عن الوصفات الطبية وصرف الأدوية</p>
    </div>
</div>

<!-- Search Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-search me-2"></i>
            البحث في الوصفات
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="<?= App::url('pharmacist/prescriptions') ?>" class="row g-3">
            <div class="col-md-6">
                <label for="search" class="form-label">كود الوصفة أو اسم المريض</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="<?= htmlspecialchars($search) ?>" placeholder="أدخل كود الوصفة أو اسم المريض">
            </div>
            <div class="col-md-4">
                <label for="status" class="form-label">حالة الوصفة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="active" <?= $currentStatus === 'active' ? 'selected' : '' ?>>نشطة</option>
                    <option value="expired" <?= $currentStatus === 'expired' ? 'selected' : '' ?>>منتهية الصلاحية</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-search me-1"></i>
                    بحث
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Quick Search -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-lightning me-2"></i>
            بحث سريع
        </h5>
    </div>
    <div class="card-body">
        <form id="quickSearchForm" class="row g-3">
            <div class="col-md-8">
                <input type="text" class="form-control" id="prescriptionCode" 
                       placeholder="أدخل كود الوصفة للبحث السريع" required>
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-success w-100">
                    <i class="bi bi-search me-1"></i>
                    بحث سريع
                </button>
            </div>
        </form>
        
        <div id="quickSearchResult" class="mt-3" style="display: none;">
            <!-- نتيجة البحث السريع ستظهر هنا -->
        </div>
    </div>
</div>

<!-- Results -->
<?php if (!empty($prescriptions) || !empty($search) || !empty($currentStatus)): ?>
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="bi bi-list-ul me-2"></i>
                نتائج البحث
                <?php if (!empty($prescriptions)): ?>
                    <span class="badge bg-primary ms-2"><?= count($prescriptions) ?></span>
                <?php endif; ?>
            </h5>
        </div>
        <div class="card-body">
            <?php if (!empty($prescriptions)): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>كود الوصفة</th>
                                <th>المريض</th>
                                <th>الطبيب</th>
                                <th>تاريخ الإصدار</th>
                                <th>تاريخ انتهاء الصلاحية</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($prescriptions as $prescription): ?>
                                <tr>
                                    <td>
                                        <strong><?= htmlspecialchars($prescription['prescription_code']) ?></strong>
                                    </td>
                                    <td><?= htmlspecialchars($prescription['patient_name']) ?></td>
                                    <td><?= htmlspecialchars($prescription['doctor_name']) ?></td>
                                    <td><?= DateHelper::formatArabic($prescription['issue_date']) ?></td>
                                    <td>
                                        <?php 
                                        $expiryDate = strtotime($prescription['expiry_date']);
                                        $isExpired = $expiryDate < time();
                                        ?>
                                        <span class="<?= $isExpired ? 'text-danger' : 'text-success' ?>">
                                            <?= DateHelper::formatArabic($prescription['expiry_date']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($isExpired): ?>
                                            <span class="badge bg-danger">منتهية الصلاحية</span>
                                        <?php else: ?>
                                            <span class="badge bg-success">نشطة</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?= App::url('pharmacist/prescription/' . $prescription['id']) ?>" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye me-1"></i>
                                            عرض
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="bi bi-search display-4 text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد نتائج</h5>
                    <p class="text-muted">جرب البحث بكلمات مختلفة أو تحقق من معايير البحث</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>

<!-- Modal for Prescription Details -->
<div class="modal fade" id="prescriptionModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الوصفة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="prescriptionModalBody">
                <!-- محتوى الوصفة سيظهر هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="dispenseBtn" style="display: none;">
                    <i class="bi bi-capsule me-1"></i>
                    صرف الدواء
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const quickSearchForm = document.getElementById('quickSearchForm');
    const quickSearchResult = document.getElementById('quickSearchResult');
    const prescriptionModal = new bootstrap.Modal(document.getElementById('prescriptionModal'));
    const prescriptionModalBody = document.getElementById('prescriptionModalBody');
    const dispenseBtn = document.getElementById('dispenseBtn');

    // البحث السريع
    quickSearchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const prescriptionCode = document.getElementById('prescriptionCode').value;
        
        if (!prescriptionCode) {
            alert('يرجى إدخال كود الوصفة');
            return;
        }

        // إظهار مؤشر التحميل
        quickSearchResult.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p class="mt-2">جاري البحث...</p></div>';
        quickSearchResult.style.display = 'block';

        // إرسال طلب البحث
        fetch('<?= App::url('pharmacist/search-prescription') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'prescription_code=' + encodeURIComponent(prescriptionCode)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showPrescriptionDetails(data.prescription, data.medications);
                quickSearchResult.style.display = 'none';
            } else {
                quickSearchResult.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        ${data.message}
                    </div>
                `;
            }
        })
        .catch(error => {
            quickSearchResult.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    حدث خطأ في البحث
                </div>
            `;
        });
    });

    // عرض تفاصيل الوصفة
    function showPrescriptionDetails(prescription, medications) {
        let medicationsHtml = '';
        let hasUndispensed = false;
        
        medications.forEach(medication => {
            const dispensedStatus = medication.is_dispensed ? 
                '<span class="badge bg-success">تم الصرف</span>' : 
                '<span class="badge bg-warning">لم يتم الصرف</span>';
            
            const dispenseButton = !medication.is_dispensed ? 
                `<button class="btn btn-sm btn-primary dispense-medication" data-medication-id="${medication.id}">
                    <i class="bi bi-capsule me-1"></i>صرف الدواء
                </button>` : '';

            if (!medication.is_dispensed) {
                hasUndispensed = true;
            }

            medicationsHtml += `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">${medication.medication_name}</h6>
                                <p class="mb-1 text-muted">الجرعة: ${medication.dosage}</p>
                                <p class="mb-1 text-muted">التكرار: ${medication.frequency}</p>
                                <p class="mb-1 text-muted">المدة: ${medication.duration}</p>
                                ${medication.instructions ? `<p class="mb-1 text-muted">التعليمات: ${medication.instructions}</p>` : ''}
                                <p class="mb-0 text-muted">الكمية: ${medication.quantity}</p>
                            </div>
                            <div class="text-end">
                                ${dispensedStatus}
                                ${dispenseButton}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        // إظهار/إخفاء زر صرف جميع الأدوية
        if (hasUndispensed) {
            dispenseBtn.style.display = 'inline-block';
            dispenseBtn.onclick = function() {
                if (confirm('هل تريد صرف جميع الأدوية غير المصروفة؟')) {
                    dispenseAllMedications(medications.filter(m => !m.is_dispensed));
                }
            };
        } else {
            dispenseBtn.style.display = 'none';
        }

        prescriptionModalBody.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">معلومات الوصفة</h6>
                    <p><strong>كود الوصفة:</strong> ${prescription.prescription_code}</p>
                    <p><strong>المريض:</strong> ${prescription.patient_name}</p>
                    <p><strong>الطبيب:</strong> ${prescription.doctor_name}</p>
                    <p><strong>تاريخ الإصدار:</strong> ${prescription.issue_date}</p>
                    <p><strong>تاريخ انتهاء الصلاحية:</strong> ${prescription.expiry_date}</p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">التشخيص</h6>
                    <p>${prescription.diagnosis || 'غير محدد'}</p>
                    ${prescription.notes ? `<h6 class="text-primary">ملاحظات</h6><p>${prescription.notes}</p>` : ''}
                </div>
            </div>
            <hr>
            <h6 class="text-primary">الأدوية</h6>
            ${medicationsHtml}
        `;

        prescriptionModal.show();
    }

    // صرف دواء واحد
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('dispense-medication')) {
            const medicationId = e.target.dataset.medicationId;
            const quantity = prompt('أدخل الكمية المصروفة:');
            
            if (quantity === null) return;
            
            dispenseMedication(medicationId, quantity);
        }
    });

    // صرف جميع الأدوية
    function dispenseAllMedications(medications) {
        let processed = 0;
        const total = medications.length;
        
        medications.forEach(medication => {
            dispenseMedication(medication.id, medication.quantity, () => {
                processed++;
                if (processed === total) {
                    alert('تم صرف جميع الأدوية بنجاح');
                    location.reload();
                }
            });
        });
    }

    // دالة صرف الدواء
    function dispenseMedication(medicationId, quantity, callback = null) {
        fetch('<?= App::url('pharmacist/dispense-medication') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `medication_id=${medicationId}&dispensed_quantity=${quantity}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (callback) {
                    callback();
                } else {
                    alert('تم صرف الدواء بنجاح');
                    location.reload();
                }
            } else {
                alert('فشل في صرف الدواء: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في صرف الدواء');
        });
    }
});
</script> 