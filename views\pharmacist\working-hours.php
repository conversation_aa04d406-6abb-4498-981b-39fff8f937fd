<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">ساعات العمل</h1>
        <p class="text-muted">إدارة ساعات العمل الخاصة بك</p>
    </div>
    <div>
        <a href="<?= App::url('pharmacist/dashboard') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right"></i>
            العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- Working Hours Form -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-clock me-2"></i>
                    تحديد ساعات العمل
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= App::url('pharmacist/working-hours') ?>">
                    <?php
                    $days = [
                        'sunday' => 'الأحد',
                        'monday' => 'الاثنين',
                        'tuesday' => 'الثلاثاء',
                        'wednesday' => 'الأربعاء',
                        'thursday' => 'الخميس',
                        'friday' => 'الجمعة',
                        'saturday' => 'السبت'
                    ];
                    
                    $workingHoursMap = [];
                    foreach ($workingHours as $hour) {
                        $workingHoursMap[$hour['day']] = $hour;
                    }
                    ?>
                    
                    <?php foreach ($days as $dayKey => $dayName): ?>
                        <?php 
                        $dayData = $workingHoursMap[$dayKey] ?? [
                            'start_time' => '08:00',
                            'end_time' => '17:00',
                            'is_working' => 1
                        ];
                        ?>
                        <div class="row mb-3 align-items-center">
                            <div class="col-md-2">
                                <label class="form-label fw-bold"><?= $dayName ?></label>
                            </div>
                            <div class="col-md-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" 
                                           id="<?= $dayKey ?>_working" 
                                           name="<?= $dayKey ?>_working" 
                                           value="1"
                                           <?= ($dayData['is_working'] ?? 1) ? 'checked' : '' ?>>
                                    <label class="form-check-label" for="<?= $dayKey ?>_working">
                                        يوم عمل
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">وقت البداية</label>
                                <input type="time" class="form-control" 
                                       name="<?= $dayKey ?>_start" 
                                       value="<?= htmlspecialchars($dayData['start_time']) ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">وقت النهاية</label>
                                <input type="time" class="form-control" 
                                       name="<?= $dayKey ?>_end" 
                                       value="<?= htmlspecialchars($dayData['end_time']) ?>">
                            </div>
                            <div class="col-md-2">
                                <span class="badge bg-<?= ($dayData['is_working'] ?? 1) ? 'success' : 'secondary' ?>">
                                    <?= ($dayData['is_working'] ?? 1) ? 'مفتوح' : 'مغلق' ?>
                                </span>
                            </div>
                        </div>
                        <hr class="my-3">
                    <?php endforeach; ?>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-save me-2"></i>
                            حفظ ساعات العمل
                        </button>
                        <button type="reset" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise me-2"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Current Schedule Summary -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-calendar3 me-2"></i>
                    ملخص الجدول الحالي
                </h6>
            </div>
            <div class="card-body">
                <?php 
                $workingDays = 0;
                $totalHours = 0;
                foreach ($workingHours as $hour) {
                    if ($hour['is_working']) {
                        $workingDays++;
                        $start = new DateTime($hour['start_time']);
                        $end = new DateTime($hour['end_time']);
                        $diff = $start->diff($end);
                        $totalHours += $diff->h + ($diff->i / 60);
                    }
                }
                ?>
                
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary mb-1"><?= $workingDays ?></h4>
                            <small class="text-muted">أيام العمل</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success mb-1"><?= round($totalHours, 1) ?></h4>
                        <small class="text-muted">ساعات العمل</small>
                    </div>
                </div>
                
                <hr class="my-3">
                
                <h6 class="mb-3">أيام العمل:</h6>
                <?php foreach ($workingHours as $hour): ?>
                    <?php if ($hour['is_working']): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="fw-medium">
                                <?= $days[$hour['day']] ?>
                            </span>
                            <span class="text-muted small">
                                <?= $hour['start_time'] ?> - <?= $hour['end_time'] ?>
                            </span>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- Tips Card -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-lightbulb me-2"></i>
                    نصائح
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        تأكد من تحديد أوقات مناسبة للعمل
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        يمكنك تحديد أيام راحة في الأسبوع
                    </li>
                    <li class="mb-2">
                        <i class="bi bi-check-circle text-success me-2"></i>
                        سيتم عرض هذه الساعات للمرضى
                    </li>
                    <li>
                        <i class="bi bi-check-circle text-success me-2"></i>
                        يمكنك تحديث الساعات في أي وقت
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Form Validation -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    const timeInputs = document.querySelectorAll('input[type="time"]');
    
    // تحديث حالة الأيام عند تغيير checkbox
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const dayKey = this.id.replace('_working', '');
            const startInput = document.querySelector(`input[name="${dayKey}_start"]`);
            const endInput = document.querySelector(`input[name="${dayKey}_end"]`);
            const badge = this.closest('.row').querySelector('.badge');
            
            if (this.checked) {
                startInput.disabled = false;
                endInput.disabled = false;
                badge.className = 'badge bg-success';
                badge.textContent = 'مفتوح';
            } else {
                startInput.disabled = true;
                endInput.disabled = true;
                badge.className = 'badge bg-secondary';
                badge.textContent = 'مغلق';
            }
        });
    });
    
    // التحقق من صحة الأوقات
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                const dayKey = checkbox.id.replace('_working', '');
                const startInput = document.querySelector(`input[name="${dayKey}_start"]`);
                const endInput = document.querySelector(`input[name="${dayKey}_end"]`);
                
                if (startInput.value >= endInput.value) {
                    alert('وقت النهاية يجب أن يكون بعد وقت البداية');
                    isValid = false;
                    return;
                }
            }
        });
        
        if (!isValid) {
            e.preventDefault();
        }
    });
});
</script> 