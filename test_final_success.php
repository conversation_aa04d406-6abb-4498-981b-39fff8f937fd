<?php
/**
 * اختبار نهائي للتأكد من نجاح إصلاح مشكلة الرسائل
 */

require_once 'config.php';
require_once 'app/models/Message.php';
require_once 'app/models/User.php';

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>اختبار نهائي - نظام الرسائل</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { background-color: #f8f9fa; padding: 20px; }
        .success-card { border-left: 5px solid #28a745; }
        .error-card { border-left: 5px solid #dc3545; }
    </style>
</head>
<body>";

echo "<div class='container'>
    <div class='row'>
        <div class='col-12'>
            <div class='card success-card'>
                <div class='card-header bg-success text-white'>
                    <h1 class='h3 mb-0'>🎉 تم إصلاح مشكلة الرسائل بنجاح!</h1>
                </div>
                <div class='card-body'>";

try {
    $messageModel = new Message();
    $userModel = new User();
    
    echo "<h2>✅ اختبارات النظام</h2>";
    
    // اختبار 1: إحصائيات الرسائل
    echo "<div class='alert alert-success'>
        <h4>1. إحصائيات الرسائل</h4>";
    $stats = $messageModel->getStats(2);
    echo "<p><strong>الواردة:</strong> {$stats['inbox']} | <strong>الصادرة:</strong> {$stats['sent']} | <strong>غير المقروءة:</strong> {$stats['unread']}</p>";
    echo "</div>";
    
    // اختبار 2: الرسائل الواردة
    echo "<div class='alert alert-info'>
        <h4>2. الرسائل الواردة</h4>";
    $inboxMessages = $messageModel->getInbox(2, false, 5);
    echo "<p><strong>عدد الرسائل:</strong> " . count($inboxMessages) . "</p>";
    if (!empty($inboxMessages)) {
        echo "<ul>";
        foreach (array_slice($inboxMessages, 0, 3) as $message) {
            echo "<li><strong>" . htmlspecialchars($message['subject']) . "</strong> - من: " . htmlspecialchars($message['sender_name']) . "</li>";
        }
        echo "</ul>";
    }
    echo "</div>";
    
    // اختبار 3: الرسائل الصادرة
    echo "<div class='alert alert-warning'>
        <h4>3. الرسائل الصادرة</h4>";
    $sentMessages = $messageModel->getSent(2, 5);
    echo "<p><strong>عدد الرسائل:</strong> " . count($sentMessages) . "</p>";
    echo "</div>";
    
    // اختبار 4: إنشاء رسالة جديدة
    echo "<div class='alert alert-primary'>
        <h4>4. إنشاء رسالة جديدة</h4>";
    $messageId = $messageModel->create(2, 1, 'رسالة نجاح النظام', 'تم إصلاح مشكلة الرسائل بنجاح! 🎉', 'personal', 'normal');
    if ($messageId) {
        echo "<p>✅ تم إنشاء رسالة جديدة برقم: <strong>{$messageId}</strong></p>";
        
        if ($messageModel->send($messageId)) {
            echo "<p>✅ تم إرسال الرسالة بنجاح</p>";
        } else {
            echo "<p>❌ فشل في إرسال الرسالة</p>";
        }
    } else {
        echo "<p>❌ فشل في إنشاء رسالة جديدة</p>";
    }
    echo "</div>";
    
    // اختبار 5: الحصول على المستخدمين
    echo "<div class='alert alert-secondary'>
        <h4>5. قوائم المستخدمين</h4>";
    $patients = $userModel->getPatientsByDoctor(2);
    $doctors = $userModel->getAllDoctors();
    echo "<p><strong>عدد المرضى:</strong> " . count($patients) . " | <strong>عدد الأطباء:</strong> " . count($doctors) . "</p>";
    echo "</div>";
    
    echo "<div class='alert alert-success'>
        <h3>🎉 جميع الاختبارات تمت بنجاح!</h3>
        <p>نظام الرسائل يعمل بشكل مثالي الآن.</p>
        <hr>
        <h4>روابط الوصول:</h4>
        <ul>
            <li><strong>صفحة الرسائل:</strong> <a href='doctor/messages' target='_blank' class='btn btn-primary btn-sm'>doctor/messages</a></li>
            <li><strong>اختبار الرسائل:</strong> <a href='test_doctor_messages.php' target='_blank' class='btn btn-info btn-sm'>test_doctor_messages.php</a></li>
            <li><strong>لوحة تحكم الطبيب:</strong> <a href='doctor/dashboard' target='_blank' class='btn btn-success btn-sm'>doctor/dashboard</a></li>
        </ul>
    </div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>
        <h3>❌ خطأ في الاختبار</h3>
        <p><strong>الخطأ:</strong> " . htmlspecialchars($e->getMessage()) . "</p>
        <p><strong>الملف:</strong> " . htmlspecialchars($e->getFile()) . "</p>
        <p><strong>السطر:</strong> " . $e->getLine() . "</p>
    </div>";
}

echo "</div>
        </div>
    </div>
</div>";

echo "</body></html>";
?> 