#!/bin/bash

# تعيين الترميز
export LANG=en_US.UTF-8

echo "========================================"
echo "       HealthKey Database Setup"
echo "========================================"
echo

echo "🚀 بدء إعداد قاعدة البيانات..."
echo

# التحقق من وجود PHP
if ! command -v php &> /dev/null; then
    echo "❌ PHP غير مثبت أو غير موجود في PATH"
    echo "يرجى تثبيت PHP أولاً"
    exit 1
fi

echo "✅ تم العثور على PHP"
echo

# الحصول على مجلد الملف الحالي
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# تشغيل إعداد قاعدة البيانات
echo "📁 تشغيل ملف الإعداد..."
php "$SCRIPT_DIR/setup.php"

if [ $? -eq 0 ]; then
    echo
    echo "✅ تم إعداد قاعدة البيانات بنجاح!"
    echo
    echo "🧪 تشغيل اختبارات قاعدة البيانات..."
    echo
    php "$SCRIPT_DIR/test_connection.php"
else
    echo
    echo "❌ فشل في إعداد قاعدة البيانات"
fi

echo
echo "========================================"
echo "           انتهى الإعداد"
echo "========================================"
