<?php
/**
 * اختبار إصلاح خطأ Inventory
 */

require_once 'config.php';

echo "<h1>اختبار إصلاح خطأ Inventory</h1>";

try {
    // اختبار إنشاء متحكم الصيدلي
    $pharmacistController = new PharmacistController();
    echo "✅ تم إنشاء PharmacistController بنجاح<br>";
    
    // اختبار استدعاء دالة dispensing
    echo "<h2>اختبار دالة dispensing</h2>";
    
    // محاكاة طلب GET
    $_GET['search'] = '';
    $_GET['status'] = 'active';
    
    // استدعاء الدالة
    $pharmacistController->dispensing();
    
    echo "✅ تم تنفيذ دالة dispensing بنجاح<br>";
    
    // اختبار الدوال المتعلقة بالمخزون
    echo "<h2>اختبار دوال المخزون</h2>";
    
    $pharmacistController->inventory();
    echo "✅ دالة inventory تعمل بنجاح<br>";
    
    $pharmacistController->addInventory();
    echo "✅ دالة addInventory تعمل بنجاح<br>";
    
    $pharmacistController->editInventory(1);
    echo "✅ دالة editInventory تعمل بنجاح<br>";
    
    $pharmacistController->deleteInventory(1);
    echo "✅ دالة deleteInventory تعمل بنجاح<br>";
    
    $pharmacistController->updateQuantity();
    echo "✅ دالة updateQuantity تعمل بنجاح<br>";
    
    echo "<h2>✅ تم إصلاح جميع الأخطاء بنجاح!</h2>";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>روابط الاختبار</h2>";
echo "<ul>";
echo "<li><a href='http://localhost/HealthKey/pharmacist/dispensing' target='_blank'>صفحة صرف الأدوية</a></li>";
echo "<li><a href='http://localhost/HealthKey/pharmacist/dashboard' target='_blank'>لوحة التحكم</a></li>";
echo "<li><a href='http://localhost/HealthKey/pharmacist/prescriptions' target='_blank'>الوصفات</a></li>";
echo "</ul>";

echo "<h2>🏁 انتهى الاختبار</h2>";
echo "<p>تم إصلاح خطأ Inventory بنجاح! يمكنك الآن الوصول إلى صفحة صرف الأدوية.</p>";
?> 