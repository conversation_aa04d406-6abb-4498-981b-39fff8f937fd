<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-capsule me-2"></i>
            <?= htmlspecialchars($medication['name']) ?>
        </h1>
        <p class="text-muted">تفاصيل الدواء والمعلومات الطبية</p>
    </div>
    <div>
        <a href="<?= App::url('patient/medications') ?>" class="btn btn-outline-primary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للأدوية
        </a>
    </div>
</div>

<div class="row">
    <!-- Medication Information -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    معلومات الدواء
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">اسم الدواء:</label>
                            <p class="mb-0"><?= htmlspecialchars($medication['name']) ?></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">الاسم العلمي:</label>
                            <p class="mb-0"><?= htmlspecialchars($medication['generic_name']) ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">الفئة:</label>
                            <p class="mb-0"><?= htmlspecialchars($medication['category']) ?></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">القوة:</label>
                            <p class="mb-0"><?= htmlspecialchars($medication['strength']) ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">شكل الجرعة:</label>
                            <p class="mb-0"><?= htmlspecialchars($medication['dosage_form']) ?></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">نوع الوصفة:</label>
                            <span class="badge bg-<?= $medication['prescription_required'] ? 'warning' : 'success' ?>">
                                <?= $medication['prescription_required'] ? 'تحتاج وصفة طبية' : 'بدون وصفة طبية' ?>
                            </span>
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($medication['description'])): ?>
                <div class="mb-3">
                    <label class="form-label fw-bold">الوصف:</label>
                    <p class="mb-0"><?= nl2br(htmlspecialchars($medication['description'])) ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Side Effects -->
        <?php if (!empty($medication['side_effects'])): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    الآثار الجانبية
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>تنبيه:</strong> قد تحدث بعض الآثار الجانبية عند تناول هذا الدواء
                </div>
                <p class="mb-0"><?= nl2br(htmlspecialchars($medication['side_effects'])) ?></p>
            </div>
        </div>
        <?php endif; ?>

        <!-- Contraindications -->
        <?php if (!empty($medication['contraindications'])): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-x-circle me-2"></i>
                    موانع الاستخدام
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <i class="bi bi-x-circle me-2"></i>
                    <strong>تحذير:</strong> لا تستخدم هذا الدواء في الحالات التالية
                </div>
                <p class="mb-0"><?= nl2br(htmlspecialchars($medication['contraindications'])) ?></p>
            </div>
        </div>
        <?php endif; ?>

        <!-- Storage Conditions -->
        <?php if (!empty($medication['storage_conditions'])): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-thermometer-half me-2"></i>
                    شروط التخزين
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-0"><?= nl2br(htmlspecialchars($medication['storage_conditions'])) ?></p>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if ($medication['prescription_required']): ?>
                        <a href="<?= App::url('patient/book-appointment') ?>" class="btn btn-primary">
                            <i class="bi bi-calendar-plus me-2"></i>
                            حجز موعد للحصول على وصفة
                        </a>
                    <?php else: ?>
                        <button type="button" class="btn btn-success" onclick="showPharmacyInfo()">
                            <i class="bi bi-shop me-2"></i>
                            البحث عن صيدلية قريبة
                        </button>
                    <?php endif; ?>
                    
                    <a href="<?= App::url('patient/prescriptions') ?>" class="btn btn-outline-primary">
                        <i class="bi bi-prescription2 me-2"></i>
                        عرض وصفاتي
                    </a>
                    
                    <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                        <i class="bi bi-printer me-2"></i>
                        طباعة المعلومات
                    </button>
                </div>
            </div>
        </div>

        <!-- Medication Status -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    حالة الدواء
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <i class="bi bi-check-circle text-success me-2"></i>
                    <span>الدواء متاح ونشط</span>
                </div>
                
                <div class="d-flex align-items-center mb-3">
                    <i class="bi bi-calendar-check text-info me-2"></i>
                    <span>تم إضافته: <?= $medication['created_at'] ? date('Y-m-d', strtotime($medication['created_at'])) : 'غير محدد' ?></span>
                </div>
                
                <?php if ($medication['updated_at']): ?>
                <div class="d-flex align-items-center">
                    <i class="bi bi-arrow-clockwise text-warning me-2"></i>
                    <span>آخر تحديث: <?= date('Y-m-d', strtotime($medication['updated_at'])) ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Similar Medications -->
        <?php if (!empty($similarMedications)): ?>
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-arrow-repeat me-2"></i>
                    أدوية مشابهة
                </h5>
            </div>
            <div class="card-body">
                <?php foreach ($similarMedications as $similar): ?>
                    <div class="d-flex align-items-center mb-3 p-2 bg-light rounded">
                        <div class="flex-grow-1">
                            <h6 class="mb-1"><?= htmlspecialchars($similar['name']) ?></h6>
                            <p class="mb-0 text-muted small"><?= htmlspecialchars($similar['generic_name']) ?></p>
                        </div>
                        <a href="<?= App::url('patient/view-medication/' . $similar['id']) ?>" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-eye"></i>
                        </a>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Pharmacy Modal -->
<div class="modal fade" id="pharmacyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">البحث عن صيدلية قريبة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>معلومات:</strong> يمكنك العثور على هذا الدواء في معظم الصيدليات المحلية
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>نصائح للشراء:</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check text-success me-2"></i>تأكد من تاريخ انتهاء الصلاحية</li>
                            <li><i class="bi bi-check text-success me-2"></i>تحقق من العبوة والتغليف</li>
                            <li><i class="bi bi-check text-success me-2"></i>اقرأ النشرة الطبية</li>
                            <li><i class="bi bi-check text-success me-2"></i>استشر الصيدلي إذا لزم الأمر</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>معلومات مهمة:</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-info text-info me-2"></i>اسم الدواء: <?= htmlspecialchars($medication['name']) ?></li>
                            <li><i class="bi bi-info text-info me-2"></i>الاسم العلمي: <?= htmlspecialchars($medication['generic_name']) ?></li>
                            <li><i class="bi bi-info text-info me-2"></i>القوة: <?= htmlspecialchars($medication['strength']) ?></li>
                            <li><i class="bi bi-info text-info me-2"></i>الشكل: <?= htmlspecialchars($medication['dosage_form']) ?></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="searchNearbyPharmacies()">
                    <i class="bi bi-geo-alt me-2"></i>
                    البحث عن صيدليات قريبة
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Show Pharmacy Info Modal
function showPharmacyInfo() {
    $('#pharmacyModal').modal('show');
}

// Search Nearby Pharmacies
function searchNearbyPharmacies() {
    // في التطبيق الحقيقي، سيتم استخدام خرائط Google أو خدمة مماثلة
    alert('سيتم إضافة ميزة البحث عن الصيدليات القريبة قريباً');
}

// Initialize page
$(document).ready(function() {
    // Initialize tooltips
    $('[title]').tooltip();
});
</script>

<style>
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.badge {
    font-size: 0.875rem;
}

.alert {
    border-radius: 0.375rem;
}

.list-unstyled li {
    margin-bottom: 0.5rem;
}

.modal-content {
    border-radius: 0.5rem;
}

@media print {
    .btn, .modal {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}
</style> 