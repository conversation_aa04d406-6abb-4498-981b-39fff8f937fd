<?php
/**
 * نقطة الدخول الرئيسية للتطبيق (Front Controller)
 * جميع الطلبات تمر عبر هذا الملف
 */

// بدء الجلسة
session_start();

// تضمين ملف الإعدادات (يحمل جميع الملفات المطلوبة)
require_once '../config.php';

// معالجة الأخطاء
set_error_handler(function($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }
    
    $errorMessage = "خطأ في الملف $file على السطر $line: $message";
    
    if (APP_DEBUG) {
        echo "<div style='background: #ffebee; color: #c62828; padding: 10px; margin: 10px; border-radius: 5px;'>";
        echo "<strong>خطأ في التطبيق:</strong><br>";
        echo htmlspecialchars($errorMessage);
        echo "</div>";
    } else {
        error_log($errorMessage);
    }
    
    return true;
});

// معالجة الاستثناءات
set_exception_handler(function($exception) {
    $errorMessage = "استثناء غير معالج: " . $exception->getMessage() . 
                   " في الملف " . $exception->getFile() . 
                   " على السطر " . $exception->getLine();
    
    if (APP_DEBUG) {
        echo "<div style='background: #ffebee; color: #c62828; padding: 15px; margin: 10px; border-radius: 5px; font-family: monospace;'>";
        echo "<h3>خطأ في التطبيق</h3>";
        echo "<strong>الرسالة:</strong> " . htmlspecialchars($exception->getMessage()) . "<br>";
        echo "<strong>الملف:</strong> " . htmlspecialchars($exception->getFile()) . "<br>";
        echo "<strong>السطر:</strong> " . $exception->getLine() . "<br>";
        echo "<strong>التتبع:</strong><br>";
        echo "<pre>" . htmlspecialchars($exception->getTraceAsString()) . "</pre>";
        echo "</div>";
    } else {
        error_log($errorMessage);
        
        // عرض صفحة خطأ عامة
        http_response_code(500);
        echo "<!DOCTYPE html>
        <html lang='ar' dir='rtl'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>خطأ في الخادم - " . APP_NAME . "</title>
            <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; }
            </style>
        </head>
        <body class='bg-light'>
            <div class='container mt-5'>
                <div class='row justify-content-center'>
                    <div class='col-md-6'>
                        <div class='card'>
                            <div class='card-body text-center'>
                                <h1 class='text-danger'>خطأ في الخادم</h1>
                                <p>عذراً، حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى لاحقاً.</p>
                                <a href='" . APP_URL . "' class='btn btn-primary'>العودة للرئيسية</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>";
    }
});

try {
    // معالجة الملفات الثابتة
    $requestUri = $_SERVER['REQUEST_URI'];
    $staticFiles = ['css', 'js', 'img', 'uploads'];
    
    foreach ($staticFiles as $type) {
        if (strpos($requestUri, "/$type/") === 0) {
            $filePath = __DIR__ . $requestUri;
            if (file_exists($filePath)) {
                $extension = pathinfo($filePath, PATHINFO_EXTENSION);
                $mimeTypes = [
                    'css' => 'text/css',
                    'js' => 'application/javascript',
                    'png' => 'image/png',
                    'jpg' => 'image/jpeg',
                    'jpeg' => 'image/jpeg',
                    'gif' => 'image/gif',
                    'ico' => 'image/x-icon',
                    'pdf' => 'application/pdf'
                ];
                
                $mimeType = $mimeTypes[$extension] ?? 'text/plain';
                header("Content-Type: $mimeType");
                header("Cache-Control: public, max-age=31536000");
                readfile($filePath);
                exit;
            }
        }
    }
    
    // تشغيل التطبيق
    $app = new App();
    
} catch (Exception $e) {
    // في حالة فشل تشغيل التطبيق
    if (APP_DEBUG) {
        echo "<div style='background: #ffebee; color: #c62828; padding: 15px; margin: 10px; border-radius: 5px;'>";
        echo "<h3>فشل في تشغيل التطبيق</h3>";
        echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><strong>الملف:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
        echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
        echo "</div>";
    } else {
        error_log("فشل في تشغيل التطبيق: " . $e->getMessage());
        
        http_response_code(500);
        echo "<!DOCTYPE html>
        <html lang='ar' dir='rtl'>
        <head>
            <meta charset='UTF-8'>
            <title>خطأ - " . APP_NAME . "</title>
            <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
        </head>
        <body class='bg-light d-flex align-items-center justify-content-center' style='min-height: 100vh; direction: rtl;'>
            <div class='text-center'>
                <h1 class='text-danger'>خطأ في النظام</h1>
                <p>عذراً، النظام غير متاح حالياً. يرجى المحاولة لاحقاً.</p>
            </div>
        </body>
        </html>";
    }
}
?>
