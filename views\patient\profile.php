<?php
/**
 * صفحة الملف الشخصي للمريض
 */
?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-person-circle me-2"></i>
            الملف الشخصي
        </h1>
        <p class="text-muted">تعديل المعلومات الشخصية والبيانات الطبية</p>
    </div>
    <div class="d-flex gap-2">
        <button class="btn btn-outline-primary" onclick="window.print()">
            <i class="bi bi-printer me-2"></i>
            طباعة الملف
        </button>
        <button class="btn btn-primary" onclick="saveProfile()">
            <i class="bi bi-save me-2"></i>
            حفظ التغييرات
        </button>
    </div>
</div>

<!-- رسائل النجاح والخطأ -->
<?php if (SessionHelper::hasFlash()): ?>
    <?php 
    $flashMessages = SessionHelper::getAllFlash();
    foreach ($flashMessages as $key => $flashMessage): 
    ?>
    <div class="alert alert-<?= $flashMessage['type'] ?> alert-dismissible fade show" role="alert">
        <?= $flashMessage['message'] ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endforeach; ?>
<?php endif; ?>

<div class="row">
    <!-- المعلومات الشخصية -->
    <div class="col-xl-8">
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-person me-2"></i>
                    المعلومات الشخصية
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="profileForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">الاسم الأول <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="first_name" name="first_name" 
                                   value="<?= htmlspecialchars($patient['first_name'] ?? '') ?>" required>
                            <?php if (SessionHelper::getValidationError('first_name')): ?>
                                <div class="text-danger small"><?= SessionHelper::getValidationError('first_name') ?></div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">الاسم الأخير <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="last_name" name="last_name" 
                                   value="<?= htmlspecialchars($patient['last_name'] ?? '') ?>" required>
                            <?php if (SessionHelper::getValidationError('last_name')): ?>
                                <div class="text-danger small"><?= SessionHelper::getValidationError('last_name') ?></div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?= htmlspecialchars($patient['email'] ?? '') ?>" required>
                            <?php if (SessionHelper::getValidationError('email')): ?>
                                <div class="text-danger small"><?= SessionHelper::getValidationError('email') ?></div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف <span class="text-danger">*</span></label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="<?= htmlspecialchars($patient['phone'] ?? '') ?>" required>
                            <?php if (SessionHelper::getValidationError('phone')): ?>
                                <div class="text-danger small"><?= SessionHelper::getValidationError('phone') ?></div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="national_id" class="form-label">رقم الهوية الوطنية</label>
                            <input type="text" class="form-control" id="national_id" name="national_id" 
                                   value="<?= htmlspecialchars($patient['national_id'] ?? '') ?>" readonly>
                            <div class="form-text">لا يمكن تعديل رقم الهوية الوطنية</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="date_of_birth" class="form-label">تاريخ الميلاد</label>
                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" 
                                   value="<?= htmlspecialchars($patient['date_of_birth'] ?? '') ?>">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="gender" class="form-label">الجنس</label>
                            <select class="form-select" id="gender" name="gender">
                                <option value="">اختر الجنس</option>
                                <option value="male" <?= ($patient['gender'] ?? '') === 'male' ? 'selected' : '' ?>>ذكر</option>
                                <option value="female" <?= ($patient['gender'] ?? '') === 'female' ? 'selected' : '' ?>>أنثى</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="emergency_contact" class="form-label">جهة الاتصال في الطوارئ</label>
                            <input type="text" class="form-control" id="emergency_contact" name="emergency_contact" 
                                   value="<?= htmlspecialchars($patient['emergency_contact'] ?? '') ?>">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="emergency_phone" class="form-label">هاتف الطوارئ</label>
                            <input type="tel" class="form-control" id="emergency_phone" name="emergency_phone" 
                                   value="<?= htmlspecialchars($patient['emergency_phone'] ?? '') ?>">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="address" class="form-label">العنوان</label>
                            <textarea class="form-control" id="address" name="address" rows="3"><?= htmlspecialchars($patient['address'] ?? '') ?></textarea>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-save me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- تغيير كلمة المرور -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-shield-lock me-2"></i>
                    تغيير كلمة المرور
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= App::url('patient/change-password') ?>" id="passwordForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="current_password" class="form-label">كلمة المرور الحالية <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="new_password" class="form-label">كلمة المرور الجديدة <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-warning">
                            <i class="bi bi-key me-2"></i>
                            تغيير كلمة المرور
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- المعلومات الطبية -->
    <div class="col-xl-4">
        <!-- معلومات الحساب -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    معلومات الحساب
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-6">
                        <strong>رقم العضوية:</strong>
                    </div>
                    <div class="col-6">
                        <span class="badge bg-primary"><?= $patient['id'] ?></span>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-6">
                        <strong>نوع الحساب:</strong>
                    </div>
                    <div class="col-6">
                        <span class="badge bg-success">مريض</span>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-6">
                        <strong>حالة الحساب:</strong>
                    </div>
                    <div class="col-6">
                        <?php if ($patient['is_active']): ?>
                            <span class="badge bg-success">نشط</span>
                        <?php else: ?>
                            <span class="badge bg-danger">غير نشط</span>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-6">
                        <strong>تاريخ التسجيل:</strong>
                    </div>
                    <div class="col-6">
                        <?= date('Y/m/d', strtotime($patient['created_at'])) ?>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-6">
                        <strong>آخر تحديث:</strong>
                    </div>
                    <div class="col-6">
                        <?= date('Y/m/d', strtotime($patient['updated_at'])) ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- الحساسيات -->
        <div class="card shadow mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    الحساسيات
                </h5>
                <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addAllergyModal">
                    <i class="bi bi-plus"></i>
                    إضافة
                </button>
            </div>
            <div class="card-body">
                <?php if (!empty($allergies)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($allergies as $allergy): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1"><?= htmlspecialchars($allergy['allergen']) ?></h6>
                                    <?php if (!empty($allergy['reaction'])): ?>
                                        <small class="text-muted"><?= htmlspecialchars($allergy['reaction']) ?></small>
                                    <?php endif; ?>
                                </div>
                                <span class="badge bg-<?= $allergy['severity'] === 'severe' ? 'danger' : ($allergy['severity'] === 'moderate' ? 'warning' : 'info') ?>">
                                    <?= $allergy['severity'] === 'severe' ? 'شديدة' : ($allergy['severity'] === 'moderate' ? 'متوسطة' : 'خفيفة') ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-check-circle fs-1 mb-3"></i>
                        <p>لا توجد حساسيات مسجلة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- الإحصائيات السريعة -->
        <div class="card shadow mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-primary mb-1"><?= isset($stats['appointments']['total']) ? $stats['appointments']['total'] : 0 ?></h4>
                            <small class="text-muted">المواعيد</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-success mb-1"><?= isset($stats['prescriptions']['total']) ? $stats['prescriptions']['total'] : 0 ?></h4>
                            <small class="text-muted">الوصفات</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-info mb-1"><?= isset($stats['medical_records']['total']) ? $stats['medical_records']['total'] : 0 ?></h4>
                            <small class="text-muted">السجلات الطبية</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-3">
                            <h4 class="text-warning mb-1"><?= count($allergies) ?></h4>
                            <small class="text-muted">الحساسيات</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إضافة حساسية -->
<div class="modal fade" id="addAllergyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة حساسية جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="<?= App::url('patient/add-allergy') ?>">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="allergen" class="form-label">المادة المسببة للحساسية <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="allergen" name="allergen" required>
                    </div>
                    <div class="mb-3">
                        <label for="reaction" class="form-label">رد الفعل</label>
                        <textarea class="form-control" id="reaction" name="reaction" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="severity" class="form-label">شدة الحساسية</label>
                        <select class="form-select" id="severity" name="severity">
                            <option value="mild">خفيفة</option>
                            <option value="moderate">متوسطة</option>
                            <option value="severe">شديدة</option>
                            <option value="life_threatening">مهددة للحياة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="onset_date" class="form-label">تاريخ بداية الحساسية</label>
                        <input type="date" class="form-control" id="onset_date" name="onset_date">
                    </div>
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة الحساسية</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// حفظ الملف الشخصي
function saveProfile() {
    document.getElementById('profileForm').submit();
}

// التحقق من تطابق كلمات المرور
document.getElementById('passwordForm').addEventListener('submit', function(e) {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (newPassword !== confirmPassword) {
        e.preventDefault();
        alert('كلمات المرور غير متطابقة');
        return false;
    }
    
    if (newPassword.length < 6) {
        e.preventDefault();
        alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        return false;
    }
});

// التحقق من صحة البيانات قبل الإرسال
document.getElementById('profileForm').addEventListener('submit', function(e) {
    const email = document.getElementById('email').value;
    const phone = document.getElementById('phone').value;
    
    // التحقق من صحة البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        e.preventDefault();
        alert('يرجى إدخال بريد إلكتروني صحيح');
        return false;
    }
    
    // التحقق من صحة رقم الهاتف
    const phoneRegex = /^[0-9+\-\s()]+$/;
    if (!phoneRegex.test(phone)) {
        e.preventDefault();
        alert('يرجى إدخال رقم هاتف صحيح');
        return false;
    }
});
</script> 