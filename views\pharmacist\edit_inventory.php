<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">تعديل معلومات الدواء</h1>
        <p class="text-muted">تعديل معلومات <?= htmlspecialchars($item['name']) ?></p>
    </div>
    <div>
        <a href="<?= App::url('pharmacist/inventory') ?>" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            العودة للمخزون
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-pencil-square me-2"></i>
                    معلومات الدواء
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= App::url('pharmacist/edit-inventory/' . $item['id']) ?>">
                    <div class="row">
                        <!-- اسم الدواء -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم الدواء *</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?= htmlspecialchars($item['name']) ?>" required>
                        </div>
                        
                        <!-- الاسم العلمي -->
                        <div class="col-md-6 mb-3">
                            <label for="generic_name" class="form-label">الاسم العلمي</label>
                            <input type="text" class="form-control" id="generic_name" name="generic_name" 
                                   value="<?= htmlspecialchars($item['generic_name'] ?? '') ?>">
                        </div>
                        
                        <!-- الفئة -->
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">الفئة</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">اختر الفئة</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= htmlspecialchars($category) ?>" 
                                            <?= $item['category'] == $category ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($category) ?>
                                    </option>
                                <?php endforeach; ?>
                                <option value="مسكنات" <?= $item['category'] == 'مسكنات' ? 'selected' : '' ?>>مسكنات</option>
                                <option value="مضادات حيوية" <?= $item['category'] == 'مضادات حيوية' ? 'selected' : '' ?>>مضادات حيوية</option>
                                <option value="مضادات الحموضة" <?= $item['category'] == 'مضادات الحموضة' ? 'selected' : '' ?>>مضادات الحموضة</option>
                                <option value="فيتامينات" <?= $item['category'] == 'فيتامينات' ? 'selected' : '' ?>>فيتامينات</option>
                                <option value="أخرى" <?= $item['category'] == 'أخرى' ? 'selected' : '' ?>>أخرى</option>
                            </select>
                        </div>
                        
                        <!-- الشكل الدوائي -->
                        <div class="col-md-6 mb-3">
                            <label for="dosage_form" class="form-label">الشكل الدوائي</label>
                            <select class="form-select" id="dosage_form" name="dosage_form">
                                <option value="">اختر الشكل</option>
                                <option value="أقراص" <?= $item['dosage_form'] == 'أقراص' ? 'selected' : '' ?>>أقراص</option>
                                <option value="كبسولات" <?= $item['dosage_form'] == 'كبسولات' ? 'selected' : '' ?>>كبسولات</option>
                                <option value="شراب" <?= $item['dosage_form'] == 'شراب' ? 'selected' : '' ?>>شراب</option>
                                <option value="حقن" <?= $item['dosage_form'] == 'حقن' ? 'selected' : '' ?>>حقن</option>
                                <option value="كريم" <?= $item['dosage_form'] == 'كريم' ? 'selected' : '' ?>>كريم</option>
                                <option value="مرهم" <?= $item['dosage_form'] == 'مرهم' ? 'selected' : '' ?>>مرهم</option>
                                <option value="قطرات" <?= $item['dosage_form'] == 'قطرات' ? 'selected' : '' ?>>قطرات</option>
                            </select>
                        </div>
                        
                        <!-- القوة -->
                        <div class="col-md-6 mb-3">
                            <label for="strength" class="form-label">القوة</label>
                            <input type="text" class="form-control" id="strength" name="strength" 
                                   value="<?= htmlspecialchars($item['strength'] ?? '') ?>" 
                                   placeholder="مثال: 500mg">
                        </div>
                        
                        <!-- الشركة المصنعة -->
                        <div class="col-md-6 mb-3">
                            <label for="manufacturer" class="form-label">الشركة المصنعة</label>
                            <input type="text" class="form-control" id="manufacturer" name="manufacturer" 
                                   value="<?= htmlspecialchars($item['manufacturer'] ?? '') ?>">
                        </div>
                        
                        <!-- الكمية -->
                        <div class="col-md-4 mb-3">
                            <label for="quantity" class="form-label">الكمية المتوفرة *</label>
                            <input type="number" class="form-control" id="quantity" name="quantity" 
                                   value="<?= htmlspecialchars($item['quantity']) ?>" min="0" required>
                        </div>
                        
                        <!-- سعر الوحدة -->
                        <div class="col-md-4 mb-3">
                            <label for="unit_price" class="form-label">سعر الوحدة (ريال) *</label>
                            <input type="number" class="form-control" id="unit_price" name="unit_price" 
                                   value="<?= htmlspecialchars($item['unit_price']) ?>" 
                                   min="0" step="0.01" required>
                        </div>
                        
                        <!-- مستوى إعادة الطلب -->
                        <div class="col-md-4 mb-3">
                            <label for="reorder_level" class="form-label">مستوى إعادة الطلب</label>
                            <input type="number" class="form-control" id="reorder_level" name="reorder_level" 
                                   value="<?= htmlspecialchars($item['reorder_level']) ?>" min="0">
                        </div>
                        
                        <!-- تاريخ انتهاء الصلاحية -->
                        <div class="col-md-6 mb-3">
                            <label for="expiry_date" class="form-label">تاريخ انتهاء الصلاحية</label>
                            <input type="date" class="form-control" id="expiry_date" name="expiry_date" 
                                   value="<?= htmlspecialchars($item['expiry_date']) ?>">
                        </div>
                        
                        <!-- رقم الدفعة -->
                        <div class="col-md-6 mb-3">
                            <label for="batch_number" class="form-label">رقم الدفعة</label>
                            <input type="text" class="form-control" id="batch_number" name="batch_number" 
                                   value="<?= htmlspecialchars($item['batch_number'] ?? '') ?>">
                        </div>
                        
                        <!-- الموقع -->
                        <div class="col-md-12 mb-3">
                            <label for="location" class="form-label">الموقع في المخزن</label>
                            <input type="text" class="form-control" id="location" name="location" 
                                   value="<?= htmlspecialchars($item['location'] ?? '') ?>" 
                                   placeholder="مثال: رف A - صف 1">
                        </div>
                        
                        <!-- الوصف -->
                        <div class="col-md-12 mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      placeholder="وصف الدواء واستخداماته..."><?= htmlspecialchars($item['description'] ?? '') ?></textarea>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="<?= App::url('pharmacist/inventory') ?>" class="btn btn-secondary">
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- معلومات إضافية -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    معلومات الدواء
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <strong>تاريخ الإضافة:</strong><br>
                    <span class="text-muted"><?= date('Y-m-d H:i', strtotime($item['created_at'])) ?></span>
                </div>
                
                <div class="mb-3">
                    <strong>آخر تحديث:</strong><br>
                    <span class="text-muted"><?= date('Y-m-d H:i', strtotime($item['updated_at'])) ?></span>
                </div>
                
                <div class="mb-3">
                    <strong>حالة المخزون:</strong><br>
                    <?php if ($item['quantity'] <= $item['reorder_level']): ?>
                        <span class="badge bg-warning">منخفض</span>
                    <?php elseif ($item['quantity'] == 0): ?>
                        <span class="badge bg-danger">نفذ</span>
                    <?php else: ?>
                        <span class="badge bg-success">متوفر</span>
                    <?php endif; ?>
                </div>
                
                <?php if ($item['expiry_date']): ?>
                <div class="mb-3">
                    <strong>حالة الصلاحية:</strong><br>
                    <?php 
                    $expiryDate = new DateTime($item['expiry_date']);
                    $today = new DateTime();
                    $diff = $today->diff($expiryDate);
                    ?>
                    <?php if ($expiryDate < $today): ?>
                        <span class="badge bg-danger">منتهي الصلاحية</span>
                    <?php elseif ($diff->days <= 30): ?>
                        <span class="badge bg-warning">ينتهي قريباً (<?= $diff->days ?> يوم)</span>
                    <?php else: ?>
                        <span class="badge bg-success">صالح (<?= $diff->days ?> يوم)</span>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                
                <div class="alert alert-info">
                    <h6><i class="bi bi-lightbulb me-2"></i>نصائح للتعديل:</h6>
                    <ul class="mb-0">
                        <li>تأكد من صحة الكمية المتوفرة</li>
                        <li>راجع تاريخ انتهاء الصلاحية</li>
                        <li>حدّث مستوى إعادة الطلب حسب الحاجة</li>
                        <li>تأكد من صحة سعر الوحدة</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// التحقق من صحة البيانات قبل الإرسال
document.querySelector('form').addEventListener('submit', function(e) {
    const quantity = document.getElementById('quantity').value;
    const unitPrice = document.getElementById('unit_price').value;
    const expiryDate = document.getElementById('expiry_date').value;
    
    if (quantity < 0) {
        e.preventDefault();
        alert('الكمية يجب أن تكون أكبر من أو تساوي صفر');
        return;
    }
    
    if (unitPrice < 0) {
        e.preventDefault();
        alert('سعر الوحدة يجب أن يكون أكبر من أو يساوي صفر');
        return;
    }
    
    if (expiryDate && new Date(expiryDate) < new Date()) {
        if (!confirm('تاريخ انتهاء الصلاحية في الماضي. هل تريد المتابعة؟')) {
            e.preventDefault();
            return;
        }
    }
});
</script> 