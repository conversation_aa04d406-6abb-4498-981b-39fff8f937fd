<?php
/**
 * ملف اختبار الاتصال بقاعدة البيانات
 * Database Connection Test Script
 */

// تضمين ملف التكوين
require_once __DIR__ . '/../config.php';

class DatabaseTester
{
    private $db;
    
    public function __construct()
    {
        try {
            // استخدام الاتصال المباشر بدلاً من كائن Database
            $this->db = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
                ]
            );
            echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n";
        } catch (PDOException $e) {
            echo "❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "\n";
            exit(1);
        }
    }

    /**
     * اختبار الاتصال الأساسي
     */
    public function testBasicConnection()
    {
        echo "\n🔍 اختبار الاتصال الأساسي...\n";
        
        try {
            $stmt = $this->db->query("SELECT 1 as test, NOW() as current_time");
            $result = $stmt->fetch();
            
            if ($result && $result['test'] == 1) {
                echo "✅ الاتصال الأساسي يعمل بنجاح\n";
                echo "⏰ الوقت الحالي في قاعدة البيانات: " . $result['current_time'] . "\n";
                return true;
            } else {
                echo "❌ فشل في الاتصال الأساسي\n";
                return false;
            }
        } catch (PDOException $e) {
            echo "❌ خطأ في الاتصال: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * اختبار الجداول الأساسية
     */
    public function testTables()
    {
        echo "\n🗃️ اختبار الجداول الأساسية...\n";
        
        $tables = [
            'users' => 'جدول المستخدمين',
            'doctors' => 'جدول الأطباء',
            'pharmacists' => 'جدول الصيادلة',
            'appointments' => 'جدول المواعيد',
            'medical_records' => 'جدول السجلات الطبية',
            'prescriptions' => 'جدول الوصفات',
            'prescription_medications' => 'جدول أدوية الوصفات',
            'allergies' => 'جدول الحساسيات',
            'lab_tests' => 'جدول الفحوصات',
            'notifications' => 'جدول الإشعارات',
            'system_settings' => 'جدول الإعدادات'
        ];
        
        $allTablesExist = true;
        
        foreach ($tables as $table => $description) {
            try {
                $stmt = $this->db->query("SELECT COUNT(*) as count FROM `$table`");
                $result = $stmt->fetch();
                echo "✅ $description ($table): {$result['count']} سجل\n";
            } catch (PDOException $e) {
                echo "❌ $description ($table): غير موجود أو خطأ\n";
                $allTablesExist = false;
            }
        }
        
        return $allTablesExist;
    }

    /**
     * اختبار البيانات الأولية
     */
    public function testInitialData()
    {
        echo "\n📊 اختبار البيانات الأولية...\n";
        
        try {
            // اختبار المستخدمين
            $stmt = $this->db->query("SELECT user_type, COUNT(*) as count FROM users GROUP BY user_type");
            $users = $stmt->fetchAll();

            echo "👥 المستخدمين حسب النوع:\n";
            foreach ($users as $user) {
                $typeLabel = $this->getUserTypeLabel($user['user_type']);
                echo "   - $typeLabel: {$user['count']}\n";
            }

            // اختبار المدير
            $stmt = $this->db->query("SELECT * FROM users WHERE user_type = 'admin' LIMIT 1");
            $admin = $stmt->fetch();
            if ($admin) {
                echo "✅ حساب المدير موجود: {$admin['email']}\n";
            } else {
                echo "❌ حساب المدير غير موجود\n";
                return false;
            }

            // اختبار الإعدادات
            $stmt = $this->db->query("SELECT COUNT(*) as count FROM system_settings");
            $settings = $stmt->fetch();
            echo "⚙️ إعدادات النظام: {$settings['count']} إعداد\n";

            return true;
        } catch (PDOException $e) {
            echo "❌ خطأ في اختبار البيانات: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * اختبار العمليات الأساسية (CRUD)
     */
    public function testCRUDOperations()
    {
        echo "\n🔧 اختبار العمليات الأساسية (CRUD)...\n";
        
        try {
            // اختبار الإدراج
            $testData = [
                'key' => 'test_setting_' . time(),
                'value' => 'test_value',
                'type' => 'string',
                'description' => 'إعداد تجريبي للاختبار',
                'is_public' => 0
            ];
            
            $insertId = $this->db->insert(
                "INSERT INTO system_settings (`key`, `value`, `type`, `description`, `is_public`) 
                 VALUES (:key, :value, :type, :description, :is_public)",
                [
                    ':key' => $testData['key'],
                    ':value' => $testData['value'],
                    ':type' => $testData['type'],
                    ':description' => $testData['description'],
                    ':is_public' => $testData['is_public']
                ]
            );
            
            if ($insertId) {
                echo "✅ اختبار الإدراج (INSERT): نجح - ID: $insertId\n";
            } else {
                echo "❌ اختبار الإدراج (INSERT): فشل\n";
                return false;
            }
            
            // اختبار القراءة
            $readData = $this->db->selectOne(
                "SELECT * FROM system_settings WHERE id = :id",
                [':id' => $insertId]
            );
            
            if ($readData && $readData['key'] === $testData['key']) {
                echo "✅ اختبار القراءة (SELECT): نجح\n";
            } else {
                echo "❌ اختبار القراءة (SELECT): فشل\n";
                return false;
            }
            
            // اختبار التحديث
            $updateResult = $this->db->update(
                "UPDATE system_settings SET value = :value WHERE id = :id",
                [':value' => 'updated_value', ':id' => $insertId]
            );
            
            if ($updateResult) {
                echo "✅ اختبار التحديث (UPDATE): نجح\n";
            } else {
                echo "❌ اختبار التحديث (UPDATE): فشل\n";
                return false;
            }
            
            // اختبار الحذف
            $deleteResult = $this->db->delete(
                "DELETE FROM system_settings WHERE id = :id",
                [':id' => $insertId]
            );
            
            if ($deleteResult) {
                echo "✅ اختبار الحذف (DELETE): نجح\n";
            } else {
                echo "❌ اختبار الحذف (DELETE): فشل\n";
                return false;
            }
            
            return true;
        } catch (Exception $e) {
            echo "❌ خطأ في اختبار العمليات: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * اختبار الاستعلامات المعقدة
     */
    public function testComplexQueries()
    {
        echo "\n🔍 اختبار الاستعلامات المعقدة...\n";
        
        try {
            // اختبار JOIN
            $joinQuery = "
                SELECT u.first_name, u.last_name, u.email, d.specialization 
                FROM users u 
                LEFT JOIN doctors d ON u.id = d.user_id 
                WHERE u.user_type = 'doctor'
                LIMIT 5
            ";
            
            $doctors = $this->db->select($joinQuery);
            echo "✅ اختبار JOIN: " . count($doctors) . " طبيب\n";
            
            // اختبار GROUP BY
            $groupQuery = "
                SELECT user_type, COUNT(*) as count 
                FROM users 
                GROUP BY user_type 
                ORDER BY count DESC
            ";
            
            $userStats = $this->db->select($groupQuery);
            echo "✅ اختبار GROUP BY: " . count($userStats) . " مجموعة\n";
            
            // اختبار المشاهدات (Views)
            try {
                $viewQuery = "SELECT COUNT(*) as count FROM appointments_view";
                $viewResult = $this->db->selectOne($viewQuery);
                echo "✅ اختبار المشاهدات (Views): {$viewResult['count']} موعد\n";
            } catch (Exception $e) {
                echo "⚠️ المشاهدات غير متاحة: " . $e->getMessage() . "\n";
            }
            
            return true;
        } catch (Exception $e) {
            echo "❌ خطأ في الاستعلامات المعقدة: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * اختبار الأداء
     */
    public function testPerformance()
    {
        echo "\n⚡ اختبار الأداء...\n";
        
        try {
            $startTime = microtime(true);
            
            // تنفيذ عدة استعلامات
            for ($i = 0; $i < 10; $i++) {
                $this->db->selectOne("SELECT COUNT(*) as count FROM users");
            }
            
            $endTime = microtime(true);
            $executionTime = ($endTime - $startTime) * 1000; // بالميلي ثانية
            
            echo "✅ وقت تنفيذ 10 استعلامات: " . number_format($executionTime, 2) . " ميلي ثانية\n";
            
            if ($executionTime < 1000) {
                echo "✅ الأداء ممتاز\n";
            } elseif ($executionTime < 3000) {
                echo "⚠️ الأداء مقبول\n";
            } else {
                echo "❌ الأداء بطيء\n";
            }
            
            return true;
        } catch (Exception $e) {
            echo "❌ خطأ في اختبار الأداء: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * تشغيل جميع الاختبارات
     */
    public function runAllTests()
    {
        echo "🧪 بدء اختبار قاعدة البيانات HealthKey\n";
        echo str_repeat("=", 60) . "\n";
        
        $tests = [
            'testBasicConnection' => 'الاتصال الأساسي',
            'testTables' => 'الجداول',
            'testInitialData' => 'البيانات الأولية',
            'testCRUDOperations' => 'العمليات الأساسية',
            'testComplexQueries' => 'الاستعلامات المعقدة',
            'testPerformance' => 'الأداء'
        ];
        
        $passedTests = 0;
        $totalTests = count($tests);
        
        foreach ($tests as $method => $description) {
            if ($this->$method()) {
                $passedTests++;
            }
        }
        
        echo "\n" . str_repeat("=", 60) . "\n";
        echo "📊 نتائج الاختبار:\n";
        echo "✅ نجح: $passedTests من $totalTests اختبار\n";
        
        if ($passedTests === $totalTests) {
            echo "🎉 جميع الاختبارات نجحت! قاعدة البيانات جاهزة للاستخدام\n";
        } else {
            $failedTests = $totalTests - $passedTests;
            echo "⚠️ فشل $failedTests اختبار. يرجى مراجعة الأخطاء أعلاه\n";
        }
        
        return $passedTests === $totalTests;
    }

    /**
     * الحصول على تسمية نوع المستخدم
     */
    private function getUserTypeLabel($type)
    {
        $labels = [
            'admin' => 'مدير',
            'doctor' => 'طبيب',
            'patient' => 'مريض',
            'pharmacist' => 'صيدلي'
        ];
        
        return $labels[$type] ?? $type;
    }
}

// تشغيل الاختبارات إذا تم استدعاء الملف مباشرة
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $tester = new DatabaseTester();
    $tester->runAllTests();
}
