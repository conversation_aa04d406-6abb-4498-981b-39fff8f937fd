<?php

/**
 * فئة التطبيق الرئيسية (Router)
 * مسؤولة عن معالجة مسارات URL وتوجيه الطلبات إلى المتحكم المناسب
 */
class App
{
    protected $controller = 'PagesController';
    protected $method = 'index';
    protected $params = [];
    protected $routes = [];

    public function __construct()
    {
        $this->setupRoutes();
        $this->parseUrl();
    }

    /**
     * إعداد المسارات المخصصة
     */
    private function setupRoutes()
    {
        $this->routes = [
            // المسارات العامة
            '' => ['controller' => 'PagesController', 'method' => 'index'],
            'home' => ['controller' => 'PagesController', 'method' => 'index'],
            'about' => ['controller' => 'PagesController', 'method' => 'about'],
            
            // مسارات المصادقة
            'login' => ['controller' => 'AuthController', 'method' => 'login'],
            'register' => ['controller' => 'AuthController', 'method' => 'register'],
            'logout' => ['controller' => 'AuthController', 'method' => 'logout'],
            
            // مسارات المريض
            'patient' => ['controller' => 'PatientController', 'method' => 'dashboard'],
            'patient/dashboard' => ['controller' => 'PatientController', 'method' => 'dashboard'],
            'patient/profile' => ['controller' => 'PatientController', 'method' => 'profile'],
            'patient/medical-record' => ['controller' => 'PatientController', 'method' => 'medicalRecord'],
            'patient/medical-records' => ['controller' => 'PatientController', 'method' => 'medicalRecords'],
            'patient/prescriptions' => ['controller' => 'PatientController', 'method' => 'prescriptions'],
            'patient/view-prescription' => ['controller' => 'PatientController', 'method' => 'viewPrescription'],
            'patient/download-prescription' => ['controller' => 'PatientController', 'method' => 'downloadPrescription'],
            'patient/export-prescriptions' => ['controller' => 'PatientController', 'method' => 'exportPrescriptions'],
            'patient/medications' => ['controller' => 'PatientController', 'method' => 'medications'],
            'patient/view-medication' => ['controller' => 'PatientController', 'method' => 'viewMedication'],
            'patient/notifications' => ['controller' => 'PatientController', 'method' => 'notifications'],
            'patient/mark-notification-read' => ['controller' => 'PatientController', 'method' => 'markNotificationRead'],
            'patient/mark-all-notifications-read' => ['controller' => 'PatientController', 'method' => 'markAllNotificationsRead'],
            'patient/delete-notification' => ['controller' => 'PatientController', 'method' => 'deleteNotification'],
            'patient/messages' => ['controller' => 'PatientController', 'method' => 'messages'],
            'patient/view-message' => ['controller' => 'PatientController', 'method' => 'viewMessage'],
            'patient/compose-message' => ['controller' => 'PatientController', 'method' => 'composeMessage'],
            'patient/contact-doctor' => ['controller' => 'PatientController', 'method' => 'contactDoctor'],
            'patient/emergency-contacts' => ['controller' => 'PatientController', 'method' => 'emergencyContacts'],
            'patient/mark-message-read' => ['controller' => 'PatientController', 'method' => 'markMessageRead'],
            'patient/delete-message' => ['controller' => 'PatientController', 'method' => 'deleteMessage'],
            'patient/appointments' => ['controller' => 'PatientController', 'method' => 'appointments'],
            'patient/book-appointment' => ['controller' => 'PatientController', 'method' => 'bookAppointment'],
            'patient/cancel-appointment' => ['controller' => 'PatientController', 'method' => 'cancelAppointment'],
            'patient/rate-appointment' => ['controller' => 'PatientController', 'method' => 'rateAppointment'],
            'patient/appointment-details' => ['controller' => 'PatientController', 'method' => 'appointmentDetails'],
            'patient/get-available-slots' => ['controller' => 'PatientController', 'method' => 'getAvailableSlots'],
            'patient/medical-record-details' => ['controller' => 'PatientController', 'method' => 'medicalRecordDetails'],
            'patient/lab-test-results' => ['controller' => 'PatientController', 'method' => 'labTestResults'],
            'patient/export-medical-records' => ['controller' => 'PatientController', 'method' => 'exportMedicalRecords'],
            'patient/download-lab-results' => ['controller' => 'PatientController', 'method' => 'downloadLabResults'],
            'patient/vitals' => ['controller' => 'PatientController', 'method' => 'vitals'],
            'patient/allergies' => ['controller' => 'PatientController', 'method' => 'allergies'],
            'patient/delete-allergy' => ['controller' => 'PatientController', 'method' => 'deleteAllergy'],
            
            // مسارات الطبيب
            'doctor' => ['controller' => 'DoctorController', 'method' => 'dashboard'],
            'doctor/dashboard' => ['controller' => 'DoctorController', 'method' => 'dashboard'],
            'doctor/patients' => ['controller' => 'DoctorController', 'method' => 'patients'],
            'doctor/medical-records' => ['controller' => 'DoctorController', 'method' => 'medicalRecords'],
            'doctor/add-medical-record' => ['controller' => 'DoctorController', 'method' => 'addMedicalRecord'],
            'doctor/appointments' => ['controller' => 'DoctorController', 'method' => 'appointments'],
            'doctor/schedule' => ['controller' => 'DoctorController', 'method' => 'schedule'],
            'doctor/prescriptions' => ['controller' => 'DoctorController', 'method' => 'prescriptions'],
            'doctor/create-prescription' => ['controller' => 'DoctorController', 'method' => 'createPrescription'],
            'doctor/reports' => ['controller' => 'DoctorController', 'method' => 'reports'],
            'doctor/analytics' => ['controller' => 'DoctorController', 'method' => 'analytics'],
            'doctor/profile' => ['controller' => 'DoctorController', 'method' => 'profile'],
            'doctor/messages' => ['controller' => 'DoctorController', 'method' => 'messages'],
            'doctor/notifications' => ['controller' => 'DoctorController', 'method' => 'notifications'],
            
            // مسارات الصيدلي
            'pharmacist' => ['controller' => 'PharmacistController', 'method' => 'dashboard'],
            'pharmacist/dashboard' => ['controller' => 'PharmacistController', 'method' => 'dashboard'],
            'pharmacist/prescriptions' => ['controller' => 'PharmacistController', 'method' => 'prescriptions'],
            'pharmacist/dispensing' => ['controller' => 'PharmacistController', 'method' => 'dispensing'],
            'pharmacist/prescription-details' => ['controller' => 'PharmacistController', 'method' => 'prescriptionDetails'],
            'pharmacist/dispense-medication' => ['controller' => 'PharmacistController', 'method' => 'dispenseMedication'],
            'pharmacist/dispensing-report' => ['controller' => 'PharmacistController', 'method' => 'dispensingReport'],
            'pharmacist/reports' => ['controller' => 'PharmacistController', 'method' => 'reports'],
            'pharmacist/notifications' => ['controller' => 'PharmacistController', 'method' => 'notifications'],
            'pharmacist/profile' => ['controller' => 'PharmacistController', 'method' => 'profile'],
            
            // مسارات المخزون
            'pharmacist/inventory' => ['controller' => 'PharmacistController', 'method' => 'inventory'],
            'pharmacist/add-inventory' => ['controller' => 'PharmacistController', 'method' => 'addInventory'],
            'pharmacist/edit-inventory' => ['controller' => 'PharmacistController', 'method' => 'editInventory'],
            'pharmacist/delete-inventory' => ['controller' => 'PharmacistController', 'method' => 'deleteInventory'],
            'pharmacist/update-quantity' => ['controller' => 'PharmacistController', 'method' => 'updateQuantity'],
            'pharmacist/working-hours' => ['controller' => 'PharmacistController', 'method' => 'workingHours'],
            
            // مسارات الموردين
            'pharmacist/suppliers' => ['controller' => 'PharmacistController', 'method' => 'suppliers'],
            'pharmacist/add-supplier' => ['controller' => 'PharmacistController', 'method' => 'addSupplier'],
            'pharmacist/edit-supplier' => ['controller' => 'PharmacistController', 'method' => 'editSupplier'],
            'pharmacist/delete-supplier' => ['controller' => 'PharmacistController', 'method' => 'deleteSupplier'],
            
            // مسارات الأدوية
            'pharmacist/medications' => ['controller' => 'PharmacistController', 'method' => 'medications'],
            'pharmacist/add-medication' => ['controller' => 'PharmacistController', 'method' => 'addMedication'],
            'pharmacist/edit-medication' => ['controller' => 'PharmacistController', 'method' => 'editMedication'],
            'pharmacist/delete-medication' => ['controller' => 'PharmacistController', 'method' => 'deleteMedication'],
            
            // مسارات الرسائل
            'pharmacist/messages' => ['controller' => 'PharmacistController', 'method' => 'messages'],
            'pharmacist/view-message' => ['controller' => 'PharmacistController', 'method' => 'viewMessage'],
            'pharmacist/compose-message' => ['controller' => 'PharmacistController', 'method' => 'composeMessage'],
            'pharmacist/delete-message' => ['controller' => 'PharmacistController', 'method' => 'deleteMessage'],
            'pharmacist/update-message-status' => ['controller' => 'PharmacistController', 'method' => 'updateMessageStatus'],
            
            // مسارات المدير
            'admin' => ['controller' => 'AdminController', 'method' => 'dashboard'],
            'admin/dashboard' => ['controller' => 'AdminController', 'method' => 'dashboard'],
            'admin/users' => ['controller' => 'AdminController', 'method' => 'users'],
            'admin/add-user' => ['controller' => 'AdminController', 'method' => 'addUser'],
            'admin/edit-user' => ['controller' => 'AdminController', 'method' => 'editUser'],
            'admin/reset-user-password' => ['controller' => 'AdminController', 'method' => 'resetUserPassword'],
            'admin/toggle-user-status' => ['controller' => 'AdminController', 'method' => 'toggleUserStatus'],
            'admin/delete-user' => ['controller' => 'AdminController', 'method' => 'deleteUser'],
            'admin/bulk-user-action' => ['controller' => 'AdminController', 'method' => 'bulkUserAction'],
            'admin/api/users/stats' => ['controller' => 'AdminController', 'method' => 'apiUsersStats'],
            'admin/appointments' => ['controller' => 'AdminController', 'method' => 'appointments'],
            'admin/appointments/confirm' => ['controller' => 'AdminController', 'method' => 'confirmAppointment'],
            'admin/appointments/cancel' => ['controller' => 'AdminController', 'method' => 'cancelAppointment'],
            'admin/appointments/complete' => ['controller' => 'AdminController', 'method' => 'completeAppointment'],
            'admin/appointments/export' => ['controller' => 'AdminController', 'method' => 'exportAppointments'],
            'admin/prescriptions' => ['controller' => 'AdminController', 'method' => 'prescriptions'],
            'admin/reports' => ['controller' => 'AdminController', 'method' => 'reports'],
            'admin/reports/export' => ['controller' => 'AdminController', 'method' => 'exportReport'],
            'admin/notifications' => ['controller' => 'AdminController', 'method' => 'notifications'],
            'admin/messages' => ['controller' => 'AdminController', 'method' => 'messages'],
            'admin/analytics' => ['controller' => 'AdminController', 'method' => 'analytics'],
            'admin/activity-log' => ['controller' => 'AdminController', 'method' => 'activityLog'],
            'admin/settings' => ['controller' => 'AdminController', 'method' => 'settings'],
            'admin/backup' => ['controller' => 'AdminController', 'method' => 'backup'],
        ];
    }

    /**
     * تحليل URL وتحديد المتحكم والطريقة والمعاملات
     */
    private function parseUrl()
    {
        if (isset($_GET['url'])) {
            $url = rtrim($_GET['url'], '/');
            $url = filter_var($url, FILTER_SANITIZE_URL);
            $url = explode('/', $url);
        } else {
            $url = [''];
        }

        $path = implode('/', $url);

        // البحث في المسارات المخصصة أولاً
        if (isset($this->routes[$path])) {
            $this->controller = $this->routes[$path]['controller'];
            $this->method = $this->routes[$path]['method'];
            $this->params = array_slice($url, 2); // المعاملات الإضافية
        } else {
            // البحث في المسارات الديناميكية
            $dynamicRoute = $this->findDynamicRoute($path);
            if ($dynamicRoute) {
                $this->controller = $dynamicRoute['controller'];
                $this->method = $dynamicRoute['method'];
                $this->params = $dynamicRoute['params'];
            } else {
                // التحليل التقليدي للمسار
                $this->parseTraditionalUrl($url);
            }
        }

        $this->loadController();
    }

    /**
     * البحث في المسارات الديناميكية
     */
    private function findDynamicRoute($path)
    {
        $urlParts = explode('/', $path);
        
        // البحث عن مسارات مثل admin/edit-user/{id}
        if (count($urlParts) >= 3 && $urlParts[0] === 'admin') {
            $basePath = $urlParts[0] . '/' . $urlParts[1];
            $param = $urlParts[2];
            
            // التحقق من المسارات الديناميكية المعروفة
            $dynamicRoutes = [
                'admin/edit-user' => ['controller' => 'AdminController', 'method' => 'editUser'],
                'admin/delete-user' => ['controller' => 'AdminController', 'method' => 'deleteUser'],
                'admin/view-user' => ['controller' => 'AdminController', 'method' => 'viewUser'],
            ];
            
            if (isset($dynamicRoutes[$basePath])) {
                return [
                    'controller' => $dynamicRoutes[$basePath]['controller'],
                    'method' => $dynamicRoutes[$basePath]['method'],
                    'params' => [$param]
                ];
            }
        }
        
        return null;
    }

    /**
     * تحليل المسار بالطريقة التقليدية (controller/method/params)
     */
    private function parseTraditionalUrl($url)
    {
        // تحديد المتحكم
        if (isset($url[0]) && !empty($url[0])) {
            $controllerName = ucfirst($url[0]) . 'Controller';
            if (file_exists('../app/controllers/' . $controllerName . '.php')) {
                $this->controller = $controllerName;
                unset($url[0]);
            }
        }

        // تحديد الطريقة
        if (isset($url[1]) && !empty($url[1])) {
            $this->method = $url[1];
            unset($url[1]);
        }

        // تحديد المعاملات
        $this->params = $url ? array_values($url) : [];
    }

    /**
     * تحميل المتحكم وتنفيذ الطريقة
     */
    private function loadController()
    {
        // تحميل ملف المتحكم
        $controllerFile = '../app/controllers/' . $this->controller . '.php';
        
        if (file_exists($controllerFile)) {
            require_once $controllerFile;
            
            // إنشاء مثيل من المتحكم
            $this->controller = new $this->controller;
            
            // التحقق من وجود الطريقة
            if (method_exists($this->controller, $this->method)) {
                call_user_func_array([$this->controller, $this->method], $this->params);
            } else {
                $this->show404();
            }
        } else {
            $this->show404();
        }
    }

    /**
     * عرض صفحة 404
     */
    private function show404()
    {
        http_response_code(404);
        require_once '../views/errors/404.php';
        exit;
    }

    /**
     * إضافة مسار مخصص
     */
    public static function addRoute($path, $controller, $method)
    {
        $this->routes[$path] = [
            'controller' => $controller,
            'method' => $method
        ];
    }

    /**
     * الحصول على URL الأساسي
     */
    public static function getBaseUrl()
    {
        return APP_URL;
    }

    /**
     * إنشاء رابط
     */
    public static function url($path = '')
    {
        $baseUrl = rtrim(APP_URL, '/');
        $path = ltrim($path, '/');
        return $baseUrl . '/' . $path;
    }

    /**
     * إعادة التوجيه
     */
    public static function redirect($path = '')
    {
        $url = self::url($path);
        header("Location: $url");
        exit;
    }

    /**
     * التحقق من نوع الطلب
     */
    public static function isPost()
    {
        return $_SERVER['REQUEST_METHOD'] === 'POST';
    }

    public static function isGet()
    {
        return $_SERVER['REQUEST_METHOD'] === 'GET';
    }

    public static function isAjax()
    {
        return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    /**
     * الحصول على بيانات POST بشكل آمن
     */
    public static function post($key = null, $default = null)
    {
        if ($key === null) {
            return $_POST;
        }
        return isset($_POST[$key]) ? $_POST[$key] : $default;
    }

    /**
     * الحصول على بيانات GET بشكل آمن
     */
    public static function get($key = null, $default = null)
    {
        if ($key === null) {
            return $_GET;
        }
        return isset($_GET[$key]) ? $_GET[$key] : $default;
    }

    /**
     * التحقق من المسار الحالي
     */
    public static function isCurrentPath($path)
    {
        $currentUrl = isset($_GET['url']) ? $_GET['url'] : '';
        $currentUrl = rtrim($currentUrl, '/');
        $path = rtrim($path, '/');
        
        return $currentUrl === $path;
    }
}
