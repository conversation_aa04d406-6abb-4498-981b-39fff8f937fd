<?php
// تضمين قالب المدير
$pageTitle = 'سجل النشاطات';
$breadcrumbs = [
    ['title' => 'لوحة التحكم', 'url' => App::url('admin/dashboard')],
    ['title' => 'سجل النشاطات', 'url' => '#']
];

// بدء تخزين المحتوى
ob_start();
?>

<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-dark">
                        <i class="fas fa-history text-primary me-2"></i>
                        سجل النشاطات
                    </h1>
                    <p class="text-muted mb-0">مراقبة وتتبع جميع الأنشطة في النظام</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="exportActivityLog()">
                        <i class="fas fa-download me-1"></i>
                        تصدير السجل
                    </button>
                    <button class="btn btn-outline-danger" onclick="clearActivityLog()">
                        <i class="fas fa-trash me-1"></i>
                        مسح السجل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card bg-gradient-primary text-white">
                <div class="stats-content">
                    <div class="stats-number" id="totalActivities">0</div>
                    <div class="stats-label">إجمالي النشاطات</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-gradient-success text-white">
                <div class="stats-content">
                    <div class="stats-number" id="todayActivities">0</div>
                    <div class="stats-label">نشاطات اليوم</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-gradient-warning text-white">
                <div class="stats-content">
                    <div class="stats-number" id="activeUsers">0</div>
                    <div class="stats-label">المستخدمين النشطين</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-users"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-gradient-info text-white">
                <div class="stats-content">
                    <div class="stats-number" id="errorCount">0</div>
                    <div class="stats-label">الأخطاء</div>
                </div>
                <div class="stats-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر البحث
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">نوع النشاط</label>
                            <select class="form-select" id="activityType">
                                <option value="">جميع الأنواع</option>
                                <option value="login">تسجيل الدخول</option>
                                <option value="logout">تسجيل الخروج</option>
                                <option value="create">إنشاء</option>
                                <option value="update">تحديث</option>
                                <option value="delete">حذف</option>
                                <option value="view">عرض</option>
                                <option value="export">تصدير</option>
                                <option value="import">استيراد</option>
                                <option value="error">خطأ</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">نوع المستخدم</label>
                            <select class="form-select" id="userType">
                                <option value="">جميع المستخدمين</option>
                                <option value="admin">مدير</option>
                                <option value="doctor">طبيب</option>
                                <option value="patient">مريض</option>
                                <option value="pharmacist">صيدلي</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="startDate">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="endDate">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">البحث في النص</label>
                            <input type="text" class="form-control" id="searchText" placeholder="ابحث في النشاطات...">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">مستوى الأهمية</label>
                            <select class="form-select" id="severity">
                                <option value="">جميع المستويات</option>
                                <option value="low">منخفض</option>
                                <option value="medium">متوسط</option>
                                <option value="high">عالي</option>
                                <option value="critical">حرج</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button class="btn btn-primary" onclick="loadActivityLog()">
                                    <i class="fas fa-search me-1"></i>
                                    بحث
                                </button>
                                <button class="btn btn-outline-secondary" onclick="resetFilters()">
                                    <i class="fas fa-undo me-1"></i>
                                    إعادة تعيين
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول النشاطات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        قائمة النشاطات
                    </h5>
                    <div class="d-flex gap-2">
                        <select class="form-select form-select-sm" id="pageSize" style="width: auto;">
                            <option value="10">10</option>
                            <option value="25" selected>25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <button class="btn btn-sm btn-outline-primary" onclick="refreshActivityLog()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="activityTable">
                            <thead class="table-light">
                                <tr>
                                    <th>
                                        <input type="checkbox" class="form-check-input" id="selectAll" onchange="toggleSelectAll()">
                                    </th>
                                    <th>التاريخ والوقت</th>
                                    <th>المستخدم</th>
                                    <th>نوع النشاط</th>
                                    <th>الوصف</th>
                                    <th>IP العنوان</th>
                                    <th>مستوى الأهمية</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="activityTableBody">
                                <!-- سيتم تحميل البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- رسالة التحميل -->
                    <div id="loadingMessage" class="text-center py-4" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2 text-muted">جاري تحميل النشاطات...</p>
                    </div>
                    
                    <!-- رسالة عدم وجود بيانات -->
                    <div id="noDataMessage" class="text-center py-4" style="display: none;">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد نشاطات</h5>
                        <p class="text-muted">لم يتم العثور على نشاطات تطابق معايير البحث</p>
                    </div>
                    
                    <!-- ترقيم الصفحات -->
                    <nav aria-label="ترقيم النشاطات" id="paginationContainer">
                        <!-- سيتم إنشاء الترقيم هنا -->
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal تفاصيل النشاط -->
<div class="modal fade" id="activityDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>
                    تفاصيل النشاط
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="activityDetailsContent">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="exportActivityDetails()">
                    <i class="fas fa-download me-1"></i>
                    تصدير التفاصيل
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تأكيد الحذف
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف النشاط المحدد؟</p>
                <p class="text-muted small">لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" onclick="confirmDeleteActivity()">
                    <i class="fas fa-trash me-1"></i>
                    حذف
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// متغيرات عامة
let currentPage = 1;
let totalPages = 1;
let selectedActivities = [];
let currentFilters = {};

// تحميل النشاطات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadActivityLog();
    updateStats();
    
    // إعداد التواريخ الافتراضية
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    document.getElementById('endDate').value = today.toISOString().split('T')[0];
    document.getElementById('startDate').value = thirtyDaysAgo.toISOString().split('T')[0];
});

// تحميل سجل النشاطات
function loadActivityLog(page = 1) {
    showLoading();
    
    const filters = {
        type: document.getElementById('activityType').value,
        user_type: document.getElementById('userType').value,
        start_date: document.getElementById('startDate').value,
        end_date: document.getElementById('endDate').value,
        search: document.getElementById('searchText').value,
        severity: document.getElementById('severity').value,
        page: page,
        page_size: document.getElementById('pageSize').value
    };
    
    currentFilters = filters;
    
    fetch(`<?= App::url('admin/activity-log') ?>?action=list`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(filters)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            renderActivityLog(data.activities);
            renderPagination(data.pagination);
            updateStats(data.stats);
        } else {
            showAlert('خطأ في تحميل النشاطات: ' + data.message, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Error:', error);
        showAlert('خطأ في الاتصال بالخادم', 'error');
    });
}

// عرض النشاطات
function renderActivityLog(activities) {
    const tbody = document.getElementById('activityTableBody');
    const noDataMessage = document.getElementById('noDataMessage');
    
    if (!activities || activities.length === 0) {
        tbody.innerHTML = '';
        noDataMessage.style.display = 'block';
        return;
    }
    
    noDataMessage.style.display = 'none';
    
    tbody.innerHTML = activities.map(activity => `
        <tr>
            <td>
                <input type="checkbox" class="form-check-input activity-checkbox" 
                       value="${activity.id}" onchange="toggleActivitySelection(${activity.id})">
            </td>
            <td>
                <div class="d-flex flex-column">
                    <span class="fw-bold">${formatDateTime(activity.created_at)}</span>
                    <small class="text-muted">${getTimeAgo(activity.created_at)}</small>
                </div>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="avatar-sm me-2">
                        <i class="fas fa-user"></i>
                    </div>
                    <div>
                        <div class="fw-bold">${activity.user_name || 'غير محدد'}</div>
                        <small class="text-muted">${getUserTypeLabel(activity.user_type)}</small>
                    </div>
                </div>
            </td>
            <td>
                <span class="badge ${getActivityTypeBadge(activity.activity_type)}">
                    ${getActivityTypeLabel(activity.activity_type)}
                </span>
            </td>
            <td>
                <div class="text-truncate" style="max-width: 200px;" title="${activity.description}">
                    ${activity.description}
                </div>
            </td>
            <td>
                <code class="small">${activity.ip_address || 'غير محدد'}</code>
            </td>
            <td>
                <span class="badge ${getSeverityBadge(activity.severity)}">
                    ${getSeverityLabel(activity.severity)}
                </span>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="viewActivityDetails(${activity.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteActivity(${activity.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// عرض ترقيم الصفحات
function renderPagination(pagination) {
    const container = document.getElementById('paginationContainer');
    
    if (!pagination || pagination.total_pages <= 1) {
        container.innerHTML = '';
        return;
    }
    
    currentPage = pagination.current_page;
    totalPages = pagination.total_pages;
    
    let paginationHTML = `
        <ul class="pagination justify-content-center mb-0">
            <li class="page-item ${pagination.current_page <= 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadActivityLog(${pagination.current_page - 1})">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
    `;
    
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadActivityLog(${i})">${i}</a>
            </li>
        `;
    }
    
    paginationHTML += `
            <li class="page-item ${pagination.current_page >= pagination.total_pages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadActivityLog(${pagination.current_page + 1})">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        </ul>
    `;
    
    container.innerHTML = paginationHTML;
}

// تحديث الإحصائيات
function updateStats(stats = null) {
    if (stats) {
        document.getElementById('totalActivities').textContent = stats.total || 0;
        document.getElementById('todayActivities').textContent = stats.today || 0;
        document.getElementById('activeUsers').textContent = stats.active_users || 0;
        document.getElementById('errorCount').textContent = stats.errors || 0;
    }
}

// عرض تفاصيل النشاط
function viewActivityDetails(activityId) {
    fetch(`<?= App::url('admin/activity-log') ?>?action=details&id=${activityId}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const activity = data.activity;
            document.getElementById('activityDetailsContent').innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات النشاط</h6>
                        <table class="table table-sm">
                            <tr><td><strong>المعرف:</strong></td><td>${activity.id}</td></tr>
                            <tr><td><strong>النوع:</strong></td><td>${getActivityTypeLabel(activity.activity_type)}</td></tr>
                            <tr><td><strong>التاريخ:</strong></td><td>${formatDateTime(activity.created_at)}</td></tr>
                            <tr><td><strong>مستوى الأهمية:</strong></td><td>${getSeverityLabel(activity.severity)}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>معلومات المستخدم</h6>
                        <table class="table table-sm">
                            <tr><td><strong>الاسم:</strong></td><td>${activity.user_name || 'غير محدد'}</td></tr>
                            <tr><td><strong>النوع:</strong></td><td>${getUserTypeLabel(activity.user_type)}</td></tr>
                            <tr><td><strong>IP العنوان:</strong></td><td>${activity.ip_address || 'غير محدد'}</td></tr>
                            <tr><td><strong>User Agent:</strong></td><td>${activity.user_agent || 'غير محدد'}</td></tr>
                        </table>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>الوصف</h6>
                        <p class="border rounded p-3 bg-light">${activity.description}</p>
                    </div>
                </div>
                ${activity.details ? `
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>التفاصيل الإضافية</h6>
                        <pre class="border rounded p-3 bg-light">${JSON.stringify(JSON.parse(activity.details), null, 2)}</pre>
                    </div>
                </div>
                ` : ''}
            `;
            
            new bootstrap.Modal(document.getElementById('activityDetailsModal')).show();
        } else {
            showAlert('خطأ في تحميل تفاصيل النشاط', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال بالخادم', 'error');
    });
}

// حذف نشاط
function deleteActivity(activityId) {
    selectedActivities = [activityId];
    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// تأكيد حذف النشاط
function confirmDeleteActivity() {
    if (selectedActivities.length === 0) return;
    
    fetch(`<?= App::url('admin/activity-log') ?>?action=delete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ ids: selectedActivities })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('تم حذف النشاط بنجاح', 'success');
            loadActivityLog(currentPage);
            selectedActivities = [];
            bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal')).hide();
        } else {
            showAlert('خطأ في حذف النشاط: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('خطأ في الاتصال بالخادم', 'error');
    });
}

// تبديل تحديد النشاط
function toggleActivitySelection(activityId) {
    const index = selectedActivities.indexOf(activityId);
    if (index > -1) {
        selectedActivities.splice(index, 1);
    } else {
        selectedActivities.push(activityId);
    }
}

// تبديل تحديد الكل
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.activity-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
        if (selectAll.checked) {
            selectedActivities.push(parseInt(checkbox.value));
        } else {
            selectedActivities = [];
        }
    });
}

// إعادة تعيين الفلاتر
function resetFilters() {
    document.getElementById('activityType').value = '';
    document.getElementById('userType').value = '';
    document.getElementById('searchText').value = '';
    document.getElementById('severity').value = '';
    
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    document.getElementById('endDate').value = today.toISOString().split('T')[0];
    document.getElementById('startDate').value = thirtyDaysAgo.toISOString().split('T')[0];
    
    loadActivityLog();
}

// تحديث النشاطات
function refreshActivityLog() {
    loadActivityLog(currentPage);
}

// تصدير سجل النشاطات
function exportActivityLog() {
    const filters = { ...currentFilters };
    filters.export = true;
    
    const queryString = new URLSearchParams(filters).toString();
    window.open(`<?= App::url('admin/activity-log') ?>?action=export&${queryString}`, '_blank');
}

// تصدير تفاصيل النشاط
function exportActivityDetails() {
    // سيتم تنفيذ التصدير هنا
    showAlert('سيتم إضافة ميزة التصدير قريباً', 'info');
}

// مسح سجل النشاطات
function clearActivityLog() {
    if (confirm('هل أنت متأكد من مسح جميع النشاطات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        fetch(`<?= App::url('admin/activity-log') ?>?action=clear`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('تم مسح سجل النشاطات بنجاح', 'success');
                loadActivityLog();
            } else {
                showAlert('خطأ في مسح السجل: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('خطأ في الاتصال بالخادم', 'error');
        });
    }
}

// دوال مساعدة
function showLoading() {
    document.getElementById('loadingMessage').style.display = 'block';
    document.getElementById('activityTable').style.display = 'none';
}

function hideLoading() {
    document.getElementById('loadingMessage').style.display = 'none';
    document.getElementById('activityTable').style.display = 'table';
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('ar-SA');
}

function getTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;
    
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 60) return `منذ ${minutes} دقيقة`;
    if (hours < 24) return `منذ ${hours} ساعة`;
    return `منذ ${days} يوم`;
}

function getActivityTypeLabel(type) {
    const types = {
        'login': 'تسجيل الدخول',
        'logout': 'تسجيل الخروج',
        'create': 'إنشاء',
        'update': 'تحديث',
        'delete': 'حذف',
        'view': 'عرض',
        'export': 'تصدير',
        'import': 'استيراد',
        'error': 'خطأ'
    };
    return types[type] || type;
}

function getActivityTypeBadge(type) {
    const badges = {
        'login': 'bg-success',
        'logout': 'bg-secondary',
        'create': 'bg-primary',
        'update': 'bg-warning',
        'delete': 'bg-danger',
        'view': 'bg-info',
        'export': 'bg-success',
        'import': 'bg-primary',
        'error': 'bg-danger'
    };
    return badges[type] || 'bg-secondary';
}

function getUserTypeLabel(type) {
    const types = {
        'admin': 'مدير',
        'doctor': 'طبيب',
        'patient': 'مريض',
        'pharmacist': 'صيدلي'
    };
    return types[type] || type;
}

function getSeverityLabel(severity) {
    const severities = {
        'low': 'منخفض',
        'medium': 'متوسط',
        'high': 'عالي',
        'critical': 'حرج'
    };
    return severities[severity] || severity;
}

function getSeverityBadge(severity) {
    const badges = {
        'low': 'bg-success',
        'medium': 'bg-warning',
        'high': 'bg-danger',
        'critical': 'bg-dark'
    };
    return badges[severity] || 'bg-secondary';
}

function showAlert(message, type = 'info') {
    // يمكن استخدام مكتبة للتنبيهات أو إنشاء تنبيه مخصص
    alert(message);
}
</script>

<?php
// إنهاء تخزين المحتوى
$content = ob_get_clean();
// تضمين قالب المدير
include __DIR__ . '/../layouts/admin.php';
?> 