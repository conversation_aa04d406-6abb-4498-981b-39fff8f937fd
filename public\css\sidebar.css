/**
 * أنماط الشريط الجانبي المشتركة
 * HealthKey Sidebar Common Styles
 */

/* ===== الشريط الجانبي الأساسي ===== */
.sidebar-admin,
.sidebar-doctor,
.sidebar-patient,
.sidebar-pharmacist {
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    height: 100vh;
    width: 280px;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    box-shadow: -4px 0 20px rgba(0,0,0,0.15);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-y: auto;
}

/* ===== رأس الشريط الجانبي ===== */
.sidebar-header {
    padding: 1.5rem 1rem 1rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(0,0,0,0.1);
}

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.sidebar-brand i {
    font-size: 1.5rem;
}

/* ألوان مختلفة لكل نوع مستخدم */
.sidebar-admin .sidebar-brand i { color: #2c5aa0; }
.sidebar-doctor .sidebar-brand i { color: #e74c3c; }
.sidebar-patient .sidebar-brand i { color: #27ae60; }
.sidebar-pharmacist .sidebar-brand i { color: #9b59b6; }

.brand-text {
    font-size: 1.25rem;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: rgba(255,255,255,0.7);
    font-size: 1.1rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    min-height: 36px;
}

.sidebar-toggle:hover {
    color: #ffffff;
    background: rgba(255,255,255,0.1);
    transform: scale(1.1);
}

.sidebar-toggle:active {
    transform: scale(0.95);
}

.sidebar-toggle i {
    transition: transform 0.2s ease;
}

.sidebar-toggle:hover i {
    transform: rotate(90deg);
}

/* ===== معلومات المستخدم ===== */
.sidebar-user {
    padding: 1rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: rgba(0,0,0,0.05);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 1.25rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

/* ألوان مختلفة لكل نوع مستخدم */
.sidebar-admin .user-avatar { background: linear-gradient(135deg, #2c5aa0, #4a7bc8); }
.sidebar-doctor .user-avatar { background: linear-gradient(135deg, #e74c3c, #c0392b); }
.sidebar-patient .user-avatar { background: linear-gradient(135deg, #27ae60, #2ecc71); }
.sidebar-pharmacist .user-avatar { background: linear-gradient(135deg, #9b59b6, #8e44ad); }

.user-info {
    flex: 1;
}

.user-name {
    font-weight: 600;
    color: #ffffff;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.user-role {
    font-size: 0.75rem;
    color: rgba(255,255,255,0.7);
}

/* ===== أقسام التنقل ===== */
.nav-section {
    padding: 0.75rem 1rem 0.5rem;
    margin-top: 0.5rem;
}

.nav-section-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: rgba(255,255,255,0.5);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: 0.5rem;
}

/* ===== عناصر التنقل ===== */
.sidebar-nav {
    flex: 1;
    padding: 0.5rem 0;
}

.sidebar-admin .nav-item,
.sidebar-doctor .nav-item,
.sidebar-patient .nav-item,
.sidebar-pharmacist .nav-item {
    margin: 0.125rem 0.75rem;
}

.sidebar-admin .nav-link,
.sidebar-doctor .nav-link,
.sidebar-patient .nav-link,
.sidebar-pharmacist .nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.sidebar-admin .nav-link::before,
.sidebar-doctor .nav-link::before,
.sidebar-patient .nav-link::before,
.sidebar-pharmacist .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1));
    transition: width 0.3s ease;
}

.sidebar-admin .nav-link:hover::before,
.sidebar-doctor .nav-link:hover::before,
.sidebar-patient .nav-link:hover::before,
.sidebar-pharmacist .nav-link:hover::before {
    width: 100%;
}

.sidebar-admin .nav-link:hover,
.sidebar-doctor .nav-link:hover,
.sidebar-patient .nav-link:hover,
.sidebar-pharmacist .nav-link:hover {
    color: #ffffff;
    background: rgba(255,255,255,0.1);
    transform: translateX(-4px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

/* حالات نشطة مختلفة لكل نوع مستخدم */
.sidebar-admin .nav-link.active {
    background: linear-gradient(135deg, #2c5aa0, #4a7bc8);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(44, 90, 160, 0.4);
    transform: translateX(-4px);
}

.sidebar-doctor .nav-link.active {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.4);
    transform: translateX(-4px);
}

.sidebar-patient .nav-link.active {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.4);
    transform: translateX(-4px);
}

.sidebar-pharmacist .nav-link.active {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(155, 89, 182, 0.4);
    transform: translateX(-4px);
}

.sidebar-admin .nav-link.active::before,
.sidebar-doctor .nav-link.active::before,
.sidebar-patient .nav-link.active::before,
.sidebar-pharmacist .nav-link.active::before {
    display: none;
}

.sidebar-admin .nav-link i,
.sidebar-doctor .nav-link i,
.sidebar-patient .nav-link i,
.sidebar-pharmacist .nav-link i {
    font-size: 1.1rem;
    min-width: 20px;
    text-align: center;
}

.sidebar-admin .nav-link span,
.sidebar-doctor .nav-link span,
.sidebar-patient .nav-link span,
.sidebar-pharmacist .nav-link span {
    flex: 1;
    font-weight: 500;
}

/* ===== شارات التنقل ===== */
.nav-badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.7rem;
    font-weight: 600;
    border-radius: 12px;
    background: rgba(255,255,255,0.2);
    color: #ffffff;
    min-width: 20px;
    text-align: center;
}

.nav-badge.bg-success {
    background: #198754 !important;
}

.nav-badge.bg-info {
    background: #0dcaf0 !important;
}

.nav-badge.bg-warning {
    background: #ffc107 !important;
    color: #212529;
}

.nav-badge.bg-danger {
    background: #dc3545 !important;
}

/* ===== تذييل الشريط الجانبي ===== */
.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid rgba(255,255,255,0.1);
    background: rgba(0,0,0,0.1);
}

.system-status {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: rgba(255,255,255,0.8);
}

.status-item i {
    font-size: 0.7rem;
}

.status-item .text-success {
    color: #28a745 !important;
}

/* ===== التخطيط المتجاوب ===== */
@media (max-width: 768px) {
    .sidebar-admin,
    .sidebar-doctor,
    .sidebar-patient,
    .sidebar-pharmacist {
        transform: translateX(100%);
        width: 280px;
        position: fixed;
        top: 0;
        right: 0;
        z-index: 1050;
        transition: transform 0.3s ease;
        display: flex;
        flex-direction: column;
    }
    
    .sidebar-admin.show,
    .sidebar-doctor.show,
    .sidebar-patient.show,
    .sidebar-pharmacist.show {
        transform: translateX(0) !important;
        box-shadow: -4px 0 20px rgba(0,0,0,0.3);
        visibility: visible !important;
        opacity: 1 !important;
    }
    
    .sidebar-admin.collapsed,
    .sidebar-doctor.collapsed,
    .sidebar-patient.collapsed,
    .sidebar-pharmacist.collapsed {
        transform: translateX(100%);
    }
    
    /* إضافة overlay للخلفية */
    body.sidebar-open::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 1040;
    }
}

/* ===== تخصيص شريط التمرير ===== */
.sidebar-admin::-webkit-scrollbar,
.sidebar-doctor::-webkit-scrollbar,
.sidebar-patient::-webkit-scrollbar,
.sidebar-pharmacist::-webkit-scrollbar {
    width: 6px;
}

.sidebar-admin::-webkit-scrollbar-track,
.sidebar-doctor::-webkit-scrollbar-track,
.sidebar-patient::-webkit-scrollbar-track,
.sidebar-pharmacist::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
    border-radius: 3px;
}

.sidebar-admin::-webkit-scrollbar-thumb,
.sidebar-doctor::-webkit-scrollbar-thumb,
.sidebar-patient::-webkit-scrollbar-thumb,
.sidebar-pharmacist::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
}

.sidebar-admin::-webkit-scrollbar-thumb:hover,
.sidebar-doctor::-webkit-scrollbar-thumb:hover,
.sidebar-patient::-webkit-scrollbar-thumb:hover,
.sidebar-pharmacist::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5);
}

/* ===== تحسينات إضافية ===== */
.sidebar-admin .nav-link:focus,
.sidebar-doctor .nav-link:focus,
.sidebar-patient .nav-link:focus,
.sidebar-pharmacist .nav-link:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(255,255,255,0.3);
}

/* تأثيرات التحميل */
.sidebar-admin.loading,
.sidebar-doctor.loading,
.sidebar-patient.loading,
.sidebar-pharmacist.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* تحسين الأداء */
.sidebar-admin *,
.sidebar-doctor *,
.sidebar-patient *,
.sidebar-pharmacist * {
    will-change: transform;
}

/* زر فتح الشريط الجانبي للموبايل */
.mobile-sidebar-toggle {
    position: sticky;
    top: 1rem;
    z-index: 1000;
}

.mobile-sidebar-toggle .btn {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
}

.mobile-sidebar-toggle .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0,0,0,0.2);
}

.mobile-sidebar-toggle .btn:active {
    transform: translateY(0);
} 