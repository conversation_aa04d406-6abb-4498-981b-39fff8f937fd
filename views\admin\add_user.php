<?php
// صفحة إضافة مستخدم جديد
// تعتمد على نفس تنسيق صفحة التعديل
?>
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="bi bi-person-plus me-2"></i>إضافة مستخدم جديد</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?= App::url('admin/add-user') ?>" id="addUserForm">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="mb-3 text-primary"><i class="bi bi-person me-2"></i>البيانات الأساسية</h6>
                                <div class="mb-3">
                                    <label for="first_name" class="form-label">الاسم الأول <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="first_name" name="first_name" value="<?= htmlspecialchars(SessionHelper::getOldInput('first_name') ?? '') ?>" required>
                                    <?php if (SessionHelper::getValidationError('first_name')): ?>
                                        <div class="invalid-feedback d-block"><?= SessionHelper::getValidationError('first_name') ?></div>
                                    <?php endif; ?>
                                </div>
                                <div class="mb-3">
                                    <label for="last_name" class="form-label">الاسم الأخير <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="last_name" name="last_name" value="<?= htmlspecialchars(SessionHelper::getOldInput('last_name') ?? '') ?>" required>
                                    <?php if (SessionHelper::getValidationError('last_name')): ?>
                                        <div class="invalid-feedback d-block"><?= SessionHelper::getValidationError('last_name') ?></div>
                                    <?php endif; ?>
                                </div>
                                <div class="mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control" id="email" name="email" value="<?= htmlspecialchars(SessionHelper::getOldInput('email') ?? '') ?>" required>
                                    <?php if (SessionHelper::getValidationError('email')): ?>
                                        <div class="invalid-feedback d-block"><?= SessionHelper::getValidationError('email') ?></div>
                                    <?php endif; ?>
                                </div>
                                <div class="mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" value="<?= htmlspecialchars(SessionHelper::getOldInput('phone') ?? '') ?>">
                                    <?php if (SessionHelper::getValidationError('phone')): ?>
                                        <div class="invalid-feedback d-block"><?= SessionHelper::getValidationError('phone') ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="mb-3 text-primary"><i class="bi bi-card-text me-2"></i>البيانات الإضافية</h6>
                                <div class="mb-3">
                                    <label for="user_type" class="form-label">نوع المستخدم <span class="text-danger">*</span></label>
                                    <select class="form-select" id="user_type" name="user_type" required onchange="toggleExtraFields()">
                                        <option value="">اختر نوع المستخدم</option>
                                        <option value="patient" <?= (SessionHelper::getOldInput('user_type') === 'patient') ? 'selected' : '' ?>>مريض</option>
                                        <option value="doctor" <?= (SessionHelper::getOldInput('user_type') === 'doctor') ? 'selected' : '' ?>>طبيب</option>
                                        <option value="pharmacist" <?= (SessionHelper::getOldInput('user_type') === 'pharmacist') ? 'selected' : '' ?>>صيدلي</option>
                                        <option value="admin" <?= (SessionHelper::getOldInput('user_type') === 'admin') ? 'selected' : '' ?>>مدير</option>
                                    </select>
                                    <?php if (SessionHelper::getValidationError('user_type')): ?>
                                        <div class="invalid-feedback d-block"><?= SessionHelper::getValidationError('user_type') ?></div>
                                    <?php endif; ?>
                                </div>
                                <div class="mb-3">
                                    <label for="national_id" class="form-label">رقم الهوية الوطنية</label>
                                    <input type="text" class="form-control" id="national_id" name="national_id" value="<?= htmlspecialchars(SessionHelper::getOldInput('national_id') ?? '') ?>">
                                    <?php if (SessionHelper::getValidationError('national_id')): ?>
                                        <div class="invalid-feedback d-block"><?= SessionHelper::getValidationError('national_id') ?></div>
                                    <?php endif; ?>
                                </div>
                                <div class="mb-3">
                                    <label for="date_of_birth" class="form-label">تاريخ الميلاد</label>
                                    <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" value="<?= SessionHelper::getOldInput('date_of_birth') ?>">
                                    <?php if (SessionHelper::getValidationError('date_of_birth')): ?>
                                        <div class="invalid-feedback d-block"><?= SessionHelper::getValidationError('date_of_birth') ?></div>
                                    <?php endif; ?>
                                </div>
                                <div class="mb-3">
                                    <label for="gender" class="form-label">الجنس</label>
                                    <select class="form-select" id="gender" name="gender">
                                        <option value="">اختر الجنس</option>
                                        <option value="male" <?= (SessionHelper::getOldInput('gender') === 'male') ? 'selected' : '' ?>>ذكر</option>
                                        <option value="female" <?= (SessionHelper::getOldInput('gender') === 'female') ? 'selected' : '' ?>>أنثى</option>
                                    </select>
                                    <?php if (SessionHelper::getValidationError('gender')): ?>
                                        <div class="invalid-feedback d-block"><?= SessionHelper::getValidationError('gender') ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="address" class="form-label">العنوان</label>
                                    <textarea class="form-control" id="address" name="address" rows="3"><?= htmlspecialchars(SessionHelper::getOldInput('address') ?? '') ?></textarea>
                                    <?php if (SessionHelper::getValidationError('address')): ?>
                                        <div class="invalid-feedback d-block"><?= SessionHelper::getValidationError('address') ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="emergency_contact" class="form-label">جهة الاتصال في الطوارئ</label>
                                    <input type="text" class="form-control" id="emergency_contact" name="emergency_contact" value="<?= htmlspecialchars(SessionHelper::getOldInput('emergency_contact') ?? '') ?>">
                                    <?php if (SessionHelper::getValidationError('emergency_contact')): ?>
                                        <div class="invalid-feedback d-block"><?= SessionHelper::getValidationError('emergency_contact') ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="emergency_phone" class="form-label">هاتف الطوارئ</label>
                                    <input type="tel" class="form-control" id="emergency_phone" name="emergency_phone" value="<?= htmlspecialchars(SessionHelper::getOldInput('emergency_phone') ?? '') ?>">
                                    <?php if (SessionHelper::getValidationError('emergency_phone')): ?>
                                        <div class="invalid-feedback d-block"><?= SessionHelper::getValidationError('emergency_phone') ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <!-- حقول الطبيب -->
                        <div class="row" id="doctorFields" style="display: none;">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="specialization" class="form-label">التخصص</label>
                                    <input type="text" class="form-control" id="specialization" name="specialization" value="<?= htmlspecialchars(SessionHelper::getOldInput('specialization') ?? '') ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="years_of_experience" class="form-label">سنوات الخبرة</label>
                                    <input type="number" min="0" class="form-control" id="years_of_experience" name="years_of_experience" value="<?= htmlspecialchars(SessionHelper::getOldInput('years_of_experience') ?? '') ?>">
                                </div>
                            </div>
                        </div>
                        <!-- حقول الصيدلي -->
                        <div class="row" id="pharmacistFields" style="display: none;">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="pharmacy_name" class="form-label">اسم الصيدلية</label>
                                    <input type="text" class="form-control" id="pharmacy_name" name="pharmacy_name" value="<?= htmlspecialchars(SessionHelper::getOldInput('pharmacy_name') ?? '') ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="pharmacy_address" class="form-label">عنوان الصيدلية</label>
                                    <input type="text" class="form-control" id="pharmacy_address" name="pharmacy_address" value="<?= htmlspecialchars(SessionHelper::getOldInput('pharmacy_address') ?? '') ?>">
                                </div>
                            </div>
                        </div>
                        
                        <!-- حالة النشاط -->
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                               <?= (SessionHelper::getOldInput('is_active') == 1) ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="is_active">
                                            المستخدم نشط
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex justify-content-between mt-4">
                            <button type="submit" class="btn btn-success px-4">
                                <i class="bi bi-plus-circle me-2"></i>إضافة المستخدم
                            </button>
                            <a href="<?= App::url('admin/users') ?>" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle me-2"></i>إلغاء
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
function toggleExtraFields() {
    var userType = document.getElementById('user_type').value;
    document.getElementById('doctorFields').style.display = (userType === 'doctor') ? 'flex' : 'none';
    document.getElementById('pharmacistFields').style.display = (userType === 'pharmacist') ? 'flex' : 'none';
}
document.addEventListener('DOMContentLoaded', function() {
    toggleExtraFields();
});
</script> 