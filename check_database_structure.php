<?php
// تضمين الملفات المطلوبة
require_once 'app/core/Database.php';

// إنشاء اتصال بقاعدة البيانات
$db = Database::getInstance();

echo "<h2>فحص هيكل قاعدة البيانات</h2>";

try {
    // فحص جدول users
    echo "<h3>جدول users:</h3>";
    $columns = $db->select("DESCRIBE users");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";

    // فحص جدول appointments
    echo "<h3>جدول appointments:</h3>";
    $columns = $db->select("DESCRIBE appointments");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";

    // فحص جدول prescriptions
    echo "<h3>جدول prescriptions:</h3>";
    $columns = $db->select("DESCRIBE prescriptions");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";

    // فحص عدد السجلات في كل جدول
    echo "<h3>عدد السجلات:</h3>";
    $usersCount = $db->selectOne("SELECT COUNT(*) as count FROM users")['count'];
    $appointmentsCount = $db->selectOne("SELECT COUNT(*) as count FROM appointments")['count'];
    $prescriptionsCount = $db->selectOne("SELECT COUNT(*) as count FROM prescriptions")['count'];
    
    echo "<ul>";
    echo "<li>المستخدمين: $usersCount</li>";
    echo "<li>المواعيد: $appointmentsCount</li>";
    echo "<li>الوصفات الطبية: $prescriptionsCount</li>";
    echo "</ul>";

    echo "<h3>✅ تم فحص هيكل قاعدة البيانات بنجاح!</h3>";

} catch (Exception $e) {
    echo "<h3>❌ خطأ في فحص قاعدة البيانات:</h3>";
    echo "<p>الخطأ: " . $e->getMessage() . "</p>";
    echo "<p>الملف: " . $e->getFile() . "</p>";
    echo "<p>السطر: " . $e->getLine() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص هيكل قاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4>فحص هيكل قاعدة البيانات</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5>تعليمات الفحص:</h5>
                            <ol>
                                <li>راجع هيكل الجداول المعروضة أعلاه</li>
                                <li>تأكد من وجود جميع الأعمدة المطلوبة</li>
                                <li>إذا كانت هناك أعمدة مفقودة، قم بإضافتها</li>
                                <li>إذا كانت النتائج صحيحة، يمكنك الوصول إلى صفحة التحليلات</li>
                            </ol>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                            <a href="<?= App::url('admin/analytics') ?>" class="btn btn-primary">
                                <i class="bi bi-graph-up"></i> الانتقال إلى صفحة التحليلات
                            </a>
                            <a href="<?= App::url('admin/dashboard') ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> العودة إلى لوحة التحكم
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 