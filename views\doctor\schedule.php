<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-calendar-week me-2"></i>
            الجدول الزمني
        </h1>
        <p class="text-muted">عرض وإدارة الجدول الزمني للمواعيد</p>
    </div>
    <div>
        <a href="<?= App::url('doctor/appointments') ?>" class="btn btn-outline-primary">
            <i class="bi bi-calendar-check me-2"></i>
            المواعيد
        </a>
        <a href="<?= App::url('doctor/dashboard') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- Week Navigation -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="mb-0">
                    <i class="bi bi-calendar-range me-2"></i>
                    الأسبوع: <?= date('Y-m-d', strtotime($startOfWeek)) ?> - <?= date('Y-m-d', strtotime($endOfWeek)) ?>
                </h5>
            </div>
            <div class="col-md-6">
                <div class="d-flex justify-content-md-end gap-2">
                    <a href="<?= App::url('doctor/schedule?date=' . date('Y-m-d', strtotime('-1 week', strtotime($currentDate)))) ?>" 
                       class="btn btn-outline-secondary">
                        <i class="bi bi-chevron-left me-1"></i>
                        الأسبوع السابق
                    </a>
                    <a href="<?= App::url('doctor/schedule?date=' . date('Y-m-d')) ?>" 
                       class="btn btn-outline-primary">
                        <i class="bi bi-calendar-day me-1"></i>
                        هذا الأسبوع
                    </a>
                    <a href="<?= App::url('doctor/schedule?date=' . date('Y-m-d', strtotime('+1 week', strtotime($currentDate)))) ?>" 
                       class="btn btn-outline-secondary">
                        الأسبوع التالي
                        <i class="bi bi-chevron-right ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['total'] ?></h4>
                        <p class="mb-0">إجمالي المواعيد</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-check display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['this_week'] ?></h4>
                        <p class="mb-0">مواعيد هذا الأسبوع</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-week display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['today'] ?></h4>
                        <p class="mb-0">مواعيد اليوم</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-day display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count($timeSlots) ?></h4>
                        <p class="mb-0">فترات زمنية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Schedule Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-table me-2"></i>
            الجدول الزمني الأسبوعي
        </h5>
        <div>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                <i class="bi bi-printer me-1"></i>
                طباعة
            </button>
            <button type="button" class="btn btn-sm btn-outline-success" onclick="exportSchedule()">
                <i class="bi bi-download me-1"></i>
                تصدير
            </button>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-bordered mb-0 schedule-table">
                <thead class="table-light">
                    <tr>
                        <th style="width: 100px;">الوقت</th>
                        <?php foreach ($schedule as $dayKey => $day): ?>
                            <th class="text-center">
                                <div class="fw-bold"><?= $day['name'] ?></div>
                                <small class="text-muted"><?= date('Y-m-d', strtotime($day['date'])) ?></small>
                            </th>
                        <?php endforeach; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($timeSlots as $timeSlot): ?>
                        <tr>
                            <td class="text-center fw-bold bg-light">
                                <?= $timeSlot ?>
                            </td>
                            <?php foreach ($schedule as $dayKey => $day): ?>
                                <td class="schedule-cell" style="height: 80px; vertical-align: middle;">
                                    <?php 
                                    $appointment = $day['appointments'][$timeSlot];
                                    $isToday = $day['date'] === date('Y-m-d');
                                    $cellClass = $isToday ? 'table-warning' : '';
                                    ?>
                                    
                                    <?php if ($appointment): ?>
                                        <div class="appointment-card <?= $cellClass ?>" 
                                             onclick="viewAppointment(<?= $appointment['id'] ?>)"
                                             style="cursor: pointer;">
                                            <div class="appointment-header">
                                                <strong><?= htmlspecialchars($appointment['patient_name']) ?></strong>
                                                <span class="badge bg-<?= Appointment::getStatusColor($appointment['status']) ?> float-end">
                                                    <?= Appointment::getStatusLabel($appointment['status']) ?>
                                                </span>
                                            </div>
                                            <div class="appointment-body">
                                                <small class="text-muted">
                                                    <?= htmlspecialchars($appointment['reason'] ?? 'غير محدد') ?>
                                                </small>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="empty-slot <?= $cellClass ?> text-center">
                                            <small class="text-muted">
                                                <i class="bi bi-plus-circle"></i>
                                                <br>متاح
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                </td>
                            <?php endforeach; ?>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="bi bi-calendar-plus display-4 mb-3"></i>
                <h5>حجز موعد جديد</h5>
                <p class="mb-3">حجز موعد جديد في الجدول</p>
                <a href="<?= App::url('doctor/schedule-appointment') ?>" class="btn btn-light">
                    <i class="bi bi-plus-circle me-2"></i>
                    حجز موعد
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="bi bi-file-earmark-medical display-4 mb-3"></i>
                <h5>إضافة سجل طبي</h5>
                <p class="mb-3">إضافة سجل طبي جديد</p>
                <a href="<?= App::url('doctor/add-medical-record') ?>" class="btn btn-light">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة سجل
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="bi bi-prescription2 display-4 mb-3"></i>
                <h5>إنشاء وصفة طبية</h5>
                <p class="mb-3">إنشاء وصفة طبية جديدة</p>
                <a href="<?= App::url('doctor/create-prescription') ?>" class="btn btn-light">
                    <i class="bi bi-plus-circle me-2"></i>
                    إنشاء وصفة
                </a>
            </div>
        </div>
    </div>
</div>

<!-- View Appointment Modal -->
<div class="modal fade" id="viewAppointmentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-calendar-check me-2"></i>
                    تفاصيل الموعد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="appointmentDetails">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="editAppointment()">
                    <i class="bi bi-pencil me-2"></i>
                    تعديل
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.schedule-table {
    font-size: 0.875rem;
}

.schedule-cell {
    padding: 8px !important;
    position: relative;
}

.appointment-card {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border: 1px solid #2196f3;
    border-radius: 6px;
    padding: 8px;
    margin: 2px;
    transition: all 0.3s ease;
}

.appointment-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
}

.appointment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.appointment-body {
    font-size: 0.75rem;
}

.empty-slot {
    color: #6c757d;
    font-size: 0.75rem;
    padding: 8px;
}

.table-warning {
    background-color: #fff3cd !important;
}

@media print {
    .btn, .card-header, .modal {
        display: none !important;
    }
    
    .schedule-table {
        font-size: 0.75rem;
    }
    
    .appointment-card {
        background: #f8f9fa !important;
        border: 1px solid #dee2e6 !important;
    }
}
</style>

<script>
let currentAppointmentId = null;

function viewAppointment(appointmentId) {
    currentAppointmentId = appointmentId;
    
    // تحميل تفاصيل الموعد
    fetch(`<?= App::url('doctor/view-appointment/') ?>${appointmentId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('appointmentDetails').innerHTML = data.html;
                new bootstrap.Modal(document.getElementById('viewAppointmentModal')).show();
            } else {
                alert('فشل في تحميل تفاصيل الموعد');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحميل التفاصيل');
        });
}

function editAppointment() {
    if (currentAppointmentId) {
        window.location.href = `<?= App::url('doctor/edit-appointment/') ?>${currentAppointmentId}`;
    }
}

function exportSchedule() {
    const date = '<?= htmlspecialchars($currentDate) ?>';
    const url = `<?= App::url('doctor/export-schedule') ?>?date=${encodeURIComponent(date)}`;
    
    window.open(url, '_blank');
}

// تحسين التفاعل
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات بصرية للخلايا الفارغة
    const emptySlots = document.querySelectorAll('.empty-slot');
    emptySlots.forEach(slot => {
        slot.addEventListener('click', function() {
            const cell = this.closest('.schedule-cell');
            const dayDate = cell.getAttribute('data-date');
            const timeSlot = cell.getAttribute('data-time');
            
            if (dayDate && timeSlot) {
                window.location.href = `<?= App::url('doctor/schedule-appointment') ?>?date=${dayDate}&time=${timeSlot}`;
            }
        });
    });
    
    // إضافة بيانات للخلايا
    const scheduleCells = document.querySelectorAll('.schedule-cell');
    scheduleCells.forEach((cell, index) => {
        const row = Math.floor(index / 7);
        const col = index % 7;
        const timeSlot = '<?= implode("','", $timeSlots) ?>'.split(',')[row];
        const dayKeys = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        const dayKey = dayKeys[col];
        
        if (dayKey && timeSlot) {
            const dayDate = '<?= $schedule["monday"]["date"] ?>';
            const dayOffset = col;
            const cellDate = new Date(dayDate);
            cellDate.setDate(cellDate.getDate() + dayOffset);
            
            cell.setAttribute('data-date', cellDate.toISOString().split('T')[0]);
            cell.setAttribute('data-time', timeSlot);
        }
    });
});
</script> 