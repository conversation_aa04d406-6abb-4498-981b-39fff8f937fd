<?php
// اختبار صفحة المخزون
echo "<h1>اختبار صفحة المخزون</h1>";

// تعريف الثوابت المطلوبة
define('DB_HOST', 'localhost');
define('DB_NAME', 'healthkey');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');
define('APP_DEBUG', true);
define('APP_URL', 'http://localhost/HealthKey');

// محاكاة جلسة المستخدم
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'pharmacist';
$_SESSION['user'] = [
    'id' => 1,
    'first_name' => 'أحمد',
    'last_name' => 'محمد',
    'email' => '<EMAIL>',
    'user_type' => 'pharmacist'
];

try {
    require_once 'app/core/Database.php';
    require_once 'app/core/Controller.php';
    require_once 'app/core/App.php';
    require_once 'app/controllers/PharmacistController.php';
    
    echo "<h2>✅ تم تحميل الملفات بنجاح</h2>";
    
    // اختبار إنشاء متحكم الصيدلي
    $controller = new PharmacistController();
    echo "<h2>✅ تم إنشاء متحكم الصيدلي بنجاح</h2>";
    
    // اختبار دالة inventory
    echo "<h3>اختبار دالة inventory:</h3>";
    try {
        $controller->inventory();
        echo "<p style='color: green;'>✅ تم تنفيذ دالة inventory بنجاح</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في دالة inventory: " . $e->getMessage() . "</p>";
        echo "<p>تفاصيل الخطأ: " . $e->getTraceAsString() . "</p>";
    }
    
    // اختبار دالة getInventoryStats
    echo "<h3>اختبار دالة getInventoryStats:</h3>";
    try {
        $stats = $controller->getInventoryStats();
        echo "<p style='color: green;'>✅ تم تنفيذ دالة getInventoryStats بنجاح</p>";
        echo "<pre>" . print_r($stats, true) . "</pre>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في دالة getInventoryStats: " . $e->getMessage() . "</p>";
    }
    
    // اختبار دالة getInventoryCategories
    echo "<h3>اختبار دالة getInventoryCategories:</h3>";
    try {
        $categories = $controller->getInventoryCategories();
        echo "<p style='color: green;'>✅ تم تنفيذ دالة getInventoryCategories بنجاح</p>";
        echo "<pre>" . print_r($categories, true) . "</pre>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في دالة getInventoryCategories: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ عام: " . $e->getMessage() . "</p>";
    echo "<p>تفاصيل الخطأ: " . $e->getTraceAsString() . "</p>";
}
?> 