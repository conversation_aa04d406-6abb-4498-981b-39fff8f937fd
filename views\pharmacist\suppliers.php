<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">إدارة الموردين</h1>
        <p class="text-muted">إدارة موردي الأدوية والمستلزمات الطبية</p>
    </div>
    <div>
        <a href="<?= App::url('pharmacist/add-supplier') ?>" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>
            إضافة مورد جديد
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count($suppliers) ?></h4>
                        <p class="mb-0">إجمالي الموردين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-truck display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count(array_filter($suppliers, function($s) { return $s['status'] == 'active'; })) ?></h4>
                        <p class="mb-0">الموردين النشطين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count(array_filter($suppliers, function($s) { return $s['status'] == 'inactive'; })) ?></h4>
                        <p class="mb-0">الموردين غير النشطين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-pause-circle display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count(array_filter($suppliers, function($s) { return !empty($s['email']); })) ?></h4>
                        <p class="mb-0">لديهم بريد إلكتروني</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-envelope display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- البحث والفلترة -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-search me-2"></i>
                    البحث في الموردين
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" action="<?= App::url('pharmacist/suppliers') ?>" class="row g-3">
                    <div class="col-md-6">
                        <input type="text" name="search" class="form-control" 
                               placeholder="البحث في اسم المورد أو الشخص المسؤول..." 
                               value="<?= htmlspecialchars($search) ?>">
                    </div>
                    <div class="col-md-4">
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="active" <?= $currentStatus == 'active' ? 'selected' : '' ?>>نشط</option>
                            <option value="inactive" <?= $currentStatus == 'inactive' ? 'selected' : '' ?>>غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-search me-1"></i>
                            بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?= App::url('pharmacist/add-supplier') ?>" class="btn btn-success">
                        <i class="bi bi-plus-circle me-2"></i>
                        إضافة مورد جديد
                    </a>
                    <a href="<?= App::url('pharmacist/inventory') ?>" class="btn btn-info">
                        <i class="bi bi-box-seam me-2"></i>
                        إدارة المخزون
                    </a>
                    <a href="<?= App::url('pharmacist/medications') ?>" class="btn btn-warning">
                        <i class="bi bi-pills me-2"></i>
                        إدارة الأدوية
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Suppliers Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-list me-2"></i>
            قائمة الموردين
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($suppliers)): ?>
            <div class="text-center py-5">
                <i class="bi bi-truck display-1 text-muted"></i>
                <h4 class="mt-3">لا يوجد موردين</h4>
                <p class="text-muted">لم يتم إضافة أي موردين بعد</p>
                <a href="<?= App::url('pharmacist/add-supplier') ?>" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    إضافة أول مورد
                </a>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم المورد</th>
                            <th>الشخص المسؤول</th>
                            <th>البريد الإلكتروني</th>
                            <th>الهاتف</th>
                            <th>الحالة</th>
                            <th>تاريخ الإضافة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($suppliers as $supplier): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                            <i class="bi bi-building"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0"><?= htmlspecialchars($supplier['name']) ?></h6>
                                            <?php if (!empty($supplier['website'])): ?>
                                                <small class="text-muted">
                                                    <a href="<?= htmlspecialchars($supplier['website']) ?>" target="_blank">
                                                        <i class="bi bi-link-45deg me-1"></i>
                                                        الموقع الإلكتروني
                                                    </a>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if (!empty($supplier['contact_person'])): ?>
                                        <span class="badge bg-light text-dark">
                                            <i class="bi bi-person me-1"></i>
                                            <?= htmlspecialchars($supplier['contact_person']) ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">غير محدد</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($supplier['email'])): ?>
                                        <a href="mailto:<?= htmlspecialchars($supplier['email']) ?>" class="text-decoration-none">
                                            <i class="bi bi-envelope me-1"></i>
                                            <?= htmlspecialchars($supplier['email']) ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">غير محدد</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($supplier['phone'])): ?>
                                        <a href="tel:<?= htmlspecialchars($supplier['phone']) ?>" class="text-decoration-none">
                                            <i class="bi bi-telephone me-1"></i>
                                            <?= htmlspecialchars($supplier['phone']) ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">غير محدد</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($supplier['status'] == 'active'): ?>
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle me-1"></i>
                                            نشط
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">
                                            <i class="bi bi-pause-circle me-1"></i>
                                            غير نشط
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?= date('Y-m-d', strtotime($supplier['created_at'])) ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= App::url('pharmacist/edit-supplier/' . $supplier['id']) ?>" 
                                           class="btn btn-sm btn-outline-primary" title="تعديل">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="viewSupplier(<?= $supplier['id'] ?>)" title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="deleteSupplier(<?= $supplier['id'] ?>, '<?= htmlspecialchars($supplier['name']) ?>')" 
                                                title="حذف">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Supplier Details Modal -->
<div class="modal fade" id="supplierModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل المورد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="supplierModalBody">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <a href="#" class="btn btn-primary" id="editSupplierBtn">
                    <i class="bi bi-pencil me-2"></i>
                    تعديل
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function viewSupplier(id) {
    // هنا يمكن إضافة كود لعرض تفاصيل المورد
    // يمكن استخدام AJAX لجلب البيانات
    alert('عرض تفاصيل المورد رقم: ' + id);
}

function deleteSupplier(id, name) {
    if (confirm('هل أنت متأكد من حذف المورد "' + name + '"؟')) {
        window.location.href = '<?= App::url('pharmacist/delete-supplier/') ?>' + id;
    }
}

// تفعيل tooltips
var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
});
</script> 