<?php
/**
 * فحص هيكل جدول inventory
 */

require_once 'config.php';
require_once 'app/core/Database.php';

echo "<h1>فحص هيكل جدول inventory</h1>";

try {
    $db = Database::getInstance();
    
    // عرض أعمدة الجدول
    echo "<h3>أعمدة الجدول:</h3>";
    $columns = $db->select("SHOW COLUMNS FROM inventory");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // عرض عينة من البيانات
    $count = $db->selectOne("SELECT COUNT(*) as count FROM inventory")['count'];
    echo "✅ عدد السجلات في الجدول: $count<br>";
    
    if ($count > 0) {
        echo "<h3>عينة من البيانات:</h3>";
        $sample = $db->select("SELECT * FROM inventory LIMIT 3");
        echo "<pre>";
        print_r($sample);
        echo "</pre>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في فحص الجدول</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 