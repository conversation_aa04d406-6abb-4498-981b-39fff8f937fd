# صفحة العلامات الحيوية للمريض
# Patient Vitals Page

## نظرة عامة
تم إنشاء صفحة العلامات الحيوية للمريض لعرض وتتبع العلامات الحيوية الخاصة بالمريض بطريقة منظمة ومفهومة.

## المميزات

### 1. عرض الإحصائيات العامة
- إجمالي عدد القياسات
- تاريخ آخر قياس
- عدد العلامات المقاسة في آخر زيارة
- اسم آخر طبيب قام بالقياس

### 2. عرض أحدث العلامات الحيوية
- عرض العلامات الحيوية الأحدث مع التاريخ والوقت
- أيقونات مميزة لكل نوع من العلامات الحيوية
- تنسيق واضح للقيم والوحدات

### 3. إحصائيات العلامات الحيوية
- حساب القيم الدنيا والمتوسطة والعليا لكل علامة حيوية
- عرض الإحصائيات في بطاقات منظمة
- تغطية جميع أنواع العلامات الحيوية:
  - درجة الحرارة (°C)
  - ضغط الدم (mmHg)
  - معدل ضربات القلب (bpm)
  - معدل التنفس (/min)
  - الوزن (kg)
  - الطول (cm)

### 4. سجل العلامات الحيوية
- جدول تفاعلي يعرض جميع السجلات
- معلومات التاريخ والطبيب والتشخيص
- إمكانية عرض التفاصيل الكاملة لكل سجل
- إمكانية طباعة السجلات

## الملفات المضافة/المعدلة

### 1. المتحكم (Controller)
**الملف:** `app/controllers/PatientController.php`

#### الدوال المضافة:
- `vitals()`: الدالة الرئيسية لعرض صفحة العلامات الحيوية
- `getVitalsStats()`: حساب إحصائيات العلامات الحيوية

#### الميزات:
- استخراج السجلات الطبية التي تحتوي على العلامات الحيوية
- ترتيب السجلات حسب التاريخ (الأحدث أولاً)
- حساب الإحصائيات لكل نوع من العلامات الحيوية
- تنسيق البيانات للعرض

### 2. الصفحة (View)
**الملف:** `views/patient/vitals.php`

#### المكونات:
- **رأس الصفحة:** عنوان وأزرار الطباعة والعودة
- **بطاقات الإحصائيات:** عرض الأرقام المهمة
- **أحدث العلامات الحيوية:** عرض العلامات الأحدث مع الأيقونات
- **إحصائيات العلامات الحيوية:** بطاقات تعرض الإحصائيات لكل نوع
- **سجل العلامات الحيوية:** جدول تفاعلي مع جميع السجلات

### 3. التوجيه (Routing)
**الملف:** `app/core/App.php`

#### المسار المضاف:
```php
'patient/vitals' => ['controller' => 'PatientController', 'method' => 'vitals']
```

### 4. الشريط الجانبي (Sidebar)
**الملف:** `views/partials/sidebar_patient.php`

#### الرابط موجود بالفعل:
```php
<a class="nav-link <?= App::isCurrentPath('patient/vitals') ? 'active' : '' ?>"
   href="<?= App::url('patient/vitals') ?>" data-bs-toggle="tooltip" title="العلامات الحيوية">
```

## هيكل البيانات

### العلامات الحيوية في قاعدة البيانات
العلامات الحيوية مخزنة في حقل `vital_signs` في جدول `medical_records` كـ JSON:

```json
{
  "temperature": 37.2,
  "blood_pressure_systolic": 120,
  "blood_pressure_diastolic": 80,
  "heart_rate": 72,
  "respiratory_rate": 16,
  "weight": 70,
  "height": 175
}
```

### تنسيق العلامات الحيوية
يتم تنسيق العلامات الحيوية باستخدام دالة `MedicalRecord::formatVitalSigns()`:

```php
$formatted = [
    'درجة الحرارة' => '37.2 °C',
    'ضغط الدم' => '120/80 mmHg',
    'معدل ضربات القلب' => '72 bpm',
    'معدل التنفس' => '16 /min',
    'الوزن' => '70 kg',
    'الطول' => '175 cm'
];
```

## كيفية الاستخدام

### 1. الوصول للصفحة
- تسجيل دخول المريض
- الانتقال إلى الشريط الجانبي
- النقر على "العلامات الحيوية"

### 2. عرض البيانات
- الصفحة تعرض تلقائياً أحدث العلامات الحيوية
- يمكن مشاهدة الإحصائيات في البطاقات العلوية
- يمكن تصفح سجل العلامات الحيوية في الجدول السفلي

### 3. الطباعة والتصدير
- زر "طباعة" لطباعة الصفحة الحالية
- زر "تصدير" لتصدير سجل العلامات الحيوية (قيد التطوير)

## الاختبار

### ملف الاختبار
**الملف:** `test_vitals_page.php`

#### الميزات:
- محاكاة تسجيل دخول المريض
- اختبار استخراج البيانات
- عرض تفاصيل العلامات الحيوية
- اختبار الرابط

#### الاستخدام:
1. فتح الملف في المتصفح
2. مراجعة النتائج المعروضة
3. النقر على رابط الصفحة للاختبار المباشر

## الأمان والتحقق

### التحقق من الصلاحيات
- التحقق من تسجيل دخول المستخدم
- التحقق من نوع المستخدم (patient)
- التحقق من صحة البيانات المدخلة

### حماية البيانات
- تنظيف البيانات المعروضة باستخدام `htmlspecialchars()`
- التحقق من صحة معرفات السجلات
- حماية من هجمات SQL Injection

## التطوير المستقبلي

### الميزات المقترحة:
1. **الرسوم البيانية:** إضافة رسوم بيانية لتتبع تطور العلامات الحيوية
2. **التنبيهات:** إشعارات عند تجاوز العلامات الحيوية للقيم الطبيعية
3. **التصدير المتقدم:** تصدير البيانات بصيغ مختلفة (PDF, Excel)
4. **المقارنة:** مقارنة العلامات الحيوية بين فترات زمنية مختلفة
5. **التكامل:** ربط مع أجهزة قياس العلامات الحيوية

### التحسينات التقنية:
1. **الأداء:** تحسين استعلامات قاعدة البيانات
2. **التخزين المؤقت:** إضافة نظام تخزين مؤقت للبيانات
3. **الاستجابة:** تحسين تجربة المستخدم على الأجهزة المحمولة
4. **الوصول:** إضافة دعم للقراءة الشاشة للمستخدمين ذوي الاحتياجات الخاصة

## الدعم والمساعدة

### في حالة وجود مشاكل:
1. التحقق من وجود بيانات في جدول `medical_records`
2. التأكد من صحة تنسيق JSON في حقل `vital_signs`
3. مراجعة سجلات الأخطاء في الخادم
4. اختبار الاتصال بقاعدة البيانات

### للمطورين:
- مراجعة ملف `test_vitals_page.php` للاختبار
- استخدام أدوات التطوير في المتصفح لمراجعة الأخطاء
- التحقق من صحة المسارات في `App.php` 