<div class="lab-test-results">
    <div class="row">
        <div class="col-md-8">
            <!-- Test Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-droplet me-2"></i>
                        معلومات الفحص
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">اسم الفحص:</label>
                                <p class="mb-0"><?= htmlspecialchars($test['test_name']) ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">تاريخ الفحص:</label>
                                <p class="mb-0"><?= date('Y-m-d', strtotime($test['test_date'])) ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الطبيب:</label>
                                <p class="mb-0"><?= htmlspecialchars($test['doctor_name']) ?></p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">الحالة:</label>
                                <p class="mb-0">
                                    <span class="badge bg-<?= MedicalRecord::getLabTestStatusColor($test['status']) ?>">
                                        <?= MedicalRecord::getLabTestStatusLabel($test['status']) ?>
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <?php if (!empty($test['test_description'])): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold">وصف الفحص:</label>
                            <p class="mb-0"><?= htmlspecialchars($test['test_description']) ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($test['instructions'])): ?>
                        <div class="mb-3">
                            <label class="form-label fw-bold">تعليمات الفحص:</label>
                            <p class="mb-0"><?= nl2br(htmlspecialchars($test['instructions'])) ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Test Results -->
            <?php if ($test['status'] === 'completed' && !empty($test['results'])): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-clipboard-data me-2"></i>
                            نتائج الفحص
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>المعامل</th>
                                        <th>النتيجة</th>
                                        <th>النطاق الطبيعي</th>
                                        <th>الوحدة</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($test['results'] as $result): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($result['parameter']) ?></td>
                                            <td>
                                                <strong><?= htmlspecialchars($result['value']) ?></strong>
                                            </td>
                                            <td><?= htmlspecialchars($result['normal_range']) ?></td>
                                            <td><?= htmlspecialchars($result['unit']) ?></td>
                                            <td>
                                                <?php
                                                $status = $result['status'] ?? 'normal';
                                                $statusColor = $status === 'normal' ? 'success' : ($status === 'high' ? 'danger' : 'warning');
                                                $statusLabel = $status === 'normal' ? 'طبيعي' : ($status === 'high' ? 'مرتفع' : 'منخفض');
                                                ?>
                                                <span class="badge bg-<?= $statusColor ?>">
                                                    <?= $statusLabel ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <?php if (!empty($test['interpretation'])): ?>
                            <div class="mt-4">
                                <h6>تفسير النتائج:</h6>
                                <p class="mb-0"><?= nl2br(htmlspecialchars($test['interpretation'])) ?></p>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($test['recommendations'])): ?>
                            <div class="mt-4">
                                <h6>التوصيات:</h6>
                                <p class="mb-0"><?= nl2br(htmlspecialchars($test['recommendations'])) ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Test Notes -->
            <?php if (!empty($test['notes'])): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-sticky me-2"></i>
                            ملاحظات
                        </h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-0"><?= nl2br(htmlspecialchars($test['notes'])) ?></p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="col-md-4">
            <!-- Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-gear me-2"></i>
                        الإجراءات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if ($test['status'] === 'completed'): ?>
                            <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                                <i class="bi bi-printer me-2"></i>
                                طباعة النتائج
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="downloadResults()">
                                <i class="bi bi-download me-2"></i>
                                تحميل النتائج
                            </button>
                        <?php endif; ?>
                        <a href="<?= App::url('patient/medical-record') ?>" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-left me-2"></i>
                            العودة للسجلات
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Test Timeline -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-clock-history me-2"></i>
                        الجدول الزمني
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم طلب الفحص</h6>
                                <small class="text-muted">
                                    <?= date('Y-m-d H:i', strtotime($test['created_at'])) ?>
                                </small>
                            </div>
                        </div>
                        
                        <?php if ($test['status'] === 'in_progress'): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-warning"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">جاري إجراء الفحص</h6>
                                    <small class="text-muted">في المختبر</small>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($test['status'] === 'completed'): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">تم إكمال الفحص</h6>
                                    <small class="text-muted">
                                        <?= date('Y-m-d H:i', strtotime($test['completed_at'] ?? $test['updated_at'])) ?>
                                    </small>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($test['status'] === 'cancelled'): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker bg-danger"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">تم إلغاء الفحص</h6>
                                    <small class="text-muted">
                                        <?= date('Y-m-d H:i', strtotime($test['updated_at'])) ?>
                                    </small>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Test Status Info -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        معلومات إضافية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h6 class="mb-1 text-primary"><?= MedicalRecord::getLabTestStatusLabel($test['status']) ?></h6>
                                <small class="text-muted">حالة الفحص</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h6 class="mb-1 text-success"><?= count($test['results'] ?? []) ?></h6>
                            <small class="text-muted">عدد المعاملات</small>
                        </div>
                    </div>
                    
                    <?php if (!empty($test['lab_name'])): ?>
                        <hr>
                        <div class="text-center">
                            <small class="text-muted">مختبر: <?= htmlspecialchars($test['lab_name']) ?></small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Download Results
function downloadResults() {
    const testId = <?= $test['id'] ?>;
    window.open('<?= App::url('patient/download-lab-results') ?>/' + testId, '_blank');
}

// Show Alert
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert
    $('.container-fluid').prepend(alertHtml);
    
    // Auto dismiss after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-content {
    padding-left: 10px;
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.timeline-content small {
    font-size: 0.8rem;
}

.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

@media print {
    .btn, .card-header {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
}
</style> 