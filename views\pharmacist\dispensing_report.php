<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">تقرير الصرف</h1>
        <p class="text-muted">تقرير شامل عن الأدوية المصروفة</p>
    </div>
    <div>
        <button class="btn btn-outline-success" onclick="exportReport()">
            <i class="bi bi-download me-1"></i>
            تصدير التقرير
        </button>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-funnel me-2"></i>
            مرشحات التقرير
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" action="<?= App::url('pharmacist/dispensing-report') ?>" class="row g-3">
            <div class="col-md-4">
                <label for="start_date" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="start_date" name="start_date" 
                       value="<?= htmlspecialchars($startDate) ?>" required>
            </div>
            <div class="col-md-4">
                <label for="end_date" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="end_date" name="end_date" 
                       value="<?= htmlspecialchars($endDate) ?>" required>
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search me-1"></i>
                        تطبيق المرشحات
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['total_dispensed'] ?></h4>
                        <p class="mb-0">إجمالي الأدوية المصروفة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-capsule display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count($stats['top_medications']) ?></h4>
                        <p class="mb-0">الأدوية الأكثر صرفاً</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-graph-up display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= count($stats['daily_dispensing']) ?></h4>
                        <p class="mb-0">أيام النشاط</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-check display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $stats['total_dispensed'] > 0 ? round($stats['total_dispensed'] / count($stats['daily_dispensing']), 1) : 0 ?></h4>
                        <p class="mb-0">المتوسط اليومي</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calculator display-4 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Dispensed Medications Table -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-list-ul me-2"></i>
                    الأدوية المصروفة
                </h5>
                <span class="badge bg-primary"><?= count($dispensedMedications) ?> دواء</span>
            </div>
            <div class="card-body">
                <?php if (!empty($dispensedMedications)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>كود الوصفة</th>
                                    <th>المريض</th>
                                    <th>الدواء</th>
                                    <th>الكمية المصروفة</th>
                                    <th>تاريخ الصرف</th>
                                    <th>الطبيب</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($dispensedMedications as $medication): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($medication['prescription_code']) ?></strong>
                                        </td>
                                        <td><?= htmlspecialchars($medication['patient_name']) ?></td>
                                        <td><?= htmlspecialchars($medication['medication_name']) ?></td>
                                        <td>
                                            <span class="badge bg-success">
                                                <?= htmlspecialchars($medication['dispensed_quantity']) ?>
                                            </span>
                                        </td>
                                        <td><?= DateHelper::formatArabic($medication['dispensed_at']) ?></td>
                                        <td><?= htmlspecialchars($medication['doctor_name']) ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="bi bi-capsule display-4 text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد أدوية مصروفة</h5>
                        <p class="text-muted">في الفترة المحددة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Statistics Sidebar -->
    <div class="col-lg-4">
        <!-- Top Medications -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    الأدوية الأكثر صرفاً
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($stats['top_medications'])): ?>
                    <?php foreach ($stats['top_medications'] as $medication): ?>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <h6 class="mb-1"><?= htmlspecialchars($medication['medication_name']) ?></h6>
                                <small class="text-muted"><?= $medication['count'] ?> مرة</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-primary"><?= $medication['count'] ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="bi bi-graph-down display-4 text-muted mb-2"></i>
                        <p class="text-muted">لا توجد بيانات</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Daily Dispensing Chart -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-calendar-check me-2"></i>
                    الصرف اليومي
                </h5>
            </div>
            <div class="card-body">
                <?php if (!empty($stats['daily_dispensing'])): ?>
                    <?php foreach ($stats['daily_dispensing'] as $day): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <small class="text-muted"><?= DateHelper::formatArabic($day['date']) ?></small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-info"><?= $day['count'] ?></span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-3">
                        <i class="bi bi-calendar-x display-4 text-muted mb-2"></i>
                        <p class="text-muted">لا توجد بيانات</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Export Options -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تصدير التقرير</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="exportForm">
                    <div class="mb-3">
                        <label for="exportFormat" class="form-label">تنسيق التصدير</label>
                        <select class="form-select" id="exportFormat" name="format">
                            <option value="csv">CSV - ملف نصي</option>
                            <option value="pdf">PDF - ملف قابل للطباعة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="exportRange" class="form-label">نطاق التصدير</label>
                        <select class="form-select" id="exportRange" name="range">
                            <option value="filtered">الفترة المحددة</option>
                            <option value="all">جميع البيانات</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmExport">
                    <i class="bi bi-download me-1"></i>
                    تصدير
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const exportModal = new bootstrap.Modal(document.getElementById('exportModal'));
    const confirmExportBtn = document.getElementById('confirmExport');

    // تصدير التقرير
    window.exportReport = function() {
        exportModal.show();
    };

    confirmExportBtn.addEventListener('click', function() {
        const format = document.getElementById('exportFormat').value;
        const range = document.getElementById('exportRange').value;
        
        // بناء رابط التصدير
        const exportUrl = '<?= App::url('pharmacist/export-dispensing-report') ?>' + 
                         '?format=' + format + 
                         '&range=' + range +
                         '&start_date=<?= $startDate ?>' +
                         '&end_date=<?= $endDate ?>';
        
        // فتح رابط التصدير في نافذة جديدة
        window.open(exportUrl, '_blank');
        
        exportModal.hide();
    });

    // التحقق من صحة التواريخ
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');

    startDateInput.addEventListener('change', function() {
        if (endDateInput.value && this.value > endDateInput.value) {
            alert('تاريخ البداية لا يمكن أن يكون بعد تاريخ النهاية');
            this.value = '';
        }
    });

    endDateInput.addEventListener('change', function() {
        if (startDateInput.value && this.value < startDateInput.value) {
            alert('تاريخ النهاية لا يمكن أن يكون قبل تاريخ البداية');
            this.value = '';
        }
    });

    // تعيين التواريخ الافتراضية
    if (!startDateInput.value) {
        startDateInput.value = '<?= date('Y-m-01') ?>';
    }
    if (!endDateInput.value) {
        endDateInput.value = '<?= date('Y-m-t') ?>';
    }
});
</script>

<style>
.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.badge {
    font-size: 0.75rem;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

@media print {
    .btn, .modal, .card-header {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    .table th {
        background-color: #f8f9fa !important;
        -webkit-print-color-adjust: exact;
    }
}
</style> 