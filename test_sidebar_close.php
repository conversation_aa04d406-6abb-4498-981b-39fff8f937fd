<?php
/**
 * اختبار وظيفة إغلاق الشريط الجانبي
 * يمكن الوصول إليه عبر: http://localhost/HealthKey/test_sidebar_close.php
 */

// تضمين ملفات النظام
require_once 'app/core/App.php';
require_once 'app/helpers/SessionHelper.php';
require_once 'app/models/User.php';

// محاكاة تسجيل دخول المدير
session_start();
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'admin';
$_SESSION['user_name'] = 'مدير النظام';

// بيانات المستخدم الحالي
$currentUser = [
    'id' => 1,
    'first_name' => 'أحمد',
    'last_name' => 'محمد',
    'user_type' => 'admin',
    'email' => '<EMAIL>'
];

// إحصائيات تجريبية
$stats = [
    'users' => ['total' => 150],
    'appointments' => ['total' => 45],
    'prescriptions' => ['total' => 23]
];

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إغلاق الشريط الجانبي - HealthKey</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="public/css/style.css" rel="stylesheet">
    <link href="public/css/sidebar.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .test-header { background: linear-gradient(135deg, #2c5aa0 0%, #4a7bc8 100%); }
        .test-card { border: none; box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075); }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar for logged in users -->
            <div class="col-md-3 col-lg-2 px-0">
                <?php
                $sidebarFile = "../views/partials/sidebar_{$currentUser['user_type']}.php";
                if (file_exists($sidebarFile)) {
                    include $sidebarFile;
                }
                ?>
            </div>
            <div class="col-md-9 col-lg-10">
                <main class="main-content p-4">
                    <!-- Mobile Sidebar Toggle Button -->
                    <div class="mobile-sidebar-toggle d-lg-none mb-3">
                        <button class="btn btn-primary" onclick="SidebarFunctions.openSidebar()">
                            <i class="bi bi-list me-2"></i>
                            فتح القائمة
                        </button>
                    </div>
                    
                    <!-- Test Header -->
                    <div class="test-header text-white p-4 rounded mb-4">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h1 class="h3 mb-2">
                                    <i class="bi bi-gear me-2"></i>
                                    اختبار إغلاق الشريط الجانبي
                                </h1>
                                <p class="mb-0 opacity-75">اختبار وظيفة إغلاق الشريط الجانبي</p>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <div class="d-flex align-items-center justify-content-md-end">
                                    <div class="me-3">
                                        <small class="d-block opacity-75">المدير المسجل</small>
                                        <strong><?= $currentUser['first_name'] . ' ' . $currentUser['last_name'] ?></strong>
                                    </div>
                                    <div class="avatar-sm bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center">
                                        <i class="bi bi-person text-white"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Test Instructions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card test-card">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">
                                        <i class="bi bi-info-circle me-2"></i>
                                        تعليمات الاختبار
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>اختبار الهاتف (Mobile):</h6>
                                            <ol>
                                                <li>غير حجم النافذة إلى أقل من 768px</li>
                                                <li>انقر على "فتح القائمة" لفتح الشريط الجانبي</li>
                                                <li>انقر على زر الإغلاق (X) في أعلى الشريط الجانبي</li>
                                                <li>تأكد من إغلاق الشريط الجانبي</li>
                                                <li>انقر خارج الشريط الجانبي للتأكد من الإغلاق التلقائي</li>
                                            </ol>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>اختبار سطح المكتب (Desktop):</h6>
                                            <ol>
                                                <li>غير حجم النافذة إلى أكثر من 768px</li>
                                                <li>انقر على زر الإغلاق (X) في أعلى الشريط الجانبي</li>
                                                <li>تأكد من طي الشريط الجانبي</li>
                                                <li>انقر على أي رابط لفتح الشريط الجانبي مرة أخرى</li>
                                            </ol>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Test Functions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card test-card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">
                                        <i class="bi bi-play-circle me-2"></i>
                                        وظائف الاختبار
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2 d-md-flex">
                                        <button class="btn btn-primary" onclick="SidebarFunctions.openSidebar()">
                                            <i class="bi bi-list me-2"></i>
                                            فتح الشريط الجانبي
                                        </button>
                                        <button class="btn btn-warning" onclick="SidebarFunctions.closeSidebar()">
                                            <i class="bi bi-x-circle me-2"></i>
                                            إغلاق الشريط الجانبي
                                        </button>
                                        <button class="btn btn-info" onclick="SidebarFunctions.toggleSidebar()">
                                            <i class="bi bi-arrow-left-right me-2"></i>
                                            تبديل الشريط الجانبي
                                        </button>
                                    </div>
                                    
                                    <hr>
                                    
                                    <h6>حالة الشريط الجانبي:</h6>
                                    <div id="sidebar-status" class="alert alert-info">
                                        <i class="bi bi-info-circle me-2"></i>
                                        جاري التحقق من حالة الشريط الجانبي...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Test Results -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card test-card">
                                <div class="card-header bg-secondary text-white">
                                    <h5 class="mb-0">
                                        <i class="bi bi-clipboard-data me-2"></i>
                                        نتائج الاختبار
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div id="test-results">
                                        <p class="text-muted">سيتم عرض نتائج الاختبار هنا...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Sidebar JS -->
    <script src="public/js/sidebar.js"></script>
    
    <script>
    // تحديث حالة الشريط الجانبي
    function updateSidebarStatus() {
        const sidebar = document.querySelector('.sidebar-admin, .sidebar-doctor, .sidebar-patient, .sidebar-pharmacist');
        const statusElement = document.getElementById('sidebar-status');
        
        if (!sidebar) {
            statusElement.className = 'alert alert-danger';
            statusElement.innerHTML = '<i class="bi bi-exclamation-triangle me-2"></i>الشريط الجانبي غير موجود';
            return;
        }
        
        let status = '';
        let alertClass = '';
        
        if (window.innerWidth <= 768) {
            if (sidebar.classList.contains('show')) {
                status = 'الشريط الجانبي مفتوح (الهاتف)';
                alertClass = 'alert-success';
            } else {
                status = 'الشريط الجانبي مغلق (الهاتف)';
                alertClass = 'alert-warning';
            }
        } else {
            if (sidebar.classList.contains('collapsed')) {
                status = 'الشريط الجانبي مطوي (سطح المكتب)';
                alertClass = 'alert-warning';
            } else {
                status = 'الشريط الجانبي مفتوح (سطح المكتب)';
                alertClass = 'alert-success';
            }
        }
        
        statusElement.className = `alert ${alertClass}`;
        statusElement.innerHTML = `<i class="bi bi-info-circle me-2"></i>${status}`;
    }
    
    // تحديث النتائج
    function updateTestResults() {
        const resultsElement = document.getElementById('test-results');
        const sidebar = document.querySelector('.sidebar-admin, .sidebar-doctor, .sidebar-patient, .sidebar-pharmacist');
        
        if (!sidebar) {
            resultsElement.innerHTML = '<p class="text-danger">❌ الشريط الجانبي غير موجود</p>';
            return;
        }
        
        let results = '<ul class="list-unstyled">';
        
        // اختبار وجود الشريط الجانبي
        results += '<li class="text-success">✅ الشريط الجانبي موجود</li>';
        
        // اختبار زر الإغلاق
        const closeBtn = sidebar.querySelector('.sidebar-toggle');
        if (closeBtn) {
            results += '<li class="text-success">✅ زر الإغلاق موجود</li>';
        } else {
            results += '<li class="text-danger">❌ زر الإغلاق غير موجود</li>';
        }
        
        // اختبار الوظائف
        if (typeof SidebarFunctions !== 'undefined') {
            results += '<li class="text-success">✅ وظائف JavaScript محملة</li>';
            
            if (typeof SidebarFunctions.closeSidebar === 'function') {
                results += '<li class="text-success">✅ وظيفة الإغلاق متاحة</li>';
            } else {
                results += '<li class="text-danger">❌ وظيفة الإغلاق غير متاحة</li>';
            }
            
            if (typeof SidebarFunctions.openSidebar === 'function') {
                results += '<li class="text-success">✅ وظيفة الفتح متاحة</li>';
            } else {
                results += '<li class="text-danger">❌ وظيفة الفتح غير متاحة</li>';
            }
        } else {
            results += '<li class="text-danger">❌ وظائف JavaScript غير محملة</li>';
        }
        
        results += '</ul>';
        resultsElement.innerHTML = results;
    }
    
    // تحديث كل ثانية
    setInterval(function() {
        updateSidebarStatus();
        updateTestResults();
    }, 1000);
    
    // تحديث عند التحميل
    document.addEventListener('DOMContentLoaded', function() {
        updateSidebarStatus();
        updateTestResults();
    });
    </script>
</body>
</html> 