<?php
/**
 * اختبار إصلاح مشكلة Database::prepare
 */

// محاكاة تسجيل دخول الصيدلي
session_start();
$_SESSION['user_id'] = 2;
$_SESSION['user_type'] = 'pharmacist';
$_SESSION['user'] = [
    'id' => 2,
    'first_name' => 'أحمد',
    'last_name' => 'محمد',
    'email' => '<EMAIL>'
];

echo "<h1>اختبار إصلاح مشكلة Database::prepare</h1>";

try {
    // تضمين الملفات المطلوبة
    require_once 'config.php';
    require_once 'app/core/App.php';
    require_once 'app/core/Controller.php';
    require_once 'app/core/Database.php';
    require_once 'app/controllers/PharmacistController.php';
    
    echo "✅ تم تضمين الملفات بنجاح<br>";
    
    // اختبار فئة Database
    echo "<h2>اختبار فئة Database</h2>";
    $db = Database::getInstance();
    echo "✅ تم إنشاء مثيل قاعدة البيانات بنجاح<br>";
    
    $connection = $db->getConnection();
    echo "✅ تم الحصول على اتصال قاعدة البيانات بنجاح<br>";
    
    // اختبار prepare
    $stmt = $connection->prepare("SELECT COUNT(*) FROM working_hours WHERE user_id = ?");
    $stmt->execute([2]);
    $count = $stmt->fetchColumn();
    echo "✅ تم تنفيذ استعلام prepare بنجاح، عدد السجلات: " . $count . "<br>";
    
    // اختبار متحكم الصيدلي
    echo "<h2>اختبار متحكم الصيدلي</h2>";
    $controller = new PharmacistController();
    echo "✅ تم إنشاء متحكم الصيدلي بنجاح<br>";
    
    // اختبار دالة getWorkingHours
    $hours = $controller->getWorkingHours(2);
    echo "✅ تم استدعاء getWorkingHours بنجاح، عدد الساعات: " . count($hours) . "<br>";
    
    // اختبار دالة workingHours
    echo "<h2>اختبار دالة workingHours</h2>";
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $controller->workingHours();
    echo "✅ تم استدعاء دالة workingHours بنجاح<br>";
    
    echo "<h3>رابط صفحة ساعات العمل:</h3>";
    echo "<a href='index.php?url=pharmacist/working-hours' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>فتح صفحة ساعات العمل</a>";
    
    echo "<h2>✅ انتهى الاختبار بنجاح</h2>";
    
} catch (Exception $e) {
    echo "<h2>❌ خطأ في الاختبار</h2>";
    echo "<p><strong>الخطأ:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>الملف:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>السطر:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?> 