<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">إضافة دواء جديد</h1>
        <p class="text-muted">إضافة دواء جديد إلى مخزون الصيدلية</p>
    </div>
    <div>
        <a href="<?= App::url('pharmacist/inventory') ?>" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>
            العودة للمخزون
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-plus-circle me-2"></i>
                    معلومات الدواء
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="<?= App::url('pharmacist/add-inventory') ?>">
                    <div class="row">
                        <!-- اسم الدواء -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم الدواء *</label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?= htmlspecialchars(App::post('name', '')) ?>" required>
                        </div>
                        
                        <!-- الاسم العلمي -->
                        <div class="col-md-6 mb-3">
                            <label for="generic_name" class="form-label">الاسم العلمي</label>
                            <input type="text" class="form-control" id="generic_name" name="generic_name" 
                                   value="<?= htmlspecialchars(App::post('generic_name', '')) ?>">
                        </div>
                        
                        <!-- الفئة -->
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">الفئة</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">اختر الفئة</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= htmlspecialchars($category) ?>" 
                                            <?= App::post('category') == $category ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($category) ?>
                                    </option>
                                <?php endforeach; ?>
                                <option value="مسكنات" <?= App::post('category') == 'مسكنات' ? 'selected' : '' ?>>مسكنات</option>
                                <option value="مضادات حيوية" <?= App::post('category') == 'مضادات حيوية' ? 'selected' : '' ?>>مضادات حيوية</option>
                                <option value="مضادات الحموضة" <?= App::post('category') == 'مضادات الحموضة' ? 'selected' : '' ?>>مضادات الحموضة</option>
                                <option value="فيتامينات" <?= App::post('category') == 'فيتامينات' ? 'selected' : '' ?>>فيتامينات</option>
                                <option value="أخرى" <?= App::post('category') == 'أخرى' ? 'selected' : '' ?>>أخرى</option>
                            </select>
                        </div>
                        
                        <!-- الشكل الدوائي -->
                        <div class="col-md-6 mb-3">
                            <label for="dosage_form" class="form-label">الشكل الدوائي</label>
                            <select class="form-select" id="dosage_form" name="dosage_form">
                                <option value="">اختر الشكل</option>
                                <option value="أقراص" <?= App::post('dosage_form') == 'أقراص' ? 'selected' : '' ?>>أقراص</option>
                                <option value="كبسولات" <?= App::post('dosage_form') == 'كبسولات' ? 'selected' : '' ?>>كبسولات</option>
                                <option value="شراب" <?= App::post('dosage_form') == 'شراب' ? 'selected' : '' ?>>شراب</option>
                                <option value="حقن" <?= App::post('dosage_form') == 'حقن' ? 'selected' : '' ?>>حقن</option>
                                <option value="كريم" <?= App::post('dosage_form') == 'كريم' ? 'selected' : '' ?>>كريم</option>
                                <option value="مرهم" <?= App::post('dosage_form') == 'مرهم' ? 'selected' : '' ?>>مرهم</option>
                                <option value="قطرات" <?= App::post('dosage_form') == 'قطرات' ? 'selected' : '' ?>>قطرات</option>
                            </select>
                        </div>
                        
                        <!-- القوة -->
                        <div class="col-md-6 mb-3">
                            <label for="strength" class="form-label">القوة</label>
                            <input type="text" class="form-control" id="strength" name="strength" 
                                   value="<?= htmlspecialchars(App::post('strength', '')) ?>" 
                                   placeholder="مثال: 500mg">
                        </div>
                        
                        <!-- الشركة المصنعة -->
                        <div class="col-md-6 mb-3">
                            <label for="manufacturer" class="form-label">الشركة المصنعة</label>
                            <input type="text" class="form-control" id="manufacturer" name="manufacturer" 
                                   value="<?= htmlspecialchars(App::post('manufacturer', '')) ?>">
                        </div>
                        
                        <!-- الكمية -->
                        <div class="col-md-4 mb-3">
                            <label for="quantity" class="form-label">الكمية المتوفرة *</label>
                            <input type="number" class="form-control" id="quantity" name="quantity" 
                                   value="<?= htmlspecialchars(App::post('quantity', '')) ?>" min="0" required>
                        </div>
                        
                        <!-- سعر الوحدة -->
                        <div class="col-md-4 mb-3">
                            <label for="unit_price" class="form-label">سعر الوحدة (ريال) *</label>
                            <input type="number" class="form-control" id="unit_price" name="unit_price" 
                                   value="<?= htmlspecialchars(App::post('unit_price', '')) ?>" 
                                   min="0" step="0.01" required>
                        </div>
                        
                        <!-- مستوى إعادة الطلب -->
                        <div class="col-md-4 mb-3">
                            <label for="reorder_level" class="form-label">مستوى إعادة الطلب</label>
                            <input type="number" class="form-control" id="reorder_level" name="reorder_level" 
                                   value="<?= htmlspecialchars(App::post('reorder_level', '10')) ?>" min="0">
                        </div>
                        
                        <!-- تاريخ انتهاء الصلاحية -->
                        <div class="col-md-6 mb-3">
                            <label for="expiry_date" class="form-label">تاريخ انتهاء الصلاحية</label>
                            <input type="date" class="form-control" id="expiry_date" name="expiry_date" 
                                   value="<?= htmlspecialchars(App::post('expiry_date', '')) ?>">
                        </div>
                        
                        <!-- رقم الدفعة -->
                        <div class="col-md-6 mb-3">
                            <label for="batch_number" class="form-label">رقم الدفعة</label>
                            <input type="text" class="form-control" id="batch_number" name="batch_number" 
                                   value="<?= htmlspecialchars(App::post('batch_number', '')) ?>">
                        </div>
                        
                        <!-- الموقع -->
                        <div class="col-md-12 mb-3">
                            <label for="location" class="form-label">الموقع في المخزن</label>
                            <input type="text" class="form-control" id="location" name="location" 
                                   value="<?= htmlspecialchars(App::post('location', '')) ?>" 
                                   placeholder="مثال: رف A - صف 1">
                        </div>
                        
                        <!-- الوصف -->
                        <div class="col-md-12 mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      placeholder="وصف الدواء واستخداماته..."><?= htmlspecialchars(App::post('description', '')) ?></textarea>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="<?= App::url('pharmacist/inventory') ?>" class="btn btn-secondary">
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>
                            إضافة الدواء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- معلومات إضافية -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    معلومات مهمة
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="bi bi-lightbulb me-2"></i>نصائح لإضافة دواء جديد:</h6>
                    <ul class="mb-0">
                        <li>تأكد من صحة اسم الدواء والاسم العلمي</li>
                        <li>حدد الفئة المناسبة للدواء</li>
                        <li>أدخل الكمية المتوفرة بدقة</li>
                        <li>حدد مستوى إعادة الطلب المناسب</li>
                        <li>تأكد من تاريخ انتهاء الصلاحية</li>
                        <li>حدد موقع الدواء في المخزن</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="bi bi-exclamation-triangle me-2"></i>تحذيرات:</h6>
                    <ul class="mb-0">
                        <li>لا تضيف دواء منتهي الصلاحية</li>
                        <li>تأكد من صحة سعر الوحدة</li>
                        <li>احتفظ برقم الدفعة للتوثيق</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// التحقق من صحة البيانات قبل الإرسال
document.querySelector('form').addEventListener('submit', function(e) {
    const quantity = document.getElementById('quantity').value;
    const unitPrice = document.getElementById('unit_price').value;
    const expiryDate = document.getElementById('expiry_date').value;
    
    if (quantity < 0) {
        e.preventDefault();
        alert('الكمية يجب أن تكون أكبر من أو تساوي صفر');
        return;
    }
    
    if (unitPrice < 0) {
        e.preventDefault();
        alert('سعر الوحدة يجب أن يكون أكبر من أو يساوي صفر');
        return;
    }
    
    if (expiryDate && new Date(expiryDate) < new Date()) {
        if (!confirm('تاريخ انتهاء الصلاحية في الماضي. هل تريد المتابعة؟')) {
            e.preventDefault();
            return;
        }
    }
});
</script> 