<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-person-plus me-2"></i>
            اختيار مريض
        </h1>
        <p class="text-muted">اختر المريض لإضافة سجل طبي جديد</p>
    </div>
    <div>
        <a href="<?= App::url('doctor/patients') ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right me-2"></i>
            العودة لقائمة المرضى
        </a>
    </div>
</div>

<!-- Patient Selection -->
<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="bi bi-people me-2"></i>
            قائمة المرضى
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($patients)): ?>
            <!-- Empty State -->
            <div class="text-center py-5">
                <i class="bi bi-people display-1 text-muted"></i>
                <h4 class="mt-3 text-muted">لا توجد مرضى</h4>
                <p class="text-muted">لم يتم تسجيل أي مرضى بعد</p>
                <a href="<?= App::url('doctor/patients') ?>" class="btn btn-primary">
                    <i class="bi bi-arrow-right me-2"></i>
                    العودة لقائمة المرضى
                </a>
            </div>
        <?php else: ?>
            <!-- Patients Grid -->
            <div class="row">
                <?php foreach ($patients as $patient): ?>
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body text-center">
                                <div class="avatar-lg bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3">
                                    <i class="bi bi-person text-primary fs-1"></i>
                                </div>
                                <h5 class="card-title"><?= htmlspecialchars(User::getFullName($patient)) ?></h5>
                                <p class="text-muted mb-2">
                                    <i class="bi bi-envelope me-1"></i>
                                    <?= htmlspecialchars($patient['email'] ?? 'غير محدد') ?>
                                </p>
                                <p class="text-muted mb-3">
                                    <i class="bi bi-telephone me-1"></i>
                                    <?= htmlspecialchars($patient['phone'] ?? 'غير محدد') ?>
                                </p>
                                
                                <!-- Patient Status -->
                                <div class="mb-3">
                                    <?php if ($patient['is_active']): ?>
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle me-1"></i>
                                            نشط
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">
                                            <i class="bi bi-x-circle me-1"></i>
                                            غير نشط
                                        </span>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Action Buttons -->
                                <div class="d-grid gap-2">
                                    <a href="<?= App::url('doctor/add-medical-record?patient_id=' . $patient['id']) ?>" 
                                       class="btn btn-success">
                                        <i class="bi bi-file-earmark-medical me-2"></i>
                                        إضافة سجل طبي
                                    </a>
                                    <div class="btn-group" role="group">
                                        <a href="<?= App::url('doctor/view-patient/' . $patient['id']) ?>" 
                                           class="btn btn-outline-primary btn-sm" 
                                           title="عرض الملف الطبي">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="<?= App::url('doctor/create-prescription?patient_id=' . $patient['id']) ?>" 
                                           class="btn btn-outline-info btn-sm" 
                                           title="إنشاء وصفة طبية">
                                            <i class="bi bi-prescription2"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Quick Actions -->
<?php if (!empty($patients)): ?>
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <i class="bi bi-prescription2 display-4 mb-3"></i>
                    <h5>إنشاء وصفة طبية</h5>
                    <p class="mb-3">إنشاء وصفة طبية جديدة</p>
                    <a href="<?= App::url('doctor/create-prescription') ?>" class="btn btn-light">
                        <i class="bi bi-plus-circle me-2"></i>
                        إنشاء وصفة
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <i class="bi bi-calendar-plus display-4 mb-3"></i>
                    <h5>حجز موعد</h5>
                    <p class="mb-3">حجز موعد جديد لمريض</p>
                    <a href="<?= App::url('doctor/appointments') ?>" class="btn btn-light">
                        <i class="bi bi-calendar-plus me-2"></i>
                        حجز موعد
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <i class="bi bi-list-ul display-4 mb-3"></i>
                    <h5>السجلات الطبية</h5>
                    <p class="mb-3">عرض جميع السجلات الطبية</p>
                    <a href="<?= App::url('doctor/medical-records') ?>" class="btn btn-light">
                        <i class="bi bi-list me-2"></i>
                        عرض السجلات
                    </a>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<style>
.avatar-lg {
    width: 80px;
    height: 80px;
}
</style> 