<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title . ' - ' . APP_NAME : APP_NAME ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="<?= App::url('public/css/style.css') ?>" rel="stylesheet">
    <!-- Sidebar CSS -->
    <link href="<?= App::url('public/css/sidebar.css') ?>" rel="stylesheet">
    
    <!-- RTL Support -->
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
        }
        .navbar-brand {
            font-weight: bold;
            color: #2c5aa0 !important;
        }
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
            border-left: 1px solid #dee2e6;
        }
        .main-content {
            min-height: 100vh;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }
        .btn-primary {
            background-color: #2c5aa0;
            border-color: #2c5aa0;
        }
        .btn-primary:hover {
            background-color: #1e3f73;
            border-color: #1e3f73;
        }
        .text-primary {
            color: #2c5aa0 !important;
        }
        .alert {
            border-radius: 0.375rem;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
        <div class="container-fluid">
            <a class="navbar-brand" href="<?= App::url() ?>">
                <i class="bi bi-heart-pulse-fill me-2"></i>
                <?= APP_NAME ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <?php if (!isset($currentUser)): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= App::url() ?>">الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= App::url('about') ?>">عن النظام</a>
                        </li>
                    <?php endif; ?>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if (isset($currentUser)): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-1"></i>
                                <?= htmlspecialchars($currentUser['first_name'] . ' ' . $currentUser['last_name']) ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?= App::url($currentUser['user_type'] . '/profile') ?>">
                                    <i class="bi bi-person me-2"></i>الملف الشخصي
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?= App::url('logout') ?>">
                                    <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= App::url('login') ?>">تسجيل الدخول</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= App::url('register') ?>">إنشاء حساب</a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar for logged in users -->
            <?php if (isset($currentUser)): ?>
                <div class="col-md-3 col-lg-2 px-0">
                    <?php
                    $sidebarFile = "../views/partials/sidebar_{$currentUser['user_type']}.php";
                    if (file_exists($sidebarFile)) {
                        include $sidebarFile;
                    }
                    ?>
                </div>
                <div class="col-md-9 col-lg-10">
            <?php else: ?>
                <div class="col-12">
            <?php endif; ?>
                
                <main class="main-content p-4">
                    <!-- Mobile Sidebar Toggle Button -->
                    <?php if (isset($currentUser)): ?>
                        <div class="mobile-sidebar-toggle d-lg-none mb-3">
                            <button class="btn btn-primary" onclick="openSidebarMobile()">
                                <i class="bi bi-list me-2"></i>
                                فتح القائمة
                            </button>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Flash Messages -->
                    <?php
                    $flashMessage = null;
                    if (isset($_SESSION['flash_message'])) {
                        $flashMessage = [
                            'text' => $_SESSION['flash_message'],
                            'type' => $_SESSION['flash_type'] ?? 'info'
                        ];
                        unset($_SESSION['flash_message']);
                        unset($_SESSION['flash_type']);
                    }
                    
                    if ($flashMessage):
                        $alertClass = match($flashMessage['type']) {
                            'error' => 'alert-danger',
                            'success' => 'alert-success',
                            'warning' => 'alert-warning',
                            default => 'alert-info'
                        };
                    ?>
                        <div class="alert <?= $alertClass ?> alert-dismissible fade show" role="alert">
                            <?= htmlspecialchars($flashMessage['text']) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Page Content -->
                    <?= $content ?? '' ?>
                </main>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p class="mb-0">
                &copy; <?= date('Y') ?> <?= APP_NAME ?> - جميع الحقوق محفوظة
                <span class="mx-2">|</span>
                الإصدار <?= APP_VERSION ?>
            </p>
        </div>
    </footer>

    <!-- jQuery (must be loaded first) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?= App::url('public/js/main.js') ?>"></script>
    <!-- Sidebar JS -->
    <script src="<?= App::url('public/js/sidebar.js') ?>"></script>
    
    <!-- Additional Scripts -->
    <?php if (isset($scripts)): ?>
        <?php foreach ($scripts as $script): ?>
            <script src="<?= App::url($script) ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
</body>
</html>
