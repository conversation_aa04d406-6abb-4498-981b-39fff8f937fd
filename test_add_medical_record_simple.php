<?php
/**
 * ملف اختبار بسيط لصفحة إضافة السجل الطبي
 * يمكن الوصول إليه عبر: http://localhost/HealthKey/test_add_medical_record_simple.php
 */

// تضمين ملفات النظام
require_once 'app/core/App.php';
require_once 'app/helpers/SessionHelper.php';
require_once 'app/models/User.php';

// محاكاة تسجيل دخول الطبيب
session_start();
$_SESSION['user_id'] = 2;
$_SESSION['user_type'] = 'doctor';
$_SESSION['user_name'] = 'د. أحمد محمد';

// تحميل النموذج
$userModel = new User();

// الحصول على بيانات المريض
$patient = $userModel->findById(1); // افتراض أن المريض له معرف 1

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار صفحة إضافة السجل الطبي - HealthKey</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .test-header { background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%); }
    </style>
</head>
<body>
    <div class="container py-4">
        <!-- Header -->
        <div class="test-header text-white p-4 rounded mb-4">
            <h1 class="h3 mb-2">
                <i class="bi bi-file-earmark-medical me-2"></i>
                اختبار صفحة إضافة السجل الطبي
            </h1>
            <p class="mb-0 opacity-75">اختبار الصفحة بعد إصلاح الأخطاء</p>
        </div>

        <!-- Test Results -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-check-circle me-2"></i>
                            نتائج الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>معلومات المريض:</h6>
                                <ul class="list-unstyled">
                                    <li><strong>الاسم:</strong> <?= $patient ? User::getFullName($patient) : 'غير محدد' ?></li>
                                    <li><strong>البريد الإلكتروني:</strong> <?= $patient['email'] ?? 'غير محدد' ?></li>
                                    <li><strong>رقم الهاتف:</strong> <?= $patient['phone'] ?? 'غير محدد' ?></li>
                                    <li><strong>معرف المريض:</strong> <?= $patient['id'] ?? 'غير محدد' ?></li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>روابط الاختبار:</h6>
                                <div class="d-grid gap-2">
                                    <a href="doctor/add-medical-record?patient_id=1" class="btn btn-success">
                                        <i class="bi bi-file-earmark-medical me-2"></i>
                                        إضافة سجل طبي جديد
                                    </a>
                                    <a href="doctor/patients" class="btn btn-primary">
                                        <i class="bi bi-people me-2"></i>
                                        قائمة المرضى
                                    </a>
                                    <a href="doctor/medical-records" class="btn btn-info">
                                        <i class="bi bi-list me-2"></i>
                                        السجلات الطبية
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            حالة الاختبار
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle me-2"></i>
                            <strong>تم إصلاح الأخطاء:</strong>
                            <ul class="mb-0 mt-2">
                                <li>إصلاح خطأ `htmlspecialchars()` مع القيم الفارغة</li>
                                <li>تحسين دالة `User::getFullName()`</li>
                                <li>إضافة معالجة للقيم الفارغة</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="bi bi-lightbulb me-2"></i>
                            <strong>تعليمات:</strong>
                            <ol class="mb-0 mt-2">
                                <li>انقر على "إضافة سجل طبي جديد" لاختبار الصفحة</li>
                                <li>تحقق من عدم ظهور أي أخطاء</li>
                                <li>اختبر إدخال البيانات وحفظ السجل</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="text-center text-muted">
                    <small>
                        <i class="bi bi-code-slash me-1"></i>
                        نظام HealthKey - اختبار صفحة إضافة السجل الطبي
                        <span class="mx-2">|</span>
                        <i class="bi bi-calendar me-1"></i>
                        <?= date('Y-m-d H:i') ?>
                    </small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 